<template>
  <div class="wrap">
    <div class="btn-wrap">
      <el-button type="primary" @click="doEdit" v-if="type === 'view'" v-action:inspectTask|editInspectTask>编辑计划</el-button>
      <el-button type="success" @click="doSave" v-if="canEdit">保存计划</el-button>
      <el-button @click="doCancle">返回</el-button>
    </div>
    <div class="content">
      <div class="left">
        <el-card class="form-card">
          <div slot="header" class="card-header">
            <div>
              <svg-icon class="title-icon" icon-class="deviceInspect@info" />
              基本信息
            </div>
          </div>
          <el-form ref="form" :model="form" :rules="rules" label-width="153px" label-position="left">
            <el-form-item label="计划名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入计划名称" v-if="canEdit"></el-input>
              <span v-else>{{ form.name }}</span>
            </el-form-item>
            <el-form-item label="计划描述" prop="remark">
              <el-input type="textarea" :rows="2" placeholder="请输入计划描述" v-model="form.remark" v-if="canEdit"> </el-input>
              <span v-else>{{ form.remark }}</span>
            </el-form-item>
            <el-form-item label="执行类型" prop="inspectType">
              <el-radio-group v-model="form.inspectType" @input="changeInspectType" v-if="canEdit">
                <el-radio label="auto">自动执行</el-radio>
                <el-radio label="manual">手动执行</el-radio>
              </el-radio-group>
              <span v-else>{{ form.inspectType === 'auto' ? '自动执行' : '手动执行' }}</span>
            </el-form-item>
            <el-form-item label="执行时间" prop="timeParams" v-if="form.inspectType === 'auto'">
              <span v-if="!canEdit">{{ formatTime(form.timeParams) }}</span>
              <el-time-picker
                style="width: 120px; margin-right: 5px"
                v-model="form.timeParams.specificTime"
                placeholder="特定时间"
                value-format="HH:mm"
                v-if="canEdit"
              >
              </el-time-picker>
              <el-select
                style="width: 120px; margin-right: 5px"
                v-model="form.timeParams.repeatType"
                placeholder="重复类型"
                :filterable="true"
                v-if="canEdit"
                @change="
                  {
                    form.timeParams.specificDay = null;
                    form.timeParams.weekday = null;
                    form.timeParams.dayOfMonth = null;
                  }
                "
              >
                <el-option :label="item.label" :value="item.value" v-for="item in REPEAT_TYPE_ENUM" :key="item.value"></el-option>
              </el-select>
              <el-select
                style="width: 160px"
                v-if="form.timeParams.repeatType === 'everyWeekDay' && canEdit"
                v-model="form.timeParams.weekday"
                placeholder="重复周几"
                multiple
                collapse-tags
              >
                <el-option :label="item.label" :value="item.value" v-for="item in REPEAT_DAY_ENUM" :key="item.value"></el-option>
              </el-select>
              <el-select
                style="width: 160px"
                v-if="form.timeParams.repeatType === 'everyMonth' && canEdit"
                v-model="form.timeParams.dayOfMonth"
                placeholder="重复每月几号"
                multiple
                collapse-tags
              >
                <el-option :label="item.label" :value="item.value" v-for="item in dayOptions()" :key="item.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="超时时长" prop="timeoutDuration">
              <el-input-number
                v-if="canEdit"
                style="width: 180px"
                controls-position="right"
                placeholder="请输入超时时间"
                v-model="form.timeoutDuration"
                :min="0"
                step-strictly
              ></el-input-number>
              <span v-else>{{ form.timeoutDuration }}</span>
              秒
            </el-form-item>
            <el-form-item label="巡检人" prop="inspector">
              <mul-input
                v-if="canEdit"
                @clear="clearUsers"
                placeholder="请选择巡检人"
                :value="form.inspector"
                @click="selectUser"
                style="width: 240px"
              ></mul-input>
              <span v-else>{{ form.inspector }}</span>
            </el-form-item>
            <el-form-item label="是否启用" prop="enableStatus">
              <el-switch v-model="form.enableStatus" v-if="canEdit"></el-switch>
              <span v-else>{{ form.enableStatus ? '启用' : '禁用' }}</span>
            </el-form-item>
            <el-form-item label="是否自动创建工单" prop="createOrder">
              <el-switch v-model="form.createOrder" v-if="canEdit"></el-switch>
              <span v-else>{{ form.createOrder ? '是' : '否' }}</span>
            </el-form-item>
          </el-form>
        </el-card>
        <el-card class="table-card">
          <div slot="header" class="card-header">
            <div> <svg-icon class="title-icon" icon-class="deviceInspect@list" /> 巡检设备列表 </div>
            <div class="card-header-btn">
              <el-select v-model="searchDeviceId" placeholder="选择设备" size="mini" style="width: 160px" class="mr-10" filterable clearable>
                <el-option v-for="(item, index) in form.inspectionDevices" :key="index" :label="item.deviceName" :value="item.deviceId"> </el-option>
              </el-select>
              <el-button class="edit-btn" icon="el-icon-plus" size="mini" @click="addDevice" v-if="canEdit">添加</el-button>
              <el-button class="delete-btn" icon="el-icon-delete" size="mini" @click="deleteDevice" v-if="canEdit">删除</el-button>
              <el-button class="purple-btn" icon="el-icon-tickets" size="mini" v-if="canEdit && type === 'add'" @click="applyTemplate"
                >应用模板</el-button
              >
            </div>
          </div>

          <el-table
            :data="searchDeviceId ? form.inspectionDevices.filter((item) => item.deviceId === searchDeviceId) : form.inspectionDevices"
            height="100%"
            header-cell-class-name="device-table-header"
            @selection-change="handleDeviceSelectionChange"
            :span-method="handleSpanMethod"
          >
            <template slot="empty">
              <el-empty description="暂无设备" :image-size="60"> </el-empty>
            </template>
            <el-table-column type="selection" width="35" align="center"> </el-table-column>
            <el-table-column prop="deviceName" label="设备名称" min-width="180" show-overflow-tooltip>
              <template slot-scope="{ row }">
                <!-- 接口返回设备无deviceName时设备未添加 -->
                <template v-if="row.deviceName">
                  {{ row.deviceName }}
                </template>
                <template v-else>
                  <div class="device-not-exist"> <i class="el-icon-warning-outline icon" />该设备【{{ row.deviceId }}】可能已被删除或未添加</div>
                </template>
              </template>
            </el-table-column>
            <el-table-column label="巡检项个数" min-width="120" show-overflow-tooltip>
              <template slot-scope="{ row }">
                {{ row.inspectionParams.length }}
              </template>
            </el-table-column>
            <el-table-column label="操作" min-width="120">
              <template slot-scope="{ row }">
                <el-button @click="changeSelectedDevice(row.deviceId)" size="mini" type="primary" plain>查看巡检项</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>
      <div class="right">
        <el-card class="table-card">
          <div slot="header" class="card-header">
            <div class="tag-wrap">
              <svg-icon class="title-icon" icon-class="deviceInspect@detail" />
              巡检详情 <el-tag class="tag" v-if="selectedDevice.deviceName">{{ selectedDevice.deviceName }}</el-tag>
            </div>
          </div>
          <div class="basic-inspect">
            <div class="title">
              <span class="title-text"> 基本状态巡检 </span>
            </div>
            <div class="table-wrap">
              <el-table
                :data="selectedDevice.deviceId ? getBasicStatusInspectList : []"
                height="100%"
                style="width: 100%"
                header-cell-class-name="device-table-header"
              >
                <template slot="empty"> 未选中巡检设备 </template>
                <el-table-column prop="name" label="巡检类型" min-width="180"> </el-table-column>
                <el-table-column prop="description" label="描述" min-width="180"> </el-table-column>
              </el-table>
            </div>
          </div>
          <div class="property-inspect">
            <div class="title">
              <span class="title-text">设备属性巡检</span>
              <div>
                <el-button class="edit-btn" icon="el-icon-plus" size="mini" @click="addPropertyInspect" v-if="canEdit">添加</el-button>
                <el-button class="delete-btn" icon="el-icon-delete" size="mini" @click="deletePropertyInspect" v-if="canEdit">删除</el-button>
              </div>
            </div>
            <div class="table-wrap">
              <el-table
                :data="getPropertyInspectParams()"
                height="100%"
                style="width: 100%"
                header-cell-class-name="device-table-header"
                @selection-change="handlePropertySelectionChange"
              >
                <template slot="empty">
                  <el-empty description="暂无巡检项" :image-size="60"> </el-empty>
                </template>
                <el-table-column type="selection" width="35" align="center"> </el-table-column>
                <el-table-column prop="metric" label="巡检属性" min-width="200" show-overflow-tooltip>
                  <template slot-scope="{ row }">
                    <template v-if="row.isCompleted">
                      {{ getPropertyName(row) }}
                    </template>
                    <template v-else>
                      <el-select v-model="row.metric" placeholder="请选择巡检属性" style="width: 180px" @change="selectedPropertyChange(row)">
                        <el-option :label="item.label" :value="item.value" v-for="item in inspectPropertyList" :key="item.value"></el-option>
                      </el-select>
                    </template>
                  </template>
                </el-table-column>
                <el-table-column prop="operator" label="比较符" min-width="130" show-overflow-tooltip>
                  <template slot-scope="{ row }">
                    <template v-if="row.isCompleted">
                      {{ PROPERTY_OPT_ENUM.find((item) => item.value === row.operator).label }}
                    </template>
                    <template v-else>
                      <el-select
                        v-model="row.operator"
                        placeholder="比较符"
                        style="width: 110px"
                        v-if="['INTEGER', 'DECIMAL'].includes(getPropertyDataType(row))"
                      >
                        <el-option :label="item.label" :value="item.value" v-for="item in PROPERTY_OPT_ENUM" :key="item.value"></el-option>
                      </el-select>
                      <!-- 非数字比较符 -->
                      <el-select v-model="row.operator" placeholder="比较符" style="width: 110px" v-else>
                        <el-option :label="item.label" :value="item.value" v-for="item in PROPERTY_OPT_ENUM_NOT_NUMBER" :key="item.value"></el-option>
                      </el-select>
                    </template>
                  </template>
                </el-table-column>
                <el-table-column prop="threshold" label="预期值" min-width="200">
                  <template slot-scope="{ row }">
                    <template v-if="row.isCompleted">
                      {{ getPropertyValueLabel(row) }}
                    </template>
                    <template v-else>
                      <!-- 数值类型 -->
                      <template v-if="['INTEGER', 'DECIMAL'].includes(getPropertyDataType(row))">
                        <el-input-number
                          v-model="row.threshold"
                          style="width: 160px"
                          controls-position="right"
                          :min="getPropertyMin(row)"
                          :max="getPropertyMax(row)"
                          :step="getPropertyStep(row)"
                        >
                        </el-input-number>
                      </template>

                      <!-- 布尔类型 -->
                      <template v-else-if="getPropertyDataType(row) === 'BOOLEAN'">
                        <el-select v-model="row.threshold" style="width: 160px">
                          <el-option v-for="option in getPropertyBolOptions(row)" :key="option.value" :label="option.label" :value="option.value">
                          </el-option>
                        </el-select>
                      </template>

                      <!-- 字符串类型 -->
                      <template v-else-if="getPropertyDataType(row) === 'STRING'">
                        <el-input v-model="row.threshold" style="width: 160px" :maxlength="getPropertyMaxLength(row)" clearable />
                      </template>

                      <!-- 枚举类型 -->
                      <template v-else-if="['ENUM', 'NUMBER_ENUM'].includes(getPropertyDataType(row))">
                        <el-select v-model="row.threshold" style="width: 160px">
                          <el-option v-for="option in getPropertyOptions(row)" :key="option.value" :label="option.label" :value="option.value">
                          </el-option>
                        </el-select>
                      </template>
                    </template>
                  </template>
                </el-table-column>
                <el-table-column prop="description" label="描述" min-width="150">
                  <template slot-scope="{ row }">
                    <template v-if="row.isCompleted">
                      {{ row.description }}
                    </template>
                    <template v-else>
                      <el-input v-model="row.description" placeholder="请输入描述" style="width: 130px"></el-input>
                    </template>
                  </template>
                </el-table-column>
                <el-table-column label="操作" min-width="100" align="center" v-if="canEdit">
                  <template slot-scope="{ row }">
                    <el-button size="mini" type="text" @click="editPropertyInspect(row)" v-if="row.isCompleted">编辑</el-button>
                    <el-button size="mini" type="success" plain @click="confirmPropertyInspect(row)" v-else>完成</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script>
  import { REPEAT_TYPE_ENUM, REPEAT_DAY_ENUM, PROPERTY_OPT_ENUM, PROPERTY_OPT_ENUM_NOT_NUMBER } from '@/views/iotControl/enum';
  import { getInspectDetail, saveInspect, getProductDetailByCode } from '@/api/iotControl';
  import transform from '@/utils/transform';
  import mulInput from '@/components/mulInput';
  import userTransfer from '@/views/iotControl/modules/userTransfer.vue';
  import addDevice from '@/views/iotControl/deviceMaintenance/viewEditInspect/modules/addDevice.vue';
  import applyTemplate from '@/views/iotControl/deviceMaintenance/viewEditInspect/modules/applyTemplate.vue';
  export default {
    components: { mulInput, userTransfer, addDevice, applyTemplate },
    props: {},
    data() {
      return {
        REPEAT_TYPE_ENUM,
        REPEAT_DAY_ENUM,
        PROPERTY_OPT_ENUM,
        PROPERTY_OPT_ENUM_NOT_NUMBER,
        //整个计划是否可编辑
        canEdit: true,
        type: '',
        id: '',
        form: {
          id: '',
          name: '',
          //备注
          remark: '',
          //【manual】【auto】自动执行和手动执行
          inspectType: 'manual',
          timeParams: {
            specificTime: null,
            repeatType: null,
            dayOfMonth: null,
            weekday: null,
            specificDay: null,
          },
          timeoutDuration: undefined,
          enableStatus: true,
          //{deviceId:'',code:'',inspectionParams:[]}
          inspectionDevices: [],
          //巡检人的用户名，默认使用空间的管理员的用户名
          inspector: '',
          //是否自动创建工单
          createOrder: true,
        },
        //巡检设备列表筛选
        searchDeviceId: '',
        rules: {
          name: [{ required: true, message: '请输入计划名称', trigger: 'blur' }],
          timeoutDuration: [{ required: true, message: '请输入超时时长', trigger: 'blur' }],
          inspector: [{ required: true, message: '请选择巡检人', trigger: ['blur', 'change'] }],
        },
        basicStatusInspectList: [
          { metricType: 'onlineStatus', name: '设备状态', description: '设备状态' },
          { metricType: 'lifespan', name: '设备生命周期', description: '设备寿命状态' },
          { metricType: 'warranty', name: '设备保修状态', description: '设备保修状态' },
        ],
        //当前查看巡检项的设备
        selectedDevice: {},
        //当前设备巡检属性列表
        inspectPropertyList: [],
        //当前选中巡检属性
        propertySelection: [],
        //当前选中巡检设备
        deviceSelection: [],
        //产品可巡检属性列表(分别请求获取，存储避免重复请求)
        productInspectPropList: [],
      };
    },
    created() {
      this.type = this.$route.params.type;
      this.id = this.$route.params.id;
      if (this.type === 'view') {
        this.getInspectDetail();
        //查看时，整个计划不可编辑
        this.canEdit = false;
      }
    },
    computed: {
      getBasicStatusInspectList() {
        const params = this.form.inspectionDevices.find((item) => item.deviceId === this.selectedDevice.deviceId)?.inspectionParams || [];
        //仅显示当前选中设备包含的
        const existingMetricTypes = params.map((param) => param.metricType) || [];
        return this.basicStatusInspectList.filter((item) => existingMetricTypes.includes(item.metricType));
      },
    },
    methods: {
      getInspectDetail() {
        getInspectDetail(this.id).then(({ data }) => {
          this.form = data;
          this.formatInspectionDevices();
        });
      },
      //编辑时，预处理设备列表
      formatInspectionDevices() {
        //每项添加isCompleted:true
        this.form.inspectionDevices.forEach((item) => {
          item.inspectionParams.forEach((param) => {
            this.$set(param, 'isCompleted', true);
          });
        });
      },
      changeInspectType(value) {
        if (value === 'auto') {
          this.form.timeParams = {
            specificTime: null,
            repeatType: null,
            dayOfMonth: null,
            weekday: null,
            specificDay: null,
          };
        } else {
          this.form.timeParams = null;
        }
      },
      dayOptions() {
        return Array.from({ length: 31 }, (item, index) => ({
          label: `${index + 1}号`,
          value: index + 1,
        }));
      },
      // 巡检人弹窗
      userTransferDlg: transform(userTransfer),
      //选择用户
      selectUser() {
        this.userTransferDlg({
          propsData: {
            users: this.form.inspector ? [{ username: this.form.inspector }] : [],
            isMulti: false,
          },
          methods: {
            handleSelect: (users) => {
              this.form.inspector = users[0].username;
            },
          },
        });
      },
      clearUsers() {
        this.form.inspector = '';
      },
      addDeviceDlg: transform(addDevice),
      addDevice() {
        this.addDeviceDlg({
          propsData: {
            selectedDevices: this.form.inspectionDevices,
          },
          methods: {
            handleSelect: (devices) => {
              //inspectionDevices中已有的设备不添加
              const newDevices = devices
                .filter((item) => !this.form.inspectionDevices.some((device) => device.deviceId === item.deviceId))
                .map((item) => ({
                  deviceName: item.deviceName,
                  deviceId: item.deviceId,
                  code: item.code,
                  //设备默认包含在线状态，生命周期，保修状态巡检
                  inspectionParams: [
                    {
                      metricType: 'onlineStatus',
                      metric: 'onlineStatus',
                      operator: 'EQ',
                      threshold: '',
                      description: '在线状态',
                      inspectionType: 'staticThreshold',
                      isCompleted: true,
                    },
                    {
                      metricType: 'lifespan',
                      metric: 'lifespan',
                      operator: 'EQ',
                      threshold: '',
                      description: '寿命状态',
                      inspectionType: 'staticThreshold',
                      isCompleted: true,
                    },
                    {
                      metricType: 'warranty',
                      metric: 'warranty',
                      operator: 'EQ',
                      threshold: '',
                      description: '保修状态',
                      inspectionType: 'staticThreshold',
                      isCompleted: true,
                    },
                  ],
                }));
              this.form.inspectionDevices = [...this.form.inspectionDevices, ...newDevices];
            },
          },
        });
      },
      deleteDevice() {
        if (this.deviceSelection.length === 0) {
          this.$message.warning('请先选择设备');
          return;
        }
        this.form.inspectionDevices = this.form.inspectionDevices.filter((item) => !this.deviceSelection.includes(item));
      },
      handleDeviceSelectionChange(selection) {
        this.deviceSelection = selection;
      },
      //查看设备巡检项
      changeSelectedDevice(deviceId) {
        // 切换前需校验，是否填写完成
        const currentDevice = this.form.inspectionDevices.find((item) => item.deviceId === this.selectedDevice.deviceId);
        if (currentDevice) {
          const hasIncomplete = currentDevice.inspectionParams.some((param) => !param.isCompleted);
          if (hasIncomplete) {
            this.$message.warning(`请填写设备【${currentDevice.deviceName}】的完整巡检项`);
            return;
          }
        }
        this.selectedDevice = this.form.inspectionDevices.find((item) => item.deviceId === deviceId);
        if (this.selectedDevice && this.selectedDevice.code) {
          this.getInspectPropertyList();
        }
      },
      getInspectPropertyList() {
        if (!this.productInspectPropList[this.selectedDevice.code]) {
          getProductDetailByCode(this.selectedDevice.code).then(({ data }) => {
            //仅显示基本数据类型的属性
            this.inspectPropertyList = data.properties
              .filter((item) => ['INTEGER', 'DECIMAL', 'BOOLEAN', 'STRING', 'ENUM', 'NUMBER_ENUM'].includes(item.constraint.dataType))
              .map((item) => ({
                ...item,
                label: item.name,
                value: item.address,
              }));
            this.productInspectPropList[this.selectedDevice.code] = this.inspectPropertyList;
          });
        } else {
          this.inspectPropertyList = this.productInspectPropList[this.selectedDevice.code];
        }
      },
      getPropertyInspectParams() {
        //仅显示metricType为propertyKey的巡检项
        return (
          this.form.inspectionDevices
            .find((item) => item.deviceId === this.selectedDevice.deviceId)
            ?.inspectionParams.filter((param) => param.metricType === 'propertyKey') || []
        );
      },
      handlePropertySelectionChange(selection) {
        this.propertySelection = selection;
      },
      addPropertyInspect() {
        if (!this.selectedDevice.deviceId) {
          this.$message.warning('请先选择设备');
          return;
        }
        this.form.inspectionDevices
          .find((item) => item.deviceId === this.selectedDevice.deviceId)
          ?.inspectionParams.push({
            metricType: 'propertyKey',
            metric: '',
            operator: 'EQ',
            threshold: '',
            description: '',
            inspectionType: 'staticThreshold',
            //是否填写完成
            isCompleted: false,
          });
      },
      deletePropertyInspect() {
        if (this.propertySelection.length === 0) {
          this.$message.warning('请先选择巡检项');
          return;
        }
        const currentDevice = this.form.inspectionDevices.find((item) => item.deviceId === this.selectedDevice.deviceId);
        if (currentDevice) {
          currentDevice.inspectionParams = currentDevice.inspectionParams.filter((item) => !this.propertySelection.includes(item));
        }
      },
      // 获取属性的数据类型
      getPropertyDataType(row) {
        const property = this.inspectPropertyList.find((p) => p.value === row.metric);
        return property?.constraint?.dataType;
      },

      // 获取数值类型的最小值
      getPropertyMin(row) {
        const property = this.inspectPropertyList.find((p) => p.value === row.metric);
        return this.isValidValue(property?.constraint?.min) ? property.constraint.min : -Infinity;
      },

      // 获取数值类型的最大值
      getPropertyMax(row) {
        const property = this.inspectPropertyList.find((p) => p.value === row.metric);
        return this.isValidValue(property?.constraint?.max) ? property.constraint.max : Infinity;
      },

      // 获取数值类型的步长
      getPropertyStep(row) {
        const property = this.inspectPropertyList.find((p) => p.value === row.metric);
        return this.isValidValue(property?.constraint?.step) ? property.constraint.step : 1;
      },

      // 获取字符串类型的最大长度
      getPropertyMaxLength(row) {
        const property = this.inspectPropertyList.find((p) => p.value === row.metric);
        return property?.constraint?.maxLength;
      },

      // 获取枚举类型的选项
      getPropertyOptions(row) {
        const property = this.inspectPropertyList.find((p) => p.value === row.metric);
        return Object.entries(property?.constraint?.range).map(([label, value]) => ({
          value,
          label,
        }));
      },

      // 获取布尔类型的选项
      getPropertyBolOptions(row) {
        const property = this.inspectPropertyList.find((p) => p.value === row.metric);
        return [
          {
            label: property?.constraint?.trueText,
            value: true,
          },
          {
            label: property?.constraint?.falseText,
            value: false,
          },
        ];
      },

      // 判断值是否有效
      isValidValue(value) {
        return value !== undefined && value !== null && value !== '';
      },
      editPropertyInspect(row) {
        this.$set(row, 'isCompleted', false);
      },
      confirmPropertyInspect(row) {
        if (this.isValidValue(row.metric) && this.isValidValue(row.operator) && this.isValidValue(row.threshold)) {
          row.isCompleted = true;
        } else {
          this.$message.warning('请填写完整巡检项');
        }
      },
      // 取消
      doCancle() {
        this.$router.go(-1);
      },
      doEdit() {
        this.canEdit = true;
      },
      async doSave() {
        let bValid = await this.$refs.form.validate();
        if (!bValid) {
          return;
        }
        //校验自动执行时间
        if (this.form.inspectType === 'auto') {
          if (!this.form.timeParams.specificTime) {
            this.$message.warning('请选择执行时间');
            return;
          } else if (!this.form.timeParams.repeatType) {
            this.$message.warning('请选择执行周期');
            return;
          } else if (this.form.timeParams.repeatType === 'everyWeekDay') {
            if (!this.form.timeParams.weekday || this.form.timeParams.weekday.length === 0) {
              this.$message.warning('请选择每周执行日');
              return;
            }
          } else if (this.form.timeParams.repeatType === 'everyMonth') {
            if (!this.form.timeParams.dayOfMonth || this.form.timeParams.dayOfMonth.length === 0) {
              this.$message.warning('请选择每月执行日');
              return;
            }
          }
        }
        const currentDevice = this.form.inspectionDevices.find((item) => item.deviceId === this.selectedDevice.deviceId);
        if (currentDevice) {
          const hasIncomplete = currentDevice.inspectionParams.some((param) => !param.isCompleted);
          if (hasIncomplete) {
            this.$message.warning(`请填写设备【${currentDevice.deviceName}】的完整巡检项`);
            return;
          }
        }
        saveInspect(this.form).then(() => {
          this.$message.success('保存成功');
          this.$router.go(-1);
        });
      },
      // 获取属性名称
      getPropertyName(row) {
        const property = this.inspectPropertyList.find((p) => p.value === row.metric);
        return property?.name || row.metric;
      },
      // 获取属性显示值
      getPropertyValueLabel(row) {
        const property = this.inspectPropertyList.find((p) => p.value === row.metric);
        if (!property) return row.threshold;
        const dataType = property.constraint.dataType;
        switch (dataType) {
          case 'BOOLEAN':
            return row.threshold ? property.constraint.trueText : property.constraint.falseText;
          case 'ENUM':
          case 'NUMBER_ENUM':
            return Object.entries(property.constraint.range).find(([label, value]) => value === row.threshold)[0] || row.threshold;
          case 'INTEGER':
          case 'DECIMAL':
            return property.constraint.unit ? `${row.threshold}${property.constraint.unit}` : row.threshold;
          default:
            return row.threshold;
        }
      },
      // 格式化时间显示
      formatTime(timeParams) {
        if (timeParams.repeatType === 'everyDay') {
          return '每天' + ' ' + timeParams.specificTime;
        }
        if (timeParams.repeatType === 'everyWeekDay') {
          const weekday = timeParams.weekday?.map((item) => REPEAT_DAY_ENUM.find((day) => day.value === item).label);
          return '每周' + ' ' + weekday?.join(', ') + ' ' + timeParams.specificTime;
        }
        if (timeParams.repeatType === 'everyMonth') {
          return '每月' + ' ' + timeParams.dayOfMonth?.join(', ') + '号 ' + timeParams.specificTime;
        }
      },
      applyTemplateDlg: transform(applyTemplate),
      applyTemplate() {
        // 应用模板前需校验是否填写完成
        const currentDevice = this.form.inspectionDevices.find((item) => item.deviceId === this.selectedDevice.deviceId);
        if (currentDevice) {
          const hasIncomplete = currentDevice.inspectionParams.some((param) => !param.isCompleted);
          if (hasIncomplete) {
            this.$message.warning(`请填写设备【${currentDevice.deviceName}】的完整巡检项`);
            return;
          }
        }
        this.applyTemplateDlg({
          propsData: {},
          methods: {
            handleSelect: (template) => {
              this.$confirm('确定要应用选中模板吗，应用模板将覆盖当前巡检设备巡检项！', {
                confirmButtonText: '确认',
                cancelButtonText: '取消',
                type: 'warning',
                closeOnClickModal: false,
              }).then(() => {
                //将inspectionDevices和template中包含的productCode进行匹配，如果巡检设备的code在template中，将覆盖巡检设备的巡检项，否则保留原本的巡检项
                this.form.inspectionDevices = this.form.inspectionDevices.map((device) => ({
                  ...device,
                  //注意深拷贝
                  inspectionParams: template.params.find((p) => p.productCode === device.code)?.inspectionParams
                    ? JSON.parse(JSON.stringify(template.params.find((p) => p.productCode === device.code).inspectionParams))
                    : device.inspectionParams,
                }));
                // 设置完成标志
                this.form.inspectionDevices.forEach((device) => {
                  device.inspectionParams.forEach((param) => {
                    this.$set(param, 'isCompleted', true);
                  });
                });
                this.$message.success('应用巡检模板成功');
              });
            },
          },
        });
      },
      //选中的属性发生变化,需重置巡检项设置
      selectedPropertyChange(row) {
        row.operator = 'EQ';
        row.threshold = '';
        row.description = '';
      },
      handleSpanMethod({ row, column, rowIndex, columnIndex }) {
        //如果设备不存在，则合并单元格
        if (!row.deviceName) {
          if (columnIndex === 0) {
            return {
              rowspan: 1,
              colspan: 1,
            };
          } else if (columnIndex === 1) {
            return {
              rowspan: 1,
              colspan: 4,
            };
          } else {
            return {
              rowspan: 0,
              colspan: 0,
            };
          }
        }
      },
    },
  };
</script>
<style lang="scss" scoped>
  .wrap {
    width: 100%;
    height: 100%;
    padding: 10px;
    display: flex;
    min-width: 1520px;
    min-height: 950px;
    flex-direction: column;
    .btn-wrap {
      margin-bottom: 10px;
    }
    .content {
      display: flex;
      width: 100%;
      flex: 1;
      min-height: 0;
      .left {
        width: 40%;
        height: 100%;
        display: flex;
        flex-direction: column;
        .table-card {
          flex: 1;
          min-height: 0;
          margin-top: 10px;
          display: flex;
          flex-direction: column;
          ::v-deep .el-card__body {
            flex: 1;
            min-height: 0;
          }
        }
      }
      .right {
        width: 60%;
        height: 100%;
        padding-left: 20px;
        .table-card {
          height: 100%;
          width: 100%;
          display: flex;
          flex-direction: column;

          ::v-deep .el-card__body {
            flex: 1;
            min-height: 0;
            display: flex;
            flex-direction: column;
          }
          .basic-inspect {
            height: 230px;
            display: flex;
            flex-direction: column;
            .title {
              margin-bottom: 10px;
              font-weight: bold;
              background-color: #f2f6ff;
              padding: 10px 5px;
              border-radius: 4px;
              .title-text {
                font-size: 14px;
              }
            }
            .table-wrap {
              flex: 1;
              min-height: 0;
            }
          }
          .property-inspect {
            margin-top: 10px;
            flex: 1;
            min-height: 0;
            display: flex;
            flex-direction: column;
            .title {
              margin-bottom: 10px;
              display: flex;
              justify-content: space-between;
              align-items: center;
              font-weight: bold;
              background-color: #f2f6ff;
              padding: 10px 5px;
              border-radius: 4px;
              .title-text {
                font-size: 14px;
              }
            }
            .table-wrap {
              flex: 1;
              min-height: 0;
            }
          }
        }
      }
    }
  }
  ::v-deep .device-table-header {
    background-color: #fafafc !important;
    color: #303133 !important;
  }
  ::v-deep .el-table {
    border: 1px solid #dfe6ec;
  }
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: bold;
    .title-icon {
      margin-right: 5px;
      color: #0054ff;
    }
  }
  .tag-wrap {
    display: flex;
    align-items: center;
    justify-content: center;
    .tag {
      margin-left: 10px;
    }
  }
  .device-not-exist {
    .icon {
      margin-right: 5px;
    }
    margin-left: 0px;
  }
</style>
