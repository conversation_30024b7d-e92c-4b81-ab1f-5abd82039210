import request from '@/utils/request'

/**
 * 获取所有角色
 * @param page
 * @param size
 * @param direction
 * @param property
 * @return {AxiosPromise}
 */
export function getRoles(nameToSearch, deptId) {
  // console.log('搜索角色', nameToSearch)
  return request({
    url: '/oauth/role' + (nameToSearch ? '/' + nameToSearch : '') + `?deptId=${deptId}`,
    method: 'get',
  })
}

/**
 * 获取所有角色(根据公司查询角色列表)
 * @param {*} nameToSearch 
 * @param {*} companyId 
 * @returns 
 */
export function getRolesByCompany(nameToSearch, companyId, includeChild = true) {
  return request({
    url: `/oauth/role/inCompany?nameToSearch=${nameToSearch}&deptId=${companyId}&includeChild=${includeChild}`,
    method: 'get',
  })
}

/**
 * 获取所有角色(根据机构查询角色列表)
 * @param {*} nameToSearch 
 * @param {*} deptId 
 * @returns 
 */
export function getRolesByDept(nameToSearch, deptId, includeChild = true) {
  return request({
    url: `/oauth/role/inDept?nameToSearch=${nameToSearch}&deptId=${deptId}&includeChild=${includeChild}`,
    method: 'get',
  })
}

/**
 * 不分页获取所有角色
 * @return {AxiosPromise}
 */
export function getRolesWithNoPagination() {
  return request({
    url: '/oauth/role',
    method: 'get'
  })
}

/**
 * 保存角色
 * @param role
 * @return {AxiosPromise}
 */
export function saveRole(role) {
  return request({
    url: '/oauth/role',
    method: 'post',
    headers: {'Content-Type': 'application/json'},
    data: role
  })
}


/**
 * 删除角色
 * @param roleId
 * @return {AxiosPromise}
 */
export function deleteRole(roleId) {
  return request({
    url: '/oauth/role/' + roleId,
    method: 'delete'
  })
}

/**
 * 更新角色
 * @param roleId
 * @return {AxiosPromise}
 */
export function updateRole(role) {
  return request({
    url: '/oauth/role',
    method: 'put',
    headers: {'Content-Type': 'application/json'},
    data: role
  })
}

/**
 * 获取角色详情
 * @param id 角色id
 */
export function getRoleDetail(id) {
  return request({
    url: `/oauth/role/${id}/detail`,
    method: 'get',
  })
}
