<template>
  <div class="play-count-wrap">
    <div class="title-wrap"> 节目单下发趋势 </div>
    <div class="play-count-chart-wrap">
      <div
        class="chart-wrap"
        ref="charts"
        v-loading="loading"
        element-loading-spinner="el-icon-loading"
        element-loading-background="transparent"
      ></div>
    </div>
  </div>
</template>

<script>
  export default {
    data() {
      return {
        loading: false,
        chart: null,
      };
    },
    beforeDestroy() {
      this.chart && this.chart.destroy();
    },
    created() {
      this.getData();
    },
    methods: {
      getData(bShowLoading = true) {
        if (bShowLoading) {
          this.loading = true;
        }

        setTimeout(() => {
          this.loading = false;
          let xAxisArr = ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'];
          let countArr = [6, 33, 11, 6, 8, 0, 4, 0, 0, 0, 0, 0];
          let prevCountArr = [4, 30, 20, 5, 0, 1, 3, 8, 4, 2, 0, 1];
          this.initChart(xAxisArr, countArr, prevCountArr);
        }, 2 * 1000);
      },
      initChart(xAxisArr, countArr, prevCountArr) {
        let label = (this.periodDetail && this.periodDetail.label) || '年';
        let option = {
          color: ['rgb(35,122,245)', 'rgb(49,200,252)'],
          grid: {
            top: 30,
            bottom: 30,
            left: 40,
            right: 30,
          },
          tooltip: {
            trigger: 'axis',
            confine: true,
          },
          legend: {
            // show: true,
            // selectedMode: false,
            icon: 'rect',
            itemWidth: 14,
            itemHeight: 14,
            itemStyle: {
              borderRadius: 0, // 设置边框圆角，0 表示正方形
            },
            itemGap: 10,
            data: [`本${label}`, `上${label}`],
            textStyle: {
              color: '#ffffff',
              fontSize: 14,
            },
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: xAxisArr,
            axisLabel: {
              color: '#fff',
            },
            show: true,
            axisLine: {
              show: true,
            },
          },
          yAxis: {
            type: 'value',
            minInterval: 1,
            splitLine: {
              show: false,
            },
            axisLabel: {
              color: '#fff',
            },
          },
          series: [
            {
              name: `本${label}`,
              data: countArr,
              type: 'line',
              areaStyle: {},
              showSymbol: false,
              areaStyle: {
                color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgba(35,122,245,0.5)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(35,122,245,0.1)',
                  },
                ]),
              },
              smooth: true,
            },
            {
              name: `上${label}`,
              data: prevCountArr,
              type: 'line',
              areaStyle: {},
              showSymbol: false,
              areaStyle: {
                color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgba(49,200,252,0.5)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(49,200,252,0.1)',
                  },
                ]),
              },
              smooth: true,
            },
          ],
        };
        this.updateChart(option);
      },
      updateChart(option) {
        if (!this.chart) {
          this.chart = this.$echarts.init(this.$refs.charts);
        }
        this.chart.setOption(option, true);
      },
    },
  };
</script>

<style lang="scss" scoped>
  .play-count-wrap {
    background: url(../img/box-left-bg.png) no-repeat;
    box-sizing: border-box;
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    .title-wrap {
      font-family: 'YouSheBiaoTiHei';
      font-size: 18px;
      padding: 15px 0 10px 20px;
    }

    .play-count-chart-wrap {
      height: calc(100% - 40px);
      width: 100%;
      .chart-wrap {
        width: 100%;
        height: 100%;
      }
    }
  }
</style>
