import request from "@/utils/request";
import $qs from "qs";

//批量设置视频会议的音频
export function setVcAudio(data) {
  return request({
    url: "/webapp/vc/videoAudioBatch",
    method: "post",
    data: $qs.stringify(data)
  });
}

//批量操作视频会议
export function setVcCallAction(data) {
  return request({
    url: "/webapp/vc/calling.action",
    method: "post",
    data: $qs.stringify(data)
  });
}

//批量呼叫
export function batchCall(data) {
  return request({
    url: "/webapp/vc/batchCallTest",
    method: "post",
    data: $qs.stringify(data)
  });
}

//停止批量呼叫
export function stopBatchCall() {
  return request({
    url: "webapp/vc/batchCallStop",
    method: "post",
  });
}

//批量设置视频会议呼叫参数
export function batchSetVcCallConfig(data) {
  return request({
    url: "/webapp/vc/callConfigBatch",
    method: "post",
    data: $qs.stringify(data)
  });
}

//获取单个呼叫配置信息
export function getVcCallConfig(data) {
  return request({
    url: "/webapp/vc/getDs600RpmCfg",
    method: "post",
    data: $qs.stringify(data)
  });
}

//设置单个呼叫配置信息
export function setVcCallConfig(data) {
  return request({
    url: "/webapp/vc/save600RpmConfig",
    method: "post",
    data: $qs.stringify(data)
  });
}

//上传呼叫配置信息
export function uploadVcCallConfig(data) {
  let file = new FormData();
  file.append("file", data);
  return request({
    url: "/webapp/vc/importVcTerminalCfg",
    method: "post",
    data: file,
    headers: {
      "Content-Type": "multipart/form-data"
    }
  });
}

//获取终端呼叫详细信息
export function getTerminalVcCallConfig(data) {
  return request({
    url: "/webapp/vc/getCallInfolist.json",
    method: "post",
    data: $qs.stringify(data)
  });
}

//清空终端呼叫记录
export function clearTerminalVcCall(data) {
  return request({
    url: "webapp/vc/clearCallInfolist",
    method: "post",
    data: $qs.stringify(data)
  });
}

//获取终端呼叫历史
export function getVcCallHistory(data) {
  return request({
    // vc/getOpHistoryList => terminal/getOpHistoryList
    url: "/webapp/terminal/getOpHistoryList",
    method: "post",
    data: $qs.stringify(data)
  });
}