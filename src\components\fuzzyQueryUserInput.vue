<template>
  <div class="fuzzy-query-user-input">
    <el-autocomplete
      v-model="val"
      :fetch-suggestions="querySearchAsync"
      suffix-icon="el-icon-search"
      :placeholder="placeholder"
      @select="handleSelect"
      :size="size"
      :debounce="500"
      clearable
    >
      <template slot-scope="{ item }">
        <el-tooltip class="item" effect="dark" placement="right">
          <div slot="content">
            {{ formatItemText(item) }}
          </div>
          <div class="ellipsis-text">
            {{ formatItemText(item) }}
          </div>
        </el-tooltip>
      </template>
    </el-autocomplete>
  </div>
</template>

<script>
  import debounce from 'lodash/debounce';
  import { fuzzyQueryUser } from '@/api/user';
  export default {
    name: 'fuzzyQueryUserInput',
    props: {
      companyId: {
        default: function () {
          return this.$store.getters.companyId;
        },
      },
      placeholder: {
        type: String,
        default: '请输入用户名或昵称',
      },
      size: {
        type: String,
        default: 'small',
      },
      // 显示 nickname 还是 username
      labelType: {
        type: String,
        default: 'nickname',
      },
      // 括号里的显示文本类型
      secondLabelType: {
        type: String,
        default: 'username',
      },
      debounceTime: {
        type: Number,
        default: 600,
      },
    },
    data() {
      return {
        // 选定的值
        val: '',
        // 选定的用户
        user: null,
        // 保存获取到的结果
        list: [],
      };
    },
    created() {
      this.debounceChange = debounce(() => {
        if (this.val) {
          // 其他地方使用的时候需要判断
          this.handleSelect({
            id: undefined,
            username: this.val,
            nickname: this.val,
          });
        } else {
          this.handleChange(undefined);
        }
      }, this.debounceTime);
      //   console.log('this.$options.propsData', this.$options.propsData);
    },
    methods: {
      querySearchAsync(queryString, cb) {
        if (queryString) {
          fuzzyQueryUser(this.companyId, this.val).then((res) => {
            this.list = res.data;
            cb(this.list);
          });
        } else {
          this.list = [];
          cb(this.list);
        }
      },
      /**
       * 选中建议项
       * @param {*} e
       */
      handleSelect(e) {
        // console.log('handleSelect', e);
        this.user = e;
        this.val = e[this.labelType] || e.username;
        this.$emit('handleSelect', e);
      },
      /**
       * 格式化显示的文本
       * @param {*} item
       */
      formatItemText(item) {
        let firstName = item[this.labelType || 'username'];
        let secondName = item[this.secondLabelType || 'nickname'];
        return firstName + (secondName ? ` (${secondName})` : '');
      },
      /**
       * 在 Input 值改变时触发
       * @param {*} e
       */
      handleChange(e) {
        // 置空
        if (!e) {
          this.user = null;
          this.$emit('handleSelect', undefined);
        }
      },
      /**
       * 清空
       */
      clear() {
        this.val = '';
        this.user = null;
      },
    },
    watch: {
      val(val) {
        this.debounceChange();
      },
    },
  };
</script>

<style scoped lang="scss">
  .ellipsis-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
</style>
