<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Document</title>
  <link rel="stylesheet" href="40e93b11cbd4174c60b22cba0d630300.css" />
  <style>
    body,
    html {
      height: 100%;
      margin: 0;
    }

    body {
      background-color: transparent;
    }

    .icon {
      width: 1em;
      height: 1em;
      vertical-align: -0.15em;
      fill: currentColor;
      overflow: hidden;
    }

    .wrap {
      width: 100%;
      height: 100%;
      text-align: center;
      font-size: 30px;
    }

    .wrap .icon {
      font-size: 28vw;
    }

    .wrap .temperature {
      font-size: 36vw;
    }

    .wrap .temperature-range {
      font-size: 15vw;
    }

    .wrap .address {
      font-size: 15vw;
    }

    [v-cloak] {
      display: none;
    }
  </style>
  <script src="174365807c06490c848d7b1d45fdc348.js"></script>
  <script src="425c9b74070ac12358267bbefc7dfb2b.js"></script>
  <script src="f064009b715d60de50e4e91f7b6fa57e.js"></script>
  <script src="d4bfcfa9de50875589eaab322a015441.js"></script>
  <script src="page.js"></script>
</head>

<body>
  <div id="app" class="wrap" :style="style" v-cloak>
    <!-- <svg class="icon" aria-hidden="true">
        <use xlink:href="http://*************:8888/module/weather.svg"></use>
      </svg> -->
    <!-- <img src="http://*************:8888/module/weather.svg" alt=""> -->
    <div class="address">{{address}}</div>
    <div class="temperature-range">{{weather.wea}}</div>
    <div class="temperature">{{weather.tem}}</div>
    <div class="temperature-range">
      {{weather.tem1}} / {{weather.tem2}}
    </div>
  </div>
</body>

<script>
  window.$page = page;
  const cssProperty = ["height", "width", "fontSize"];
  const STYLE = ["color", "backgroundColor"];
  const PROP = ["address"];

  const app = new Vue({
    el: "#app",
    data: {
      props: page.props,
      mqttClient: null,
      mqttTopic: "",
      weather: {},
      address: "",
      mqtt: {}
    },
    computed: {
      style() {
        let style = {};
        for (let item of this.props) {
          if (STYLE.includes(item.field)) {
            style[item.field] = item.value;
          }
        }
        return style;
      },
    },
    created() {
      if (this.isWindows()) {
        return;
      }

      var data = window.DSS20AndroidJS.getWebMqttWs();
      var info = JSON.parse(JSON.parse(data));
      this.getMqttServerAndConnect(info);
    },
    beforeDestory() {
      this.mqttClose();
    },
    mounted() {
      window["update"] = (val, mqtt = null) => {
        let styles = [],
          props = [];
        for (let i = 0; i < val.length; i++) {
          let item = val[i];
          if (STYLE.includes(item.field)) {
            styles.push(item);
          } else if (PROP.includes(item.field)) {
            props.push(item);
            let index = this.getPropIndex(item.field);
            this.props[index].value = item.value;
          }
        }

        styles.length && this.updateStyles(styles);
        if (mqtt) {
          this.getMqttServerAndConnect(mqtt);
        } else {
          this.updateProps();
        }
      };

      window["setMqttParam"] = param => {
        this.getMqttServerAndConnect(param);
      };

      window["updateMqtt"] = param => {
        this.updateMqtt(param);
      };
    },
    methods: {
      /*
       * 日志打印
       */
      log(msg) {
        window.DSS20AndroidJS &&
          window.DSS20AndroidJS.htmlLogcat("天气6：" + msg);
      },
      updateMqtt(mqtt) {
        if (!mqtt) {
          return;
        }
        mqtt = JSON.parse(mqtt);

        if (
          (this.mqttClient && this.mqtt.ws !== mqtt.ws) ||
          (!this.mqttClient && !Object.keys(this.mqtt).length)
        ) {
          this.log("mqtt重连");
          this.mqttClose();
          this.mqtt = mqtt;
          this.getMqttServerAndConnect(mqtt);
        }
      },
      updateProps() {
        for (let prop of this.props) {
          if (prop.field === "address") {
            let address = this.formatAddress(prop.value);
            let index = address.length - 1;
            if (this.mqttClient) {
              this.mqttPublish(address[index]);
            }
          }
        }
      },
      updateStyles(styles) {
        for (let style of styles) {
          let index = this.getPropIndex(style.field);
          if (index !== -1) {
            let data = this.props[index];
            data.value = style.value;
            this.$set(this.props, index, data);
          }
        }
      },
      getPropIndex(name) {
        for (let i = 0; i < this.props.length; i++) {
          if (this.props[i].field === name) {
            return i;
          }
        }
        return -1;
      },
      formatAddress(values) {
        let address = [];
        for (let i = 0; i < province_city_area.length; i++) {
          let province = province_city_area[i];
          if (values[0] === province.value) {
            let label =
              province.label === "默认" ? this.getMac() : province.label;
            address.push(label);
            if (values.length > 1) {
              for (let j = 0; j < province.children.length; j++) {
                let city = province.children[j];
                if (values[1] === city.value) {
                  city.label !== "全部" && address.push(city.label);
                  if (values.length > 2) {
                    for (let k = 0; k < city.children.length; k++) {
                      let area = city.children[k];
                      if (values[2] === area.value) {
                        area.label !== "全部" && address.push(area.label);
                        break;
                      }
                    }
                  }
                }
                // break;
              }
            }
            // break;
          }
        }
        return address;
      },
      isWindows() {
        // var userAgent = navigator.userAgent;
        // var isWindows = userAgent.indexOf("Windows") > -1;
        return window.DSS20AndroidJS === undefined;
      },
      getMac() {
        if (!this.isWindows()) {
          var data = window.DSS20AndroidJS.terminalInfo();
          var info = JSON.parse(data);
          return info.mac;
        }

        return "北京市";
      },
      checkMacAddress(macAddress) {
        var regex = "([A-Fa-f0-9]{2}){5}[A-Fa-f0-9]{2}";
        var regexp = new RegExp(regex);
        if (!regexp.test(macAddress)) {
          return false;
        }
        return true;
      },
      /**
       * 随机ID
       * @param {*} len
       * @param {*} radix
       * @returns
       */
      randomId(len, radix) {
        var chars =
          '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('')
        var uuid = [],
          i
        radix = radix || chars.length
        if (len) {
          // Compact form
          for (i = 0; i < len; i++) uuid[i] = chars[0 | (Math.random() * radix)]
        } else {
          // rfc4122, version 4 form
          var r
          // rfc4122 requires these characters
          uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-'
          uuid[14] = '4'
          // Fill in random data.  At i==19 set the high bits of clock sequence as
          // per rfc4122, sec. 4.1.5
          for (i = 0; i < 36; i++) {
            if (!uuid[i]) {
              r = 0 | (Math.random() * 16)
              uuid[i] = chars[i == 19 ? (r & 0x3) | 0x8 : r]
            }
          }
        }
        return uuid.join('')
      },
      /**
       * 连接到MQTT服务器并订阅
       */
      getMqttServerAndConnect(param) {
        this.log("mqtt参数：" + JSON.stringify(param));

        const that = this;
        let { ws, username, password } = param;
        let mac = this.mac || ''
        // 客户端ID
        let clientId = `js_weather6_${mac}_${this.randomId(16, 16)}`;
        that.mqttClient = mqtt.connect(ws, {
          clientId,
          username,
          password,
          clean: true,
          connectTimeout: 5 * 1000,
          keepalive: 30
        });
        that.mqttClient.on("connect", () => {
          this.log("mqtt连接成功");
          this.updateProps();

          // that.mqttRefreshSubscribe();
        });
        that.mqttClient.on("error", error => {
          this.log("mqtt连接失败:" + error);
          this.weather = {}
        });
        //监听接收消息事件
        that.mqttClient.on("message", (topic, message) => {
          message = JSON.parse(message.toString());
          this.log("获取mqtt消息:" + JSON.stringify(message));
          this.address = message.city;
          this.weather = message.data[0];
        });
      },

      /**
       * 发布MQTT主题
       */
      mqttPublish(address) {
        let param = this.checkMacAddress(address) ? "mac" : "city";
        let topic = "dss2/terminal/web";
        let message = `{"command":"WEATHER","parameters":{${param}:"${address}"}}`;
        this.log("mqtt发布主题:" + message);
        this.mqttClient.publish(
          topic,
          message,
          { qos: 1, retain: true },
          (err, res) => {
            if (err) {
              this.log("mqtt发布主题失败", err);
              return;
            }
            this.mqttRefreshSubscribe(address);
          }
        );
      },
      /**
       * 订阅MQTT主题
       */
      mqttRefreshSubscribe(address) {
        const that = this;
        if (that.mqttTopic) {
          that.mqttClient.unsubscribe(that.mqttTopic);
        }
        that.mqttTopic = `server/weather/${address}`;
        this.log("mqtt订阅主题:" + that.mqttTopic);
        that.mqttClient.subscribe(that.mqttTopic, { qos: 1 }, function (
          err,
          res
        ) {
          if (err) {
            this.log("mqtt订阅主题失败:", err);
            return;
          }
        });
      },
      /**
       * 释放MQTT客户端
       */
      mqttClose() {
        if (this.mqttClient) {
          this.mqttClient.unsubscribe(this.mqttTopic);
          this.mqttClient.end();
        }
      }
    }
  });
</script>

</html>