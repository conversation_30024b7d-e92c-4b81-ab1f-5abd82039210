<template>
  <el-dialog title="播放信息" :visible.sync="bDialogShow" width="1080px" :close-on-click-modal="false" @close="closeDialog">
    <el-tabs v-model="activeTabName" @tab-click="handleChangeTab" style="margin-top:-10px;">
      <el-tab-pane label="节目单" name="playlist"></el-tab-pane>
      <el-tab-pane label="实时信息" name="realInfo"></el-tab-pane>
    </el-tabs>

    <template v-if="activeTabName === 'playlist'">
      <div class="mb-10" style="display: flex; align-items: center">
        <el-button type="primary" plain size="small" icon="el-icon-refresh-right" @click="refresh">刷新</el-button>
        <el-alert :title="alertTitle" type="error" v-if="alertTitle" class="ml-10" :closable="false"> </el-alert>
      </div>
      <el-table :data="playList" border height="600" :row-style="rowClass" v-loading="loading" key="playlist">
        <el-table-column type="expand">
          <template slot-scope="{ row }">
            <div v-for="program in row.programs" :key="program.pid" class="program-wrap">
              <div class="header">
                <div
                  class="program-name copy-text"
                  title="点击复制节目名称"
                  @click.stop="copyText(program.programName, $event)"
                  :class="{ play: program.status === 'playing' }"
                  >{{ program.programName ? '节目名称：' + program.programName : '节目id：' + program.pid }}</div
                >
                <div class="status" v-if="program.status === 'playing'">状态：播放中</div>
              </div>

              <el-table :data="program.materials" height="100%" :default-expand-all="true" border empty-text="暂无素材">
                <!-- <el-table-column label="序号" width="60">
                <template slot-scope="scope">
                  {{ scope.$index + 1 }}
                </template>
              </el-table-column> -->
                <el-table-column prop="name" label="素材名称" width="auto" :show-overflow-tooltip="true">
                  <template slot-scope="{ row }">
                    <div title="点击复制素材名称" class="copy-text" @click.stop="copyText(row.name, $event)">{{ row.name }}</div>
                  </template>
                </el-table-column>
                <el-table-column prop="size" label="文件大小" width="140" :show-overflow-tooltip="true">
                  <template slot-scope="{ row }"> {{ row.sizeWithUnit }}</template>
                </el-table-column>
                <el-table-column prop="type" label="元素类型" width="80" :show-overflow-tooltip="true" :formatter="formatMaterialType">
                </el-table-column>
              </el-table>
            </div>
            <div v-if="!row.programs || row.programs.length === 0" class="empty-wrap">暂无节目</div>
          </template>
        </el-table-column>
        <el-table-column label="序号" width="60">
          <template slot-scope="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="playlistName" label="节目单名称" width="auto" :show-overflow-tooltip="true">
          <template slot-scope="{ row }">
            <div title="点击复制节目单名称" class="copy-text" @click.stop="copyText(row.playlistName, $event)">{{ row.playlistName }}</div>
          </template>
        </el-table-column>
        <el-table-column
          prop="playlistType"
          label="播放类型"
          width="150"
          :show-overflow-tooltip="true"
          :formatter="formatPlaylistType"
        ></el-table-column>
        <el-table-column prop="dsPriority" label="优先级" width="150" :show-overflow-tooltip="true">
          <template slot-scope="{ row }">
            {{ row.playlistType === 60 ? row.dsPriority : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="publisher" label="发布者" width="80" :show-overflow-tooltip="true"> </el-table-column>
        <el-table-column prop="status" label="状态" width="150" :show-overflow-tooltip="true">
          <template slot-scope="{ row }">
            {{ row.status === 'playing' ? '播放中' : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="gmtCreate" label="下发时间" width="180" :show-overflow-tooltip="true" :formatter="formatTime"> </el-table-column>
      </el-table>
    </template>
    <template v-else>
      <div class="mb-10" style="display: flex; align-items: center">
        <el-button type="primary" plain size="small" icon="el-icon-refresh-right" @click="getTerminalRealInfo">刷新</el-button>
        <el-alert :title="realInfoAlertTitle" type="error" v-if="realInfoAlertTitle" class="ml-10" :closable="false"> </el-alert>
      </div>
      <el-table :data="realInfoList" border height="600" :row-style="rowClass" key="realInfo">
        <el-table-column prop="realinfoId" label="ID" width="150" :show-overflow-tooltip="true"> </el-table-column>
        <el-table-column prop="content" label="实时信息" width="auto" :show-overflow-tooltip="true">
          <template slot-scope="{ row }">
            {{ row.content || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="publisher" label="状态" width="150" :show-overflow-tooltip="true">
          <template slot-scope="{ row }">
            {{ row.status === 'playing' ? '播放中' : '-' }}
          </template>
        </el-table-column>
      </el-table>
    </template>
  </el-dialog>
</template>

<script>
  import { getTerminalPlayList, getProgramMaterials, getTerminalRealInfo } from '@/api/info';
  import { playTypeNameFilter } from '@/filters/index';
  import { getMaterialFromType } from '@/utils/material.js';
  import clipboard from '@/utils/clipboard';
  import moment from 'moment';

  export default {
    props: {
      oTerminal: Object,
    },
    data() {
      return {
        bDialogShow: true,
        terminalMac: this.oTerminal.mac,
        // 节目单列表
        playList: [],
        timer: null,
        loading: false,
        activeTabName: 'playlist',
        // 实时信息列表
        realInfoList: [],
      };
    },
    computed: {
      alertTitle() {
        let arr = [];
        this.playList.forEach((playlist) => {
          let str = playlist.programs
            .filter((item) => !item.exist)
            .map((item) =>
              item.programName
                ? `节目单【${playlist.playlistName}】的节目【${item.programName}】不存在`
                : `节目单【${playlist.playlistName}】中ID为【${item.pid}】的节目不存在`
            )
            .join(',');
          str && arr.push(str);
        });
        return arr.join(' , ');
      },
      realInfoAlertTitle() {
        return this.realInfoList
          .filter((item) => !item.exist)
          .map((item) =>
            item.content
              ? `实时信息【${item.content}】不存在`
              : `ID为【${item.realinfoId}】的实时信息不存在`
          )
          .join(',');
      },
    },
    methods: {
      handleChangeTab() {
        if(this.activeTabName === 'playlist'){
          this.refresh()
        }else{
          this.getTerminalRealInfo()
        }
      },
      closeDialog() {
        this.closeRefresh();
        this.$emit('closeDialog');
      },
      closeRefresh() {
        clearInterval(this.timer);
        this.timer = null;
      },
      /**
       * 刷新页面
       */
      refresh() {
        this.getTerminalPlayList();
      },
      /**
       * 查询终端的节目单列表
       */
      getTerminalPlayList(bShowLoading = false) {
        if (bShowLoading) {
          this.loading = true;
        }

        getTerminalPlayList(this.terminalMac)
          .then(({ data }) => {
            this.playList = data;

            data &&
              data.forEach((item) => {
                item.programs && item.programs.forEach((program) => this.getProgramMaterials(program));
              });
          })
          .finally(() => (this.loading = false));
      },
      /**
       * 查询节目的素材列表
       * @param program 节目
       */
      getProgramMaterials(program) {
        if (!program.pid) {
          return;
        }
        getProgramMaterials(program.pid).then(({ data }) => {
          program.materials = data;
        });
      },
      formatPlaylistType(row) {
        //51-59为带编号的默认播放
        if (row.playlistType >= 51 && row.playlistType <= 59) {
          row.playlistType = 50;
        }
        return playTypeNameFilter(row.playlistType);
      },
      formatMaterialType(row) {
        let res = getMaterialFromType(row.type);
        return (res && res.txt) || '-';
      },
      // 正在播放的节目单，高亮选中
      rowClass({ row }) {
        if (row.status === 'playing') {
          return { 'background-color': 'rgb(225, 243, 216)' };
        }
      },
      /**
       * 复制文字
       */
      copyText(text, $event) {
        clipboard(text, $event);
      },
      /**
       * 格式化时间
       */
      formatTime(row) {
        if (!row.gmtCreate) {
          return '--';
        }
        return moment(row.gmtCreate).format('YYYY-MM-DD HH:mm:ss');
      },
      /**
       * 获得终端上的正在播放的实时信息
       */
      getTerminalRealInfo() {
        getTerminalRealInfo(this.terminalMac).then(({ data }) => {
          this.realInfoList = data;
        });
      },
    },
    mounted() {
      this.handleChangeTab();
    },
  };
</script>

<style scoped lang="scss">
  .header {
    display: flex;
    height: 40px;
    line-height: 40px;
    padding-left: 10px;
    background-color: rgb(229, 229, 229);
    color: rgb(96, 98, 102);
    border: 1px solid rgb(229, 229, 229);
    .status {
      color: #67c23a;
    }
    .program-name {
      max-width: 80%;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      margin-right: 30px;

      &:hover {
        color: #1f7bc1;
      }
      &.play {
        color: #67c23a;
      }
    }
  }

  .empty-wrap {
    text-align: center;
    padding: 10px 0;
    color: #ccc;
  }

  .el-table ::v-deep th.el-table__cell {
    background: #f5f7fa;
  }

  .program-wrap ::v-deep .el-table__header {
    display: none;
  }

  .program-wrap:last-child {
    border-bottom: 1px solid rgb(229, 229, 229);
  }

  ::v-deep .el-table__expanded-cell {
    padding: 15px 20px;
  }
</style>
