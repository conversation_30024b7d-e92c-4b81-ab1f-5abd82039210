import adaptive from './adaptive';
import action from './permission';
import sticky from './sticky';
import waves from './waves';
import scrollBar from './scrollBar';
import debounce from './debounce';
import drag from './drag';
const directives = {
  adaptive,
  action,
  sticky,
  waves,
  scrollBar,
  debounce,
  drag
};

export default {
  install(Vue) {
    Object.keys(directives).forEach(key => {
      Vue.directive(key, directives[key]);
    });
  }
};
