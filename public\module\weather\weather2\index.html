<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Document</title>
  <link rel="stylesheet" href="40e93b11cbd4174c60b22cba0d630300.css" />
  <style>
    body,
    html {
      height: 100%;
      margin: 0;
    }

    .icon {
      width: 1em;
      height: 1em;
      vertical-align: -0.15em;
      fill: currentColor;
      overflow: hidden;
    }

    .wrap {
      width: 100%;
      height: 100%;
      text-align: center;
      box-sizing: border-box;
    }

    .header-wrap {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      padding: 3vmin 3vmin 5vmin 4vmin;
    }

    .place-wrap {
      display: flex;
      flex-direction: row;
      align-items: flex-end;
    }

    .place-wrap .place {
      font-size: 15vmin;
      margin-right: 3vmin;
    }

    .place-wrap .date {
      font-size: 10vmin;
    }

    .temperature-wrap {
      display: flex;
      flex-direction: row;
      align-items: center;
    }

    .temperature-wrap .left-wrap {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      width: 30%;
    }

    .temperature-wrap .left-wrap .icon-wrap {
      font-size: 44vmin;
      margin-right: 3vmin;
    }

    .temperature-wrap .left-wrap .tem {
      font-size: 24vmin;
    }

    .temperature-wrap .left-wrap .wea {
      font-size: 15vmin;
    }

    .temperature-wrap .right-wrap {
      width: 70%;
      border-left: 1px solid rgba(255, 255, 255, 1);
      margin: 0;
      padding: 0;
      display: flex;
      flex-direction: row;
      justify-content: space-around;
      padding-bottom: 4vmin;
    }

    .temperature-wrap .right-wrap li {
      list-style: none;
      width: 31%;
      text-align: center;
      display: inline-block;
      font-size: 10vmin;
    }

    .temperature-wrap .right-wrap li .title {
      color: rgba(255, 255, 255, 0.6);
      margin-top: 2vmin;
    }

    .temperature-wrap .right-wrap li .value {
      margin-top: 4vmin;
    }

    [v-cloak] {
      display: none;
    }
  </style>
  <!-- vue -->
  <script src="174365807c06490c848d7b1d45fdc348.js"></script>
  <!-- 字体图标 -->
  <script src="296265c65b5f438075cbbd630da87340.js"></script>
  <!-- 城市列表 -->
  <script src="f064009b715d60de50e4e91f7b6fa57e.js"></script>
  <!-- mqtt -->
  <script src="d4bfcfa9de50875589eaab322a015441.js"></script>
  <script src="page.js"></script>
</head>

<body>
  <div id="app" class="wrap" :style="style" v-cloak>
    <div class="header-wrap">
      <div class="place-wrap">
        <div class="place">{{address}}</div>
        <div class="date">{{weather.date}} {{weather.week}}</div>
      </div>
      <div class="update-wrap">{{updateTime}}发布</div>
    </div>

    <div class="temperature-wrap">
      <div class="left-wrap">
        <div class="icon-wrap">
          <svg class="icon" aria-hidden="true">
            <use :xlink:href="weather.iconName"></use>
          </svg>
        </div>
        <div class="tem-wrap">
          <div class="tem">{{weather.tem}}</div>
          <div class="wea">{{weather.wea}}</div>
        </div>
      </div>
      <ul class="right-wrap">
        <li>
          <div class="value">{{weather.tem1}}</div>
          <div class="title">最高温度</div>
          <div class="value">{{weather.tem2}}</div>
          <div class="title">最低温度</div>
        </li>
        <li>
          <div class="value">{{weather.win}}</div>
          <div class="title">风向</div>
          <div class="value">{{weather.win_speed}}</div>
          <div class="title">风力</div>
        </li>
        <li>
          <div class="value">{{weather.air_level}}</div>
          <div class="title">空气质量</div>
          <div class="value">{{weather.uv}}</div>
          <div class="title">紫外线指数</div>
        </li>
      </ul>
    </div>
  </div>
</body>

<script>
  window.$page = page;
  const cssProperty = ["height", "width", "fontSize"];
  const STYLE = ["color", "backgroundColor"];
  const PROP = ["address"];
  const WEA_ICON = [
    { wea: "晴", icon: "qing1" },
    { wea: "多云", icon: "duoyun" },
    { wea: "多云夜", icon: "duoyunye" },
    { wea: "阴", icon: "yin" },
    { wea: "小雨", icon: "xiaoyu" },
    { wea: "中雨", icon: "zhongyu" },
    { wea: "大雨", icon: "dayu" },
    { wea: "暴雨", icon: "baoyu" },
    { wea: "大暴雨", icon: "dabaoyu" },
    { wea: "特大暴雨", icon: "tedabaoyu" },
    { wea: "阵雨", icon: "zhenyu" },
    { wea: "雷阵雨", icon: "leizhenyu" },
    { wea: "雷阵雨伴冰雹", icon: "leizhenyubanbingbao" },
    { wea: "雨夹雪", icon: "yujiaxue" },
    { wea: "雾", icon: "wu" },
    { wea: "小雪", icon: "xiaoxue" },
    { wea: "中雪", icon: "zhongxue" },
    { wea: "大雪", icon: "daxue" },
    { wea: "暴雪", icon: "baoxue" },
    { wea: "阵雪", icon: "zhenxue" },
    { wea: "阵雪夜", icon: "zhenxue-ye" },
    { wea: "扬尘", icon: "yangsha" },
    { wea: "浮尘", icon: "fuchen1" },
    { wea: "沙尘", icon: "shachenbao1" },
    { wea: "强沙尘暴", icon: "qiangshachenbao" },
    { wea: "晴夜", icon: "qing-ye1" },
    { wea: "霾", icon: "mai" },
    { wea: "中度霾", icon: "zhongdumai" },
    { wea: "严重霾", icon: "yanzhongmai" },
    { wea: "重度霾", icon: "zhongdumai1" }
  ];

  const app = new Vue({
    el: "#app",
    data: {
      props: page.props,
      mqttClient: null,
      mqttTopic: "",
      weather: {},
      address: "",
      mqtt: {},
      updateTime: ""
    },
    computed: {
      style() {
        let style = {};
        for (let item of this.props) {
          if (STYLE.includes(item.field)) {
            style[item.field] = item.value;
          }
        }
        return style;
      }
    },
    created() {
      if (this.isWindows()) {
        return;
      }

      var data = window.DSS20AndroidJS.getWebMqttWs();
      var info = JSON.parse(JSON.parse(data));
      this.getMqttServerAndConnect(info);
    },
    beforeDestory() {
      this.mqttClose();
    },
    mounted() {
      window["update"] = (val, mqtt = null) => {
        let styles = [],
          props = [];
        for (let i = 0; i < val.length; i++) {
          let item = val[i];
          if (STYLE.includes(item.field)) {
            styles.push(item);
          } else if (PROP.includes(item.field)) {
            props.push(item);
            let index = this.getPropIndex(item.field);
            this.props[index].value = item.value;
          }
        }

        styles.length && this.updateStyles(styles);
        if (mqtt) {
          this.getMqttServerAndConnect(mqtt);
        } else {
          this.updateProps();
        }
      };

      window["setMqttParam"] = param => {
        this.getMqttServerAndConnect(param);
      };

      window["updateMqtt"] = param => {
        this.updateMqtt(param);
      };
    },
    methods: {
      /*
       * 终端打印
       */
      log(msg) {
        window.DSS20AndroidJS &&
          window.DSS20AndroidJS.htmlLogcat("天气2：" + msg);
      },
      updateMqtt(mqtt) {
        if (!mqtt) {
          return;
        }
        mqtt = JSON.parse(mqtt);

        if (
          (this.mqttClient && this.mqtt.ws !== mqtt.ws) ||
          (!this.mqttClient && !Object.keys(this.mqtt).length)
        ) {
          this.log("mqtt重连");
          this.mqttClose();
          this.mqtt = mqtt;
          this.getMqttServerAndConnect(mqtt);
        }
      },
      updateProps() {
        for (let prop of this.props) {
          if (prop.field === "address") {
            let address = this.formatAddress(prop.value);
            let index = address.length - 1;
            if (this.mqttClient) {
              this.mqttPublish(address[index]);
            }
          }
        }
      },
      updateStyles(styles) {
        for (let style of styles) {
          let index = this.getPropIndex(style.field);
          if (index !== -1) {
            let data = this.props[index];
            data.value = style.value;
            this.$set(this.props, index, data);
          }
        }
      },
      getPropIndex(name) {
        for (let i = 0; i < this.props.length; i++) {
          if (this.props[i].field === name) {
            return i;
          }
        }
        return -1;
      },
      formatAddress(values) {
        let address = [];
        for (let i = 0; i < province_city_area.length; i++) {
          let province = province_city_area[i];
          if (values[0] === province.value) {
            let label =
              province.label === "默认" ? this.getMac() : province.label;
            address.push(label);
            if (values.length > 1) {
              for (let j = 0; j < province.children.length; j++) {
                let city = province.children[j];
                if (values[1] === city.value) {
                  city.label !== "全部" && address.push(city.label);
                  if (values.length > 2) {
                    for (let k = 0; k < city.children.length; k++) {
                      let area = city.children[k];
                      if (values[2] === area.value) {
                        area.label !== "全部" && address.push(area.label);
                        break;
                      }
                    }
                  }
                }
                // break;
              }
            }
            // break;
          }
        }
        return address;
      },
      isWindows() {
        // var userAgent = navigator.userAgent;
        // var isWindows = userAgent.indexOf("Windows") > -1;
        return window.DSS20AndroidJS === undefined;
      },
      getMac() {
        if (!this.isWindows()) {
          var data = window.DSS20AndroidJS.terminalInfo();
          var info = JSON.parse(data);
          return info.mac;
        }

        return "北京市";
      },
      checkMacAddress(macAddress) {
        var regex = "([A-Fa-f0-9]{2}){5}[A-Fa-f0-9]{2}";
        var regexp = new RegExp(regex);
        if (!regexp.test(macAddress)) {
          return false;
        }
        return true;
      },
      getIconName(wea) {
        let name = "";
        for (let i = 0; i < WEA_ICON.length; i++) {
          if (WEA_ICON[i].wea === wea) {
            name = `#icon${WEA_ICON[i].icon}`;
            break;
          }
        }
        return name;
      },
      /**
       * 随机ID
       * @param {*} len
       * @param {*} radix
       * @returns
       */
      randomId(len, radix) {
        var chars =
          '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('')
        var uuid = [],
          i
        radix = radix || chars.length
        if (len) {
          // Compact form
          for (i = 0; i < len; i++) uuid[i] = chars[0 | (Math.random() * radix)]
        } else {
          // rfc4122, version 4 form
          var r
          // rfc4122 requires these characters
          uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-'
          uuid[14] = '4'
          // Fill in random data.  At i==19 set the high bits of clock sequence as
          // per rfc4122, sec. 4.1.5
          for (i = 0; i < 36; i++) {
            if (!uuid[i]) {
              r = 0 | (Math.random() * 16)
              uuid[i] = chars[i == 19 ? (r & 0x3) | 0x8 : r]
            }
          }
        }
        return uuid.join('')
      },
      /**
       * 连接到MQTT服务器并订阅
       */
      getMqttServerAndConnect(param) {
        this.log("mqtt参数：" + JSON.stringify(param));

        const that = this;
        let { ws, username, password } = param;
        let mac = this.mac || ''
        // 客户端ID
        let clientId = `js_weather2_${mac}_${this.randomId(16, 16)}`
        that.mqttClient = mqtt.connect(ws, {
          clientId,
          username,
          password,
          clean: true,
          connectTimeout: 5 * 1000,
          keepalive: 30
        });
        that.mqttClient.on("connect", () => {
          this.log("mqtt连接成功");
          this.updateProps();

          // that.mqttRefreshSubscribe();
        });
        that.mqttClient.on("error", error => {
          this.log("mqtt连接失败:" + error);
          this.weather = {}
          this.updateTime = ""
        });
        //监听接收消息事件
        that.mqttClient.on("message", (topic, message) => {
          message = JSON.parse(message.toString());
          this.log("获取mqtt消息:" + JSON.stringify(message));
          this.address = message.city;
          this.weather = message.data[0];
          message.data[0].indexs.map(item => {
            if (item.title === "紫外线指数") {
              this.weather.uv = item.level;
            }
          });
          let weaNow = message.data[0].hours[0].wea;
          this.weather.iconName = this.getIconName(weaNow);
          this.weather.wea = weaNow;
          let time = message.updateTime.split(" ")[1];
          let lastIndex = time.lastIndexOf(":");
          this.updateTime = time.substring(0, lastIndex);
        });
      },

      /**
       * 发布MQTT主题
       */
      mqttPublish(address) {
        let param = this.checkMacAddress(address) ? "mac" : "city";
        let topic = "dss2/terminal/web";
        let message = `{"command":"WEATHER","parameters":{${param}:"${address}"}}`;
        this.log("mqtt发布主题:" + message);
        this.mqttClient.publish(
          topic,
          message,
          { qos: 1, retain: true },
          (err, res) => {
            if (err) {
              this.log("mqtt发布主题失败", err);
              return;
            }
            this.mqttRefreshSubscribe(address);
          }
        );
      },
      /**
       * 订阅MQTT主题
       */
      mqttRefreshSubscribe(address) {
        const that = this;
        if (that.mqttTopic) {
          that.mqttClient.unsubscribe(that.mqttTopic);
        }
        that.mqttTopic = `server/weather/${address}`;
        this.log("mqtt订阅主题:" + that.mqttTopic);
        that.mqttClient.subscribe(that.mqttTopic, { qos: 1 }, function (
          err,
          res
        ) {
          if (err) {
            this.log("mqtt订阅主题失败:", err);
            return;
          }
        });
      },
      /**
       * 释放MQTT客户端
       */
      mqttClose() {
        if (this.mqttClient) {
          this.mqttClient.unsubscribe(this.mqttTopic);
          this.mqttClient.end();
        }
      }
    }
  });
</script>

</html>