<template>
  <div class="wrap">
    <div class="btn-wrap">
      <el-button type="primary" @click="doEdit" v-if="type === 'view'" v-action:inspectTask|editInspectTaskTemplate>编辑模板</el-button>
      <el-button type="success" @click="doSave" v-if="canEdit">保存模板</el-button>
      <el-button @click="doCancle">返回</el-button>
    </div>
    <div class="content">
      <div class="left">
        <el-card class="form-card">
          <div slot="header" class="card-header">
            <div>
              <svg-icon class="title-icon" icon-class="deviceInspect@info" />
              规则配置
            </div>
          </div>
          <el-form ref="form" :model="form" :rules="rules" label-width="153px" label-position="left">
            <el-form-item label="规则名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入规则名称" v-if="canEdit"></el-input>
              <span v-else>{{ form.name }}</span>
            </el-form-item>
            <el-form-item label="规则描述" prop="remark">
              <el-input type="textarea" :rows="2" placeholder="请输入规则描述" v-model="form.remark" v-if="canEdit"> </el-input>
              <span v-else>{{ form.remark }}</span>
            </el-form-item>
              <el-form-item label="产品类型" prop="type">
              <el-select style="width: 200px" v-model="level" placeholder="产品类型" :disabled="disabled">
                <el-option :label="item.label" :value="item.value" v-for="item in ALARM_LEVEL" :key="item.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="告警级别" prop="level">
              <el-select style="width: 200px" v-model="level" placeholder="告警级别" :disabled="disabled">
                <el-option :label="item.label" :value="item.value" v-for="item in ALARM_LEVEL" :key="item.value"></el-option>
              </el-select>
              <div class="leve-tips" style="font-style: italic">
                告警级别为
                <span style="font-weight: bold; color: #6b6b6b">普通</span>
                时发送站内信信息,级别为
                <span style="font-weight: bold; color: #f3ba0f">警告</span>&nbsp;
                <span style="font-weight: bold; color: #ff0d0d">严重</span>
                时发送邮件给设备所属的空间管理员
              </div>
            </el-form-item>
            <el-form-item label="生效时间段" prop="validPeriod">
              <el-select
                style="width: 180px"
                v-model="formValue.validPeriod.type"
                placeholder="生效时间类型"
                class="mr-6 mt-6"
                :filterable="true"
                @change="
                  {
                    formValue.validPeriod.startTime = undefined;
                    formValue.validPeriod.endTime = undefined;
                  }
                "
              >
                <el-option :label="item.label" :value="item.value" v-for="item in VALID_PERIOD_TYPE_ENUM" :key="item.value"></el-option>
              </el-select>
              <el-time-picker
                v-if="formValue.validPeriod.type === 'specificTimeRange'"
                placeholder="开始时间"
                value-format="HH:mm"
                class="mr-6 mt-6"
                v-model="formValue.validPeriod.startTime"
              />
              <el-time-picker
                v-if="formValue.validPeriod.type === 'specificTimeRange'"
                placeholder="结束时间"
                value-format="HH:mm"
                class="mr-6 mt-6"
                v-model="formValue.validPeriod.endTime"
              />
              <el-select
                style="width: 180px"
                v-model="formValue.validPeriod.repeatType"
                placeholder="重复类型"
                class="mr-6 mt-6"
                :filterable="true"
                @change="
                  {
                    formValue.validPeriod.weekday = null;
                    formValue.validPeriod.dayOfMonth = null;
                  }
                "
              >
                <el-option :label="item.label" :value="item.value" v-for="item in PERIOD_REPEAT_TYPE_ENUM" :key="item.value"></el-option>
              </el-select>
              <el-select
                style="width: 200px"
                v-if="formValue.validPeriod.repeatType === 'everyWeekDay'"
                v-model="formValue.validPeriod.weekday"
                placeholder="重复周几"
                class="mr-6 mt-6"
                multiple
                collapse-tags
              >
                <el-option :label="item.label" :value="item.value" v-for="item in REPEAT_DAY_ENUM" :key="item.value"></el-option>
              </el-select>
              <el-select
                style="width: 200px"
                v-if="formValue.validPeriod.repeatType === 'everyMonth'"
                v-model="formValue.validPeriod.dayOfMonth"
                placeholder="重复每月几号"
                class="mr-6 mt-6"
                multiple
                collapse-tags
              >
                <el-option :label="item.label" :value="item.value" v-for="item in dayOptions()" :key="item.value"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="是否自动创建工单" prop="createWorkOrder">
              <el-switch v-model="createWorkOrder" />
            </el-form-item>
            <el-form-item label="工单受理人" prop="workOrderManager" v-if="createWorkOrder">
              <mul-input
                style="width: 220px"
                placeholder="选择工单受理人"
                :value="workOrderManagerName"
                @click="selectWorkOrderManager"
                @clear="clearWorkOrderManager"
              >
              </mul-input>
              <div style="font-style: italic"> 发生告警时会同步创建工单并根据告警级别发送告警信息给工单受理人 </div>
            </el-form-item>
            <el-form-item label="是否启用" prop="enabled">
              <el-switch v-model="formValue.enabled" />
            </el-form-item>
            <el-form-item label="高级选项" @click.native="clickAdvancedOptions" class="advanced-options"> </el-form-item>
            <el-form-item label="静默时长" prop="silenceDuration" v-if="showAdvancedOptions">
              <el-input-number controls-position="right" v-model="formValue.silenceDuration" :min="0" style="width: 180px"> </el-input-number>
              <span class="ml-4">ms</span>
              <el-tooltip placement="top" content="静默时长内将不再重复执行规则">
                <i class="el-icon-warning-outline ml-4 cursor-pointer"></i>
              </el-tooltip>
            </el-form-item>
            <el-form-item label="执行方式" prop="sequenceType" v-if="['automation', 'manual'].includes(formValue.type) && showAdvancedOptions">
              <el-select v-model="formValue.sequenceType" placeholder="执行方式" style="width: 180px" :filterable="true">
                <el-option :label="item.label" :value="item.value" v-for="item in SEQUENCE_TYPE_ENUM" :key="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </el-card>
        <el-card class="table-card">
          <div slot="header" class="card-header">
            <div> <svg-icon class="title-icon" icon-class="deviceInspect@list" /> 产品规则 </div>
            <div class="card-header-btn">
              <el-select v-model="searchProductCode" placeholder="选择产品" size="mini" style="width: 160px" class="mr-10" filterable clearable>
                <el-option v-for="(item, index) in form.params" :key="index" :label="item.productName" :value="item.productCode"> </el-option>
              </el-select>
              <el-button class="edit-btn" icon="el-icon-plus" size="mini" @click="addProduct" v-if="canEdit">添加</el-button>
              <el-button class="delete-btn" icon="el-icon-delete" size="mini" @click="deleteProduct" v-if="canEdit">删除</el-button>
            </div>
          </div>

          <el-table
            :data="searchProductCode ? form.params.filter((item) => item.productCode === searchProductCode) : form.params"
            height="100%"
            header-cell-class-name="device-table-header"
            @selection-change="handleProductSelectionChange"
          >
            <template slot="empty">
              <el-empty description="暂无产品" :image-size="60"> </el-empty>
            </template>
            <el-table-column type="selection" width="35" align="center"> </el-table-column>
            <el-table-column prop="productName" label="产品名称" min-width="180" show-overflow-tooltip> </el-table-column>
            <el-table-column label="操作" min-width="120">
              <template slot-scope="{ row }">
                <el-button @click="changeSelectedProduct(row.productCode, row.productName)" size="mini" type="primary" plain>编辑</el-button>
                 <el-button @click="changeSelectedProduct(row.productCode, row.productName)" size="mini" type="primary" plain>删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>
      <div class="right">
        <el-card class="table-card">
          <div slot="header" class="card-header">
            <div class="tag-wrap">
              <svg-icon class="title-icon" icon-class="deviceInspect@detail" />
              产品规则详情 <el-tag class="tag" v-if="selectedProduct.productName">{{ selectedProduct.productName }}</el-tag>
            </div>
          </div>
          <div class="property-inspect">
            <div class="table-wrap">
              <el-form ref="formRef" :model="formValue" :rules="rules" label-width="135px" label-position="left" class="top-form">
                <el-form-item prop="whenCondition">
                  <template slot="label">
                    <div>当以下情况发生</div>
                    <div class="label-tips">(任一情况发生即执行后续操作)</div>
                  </template>
                  <div>
                    <template>
                      <el-button
                        size="medium"
                        type="primary"
                        @click="addSpecificTime()"
                        class="mr-6"
                        plain
                        v-if="['automation', 'manual'].includes(formValue.type)"
                        >添加特定时间</el-button
                      >
                      <el-button size="medium" type="primary" @click="addDevStatusChange(formValue.whenCondition)" plain>添加设备状态</el-button>
                      <el-button size="medium" type="primary" @click="addDevEventChange(formValue.whenCondition)" plain>添加设备事件</el-button>
                    </template>
                    <div class="list-wrap">
                      <div v-for="(condition, index) in formValue.whenCondition" :key="index" class="single-rule-item-wrap">
                        <template v-if="condition.type === 'specificTime'">
                          <el-time-picker v-model="condition.specificTime" placeholder="选择特定时间" class="mr-6 mt-6" value-format="HH:mm">
                          </el-time-picker>
                          <el-select
                            style="width: 180px"
                            v-model="condition.repeatType"
                            placeholder="重复类型"
                            class="mr-6 mt-6"
                            :filterable="true"
                            @change="
                              {
                                condition.specificDay = null;
                                condition.weekday = null;
                                condition.dayOfMonth = null;
                              }
                            "
                          >
                            <el-option :label="item.label" :value="item.value" v-for="item in REPEAT_TYPE_ENUM" :key="item.value"></el-option>
                          </el-select>
                          <el-date-picker
                            class="mr-6 mt-6"
                            v-model="condition.specificDay"
                            value-format="yyyy-MM-dd"
                            v-if="condition.repeatType === 'once'"
                            placeholder="选择日期"
                            type="date"
                          />
                          <el-select
                            style="width: 200px"
                            v-if="condition.repeatType === 'everyWeekDay'"
                            v-model="condition.weekday"
                            placeholder="重复周几"
                            class="mr-6 mt-6"
                            multiple
                            collapse-tags
                          >
                            <el-option :label="item.label" :value="item.value" v-for="item in REPEAT_DAY_ENUM" :key="item.value"></el-option>
                          </el-select>
                          <el-select
                            style="width: 200px"
                            v-if="condition.repeatType === 'everyMonth'"
                            v-model="condition.dayOfMonth"
                            placeholder="重复每月几号"
                            class="mr-6 mt-6"
                            multiple
                            collapse-tags
                          >
                            <el-option :label="item.label" :value="item.value" v-for="item in dayOptions()" :key="item.value"></el-option>
                          </el-select>
                        </template>
                        <template v-if="condition.type === 'deviceStatusChange'">
                          <property-cond-act
                            :deviceDetailMap="deviceDetailMap"
                            :allDeviceList="allDeviceList"
                            :property="condition"
                            @getDeviceDetail="getDeviceDetail"
                            type="condition"
                          ></property-cond-act>
                        </template>
                        <template v-if="condition.type === 'deviceEvent'">
                          <event-cond
                            :event="condition"
                            :allDeviceList="allDeviceList"
                            :deviceDetailMap="deviceDetailMap"
                            @getDeviceDetail="getDeviceDetail"
                          ></event-cond>
                        </template>
                        <el-button size="small" @click="deleteItem(index, formValue.whenCondition)" class="ml-6 mt-6">删除</el-button>
                      </div>
                      <div v-show="!formValue.whenCondition || formValue.whenCondition.length == 0" class="empty-when-conditions-block"> 请添加 </div>
                    </div>
                  </div>
                </el-form-item>
                <el-form-item label="状态限制">
                  <div>
                    <div class="opt-cond-wrap mb-2">
                      <span class="ml-6">条件限制：</span>
                      <el-select v-model="formValue.satisfyCondition" placeholder="触发类型" :filterable="true" style="width: 180px">
                        <el-option :label="item.label" :value="item.value" v-for="item in SATISFY_CONDITION_ENUM" :key="item.value"></el-option>
                      </el-select>
                    </div>
                    <el-button type="primary" class="mb-2" @click="addDevStatusChange(formValue.statusCondition)" plain>添加条件</el-button>
                    <div class="list-wrap">
                      <div v-for="(condition, index) in formValue.statusCondition" :key="index" class="single-rule-item-wrap">
                        <property-cond-act
                          :deviceDetailMap="deviceDetailMap"
                          :allDeviceList="allDeviceList"
                          :property="condition"
                          type="condition"
                          @getDeviceDetail="getDeviceDetail"
                        >
                        </property-cond-act>
                        <el-button size="small" @click="deleteItem(index, formValue.statusCondition)" class="ml-6 mt-6"> 删除 </el-button>
                      </div>
                    </div>
                  </div>
                </el-form-item>
              </el-form>
              
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script>
  import {
    PROPERTY_OPT_ENUM,
    PROPERTY_OPT_ENUM_NOT_NUMBER,
    REPEAT_TYPE_ENUM,
    PERIOD_REPEAT_TYPE_ENUM,
    VALID_PERIOD_TYPE_ENUM,
    REPEAT_DAY_ENUM,
    SATISFY_CONDITION_ENUM,
    SEQUENCE_TYPE_ENUM,
    ALARM_LEVEL,
  } from '@/views/iotControl/enum';
  import { getProductDetailByCode, getInspectTemplateDetail, saveInspectTemplate } from '@/api/iotControl';
  import transform from '@/utils/transform';
  import addProduct from '@/views/iotControl/deviceMaintenance/viewEditInspectTemplate/modules/addProduct.vue';
  export default {
    components: { addProduct },
    props: {},
    data() {
      return {
        PROPERTY_OPT_ENUM_NOT_NUMBER,
        ALARM_LEVEL,
        REPEAT_TYPE_ENUM,
        PERIOD_REPEAT_TYPE_ENUM,
        VALID_PERIOD_TYPE_ENUM,
        REPEAT_DAY_ENUM,
        PROPERTY_OPT_ENUM,
        SATISFY_CONDITION_ENUM,
        SEQUENCE_TYPE_ENUM,
        //整个任务是否可编辑
        canEdit: true,
        type: '',
        id: '',
        form: {
          id: '',
          name: '',
          //备注
          remark: '',
          //{productCode:'',productName:'',inspectionParams:[]}
          params: [],
        },
        rules: {
          name: [{ required: true, message: '请输入模板名称', trigger: 'blur' }],
        },
        rules: {
          name: [{ required: true, message: '请输入规则名称', trigger: 'blur' }],
          workOrderManager: [
            {
              required: true,
              message: '请选择工单处理人',
              trigger: 'blur',
              validator: (rule, value, callback) => {
                if (this.workOrderManager) {
                  callback();
                } else {
                  callback('请选择工单处理人');
                }
              },
            },
          ],
        },
        basicStatusInspectList: [
          { metricType: 'onlineStatus', name: '设备状态', description: '设备状态' },
          { metricType: 'lifespan', name: '设备生命周期', description: '设备寿命状态' },
          { metricType: 'warranty', name: '设备保修状态', description: '设备保修状态' },
        ],
        //当前查看巡检项的产品
        selectedProduct: {},
        //当前产品巡检属性列表
        inspectPropertyList: [],
        //当前选中巡检属性
        propertySelection: [],
        //当前选中巡检产品
        productSelection: [],
        //产品列表筛选
        searchProductCode: '',
        formValue: {
          name: '',
          type: '',
          enabled: true,
          description: '',
          // 触发条件[以下情况发生时]
          whenCondition: [],
          //触发条件的设备id列表
          whenConditionDeviceIdList: [],
          // 状态限制类型
          satisfyCondition: 'any',
          // 状态限制
          statusCondition: [],
          thenAction: [],
          // 静默时长【防止短时间内重复触发】
          silenceDuration: 0,
          // 生效时间段
          validPeriod: {
            type: 'wholeDay',
            startTime: null,
            endTime: null,
            repeatType: 'everyDay',
            dayOfMonth: null,
            weekday: null,
          },
          sequenceType: 'serial',
        },
        showAdvancedOptions: false,
      };
    },
    created() {
      this.type = this.$route.params.type;
      this.id = this.$route.params.id;
      if (this.type === 'view') {
        this.getInspectTemplateDetail();
        //查看时，整个任务不可编辑
        this.canEdit = false;
      }
    },
    computed: {
      getBasicStatusInspectList() {
        const params = this.form.params.find((item) => item.productCode === this.selectedProduct.productCode)?.inspectionParams || [];
        //仅显示当前选中设备包含的
        const existingMetricTypes = params.map((param) => param.metricType) || [];
        return this.basicStatusInspectList.filter((item) => existingMetricTypes.includes(item.metricType));
      },
    },
    methods: {
      getInspectTemplateDetail() {
        getInspectTemplateDetail(this.id).then(({ data }) => {
          this.form = data;
          this.formatInspectionProducts();
        });
      },
      //编辑时，预处理产品列表
      formatInspectionProducts() {
        //每项添加isCompleted:true
        this.form.params.forEach((item) => {
          item.inspectionParams.forEach((param) => {
            this.$set(param, 'isCompleted', true);
          });
        });
      },
      addProductDlg: transform(addProduct),
      addProduct() {
        this.addProductDlg({
          propsData: {
            selectedProducts: this.form.params,
          },
          methods: {
            handleSelect: (products) => {
              //params中已有的产品不添加
              const newProducts = products
                .filter((item) => !this.form.params.some((product) => product.productCode === item.code))
                .map((item) => ({
                  productName: item.name,
                  productCode: item.code,
                  //产品默认包含在线状态和生命周期巡检
                  inspectionParams: [
                    {
                      metricType: 'onlineStatus',
                      metric: 'onlineStatus',
                      operator: 'EQ',
                      threshold: '',
                      description: '在线状态',
                      inspectionType: 'staticThreshold',
                      isCompleted: true,
                    },
                    {
                      metricType: 'lifespan',
                      metric: 'lifespan',
                      operator: 'EQ',
                      threshold: '',
                      description: '寿命状态',
                      inspectionType: 'staticThreshold',
                      isCompleted: true,
                    },
                    {
                      metricType: 'warranty',
                      metric: 'warranty',
                      operator: 'EQ',
                      threshold: '',
                      description: '保修状态',
                      inspectionType: 'staticThreshold',
                      isCompleted: true,
                    },
                  ],
                }));
              this.form.params = [...this.form.params, ...newProducts];
            },
          },
        });
      },
      deleteProduct() {
        if (this.productSelection.length === 0) {
          this.$message.warning('请先选择产品');
          return;
        }
        this.form.params = this.form.params.filter((item) => !this.productSelection.includes(item));
      },
      handleProductSelectionChange(selection) {
        this.productSelection = selection;
      },
      //查看产品巡检项
      changeSelectedProduct(productCode, productName) {
        // 切换前需校验，是否填写完成
        const currentProduct = this.form.params.find((item) => item.productCode === this.selectedProduct.productCode);
        if (currentProduct) {
          const hasIncomplete = currentProduct.inspectionParams.some((param) => !param.isCompleted);
          if (hasIncomplete) {
            this.$message.warning(`请填写产品【${currentProduct.productName}】的完整巡检项`);
            return;
          }
        }
        this.selectedProduct = { productCode, productName };
        if (this.selectedProduct?.productCode) {
          this.getInspectPropertyList();
        }
      },
      getInspectPropertyList() {
        getProductDetailByCode(this.selectedProduct.productCode).then(({ data }) => {
          //仅显示基本数据类型的属性
          this.inspectPropertyList = data.properties
            .filter((item) => ['INTEGER', 'DECIMAL', 'BOOLEAN', 'STRING', 'ENUM', 'NUMBER_ENUM'].includes(item.constraint.dataType))
            .map((item) => ({
              ...item,
              label: item.name,
              value: item.address,
            }));
        });
      },
      getPropertyInspectParams() {
        //仅显示metricType为propertyKey的巡检项
        return (
          this.form.params
            .find((item) => item.productCode === this.selectedProduct.productCode)
            ?.inspectionParams.filter((param) => param.metricType === 'propertyKey') || []
        );
      },
      handlePropertySelectionChange(selection) {
        this.propertySelection = selection;
      },
      addPropertyInspect() {
        if (!this.selectedProduct.productCode) {
          this.$message.warning('请先选择产品');
          return;
        }
        this.form.params
          .find((item) => item.productCode === this.selectedProduct.productCode)
          ?.inspectionParams.push({
            metricType: 'propertyKey',
            metric: '',
            operator: 'EQ',
            threshold: '',
            description: '',
            inspectionType: 'staticThreshold',
            //是否填写完成
            isCompleted: false,
          });
      },
      deletePropertyInspect() {
        if (this.propertySelection.length === 0) {
          this.$message.warning('请先选择巡检项');
          return;
        }
        const currentProduct = this.form.params.find((item) => item.productCode === this.selectedProduct.productCode);
        if (currentProduct) {
          currentProduct.inspectionParams = currentProduct.inspectionParams.filter((item) => !this.propertySelection.includes(item));
        }
      },
      // 获取属性的数据类型
      getPropertyDataType(row) {
        const property = this.inspectPropertyList.find((p) => p.value === row.metric);
        return property?.constraint?.dataType;
      },

      // 获取数值类型的最小值
      getPropertyMin(row) {
        const property = this.inspectPropertyList.find((p) => p.value === row.metric);
        return this.isValidValue(property?.constraint?.min) ? property.constraint.min : -Infinity;
      },

      // 获取数值类型的最大值
      getPropertyMax(row) {
        const property = this.inspectPropertyList.find((p) => p.value === row.metric);
        return this.isValidValue(property?.constraint?.max) ? property.constraint.max : Infinity;
      },

      // 获取数值类型的步长
      getPropertyStep(row) {
        const property = this.inspectPropertyList.find((p) => p.value === row.metric);
        return this.isValidValue(property?.constraint?.step) ? property.constraint.step : 1;
      },

      // 获取字符串类型的最大长度
      getPropertyMaxLength(row) {
        const property = this.inspectPropertyList.find((p) => p.value === row.metric);
        return property?.constraint?.maxLength;
      },

      // 获取枚举类型的选项
      getPropertyOptions(row) {
        const property = this.inspectPropertyList.find((p) => p.value === row.metric);
        return Object.entries(property?.constraint?.range).map(([label, value]) => ({
          value,
          label,
        }));
      },

      // 获取布尔类型的选项
      getPropertyBolOptions(row) {
        const property = this.inspectPropertyList.find((p) => p.value === row.metric);
        return [
          {
            label: property?.constraint?.trueText,
            value: true,
          },
          {
            label: property?.constraint?.falseText,
            value: false,
          },
        ];
      },

      // 判断值是否有效
      isValidValue(value) {
        return value !== undefined && value !== null && value !== '';
      },
      editPropertyInspect(row) {
        this.$set(row, 'isCompleted', false);
      },
      confirmPropertyInspect(row) {
        if (this.isValidValue(row.metric) && this.isValidValue(row.operator) && this.isValidValue(row.threshold)) {
          row.isCompleted = true;
        } else {
          this.$message.warning('请填写完整巡检项');
        }
      },
      // 取消
      doCancle() {
        this.$router.go(-1);
      },
      doEdit() {
        this.canEdit = true;
      },
      async doSave() {
        let bValid = await this.$refs.form.validate();
        if (!bValid) {
          return;
        }
        const currentProduct = this.form.params.find((item) => item.productCode === this.selectedProduct.productCode);
        if (currentProduct) {
          const hasIncomplete = currentProduct.inspectionParams.some((param) => !param.isCompleted);
          if (hasIncomplete) {
            this.$message.warning(`请填写产品【${currentProduct.productName}】的完整巡检项`);
            return;
          }
        }
        saveInspectTemplate(this.form).then(() => {
          this.$message.success('保存成功');
          this.$router.go(-1);
        });
      },
      // 获取属性名称
      getPropertyName(row) {
        const property = this.inspectPropertyList.find((p) => p.value === row.metric);
        return property?.name || row.metric;
      },
      // 获取属性显示值
      getPropertyValueLabel(row) {
        const property = this.inspectPropertyList.find((p) => p.value === row.metric);
        if (!property) return row.threshold;
        const dataType = property.constraint.dataType;
        switch (dataType) {
          case 'BOOLEAN':
            return row.threshold ? property.constraint.trueText : property.constraint.falseText;
          case 'ENUM':
          case 'NUMBER_ENUM':
            return Object.entries(property.constraint.range).find(([label, value]) => value === row.threshold)[0] || row.threshold;
          case 'INTEGER':
          case 'DECIMAL':
            return property.constraint.unit ? `${row.threshold}${property.constraint.unit}` : row.threshold;
          default:
            return row.threshold;
        }
      },
      //选中的属性发生变化,需重置巡检项设置
      selectedPropertyChange(row) {
        row.operator = 'EQ';
        row.threshold = '';
        row.description = '';
      },
      clickAdvancedOptions() {
        this.showAdvancedOptions = !this.showAdvancedOptions;
      },
    },
  };
</script>
<style lang="scss" scoped>
  .wrap {
    width: 100%;
    height: 100%;
    padding: 10px;
    display: flex;
    min-width: 1520px;
    min-height: 950px;
    flex-direction: column;
    .btn-wrap {
      margin-bottom: 10px;
    }
    .content {
      display: flex;
      width: 100%;
      flex: 1;
      min-height: 0;
      .left {
        width: 40%;
        height: 100%;
        display: flex;
        flex-direction: column;
        .table-card {
          flex: 1;
          min-height: 0;
          margin-top: 10px;
          display: flex;
          flex-direction: column;
          ::v-deep .el-card__body {
            flex: 1;
            min-height: 0;
          }
        }
      }
      .right {
        width: 60%;
        height: 100%;
        padding-left: 20px;
        .table-card {
          height: 100%;
          width: 100%;
          display: flex;
          flex-direction: column;

          ::v-deep .el-card__body {
            flex: 1;
            min-height: 0;
            display: flex;
            flex-direction: column;
          }
          .basic-inspect {
            height: 230px;
            display: flex;
            flex-direction: column;
            .title {
              margin-bottom: 10px;
              font-weight: bold;
              background-color: #f2f6ff;
              padding: 10px 5px;
              border-radius: 4px;
              .title-text {
                font-size: 14px;
              }
            }
            .table-wrap {
              flex: 1;
              min-height: 0;
            }
          }
          .property-inspect {
            flex: 1;
            min-height: 0;
            display: flex;
            flex-direction: column;
            .title {
              margin-bottom: 10px;
              display: flex;
              justify-content: flex-end;
              align-items: center;
              font-weight: bold;
              background-color: #f2f6ff;
              padding: 10px 5px;
              border-radius: 4px;
              .title-text {
                font-size: 14px;
              }
            }
            .table-wrap {
              flex: 1;
              min-height: 0;
            }
          }
        }
      }
    }
  }
  ::v-deep .device-table-header {
    background-color: #fafafc !important;
    color: #303133 !important;
  }
  ::v-deep .el-table {
    border: 1px solid #dfe6ec;
  }
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: bold;
    .title-icon {
      margin-right: 5px;
      color: #0054ff;
    }
  }
  .tag-wrap {
    display: flex;
    align-items: center;
    justify-content: center;
    .tag {
      margin-left: 10px;
    }
  }
  .wrap {
    margin-left: 20px;
    display: block;
    ::v-deep .el-input.is-disabled .el-input__inner {
      background-color: rgba(251, 251, 251, 0.7);
      color: #5d6f93;
    }

    ::v-deep .el-textarea.is-disabled .el-textarea__inner {
      background-color: rgba(251, 251, 251, 0.7);
      color: #5d6f93;
    }
  }
  .top-form {
    padding: 20px 0 1px;
    .confirm-btn {
      margin-left: 163px;
    }
  }
  .single-rule-item-wrap {
    padding: 6px 8px 12px 8px;
    margin-top: 10px;
    border: 0.5px dashed rgba(197, 197, 197, 0.7);
    border-radius: 4px;
    display: flex;
    align-items: center;
    transition: border linear 200ms;
  }

  .single-rule-item-wrap:hover {
    border: 0.5px dashed rgb(45, 140, 240);
  }

  .chosen-rule-item {
    background-color: rgba(45, 140, 240, 0.16);
  }

  .ghost-rule-item {
    visibility: hidden;
  }
  .single-action-item-wrap {
    padding: 6px 8px 12px 8px;
    margin-top: 10px;
    border: 0.5px dashed rgba(197, 197, 197, 0.7);
    border-radius: 4px;
    display: flex;
    align-items: center;
    transition: border linear 200ms;
    user-select: none;
    .action-item {
      display: flex;
      align-items: center;
    }
  }
  .single-action-item-wrap:hover {
    border: 0.5px dashed rgb(45, 140, 240);
  }

  .drag-icon {
    width: 20px;
    opacity: 0.5;
    transition: all linear;
    cursor: move;
    &:hover {
      opacity: 0.8;
    }
  }

  .limited-width {
    max-width: 500px;
  }
  .opt-cond-wrap {
    align-items: center;
    display: flex;
    margin-bottom: 10px;
    background-color: rgb(238, 243, 247);
    padding: 10px;
    border-radius: 5px;
    width: 280px;
  }
  ::v-deep.advanced-options .el-form-item__label {
    cursor: pointer !important;
  }
  ::v-deep.advanced-options .el-form-item__label:hover {
    color: #00457a !important;
  }
  .list-wrap {
    width: fit-content;
  }
  .drag-wrap {
    display: flex;
  }

  .label-tips {
    font-size: 12px;
    font-weight: 500;
  }

  .empty-when-conditions-block {
    margin-top: 12px;
    width: 350px;
    border-radius: 4px;
    height: 62px;
    border: 0.5px dashed rgba(197, 197, 197, 0.7);
    transition: border linear 200ms;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #8e8e8f;
    &:hover {
      border: 0.5px dashed rgb(45, 140, 240);
    }
  }
</style>
