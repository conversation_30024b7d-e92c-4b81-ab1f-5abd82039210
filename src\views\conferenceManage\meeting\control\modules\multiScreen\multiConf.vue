// 多级会议的多画面设置
<template>
  <el-dialog :title="title" :visible.sync="visible" width="1000px" :close-on-click-modal="false" @close="close">
    <div class="mb-10 flex-block-between">
      <div>提示：拖动左侧会场到右侧画面</div>
      <!-- <el-select
        placeholder="预置模板"
        @change="changePreActionTemplate"
        v-model="preActionTemplateId"
        size="small"
        clearable
        v-if="showPreActionTemplateSelect"
      >
        <el-option v-for="template in preActionTemplate" :label="template.name" :value="template.uuid"> </el-option>
      </el-select> -->
      <!-- <span v-if="bPoll">（设置轮询的分屏将自动轮询，无法设置会场）</span> -->

      <el-checkbox v-model="bUnDragAllChecked" @change="handleChangeBUnDragAll">
        布局保持
        <el-tooltip effect="dark" placement="top">
          <div slot="content">
            当布局保持打开时，主会终端允许任意多个参与分屏，从会终端一个参与分屏后，该终端所在二级子树的所有终端不允许再参与分屏。保证主会布局和设置一致，从会布局和设置可能会有偏差
            <br />
            当布局保持关闭时，所有终端都可以参与分屏。主会、从会布局和设置可能都会有偏差。
          </div>
          <i class="el-icon-info"></i>
        </el-tooltip>
      </el-checkbox>
    </div>
    <div class="mb-10 pre-action-input-wrap" v-if="isPreAction">
      <div class="pre-action-input-label">名称</div>
      <el-input required class="pre-action-input" v-model="preActionName" clearable placeholder="请输入名称" size="small"></el-input>
    </div>
    <div class="content-wrap" v-loading="loading">
      <div class="list-wrap">
        <div class="header-wrap">
          <div class="title">
            <span>视频源</span>
          </div>
          <el-input
            placeholder="会场名称/呼叫号/IP"
            suffix-icon="el-icon-search"
            size="small"
            style="width: 180px"
            v-model="confName"
            v-debounce="[getMultiConfInfo]"
          />
        </div>
        <tree-draggable :node="roomsTree" :bShowCheckbox="bPoll" @dragEnd="terminalDragEnd" />
      </div>
      <div class="screen-group-wrap">
        <div class="header-wrap">
          <div class="title">设置视频源</div>
          <div>
            <!-- <el-button type="text" @click="autoFillVoice" v-if="!bPoll && !bSelectPicConf && !bSelectPicPoll">自动填充语音激励</el-button> -->
            <el-button type="text" @click="autoFillScreen">自动填充剩余画面</el-button>
            <el-button type="text" @click="doClear">清空</el-button>
          </div>
        </div>
        <screen-group
          :bPoll="bPoll"
          ref="screenGroup"
          class="screen-group"
          :rooms="masterRooms"
          :confId="confId"
          @updateRooms="updateRooms"
          @getCanSubmit="getCanSubmit"
          :selectList="selectList"
          :isPreAction="isPreAction"
          :preActionConfig="multiScreenConfig"
          :bShowVoice="false"
          :bMultiConf="true"
        ></screen-group>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="close">取消</el-button>
      <el-button @click="handleOk(false)" :disabled="!canSubmit || loading" v-if="showApplyBtn">应用</el-button>
      <el-button class="okBtn" @click="handleOk" :disabled="!canSubmit || loading">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import dlg from '@/mixins/dlg';
  import screenGroup from './components/screenGroup.vue';
  import { confControlMultiScreen, confControlPollMultiScreen, getMultiConfInfo } from '@/api/conferenceGuide';
  import { reverseTree, getTreePath, transformTree } from '@/utils/utils';
  import TreeDraggable from './components/treeDraggable.vue';

  export default {
    mixins: [dlg],
    components: {
      screenGroup,
      TreeDraggable,
    },
    props: {
      // 是否是轮询
      bPoll: {
        type: Boolean,
        default: false,
      },
      // 轮询会场是否可重复允许配置，false为会场不允许重复
      bPollTerminalRepeat: {
        type: Boolean,
        default: false,
      },
      // 会议id
      id: {
        type: Number | String,
        require: true,
      },
      // 是否是预置画面
      isPreAction: {
        type: Boolean,
        default: false,
      },
      // 显示应用按钮
      showApplyBtn: {
        type: Boolean,
        default: true,
      },
      // 视频源
      preActionSource: {
        type: Array,
        default: () => {
          return [];
        },
      },
      // 已选择的画面列表
      preActionConfig: {
        type: Object,
        default: () => {
          return {};
        },
      },
      // 预置会议模板
      preActionTemplate: {
        type: Array,
        default: () => {
          return [];
        },
      },
      /**
       * 显示右上角预置会议模板下拉框
       */
      showPreActionTemplateSelect: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        rooms: [],
        fullRooms: [],
        confName: '',
        loading: false,
        canSubmit: false,
        //已选中的会场
        selectedList: [],
        // 会议预置的名称
        preActionName: '',
        // 预置会议的id
        preActionTemplateId: '',
        multiScreenConfig: {},
        // 左侧显示的会场列表（树形结构）
        roomsTree: [],
        // 所有的会场列表（树形结构）
        fullRoomsTree: [],
        // 布局保持
        bUnDragAllChecked: false,
      };
    },
    computed: {
      confId() {
        return Number(this.id);
      },
      // 多选框选中的终端
      selectList() {
        let list = [];
        reverseTree(this.roomsTree, (node) => {
          node.type !== 'platform' && node.checked && list.push(node);
        });
        return list;
      },
      /**
       * 弹框标题
       */
      title() {
        let text = '';
        if (this.bPoll) {
          text = '设置轮询';
        } else {
          text = '设置分屏';
        }

        return text;
      },
      // 主会终端
      masterRooms() {
        if (!this.roomsTree.length || !this.roomsTree[0] || !this.roomsTree[0].children) {
          return [];
        }
        return this.roomsTree[0].children.filter((node) => node.type !== 'platform');
      },
    },
    watch: {
      selectedList(newVal) {
        // bUnDragAllChecked为false时，可以拖拽所有终端不受限制
        if (!this.bUnDragAllChecked) {
          return;
        }
        // 主会终端允许任意多个参与分屏
        // 从会终端一个参与分屏后，该终端所在二级子树的所有终端不允许再参与分屏

        // 收集所有禁止拖拽的节点
        let undragNodes = new Set();

        newVal.forEach((item) => {
          // 获取当前选中终端所在树的路径
          // 用terminalId比对，因为查询多画面时返回的是terminalId
          let paths = getTreePath(this.fullRoomsTree, (room) => room.terminalId === item.terminalId).filter((node) => node.type === 'platform');
          if (paths.length >= 2) {
            // 收集二级节点及其子节点
            reverseTree([paths[1]], (node) => {
              undragNodes.add(node.id);
            });
          }
        });

        // 遍历树结构，当前节点存在于禁止拖拽的节点列表中时，将bUnDrag字段置为true
        reverseTree(this.roomsTree, (node) => {
          if (undragNodes.has(node.id)) {
            this.$set(node, 'bUnDrag', true);
          } else {
            this.$set(node, 'bUnDrag', false);
          }
        });
      },
    },
    created() {
      if (!this.isPreAction) {
        this.getMultiConfInfo();
      } else {
        // 填充视频源
        this.rooms = this.preActionSource;
        this.fullRooms = JSON.parse(JSON.stringify(this.rooms));
        // this.updateRooms(this.selectedList);
        this.preActionName = this.preActionConfig.name || '';
        this.multiScreenConfig = this.preActionConfig;
      }
    },
    methods: {
      /**
       * 布局保持改变时
       */
      handleChangeBUnDragAll() {
        // 不能拖拽二级及其子级终端
        // 清空画面布局，防止存在冲突的二级及其子级终端
        if (this.bUnDragAllChecked) {
          this.$confirm(`该操作将清空所有会场,是否继续？`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
            .then(() => this.clear())
            .catch(() => (this.bUnDragAllChecked = !this.bUnDragAllChecked));
        } else {
          // 可以拖拽任意终端
          // 遍历树结构，将所有节点都只为可拖拽
          reverseTree(this.roomsTree, (node) => {
            this.$set(node, 'bUnDrag', false);
          });
        }
      },
      /**
       * 会场拖拽结束，将所有选中的会场置为未选中
       */
      terminalDragEnd() {
        this.bCheckedAll = false;
        reverseTree(this.roomsTree, (node) => {
          node.checked = false;
        });
      },
      /**
       * 更改多画面预置模板
       */
      changePreActionTemplate(e) {
        this.preActionTemplateId = e;
        // 使用预置模板
        let configTemplate = this.preActionTemplate.find((item) => item.uuid == e);

        if (!configTemplate) {
          return;
        }

        try {
          let target = JSON.parse(JSON.stringify(configTemplate));
          if (target) {
            if (this.bPoll) {
              // 轮询
              for (let index = 0; index < target.param.subPicPollList.length; index++) {
                let subPicPollListItem = target.param.subPicPollList[index];

                target.param.subPicPollList[index].subPicList = target.param.subPicPollList[index].subPicList.filter((item) => {
                  let tempIndex = this.fullRooms.findIndex((e) => e.type == item.venuetype && e.id == item.id);
                  // 返回存在的会场列表
                  return tempIndex != -1;
                });
                // 不能使用for循环
              }
            } else {
              // 多画面
              for (let index = 0; index < target.param.subPicList.length; index++) {
                let item = target.param.subPicList[index];
                let tempIndex = this.fullRooms.findIndex((e) => e.type == item.venuetype && e.id == item.id);
                // 该会场不存在
                if (tempIndex == -1) {
                  // 置空
                  target.param.subPicList[index] = {
                    name: '',
                    id: '',
                    type: 'forced',
                    venuetype: null,
                  };
                }
              }
            }
            console.log('target.param', target.param);
            this.multiScreenConfig = target.param;
            this.$nextTick(() => {
              this.$refs.screenGroup.initPreActionMultiScreen();
            });
          }
        } catch (error) {
          console.log('error', error);
        }
      },
      /**
       * 更新左侧终端列表，设置分屏时同一视频源只允许使用一次
       */
      updateRooms(val) {
        if (!val.length) {
          return;
        }
        this.selectedList = val;

        // 允许会场重复，不更新左侧会场列表
        if (this.bPollTerminalRepeat) {
          return;
        }

        let cloneFullRoomsTree = JSON.parse(JSON.stringify(this.fullRoomsTree));
        // 遍历树，删除选中的会场
        val
          .filter((item) => item.name)
          .forEach((item) => {
            reverseTree(cloneFullRoomsTree, (room, parent, index) => {
              if (item.terminalId === room.terminalId && room.type !== 'platform') {
                parent && parent.children && parent.children.splice(index, 1);
              }
            });
          });
        this.roomsTree = cloneFullRoomsTree;
      },
      getCanSubmit(val) {
        this.canSubmit = val;
      },
      /**
       * 获取多级会议所在的平台及终端
       */
      getMultiConfInfo() {
        getMultiConfInfo(this.confId, { online: true, search: this.confName }).then(({ data }) => {
          let tranformer = (node) => {
            if (!node.platform) return null; // 过滤叶子节点

            node.internals.forEach((terminal) => {
              terminal.terminalId = terminal.id;
              terminal.checked = false;
            });
            node.wilds.forEach((terminal) => {
              terminal.terminalId = terminal.id;
              terminal.checked = false;
            });

            return {
              name: node.platform.name,
              id: node.id,
              platformId: node.platform.id,
              type: 'platform',
              checked: false,
              children: [...node.internals, ...node.wilds],
            };
          };

          this.roomsTree = [transformTree(data.rootUnitDto, tranformer, 'subordinates')];
          // 克隆
          this.fullRoomsTree = JSON.parse(JSON.stringify(this.roomsTree));

          console.log(this.roomsTree);
        });
      },
      /**
       * 自动填充剩余的画面
       */
      autoFillScreen() {
        this.$refs.screenGroup.autoFillScreen();
      },
      /**
       * 自动填充语音激励
       */
      autoFillVoice() {
        this.$refs.screenGroup.autoFillVoice();
      },
      /**
       * 清除所有画面的终端
       */
      doClear() {
        this.$confirm(`确定清空所有会场?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          this.clear();
        });
      },
      // 清除所有画面的终端
      clear() {
        this.$refs.screenGroup.clear();
        if (!this.bPoll) {
          this.roomsTree = JSON.parse(JSON.stringify(this.fullRoomsTree));
        }
        this.selectedList = [];
        this.canSubmit = false;
      },
      /**
       * 提交多画面
       */
      handleOk(closeDlg = true) {
        this.loading = true;
        const { screenNumber, curModelType, getParticipants } = this.$refs.screenGroup;

        let param = {
          conferenceId: this.confId,
          picNum: screenNumber,
          mode: curModelType.model,
        };

        let fn;
        // 级联会议多画面轮询
        if (this.bPoll) {
          fn = confControlPollMultiScreen;
          param.subPicPollList = getParticipants();
        } else {
          // 级联会议多画面
          fn = confControlMultiScreen;
          param.subPicList = getParticipants();
        }

        fn(param)
          .then((res) => {
            this.$message.success('设置成功！');
            if (closeDlg) {
              this.close();
            }
            this.$emit('refresh');
          })
          .finally(() => {
            this.loading = false;
          });
      },
    },
  };
</script>
<style lang="scss" scoped>
  .content-wrap {
    display: flex;
    justify-content: space-between;
    height: 655px;
    .header-wrap {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 0px 10px;
      margin-bottom: 5px;
      border-bottom: 1px solid rgb(236, 234, 234);
      .title {
        font-weight: bold;
        font-size: 16px;
      }
    }
    .list-wrap {
      width: 420px;
      border: 1px solid rgb(236, 234, 234);
      padding: 10px;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      .dragArea {
        overflow: scroll;
        flex: auto;
      }
      .list-item {
        padding: 15px 10px;
        border-bottom: 1px solid #f0f0f0;
      }
    }
    .screen-group-wrap {
      border: 1px solid rgb(236, 234, 234);
      padding: 10px;
      .header-wrap {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 5px;
      }
    }
  }

  .pre-action-input-wrap {
    display: flex;
    align-items: center;

    .pre-action-input-label {
      // font-size: 16px;
      margin-right: 10px;
      display: flex;
      &::before {
        content: '*';
        color: red;
        display: block;
        margin-right: 3px;
      }
    }

    .pre-action-input {
      width: 250px;
    }
  }

  .flex-block-between {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
</style>
