<template>
  <div id="smsConfig" v-loading="loading">
    <div class="content-top">
      <div class="btn-area">
        <el-button size="small" type="primary" icon="el-icon-plus" @click="doAddSms"> 添加短信配置 </el-button>

        <el-input
          placeholder="部门名称"
          class="left margin-left20"
          suffix-icon="el-icon-search"
          size="small"
          style="width: 150px"
          v-model="companyToSearch"
          v-if="isRootCompany"
          v-debounce="[getSmsConfig]"
        />
      </div>
    </div>

    <div class="content-body">
      <div class="content-detail">
        <div class="content-detail-border">
          <div class="table-position-pagination">
            <el-table :data="smsConfigs" highlight-current-row height="100%">
              <el-table-column prop="companyName" label="部门名称" align="center" :show-overflow-tooltip="true" min-width="200" fixed="left" />

              <el-table-column prop="isOpen" label="状态" align="center" min-width="90">
                <template slot-scope="scope">
                  <svg-icon icon-class="circle" :style="{ color: scope.row.isOpen ? 'rgb(113, 179, 69)' : 'rgb(238, 63, 77)' }" class="circle-icon" />
                  <span>{{ scope.row.isOpen ? '正常' : '已停用' }}</span>
                </template>
              </el-table-column>

              <el-table-column prop="platform" label="短信平台" min-width="140" align="center" :show-overflow-tooltip="true" />

              <el-table-column prop="signName" label="短信签名" min-width="140" align="center" :show-overflow-tooltip="true" />

              <el-table-column prop="accessKey" label="Access Key" min-width="140" align="center" :show-overflow-tooltip="true" />

              <el-table-column prop="secretKey" label="Secret Key" min-width="140" align="center" :show-overflow-tooltip="true" />

              <el-table-column prop="gmtModified" label="修改时间" min-width="180" align="center" :show-overflow-tooltip="true" />

              <el-table-column prop="gmtCreate" label="创建时间" min-width="180" align="center" :show-overflow-tooltip="true" />

              <el-table-column label="操作项" align="center" :show-overflow-tooltip="true" min-width="300" fixed="right">
                <template slot-scope="scope">
                  <el-button plain size="mini" type="primary" icon="el-icon-edit" @click="doEditSms(scope.row)"> 编辑 </el-button>

                  <el-button plain size="mini" type="danger" icon="el-icon-delete" @click="doDeleteSms(scope.row)"> 删除 </el-button>

                  <el-button plain size="mini" type="success" icon="el-icon-position" @click="doSendTest(scope.row)"> 测试发送 </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>

    <sms-config-dialog ref="smsConfigDialog" @afterSave="handleAfterSave" />
    <send-sms-test ref="sendSmsTest" @beforeSend="loading = true" @afterSend="loading = false" />
  </div>
</template>

<script>
  import SmsConfigDialog from './component/smsConfigDialog';
  import SendSmsTest from './component/sendSmsTest';

  export default {
    name: 'smsConfig',
    components: {
      SmsConfigDialog,
      SendSmsTest,
    },
    data() {
      return {
        loading: false,
        // 短信配置列表 - 假数据
        smsConfigs: [],
        // 备份原始数据用于搜索
        originalSmsConfigs: [],
        companyToSearch: '',
        isRootCompany: true,
      };
    },
    methods: {
      /**
       * 添加短信配置
       */
      doAddSms() {
        this.$refs.smsConfigDialog.showDialog(this.isRootCompany);
      },

      /**
       * 编辑短信配置
       */
      doEditSms(smsConfig) {
        this.$refs.smsConfigDialog.showDialog(this.isRootCompany, smsConfig);
      },

      /**
       * 获取短信配置列表
       */
      getSmsConfig() {
        this.loading = true;
        setTimeout(() => {
          if (this.companyToSearch) {
            this.smsConfigs = this.originalSmsConfigs.filter((item) => item.companyName.includes(this.companyToSearch));
          } else {
            this.smsConfigs = [...this.originalSmsConfigs];
          }
          this.loading = false;
        }, 500);
      },

      /**
       * 删除短信配置
       */
      doDeleteSms(smsConfig) {
        this.$confirm(`确定要删除【${smsConfig.companyName}】的短信配置？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          this.loading = true;
          setTimeout(() => {
            this.originalSmsConfigs = this.originalSmsConfigs.filter((item) => item.id !== smsConfig.id);
            this.smsConfigs = this.smsConfigs.filter((item) => item.id !== smsConfig.id);
            this.$message.success('删除成功');
            this.loading = false;
          }, 500);
        });
      },

      /**
       * 测试发送短信
       */
      doSendTest(smsConfig) {
        this.$refs.sendSmsTest.showDialog(smsConfig);
      },

      /**
       * 处理保存后的数据更新
       */
      handleAfterSave(newConfig) {
        const index = this.originalSmsConfigs.findIndex((item) => item.id === newConfig.id);
        if (index !== -1) {
          // 更新现有配置
          this.originalSmsConfigs.splice(index, 1, newConfig);
        } else {
          // 添加新配置
          this.originalSmsConfigs.push(newConfig);
        }
        this.getSmsConfig(); // 更新显示列表
      },
    },

    mounted() {
      this.originalSmsConfigs = [...this.smsConfigs];
      this.getSmsConfig();
    },
  };
</script>

<style scoped lang="scss">
  .table-position-pagination {
    height: 100%;
  }

  #smsConfig {
    .el-input {
      width: 400px;
    }

    .circle-icon {
      font-size: 18px;
      margin-right: 3px;
      vertical-align: middle;
    }
  }
</style>
