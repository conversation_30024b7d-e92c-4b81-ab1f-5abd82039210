<template>
  <el-slider
    v-model="active"
    ref="slider"
    :min="min"
    :max="max"
    :step="step"
  ></el-slider>
</template>

<script>
export default {
  props: {
    //预定义
    value: String | Array,
    min: {
      type: Number,
      default: 0,
    },
    max: {
      type: Number,
      default: 100,
    },
    step: {
      type: Number,
      default: 1,
    },
  },
  data() {
    return {};
  },
  computed: {
    active: {
      get() {
        return this.value;
      },
      set(newValue) {
        //当组件值发生变化后,通过 input 事件更新值
        this.$emit("input", newValue);
      },
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-input__inner {
  background-color: var(--BgColor);
  color: var(--Color);
}
::v-deep.el-select .el-input .el-select__caret {
  color: var(--Color);
}
.el-select-dropdown__item {
  padding: 0;
  .select-item {
    padding: 0 20px;
  }
}
</style>
