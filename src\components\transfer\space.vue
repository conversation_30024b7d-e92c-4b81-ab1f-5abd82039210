<!-- 空间穿梭框 -->
<!-- 物联集控中使用 -->
<template>
  <el-dialog title="选择空间" :visible.sync="visible" width="1200px" custom-class="space-dialog" :close-on-click-modal="false" @close="close">
    <div class="margin-bottom10 search-wrap">
      <el-cascader
        class="mr-10"
        :options="labelList"
        v-model="labelId"
        placeholder="空间标签"
        ref="cascaderSelector"
        :props="{ label: 'name', value: 'id', checkStrictly: true }"
        clearable
        size="small"
        style="width: 200px; margin-right: 10px"
      >
      </el-cascader>
      <el-input
        placeholder="空间名称"
        suffix-icon="el-icon-search"
        size="small"
        v-model="spaceName"
        clearable
        style="width: 200px"
        v-debounce="[
          (e) => {
            pagination.curPage = 1;
            getSpaceList(e);
          },
        ]"
        @clear="
          () => {
            pagination.curPage = 1;
            getSpaceList();
          }
        "
      />
    </div>
    <transfer
      ref="transfer"
      keyword="id"
      :list="spaceList"
      :chooseList="chooseSpaceList"
      class="space-transfer"
      :defaultSort="{ prop: sort.prop, order: 'ascending' }"
      @sortChange="sortChange"
      :isMulti="isMulti"
      v-loading="loading"
    >
      <div slot="titleLeft">候选空间</div>
      <template slot="leftTable">
        <el-table-column label="序号" width="50" show-overflow-tooltip align="center">
          <template slot-scope="scope">
            {{ scope.$index + (pagination.curPage - 1) * pagination.size + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="spaceName" sortable :show-overflow-tooltip="true" label="空间名称" min-width="80" />
        <el-table-column prop="labelName" sortable :show-overflow-tooltip="true" label="空间标签" min-width="80"> </el-table-column>
      </template>
      <pagination
        slot="pagination"
        :total="pagination.total"
        :page.sync="pagination.curPage"
        :limit.sync="pagination.size"
        :layout="TRANSFER_PAGINATION_LAYOUT"
        @pagination="getSpaceList"
        :autoScroll="false"
      />
      <div slot="titleRight">已选空间</div>
      <template slot="rightTable">
        <el-table-column type="index" label="序号" min-width="50" align="center"> </el-table-column>
        <el-table-column prop="spaceName" sortable :show-overflow-tooltip="true" label="空间名称" min-width="80" />
        <el-table-column prop="labelName" sortable :show-overflow-tooltip="true" label="空间标签" min-width="80"> </el-table-column>
      </template>
    </transfer>
    <div slot="footer" class="dialog-footer">
      <el-button @click="close">取消</el-button>
      <el-button class="okBtn" @click="handleOk">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import dlg from '@/mixins/dlg';
  import transfer from './index.vue';
  import { getSpaceLabel, getGatewaySpaceList } from '@/api/iotControl';
  import { TRANSFER_PAGINATION_LAYOUT } from '@/utils/enum';
  export default {
    mixins: [dlg],
    components: {
      transfer,
    },
    props: {
      spaces: {
        type: Array,
        default: () => [],
        require: false,
      },
      // 是否多选
      isMulti: {
        type: Boolean,
        default: true,
      },
    },
    data() {
      return {
        loading: false,
        //空间标签
        labelList: [],
        //选中的空间标签
        labelId: [],
        // 空间名称搜索
        spaceName: '',
        // 空间列表
        spaceList: [],
        chooseSpaceList: [],

        // 分页参数
        pagination: {
          curPage: 1,
          size: 20,
          total: 0,
        },
        sort: {
          prop: 'spaceName',
          order: 'ASC',
        },
        TRANSFER_PAGINATION_LAYOUT,
      };
    },
    created() {
      getSpaceLabel().then((res) => {
        this.labelList = res.data;
        this.getSpaceList();
        if (this.spaces && this.spaces.length) {
          this.chooseSpaceList = this.spaces.map((item) => {
            return { id: item.spaceId, spaceName: item.spaceName };
          });
        }
      });
    },
    watch: {
      labelId: {
        handler() {
          this.pagination.curPage = 1;
          this.spaceName = '';
          this.getSpaceList();
        },
        deep: true,
      },
    },
    methods: {
      handleOk(closeDlg = true) {
        let list = this.$refs.transfer.rightList;
        if (!this.isMulti && list.length > 1) {
          this.$message.warning('只能选择单个空间!');
          return;
        }
        this.$emit('handleSelect', list);
        if (closeDlg) {
          this.close();
        }
      },
      /**
       * 获取空间列表
       */
      getSpaceList() {
        this.loading = true;
        getGatewaySpaceList({
          labelId: this.labelId[this.labelId.length - 1],
          page: this.pagination.curPage,
          size: this.pagination.size,
          direction: this.sort.order,
          property: this.sort.prop,
          spaceName: this.spaceName,
          includeChild: true,
        })
          .then((res) => {
            this.spaceList = res.data.rows;
            this.pagination.total = res.data.total;
          })
          .finally(() => (this.loading = false));
      },
      sortChange(col) {
        this.sort.order = 'ascending' === col.order ? 'ASC' : 'DESC';
        this.sort.prop = col.prop;
        this.getSpaceList();
      },
    },
  };
</script>
<style lang="scss" scoped></style>
