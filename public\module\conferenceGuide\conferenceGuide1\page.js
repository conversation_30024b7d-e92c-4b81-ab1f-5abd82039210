let page = {
  width: 350,
  height: 180,
  props: [
    {
      type: "el-switch",
      title: "签到",
      field: "bShowSign",
      value: true,
    },
    {
      type: "el-switch",
      title: "时间占用条",
      field: "bShowTimeLine",
      value: true,
    },
    {
      type: "el-switch",
      title: "预订人",
      field: "bShowReserver",
      value: true,
    },
    {
      type: "el-switch",
      title: "状态灯",
      field: "statusLight",
      value: true,
      control: [
        {
          value: true,
          rule: [
            "statusLightBrightness",
            "freeLightColor",
            "meetingLightColor",
            "signLightColor",
            "statusLightEffect",
          ],
        },
      ],
    },
    {
      type: "colorSelect",
      title: "空闲灯光",
      field: "freeLightColor",
      value: "3",
    },
    {
      type: "colorSelect",
      title: "会中灯光",
      field: "meetingLightColor",
      value: "5",
    },
    {
      type: "colorSelect",
      title: "签到灯光",
      field: "signLightColor",
      value: "7",
    },
  ],
};
