<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Document</title>
  <link rel="stylesheet" href="40e93b11cbd4174c60b22cba0d630300.css" />
  <style>
    .svg-icon {
      width: 1em;
      height: 1em;
      vertical-align: -0.15em;
      fill: currentColor;
      overflow: hidden;
    }

    ul {
      margin: 0;
      padding: 0;
    }

    ul li {
      list-style: none;
    }

    body,
    html {
      height: 100%;
      margin: 0;
    }

    .wrap {
      width: 100%;
      height: 100%;
      box-sizing: border-box;
      overflow: hidden;
      color: white;
      /* background: rgb(215, 20, 24); */
      position: relative;

      display: flex;
      flex-direction: column;
    }

    .wrap>img {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
    }


    .content-wrap {
      flex: 1;
      padding: 8vh 2vw;
      box-sizing: border-box;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      position: relative;
      z-index: 2;
      overflow: hidden;
    }

    .content-wrap .left-wrap {
      width: 50%;
      box-sizing: border-box;
      padding: 0 3vw;
      display: flex;
      flex-direction: column;
      justify-content: center;
      position: relative;
      font-family: SimSun, 宋体, serif;
    }

    .content-wrap .left-wrap .time-wrap {
      position: absolute;
      left: 3vw;
      top: 0;
      font-weight: bold;
    }

    .content-wrap .left-wrap .time-wrap .current-time {
      font-size: 4.6vw;
      margin-top: 2vh;
      font-family: "Microsoft YaHei";
    }

    .content-wrap .left-wrap .time-wrap .current-date {
      font-size: 2.5vw;
    }

    .content-wrap .left-wrap .title {
      font-size: 6vw;
      text-align: center;
      text-overflow: ellipsis;
      overflow: hidden;
      word-break: break-all;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      font-weight: bold;

    }

    .content-wrap .right-wrap {
      width: 50%;
      padding: 0 3vw;
      box-sizing: border-box;
      /* color: rgb(237, 244, 253) */
    }

    .content-wrap .right-wrap .current-conf-wrap {
      padding: 2vh 0;
      background: linear-gradient(to bottom, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.1));
      border-radius: 10px;
    }

    .content-wrap .right-wrap .conf-item {
      height: 18vh;
      border-left: 0.4vw solid rgb(229, 15, 24);
      padding-left: 2vw;
      margin: 4vh 1vw 4vh 2vw;
      display: flex;
      flex-direction: column;
      justify-content: space-evenly;
    }

    .content-wrap .right-wrap .subject {
      font-size: 2vw;
      font-weight: bold;
      text-overflow: ellipsis;
      overflow: hidden;
      word-break: break-all;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .content-wrap .right-wrap .time {
      font-size: 2vw;
      margin-top: 4vh;
      font-weight: bold;
    }

    .content-wrap .right-wrap .scroll-wrap {
      height: 57vh;
      overflow: hidden;
      background: linear-gradient(to bottom, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.1));
      border-radius: 10px;
      padding-top: 7vh;
      box-sizing: border-box;
    }

    .content-wrap .right-wrap li.conf-item {
      height: 18vh;
      border-color: rgb(14, 151, 123);
      margin: 0vh 1vw 7vh 2vw;
      position: relative;
    }

    .content-wrap .right-wrap li+li::before {
      content: '';
      display: inline-block;
      position: absolute;
      top: -3.5vh;
      left: -0.4vw;
      width: 100%;
      height: 0.1vw;
      background-color: rgb(58, 125, 209);
    }

    .content-wrap .empty-wrap {
      font-size: 4vh;
      margin-top: 4vh;
    }

    [v-cloak] {
      display: none;
    }
  </style>
  <!-- vue -->
  <script src="174365807c06490c848d7b1d45fdc348.js"></script>
  <!-- mqtt -->
  <script src="d4bfcfa9de50875589eaab322a015441.js"></script>
  <script src="page.js"></script>
</head>

<body>
  <div id="app" class="wrap" v-cloak :style="{
    backgroundColor:style.backgroundColor,
      color:style.color}">
    <img src="./fd4f437fe153eb5e673a4f8f55af4c3e.jpg" alt="" v-if="style.bShowBg" />
    <div class="content-wrap">
      <div class="left-wrap">
        <div class="time-wrap">
          <div class="current-date">{{currentDate}}</div>
          <div class="current-time">{{currentTime}}</div>
        </div>
        <div class="title">
          {{conferenceRoom.roomName||terminalName}}
        </div>
      </div>
      <div class="right-wrap">
        <div class="current-conf-wrap">
          <div class="conf-item">
            <template v-if="bConfs">
              <div class="subject">{{curConference.subject}}</div>
              <div class="time">{{curConference.period}}</div>
            </template>
            <div class="subject" v-else>
              暂无会议
            </div>
          </div>
        </div>
        <ul class="scroll-wrap" v-if="conferenceInfo && conferenceInfo.length">
          <li class="conf-item" v-for="(item,index) in currentPageConfs" :key="item.confId" @click="clickConference(item)">
            <div class="subject">
              {{item.subject}}
            </div>
            <div class="time">
              {{item.period}}
            </div>
          </li>
        </ul>
        <ul class="scroll-wrap" v-else>
          <li class="conf-item">
            <div class="subject">
              暂无会议
            </div>
          </li>
        </ul>
      </div>
    </div>
  </div>
  </div>
</body>

<script>
  window.$page = page;
  const cssProperty = ["height", "width", "fontSize"];
  const DATES = ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十', '十一', '十二', '十三', '十四', '十五', '十六', '十七', '十八', '十九', '二十', '二十一', '二十二', '二十三', '二十四', '二十五', '二十六', '二十七', '二十八', '二十九', '三十', '三十一'];
  const WEEKS = ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"];

  const ItemsPerPage = 2
  // 10秒
  const AutoPlayInterval = 10 * 1000

  const app = new Vue({
    el: "#app",
    data: {
      props: page.props,
      mqttClient: null,
      mqttTopic: "",
      mac: null,
      curConference: {
        status: "正在会议中",
        subject: "第三季度经营情况分析大会",
        contactPerson: "刘勇",
        period: "16:57 - 17:27",
        periodArr: ["2021-03-04", "09:00-10:30"],
        qrCode: "./b947d49f014c663d02583f0f6cfc141f.png",
        dept: "研发部",
        participant: [{
          username: "张三丰",
          signIn: true
        },
        {
          username: "李四水",
          signIn: false
        },
        {
          username: "王五",
          signIn: false
        },
        {
          username: "刘勇",
          signIn: true
        },
        {
          username: "莹莹",
          signIn: false
        },
        {
          username: "于光",
          signIn: false
        }
        ]
      },
      conferenceList: [],
      conferenceInfo: [
        {
          confId: 1,
          contactPerson: "张三丰",
          participant: [{
            username: "张三丰",
            signIn: false
          }],
          period: "11:00-12:00",
          periodArr: ["2021-03-04", "11:00-12:00"],
          qrCode: "./7a8606304f019293184038e341f24d85.jpg",
          subject: "全国密码学与自主系统智能控制专题研讨会"
        },
        {
          confId: 2,
          contactPerson: "李四水",
          participant: [{
            username: "李四水",
            signIn: false
          }],
          period: "12:30-13:30",
          periodArr: ["2021-03-04", "12:30-13:30"],
          qrCode: "./7a8606304f019293184038e341f24d85.jpg",
          subject: "智能计算及应用国际会议"
        },
        {
          confId: 4,
          contactPerson: "刘小勇",
          participant: [{
            username: "刘小勇",
            signIn: false
          }],
          period: "14:00-15:00",
          periodArr: ["2021-03-04", "14:00-15:00"],
          qrCode: "./7a8606304f019293184038e341f24d85.jpg",
          subject: "干旱气候变化与可持续性发展学术研讨会"
        },
        {
          confId: 5,
          contactPerson: "方莹莹",
          participant: [{
            username: "方莹莹",
            signIn: false
          }],
          period: "15:20-16:00",
          periodArr: ["2021-03-04", "15:20-16:00"],
          qrCode: "./7a8606304f019293184038e341f24d85.jpg",
          subject: "中国IT、网络、信息技术、电子、仪器仪表创新学术会议"
        },
        {
          confId: 3,
          contactPerson: "王五",
          participant: [{
            username: "王五",
            signIn: false
          }],
          period: "17:30-19:00",
          periodArr: ["2021-03-04", "17:30-19:00"],
          qrCode: "./7a8606304f019293184038e341f24d85.jpg",
          subject: "国际气体净化技术研讨会"
        },
        {
          confId: 6,
          contactPerson: "余秋水",
          participant: [{
            username: "余秋水",
            signIn: false
          }],
          period: "20:00-22:00",
          periodArr: ["2021-03-04", "20:00-22:00"],
          qrCode: "./7a8606304f019293184038e341f24d85.jpg",
          subject: "图像处理与模式识别在工业工程中的应用国际学术研讨会"
        },
        {
          confId: 7,
          contactPerson: "余秋水",
          participant: [{
            username: "余秋水",
            signIn: false
          }],
          period: "23:00-24:00",
          periodArr: ["2021-03-04", "23:00-24:00"],
          qrCode: "./7a8606304f019293184038e341f24d85.jpg",
          subject: "图像处理与模式识别在工业工程中的应用国际学术研讨会"
        }
      ],
      conferenceRoom: {
        roomName: "1511会议室"
      },
      terminalName: "",
      // 会议室状态，0为空闲，1为占用
      status: [{
        label: "空闲中",
      },
      {
        label: "已预约",
      }
      ],
      timeAxis: [{
        key: Math.random(),
        width: "100%",
        status: 0
      }],
      timer: null,
      statusTimer: null,
      mqtt: {},
      currentDate: '',
      currentTime: '',
      currentTimer: null,
      currentPage: 1,
      switchPageTimer: null
    },
    computed: {
      style() {
        let style = {};
        for (let prop of this.props) {
          if (cssProperty.includes(prop.field)) {
            style[prop.field] = prop.value + "%"
          } else {
            style[prop.field] = prop.value
          }
        }
        return style;
      },
      bConfs() {
        if (this.curConference && Object.keys(this.curConference).length) {
          return true
        } else {
          return false
        }
      },
      currentPageConfs() {
        const start = (this.currentPage - 1) * ItemsPerPage
        const end = start + ItemsPerPage
        return this.conferenceInfo.slice(start, end)
      },
      totalPages() {
        return Math.ceil(this.conferenceInfo.length / ItemsPerPage)
      },
    },
    created() {
      this.updateCurrentTime()

      if (!this.isWindows()) {
        this.curConference = {};
        this.conferenceInfo = [];
        this.conferenceRoom = {
          roomName: ""
        };
        const {
          mac,
          name
        } = this.getTerminalInfo();
        if (mac) {
          this.mac = mac;
          this.terminalName = name
          this.conferenceRoom = {
            roomName: ""
          }
          //初始化终端灯状态
          this.setTerminalStatus(0);
          //刷新终端灯的状态
          this.statusTimer && clearInterval(this.statusTimer);
          this.statusTimer = setInterval(this.refreshTerminalStatus, 5000);
        } else {
          this.log("终端信息获取失败");
        }
        let mqtt = this.getMqtt();
        if (mqtt) {
          this.mqtt = mqtt;
          this.getMqttServerAndConnect(mqtt);
        } else {
          this.log("mqtt地址获取失败");
        }
      }
      // let ws = "wss://rjms.putian-nst.com:443/mqtt/";
      // let username = "admin";
      // let password = "mqttServer";
      // let ws = "ws://218.94.61.65:8083/mqtt";
      // let username = "admin";
      // let password = "mqttServer";
      // let ws = "ws://192.168.89.153:8083/mqtt";
      // let username = "admin";
      // let password = "admin";
      // let ws = "ws://192.168.88.121:8080/mqtt";
      // let username = "admin";
      // let password = "";
      // let ws = "ws://192.168.89.251:8080/mqtt";
      // let username = "admin";
      // let password = "nst@aliyun";
      // this.getMqttServerAndConnect({ ws, username, password });
    },
    beforeDestory() {
      //关闭会议室状态
      this.setTerminalStatus(-1);
      this.mqttClose();
      this.IntervalClose();
      this.stopAutoPlay()
    },
    watch:{
      'style.autoPlayInterval'(){
        this.stopAutoPlay()
        this.startAutoPlay()
      }
    },
    mounted() {
      window["update"] = (val, mqtt = null) => {
        this.updateProps(val);
      };

      window["updateMqtt"] = param => {
        this.updateMqtt(param);
      };

      this.startAutoPlay()
    },
    methods: {
      startAutoPlay() {
        console.log(this.style.autoPlayInterval)
        this.switchPageTimer = setInterval(() => {
          this.nextPage()
        }, this.style.autoPlayInterval*1000 || AutoPlayInterval)
      },
      stopAutoPlay() {
        if (this.switchPageTimer) {
          clearInterval(this.switchPageTimer)
          this.switchPageTimer = null
        }
      },
      nextPage() {
        this.currentPage = this.currentPage >= this.totalPages ? 1 : this.currentPage + 1
      },
      updateCurrentTime() {
        this.initCurrentTime()
        this.currentTimer = setInterval(this.initCurrentTime, 60 * 1000)
      },
      initCurrentTime() {
        const date = new Date();
        const monthIndex = date.getMonth() + 1;
        const dateIndex = date.getDate();
        const weekIndex = date.getDay();
        const hours = ('0' + date.getHours()).slice(-2);
        const minutes = ('0' + date.getMinutes()).slice(-2);
        this.currentTime = hours + ':' + minutes
        this.currentDate = monthIndex + '月' + dateIndex + '日' + ' ' + WEEKS[weekIndex]
      },
      updateMqtt(mqtt) {
        if (!mqtt) {
          return;
        }
        mqtt = JSON.parse(mqtt);

        if (
          (this.mqttClient && this.mqtt.ws !== mqtt.ws) ||
          (!this.mqttClient && !Object.keys(this.mqtt).length)
        ) {
          this.log("mqtt重连");
          this.mqttClose();
          this.curConference = {};
          this.conferenceInfo = [];
          this.conferenceRoom = {
            roomName: ""
          };
          this.mqtt = mqtt;
          this.getMqttServerAndConnect(mqtt);
        }
      },
      /*
       * 当前运行环境是终端还是pc
       */
      isWindows() {
        return window.DSS20AndroidJS === undefined;
      },
      /*
       * state -1:关闭 0:空闲 1:会议中 2:签到
       */
      setTerminalStatus(state) {
        if (window.DSS20AndroidJS) {
          // -1 关闭
          if (state === -1) {
            window.DSS20AndroidJS.updateLedStatusByWeb(0);
          }

          // 状态灯设置
          let temp = this.getProp("statusLight");
          if (temp) {
            // 开启
            if (temp.value) {
              let fieldKey = "";
              if (state === 0) {
                // 空闲 0
                fieldKey = "freeLightColor";
              } else if (state === 1) {
                // 会中 1
                fieldKey = "meetingLightColor";
              } else if (state === 2) {
                // 签到 2
                fieldKey = "signLightColor";
              }
              if (fieldKey) {
                let tempObj = this.getProp(fieldKey);
                if (tempObj) {
                  let lightVal = tempObj.value;
                  lightVal = Number(lightVal);
                  window.DSS20AndroidJS.updateLedStatusByWeb(lightVal);
                }
              }
            } else {
              // 关闭状态灯
              window.DSS20AndroidJS.updateLedStatusByWeb(0);
            }
          }
        }
      },
      /*
       * 终端打印
       */
      log(msg) {
        if (this.isWindows()) {
          console.log(msg)
          return;
        }
        window.DSS20AndroidJS &&
          window.DSS20AndroidJS.htmlLogcat("会议指引：" + msg);
      },
      clickConference(item) {
        this.log('conferenceInfo')
        window.DSS20AndroidJS &&
          window.DSS20AndroidJS.conferenceInfo(JSON.stringify(item));
      },
      getTerminalInfo() {
        var data = window.DSS20AndroidJS.terminalInfo();
        var info = JSON.parse(data);
        return info;
      },
      getMqtt() {
        var data = window.DSS20AndroidJS.getWebMqttWs();
        var info = {};
        try {
          info = data && JSON.parse(JSON.parse(data));
        } catch (err) {
          this.log("mqtt地址解析失败！");
        }

        return info;
      },
      updateProps(props) {
        for (let prop of props) {
          let index = this.getPropIndex(prop.field);
          if (index !== -1) {
            let data = this.props[index];
            data.value = prop.value;
            this.$set(this.props, index, data);
          }
        }
      },
      getPropIndex(name) {
        for (let i = 0; i < this.props.length; i++) {
          if (this.props[i].field === name) {
            return i;
          }
        }
        return -1;
      },
      /**
       * 更新数据
       */
      updateConfByTime() {
        if (!this.conferenceList.length && this.isWindows()) {
          return;
        }
        const cur = +new Date();

        let bProcessing = false;
        let res = this.conferenceList.filter(item => {
          if (item.approved !== 4) {
            return false;
          }
          const {
            start,
            end
          } = item;
          if (end < cur) {
            return false;
          } else if (start <= cur && end >= cur) {
            bProcessing = true;
          }
          return true;
        });
        //更新会议信息
        if (bProcessing) {
          this.curConference = {
            ...res.shift(),
            status: "正在会议中"
          };
        } else {
          this.curConference = {};
        }

        this.conferenceInfo = res;

        // 这里需要过滤出时间是当天的会议 支持跨天
        const tempConfList = JSON.parse(JSON.stringify(this.conferenceList));
        // 需要针对跨天进行处理
        tempConfList.forEach(item => {
          // 创建一个新的日期对象
          const now = new Date();
          // 设置时间为凌晨 00:00:00
          now.setHours(0, 0, 0, 0);
          const todayStartTimestamp = now.getTime();
          const todayEndTimestamp = todayStartTimestamp + 1000 * 3600 * 24;
          // 开始时间在今天，结束时间不在今天 endTimeArr => [24, 0]
          if (item.start >= todayStartTimestamp && item.start <= todayEndTimestamp && item.end >=
            todayEndTimestamp) {
            item.endTimeArr = [24, 0];
            item.bInToday = true;
          }

          // 开始时间不在今天、结束时间在今天 startTimeArr => [0, 0]
          if (item.start <= todayStartTimestamp && item.end >= todayStartTimestamp && item.end <=
            todayEndTimestamp) {
            item.startTimeArr = [0, 0];
            item.bInToday = true;
          }

          // 时间包括当天 开始时间小于当天0：00 结束时间大于 当天23：59
          if (item.start <= todayStartTimestamp && item.end >= todayEndTimestamp) {
            item.startTimeArr = [0, 0];
            item.endTimeArr = [24, 0];
            item.bInToday = true;
          }

          // 开始时间在今天 结束时间在今天
          if (item.start >= todayStartTimestamp && item.end <= todayEndTimestamp) {
            item.bInToday = true;
          }
        });
      },
      //刷新终端灯的状态
      refreshTerminalStatus() {
        // 空闲
        let state = 0;

        // 会中
        if (
          Object.keys(this.curConference).length &&
          this.curConference.status === "正在会议中"
        ) {
          state = 1;
        }

        // 签到
        if (Object.keys(this.curConference).length && this.curConference.status === '签到中') {
          state = 2;
        }
        this.setTerminalStatus(state);
      },
      /**
       * 从 prop 中获取 val
       */
      getProp(field) {
        for (let prop of this.props) {
          if (prop.field == field) {
            return prop;
          }
        }
        return null;
      },
      /**
       * 随机ID
       * @param {*} len
       * @param {*} radix
       * @returns
       */
      randomId(len, radix) {
        var chars =
          '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('')
        var uuid = [],
          i
        radix = radix || chars.length
        if (len) {
          // Compact form
          for (i = 0; i < len; i++) uuid[i] = chars[0 | (Math.random() * radix)]
        } else {
          // rfc4122, version 4 form
          var r
          // rfc4122 requires these characters
          uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-'
          uuid[14] = '4'
          // Fill in random data.  At i==19 set the high bits of clock sequence as
          // per rfc4122, sec. 4.1.5
          for (i = 0; i < 36; i++) {
            if (!uuid[i]) {
              r = 0 | (Math.random() * 16)
              uuid[i] = chars[i == 19 ? (r & 0x3) | 0x8 : r]
            }
          }
        }
        return uuid.join('')
      },
      /**
       * 连接到MQTT服务器并订阅
       */
      getMqttServerAndConnect(param) {
        this.log("mqtt参数：" + JSON.stringify(param));

        const that = this;
        let {
          ws,
          username,
          password
        } = param;
        let mac = this.mac || ''
        // 客户端ID
        let clientId = `js_conferenceGuide6_${mac}_${this.randomId(16, 16)}`;

        that.mqttClient = mqtt.connect(ws, {
          clientId,
          username,
          password,
          clean: true,
          connectTimeout: 5 * 1000,
          keepalive: 30
        });
        that.mqttClient.on("connect", () => {
          this.log("mqtt连接成功");
          this.mqttRefreshSubscribe();
        });
        that.mqttClient.on("error", error => {
          this.log("mqtt连接失败：" + JSON.stringify(error));
          this.conferenceRoom = {};
          this.curConference = {};
          this.conferenceInfo = [];
        });
        //监听接收消息事件
        that.mqttClient.on("message", (topic, message) => {
          message = JSON.parse(message.toString());
          console.log(message)
          this.log("获取mqtt消息" + JSON.stringify(message));
          const {
            meetingroom,
            confDetailDTOList
          } = message;
          this.conferenceRoom = meetingroom;
          this.conferenceList = confDetailDTOList.map(item => {
            return this.getFormatData(item)
          });
          this.updateConfByTime();
        });
      },
      getPeriod(startTimeStamp, endTimeStamp, periodArr) {
        if (periodArr[1].includes('永久')) {
          return '永久会议'
        }
        let startTimeArr = periodArr[0].split(' ')
        let endTimeArr = periodArr[1].split(' ')
        let curEndTime = new Date(new Date(startTimeStamp).setHours(23, 59, 59, 999))
          .getTime()
        // 跨天会议,超过一天显示+1
        if (endTimeStamp > curEndTime) {
          endTimeArr[1] += '+1'
        }
        return startTimeArr[1] + '-' + endTimeArr[1]
      },
      getFormatData(item) {
        let periodArr = item.period.split("-");
        let start, end, startTimeArr, endTimeArr
        // 获取startTimeArr、endTimeArr （[hh,mm]）
        // 会议可能跨天
        if (item.start && item.end) {
          start = item.start
          end = item.end

          const startTime = new Date(start)
          startTimeArr = [startTime.getHours(), startTime.getMinutes()]

          let curEndTime = new Date(new Date().setHours(23, 59, 59, 999)).getTime()
          let endTime = end > curEndTime ? new Date(curEndTime) : new Date(end)
          endTimeArr = [endTime.getHours(), endTime.getMinutes()]
        } else {
          // 获取start、end (时间戳)
          // 兼容之前的写法（会议不可能跨天）
          let timeArr = periodArr[1].split("-");
          startTimeArr = timeArr[0].split(":").map(Number);
          endTimeArr = timeArr[1].split(":").map(Number);

          start = new Date(new Date().setHours(startTimeArr[0], startTimeArr[1])).getTime()
          end = new Date(new Date().setHours(endTimeArr[0], endTimeArr[1])).getTime()
        }
        return {
          period: this.getPeriod(start, end, periodArr),
          periodArr,
          start,
          end,
          startTimeArr,
          endTimeArr,
          confId: item.confId,
          subject: item.subject,
          nickname: item.nickname,
          contactPerson: item.contactPerson,
          participant: item.participant,
          approved: item.approved,
          qrCode: item.qrCode
        }
      },

      /**
       * 发布MQTT主题
       */
      mqttPublish(address) {
        let topic = "dss2/terminal/conference";
        let message = `{"command":"WEATHER","parameters":{${param}:"${address}"}}`;
        this.log("mqtt发布主题: " + message);
        this.mqttClient.publish(
          topic,
          message, {
          qos: 1,
          retain: true
        },
          (err, res) => {
            if (err) {
              this.log("mqtt发布主题失败：", err);
              return;
            }
            this.mqttRefreshSubscribe(address);
          }
        );
      },
      /**
       * 订阅MQTT主题
       */
      mqttRefreshSubscribe() {
        // this.mac = "00073D9001D7";
        // this.mac = "00073D9003C5";
        const that = this;
        if (that.mqttTopic) {
          that.mqttClient.unsubscribe(that.mqttTopic);
        }
        that.mqttTopic = `dss2/web/conference/mac/${this.mac}`;
        this.log("mqtt订阅主题：" + that.mqttTopic);
        that.mqttClient.subscribe(
          that.mqttTopic, {
          qos: 1,
          rap: true
        },
          (err, res) => {
            if (err) {
              this.log("mqtt订阅主题失败：", err);
              return;
            }

            //刷新数据
            that.timer && clearInterval(that.timer);
            that.timer = setInterval(() => {
              that.updateConfByTime();
            }, 60000);
          }
        );
      },
      /**
       * 释放MQTT客户端
       */
      mqttClose() {
        this.mqtt = {};
        if (this.mqttClient) {
          this.mqttClient.unsubscribe(this.mqttTopic);
          this.mqttClient.end();
        }
      },
      IntervalClose() {
        this.timer && clearInterval(this.timer);
        this.statusTimer && clearInterval(this.statusTimer);
        this.currentTimer && clearInterval(this.currentTimer);
      }
    }
  });

</script>

</html>