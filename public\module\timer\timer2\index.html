<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
    <link rel="stylesheet" href="40e93b11cbd4174c60b22cba0d630300.css" />
    <style>
      body,
      html {
        height: 100%;
        margin: 0;
      }
      body{
        background-color: transparent;
      }
      .wrap {
        width: 100%;
        height: 100%;
        text-align: center;
        position: relative;
      }
      .time-wrap {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        height: 100%;
        width: 100%;
        display: inline-flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        background-color: #fff;
      }
      .line-wrap {
        display: inline-block;
        width: 1px;
        height: 80%;
        margin: 0 10px;
      }
      .wrap .left-wrap {
        font-size: 18vw;
      }
      .wrap .right-wrap {
        text-align: left;
        font-size: 5.5vw;
      }
      [v-cloak] {
        display: none;
      }
    </style>
    <script src="9cccd446bf1976b29f1da834a3ef8e24.js"></script>
    <script src="174365807c06490c848d7b1d45fdc348.js"></script>
    <script src="page.js"></script>
  </head>

  <body>
    <div id="app" class="wrap">
      <div class="time-wrap" :style="style" v-cloak>
        <div class="left-wrap">{{time}}</div>
        <div class="line-wrap" :style="{backgroundColor:style.color}"></div>
        <div class="right-wrap">
          <div class="day-of-week">{{dayOfWeek}}</div>
          <div class="date">{{formatDate}}</div>
          <div class="lunar-date">{{lunarDate}}</div>
        </div>
      </div>
    </div>
  </body>

  <script>
    window.$page = page;
    const cssProperty = ["height", "width", "fontSize"];
    const weeks = [
      "星期日",
      "星期一",
      "星期二",
      "星期三",
      "星期四",
      "星期五",
      "星期六"
    ];

    const app = new Vue({
      el: "#app",
      data: {
        date: new Date(),
        props: page.props,
        timer: null
      },
      computed: {
        style() {
          let style = {};
          for (let prop of this.props) {
            let unit = cssProperty.includes(prop.field) ? "px" : "";
            style[prop.field] = prop.value + unit;
          }
          return style;
        },
        time() {
          return (
            this.formatValue(this.date.getHours()) +
            ":" +
            this.formatValue(this.date.getMinutes())
          );
        },
        dayOfWeek() {
          return weeks[this.date.getDay()];
        },
        formatDate() {
          return (
            this.formatValue(this.date.getFullYear()) +
            " · " +
            this.formatValue(this.date.getMonth() + 1) +
            " · " +
            this.formatValue(this.date.getDate())
          );
        },
        lunarDate() {
          var date = this.date
          //归零时分秒
          date.setHours(0,0,0,0)
          let lunar = chineseLunar.solarToLunar(date);
          return (
            chineseLunar.format(lunar, "A") +
            "年 · " +
            chineseLunar.format(lunar, "Md")
          );
        }
      },
      created() {
        this.setTime();
      },
      mounted() {
        window["update"] = (val, mqtt = null) => {
          this.updateProps(val);
        };
      },
      beforeDestory() {
        clearInterval(this.timer);
      },
      methods: {
        setTime() {
          clearInterval(this.timer);
          this.timer = setInterval(() => {
            this.date = new Date();
          }, 1000);
        },
        updateProps(props) {
          for (let prop of props) {
            let index = this.getPropIndex(prop.field);
            if (index !== -1) {
              let data = this.props[index];
              data.value = prop.value;
              this.$set(this.props, index, data);
            }
          }
        },
        getPropIndex(name) {
          for (let i = 0; i < this.props.length; i++) {
            if (this.props[i].field === name) {
              return i;
            }
          }
          return -1;
        },
        getWeekDay(index) {
          return weeks[index];
        },
        formatValue(val) {
          if (val < 10) {
            return "0" + val;
          }
          return val;
        }
      }
    });
  </script>
</html>
