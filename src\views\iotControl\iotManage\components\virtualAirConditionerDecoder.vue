<template>
  <span class="virtual-air-conditioner-decoder">
    <el-input v-model="displayName" type="text" placeholder="红外控令" @click.native.stop="toChangeFunctionCode" readonly :style="inputStyle" />
    <el-dialog :visible.sync="showModal" title="选择红外控令" width="680px" :close-on-click-modal="false" append-to-body>
      <el-form :label-width="'100px'" :model="formValue">
        <el-form-item label="模式" prop="selectMode" class="form-item">
          <el-select style="width: 180px" placeholder="请选择模式" v-model="formValue.selectMode" @change="handleUpdateModeValue(true)">
            <el-option v-for="item in modeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="风速" prop="selectSpeed" class="form-item">
          <el-select style="width: 180px" placeholder="请选择风速" v-model="formValue.selectSpeed">
            <el-option v-for="item in validSpeedOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="温度（℃）" prop="selectTemperature" class="form-item">
          <el-select style="width: 180px" placeholder="请选择温度" v-model="formValue.selectTemperature">
            <el-option v-for="item in validTemperatureOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button @click="handleConfirm" class="ml-10" type="primary">确定</el-button>
      </div>
    </el-dialog>
  </span>
</template>

<script>
  import driverReference from '@/assets/iot-device/rmt-driver-refer/virtual-air-conditioner.json';

  export default {
    name: 'VirtualAirConditionerDecoder',
    props: {
      //控制码
      functionCode: {
        type: Number,
        default: null,
      },
      // 是否禁用
      disabled: {
        type: Boolean,
        default: false,
      },
      // 样式
      inputStyle: {
        type: Object,
        default: () => ({}),
      },
      //是否自动弹出弹框
      autoShowModal: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        driverReference,
        //input框的值
        displayName: '',
        showModal: false,
        // 普通空调模式
        modeEnum: {},
        // 空调特殊功能
        specialFunctionEnum: {},
        //所有模式（包含特殊功能）
        modeOptions: [],
        // 空调风速
        fanSpeedEnum: {},
        speedOptions: [],
        validSpeedOptions: [],
        //空调温度
        temperatureOptions: [],
        validTemperatureOptions: [],
        // 所有的控令列表
        controlInstructions: [],
        validControlInstructions: [],
        formValue: {
          selectMode: null,
          selectSpeed: null,
          selectTemperature: null,
        },
      };
    },
    computed: {
      // 当前所选，包含value和name
      checkedInstructions() {
        if (typeof this.functionCode === 'number' && this.functionCode >= 0) {
          const detail = this.controlInstructions.find((item) => item.functionCode === this.functionCode);
          if (detail) {
            return detail;
          }
        }
        return {};
      },
    },
    watch: {
      functionCode: {
        handler(val) {
          console.log('functionCode', val);
          this.updateDisplayName();
        },
        immediate: true,
      },
    },
    created() {
      this.init(this.driverReference);
      this.showModal=this.autoShowModal;
    },
    methods: {
      toChangeFunctionCode() {
        if (this.disabled) {
          return;
        }
        this.formValue.selectMode = this.checkedInstructions.mode;
        this.formValue.selectSpeed = this.checkedInstructions.speed;
        this.formValue.selectTemperature = this.checkedInstructions.temperature;
        // 查看弹框时，更新风速和温度可选项
        this.handleUpdateModeValue(false);
        this.showModal = true;
      },
      handleCancel() {
        this.validSpeedOptions = [...this.speedOptions];
        this.validTemperatureOptions = [...this.temperatureOptions];
        this.formValue.selectTemperature = null;
        this.formValue.selectSpeed = null;
        this.formValue.selectMode = null;
        this.showModal = false;
      },
      handleConfirm() {
        const { selectTemperature, selectSpeed, selectMode } = this.formValue;
        if ([selectTemperature, selectSpeed, selectMode].includes(null)) {
          this.$message.error('请选择控制');
          return;
        }
        const checkedItem = this.validControlInstructions.find(
          (item) => item.mode === selectMode && item.speed === selectSpeed && item.temperature === selectTemperature
        );
        if (!checkedItem) {
          this.$message.error('无匹配的控令');
          return;
        }
        this.$emit('update:functionCode', checkedItem.functionCode);
        this.showModal = false;
      },
      formatDisplay(row) {
        if (row.functionCode === 0) {
          return {
            ...row,
            modeName: '关机',
            speedName: '/',
            temperature: '/',
          };
        } else if (row.functionCode === 1) {
          return {
            ...row,
            modeName: '开机',
            speedName: '/',
            temperature: '/',
          };
        } else {
          return {
            ...row,
            modeName: this.modeEnum[row.mode] || this.specialFunctionEnum[row.mode] || '/',
            speedName: this.fanSpeedEnum[row.speed] || '/',
          };
        }
      },
      mapEnum(arr, enumObj, type) {
        return (arr || []).map((item) => {
          enumObj[item[type]] = item.description;
          return { label: item.description, value: item[type] };
        });
      },
      init(res) {
        // 获取模式, 风速, 温度, controlInstructions枚举
        const modeArr = this.mapEnum(res?.mode?.value, this.modeEnum, 'mode');
        const specialFunctionArr = this.mapEnum(res?.specialFunction?.value, this.specialFunctionEnum, 'mode');
        const fanSpeedArr = this.mapEnum(res?.speed?.value, this.fanSpeedEnum, 'value');
        this.modeOptions = [...modeArr, ...specialFunctionArr];
        this.speedOptions = fanSpeedArr;
        this.temperatureOptions = [{ value: -1, label: '/' }];
        for (let i = 16; i <= 30; i++) {
          this.temperatureOptions.push({ label: String(i), value: i });
        }
        this.validSpeedOptions = [...this.speedOptions];
        this.validTemperatureOptions = [...this.temperatureOptions];
        this.controlInstructions = (res?.detail?.value || []).map(this.formatDisplay);
        this.validControlInstructions = this.controlInstructions.filter((item) => item.functionCode !== 0 && item.functionCode !== 1);

        // 根据 functionCode 更新 checkedInstructions(formValue, displayName)
        if (this.checkedInstructions && Object.keys(this.checkedInstructions).length > 0) {
          const { mode, speed, temperature, modeName, speedName } = this.checkedInstructions;
          this.formValue.selectMode = mode;
          this.formValue.selectSpeed = speed;
          this.formValue.selectTemperature = temperature;
          this.displayName = `${modeName} ${speedName && speedName !== '/' ? speedName : ''} ${
            temperature && temperature !== '/' && temperature !== -1 ? `${temperature}℃` : ''
          }`;
        }
      },
      /**
       * 更新显示的名称
       */
      updateDisplayName() {
        // 更新指令显示名称
        if (this.checkedInstructions && Object.keys(this.checkedInstructions).length > 0) {
          const { temperature, modeName, speedName } = this.checkedInstructions;
          this.displayName = `${modeName} ${speedName && speedName !== '/' ? speedName : ''} ${
            temperature && temperature !== '/' && temperature !== -1 ? `${temperature}℃` : ''
          }`;
        }
      },
      // 模式改变时，更新风速和温度可选项；查看弹框时，根据已选模式更新风速和温度可选项
      handleUpdateModeValue(ischangeMode) {
        if (ischangeMode) {
          this.formValue.selectSpeed = null;
          this.formValue.selectTemperature = null;
        }
        if (![null, '', undefined].includes(this.formValue.selectMode)) {
          let SetA = new Set();
          this.validControlInstructions.forEach((item) => {
            if (item.mode === this.formValue.selectMode) {
              SetA.add(item.speed);
            }
          });
          this.validSpeedOptions = this.speedOptions.filter((obj) => Array.from(SetA).includes(obj.value));
          let SetB = new Set();
          this.validControlInstructions.forEach((item) => {
            if (item.mode === this.formValue.selectMode) {
              SetB.add(item.temperature);
            }
          });
          this.validTemperatureOptions = this.temperatureOptions.filter((obj) => Array.from(SetB).includes(obj.value));
        }
        if (ischangeMode) {
          // 默认选
          if (this.validSpeedOptions.length === 1) {
            this.formValue.selectSpeed = this.validSpeedOptions[0].value;
          }

          if (this.validTemperatureOptions.length === 1) {
            this.formValue.selectTemperature = this.validTemperatureOptions[0].value;
          }
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .form-item {
    margin-bottom: 10px;
  }
  ::v-deep .el-dialog .el-dialog__header {
    line-height: 18px;
  }
  .param-form{
    height: 300px;
    width: 100%;
  }
</style>
