<template>
  <div :class="{ hidden: hidden }" class="pagination-container">
    <el-pagination
      v-bind="$attrs"
      :background="background"
      :current-page.sync="currentPage"
      :page-size.sync="pageSize"
      :layout="layout"
      :page-sizes="pageSizes"
      :total="total"
      :pager-count="5"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script>
  export default {
    name: 'Pagination',
    props: {
      total: {
        default: 0,
        type: Number,
      },
      page: {
        type: Number,
        default: 1,
      },
      limit: {
        type: Number,
        default: 20,
      },
      pageSizes: {
        type: Array,
        default() {
          return [20, 40, 60, 80, 100, 1000];
        },
      },
      layout: {
        type: String,
        default: 'total, sizes, prev, pager, next, jumper',
      },
      background: {
        type: Boolean,
        default: true,
      },
      autoScroll: {
        type: Boolean,
        default: true,
      },
      hidden: {
        type: <PERSON>olean,
        default: false,
      },
    },
    computed: {
      currentPage: {
        get() {
          return this.page;
        },
        set(val) {
          this.$emit('update:page', val);
        },
      },
      pageSize: {
        get() {
          return this.limit;
        },
        set(val) {
          this.$emit('update:limit', val);
        },
      },
    },
    methods: {
      handleSizeChange(val) {
        this.$emit('pagination', { page: this.currentPage, limit: val });

        try {
          // 列表分页后 el-table 自动滚动到最上方
          // 1、find table dom
          let dom = this.$parent.$el.querySelector('.el-table .el-table__body-wrapper');
          // scroll to top
          if (dom && this.autoScroll) {
            dom.scrollTo({
              top: 0,
              behavior: 'smooth',
            });
          }
        } catch (error) {
          console.log(error);
        }
      },
      handleCurrentChange(val) {
        // emit event
        this.$emit('pagination', { page: val, limit: this.pageSize });

        try {
          // 列表分页后 el-table 自动滚动到最上方
          // 1、find table dom
          let dom = this.$parent.$el.querySelector('.el-table .el-table__body-wrapper');
          // scroll to top
          if (dom && this.autoScroll) {
            dom.scrollTo({
              top: 0,
              behavior: 'smooth',
            });
          }
        } catch (error) {
          console.log(error);
        }
      },
    },
  };
</script>

<style scoped lang="scss">
  .pagination-container {
    background: #fff;
    margin-top: 0;
    border-top: 1px solid rgba(238, 238, 238, 0.644);

    .el-pagination {
      // padding: 20px 5px;
      padding: 10px 5px;
    }

    &.hidden {
      display: none;
    }
  }

  .el-dialog {
    .el-pagination {
      padding: 5px;
    }
  }
</style>
