<template>
  <div class="today-overview-wrap">
    <div class="title-wrap"> 今日数据 </div>
    <div class="overview-wrap">
      <div v-for="item in overview" :key="item.title" class="overview">
        <div class="icon-wrap">
          <svg-icon :icon-class="item.icon" />
        </div>
        <div class="right-wrap">
          <div class="title">{{ item.title }}</div>
          <div class="value">{{ item.value }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    data() {
      return {
        overview: [
          {
            value: 50,
            title: '在线终端',
            icon: 'dssDataBoard@terminal',
          },
          {
            value: 20,
            title: '节目单下发',
            icon: 'dssDataBoard@programList',
          },
          {
            value: 5,
            title: '新增素材',
            icon: 'dssDataBoard@material',
          },
          {
            value: 2,
            title: '新增节目',
            icon: 'dssDataBoard@program',
          },
        ],
      };
    },
  };
</script>

<style lang="scss" scoped>
  .today-overview-wrap {
    background: url(../img/box-left-bg.png) no-repeat;
    box-sizing: border-box;
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    .title-wrap {
      font-family: 'YouSheBiaoTiHei';
      font-size: 18px;
      padding: 15px 0 10px 20px;
    }

    .overview-wrap {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      align-items: center;
      .overview {
        width: 50%;
        padding: 20px 0;
        display: flex;
        justify-content: center;
        align-items: center;
        .icon-wrap {
          width: 70px;
          height: 70px;
          margin-right: 10px;
          background: url(../img/today-overview-bg.png) no-repeat;
          background-size: 100% 100%;
          display: flex;
          justify-content: center;
          font-size: 30px;
          color: rgb(63, 170, 255);
          padding-top: 17px;
        }
        .right-wrap {
          .title {
            font-size: 14px;
            color: rgb(217, 217, 217);
            margin-bottom: 10px;
          }
          .value {
            font-size: 28px;
            color: rgb(255, 255, 255);
          }
        }
      }
    }
  }
</style>
