import request from "@/utils/request";
import $qs from "qs";

/**
 * 获取发信箱配置列表
 * @param companyToSearch 根据公司名称搜索
 * @return {AxiosPromise}
 */
export function getMailConfig(companyToSearch) {
  return request({
    url:
      "oauth/mail/mailConfig" + (companyToSearch ? "/" + companyToSearch : ""),
    method: "get",
  });
}

/**
 * 发送测试邮件
 * @param companyId 公司ID
 * @param to 收信箱
 * @return {AxiosPromise}
 */
export function sendEmailTest(companyId, to) {
  return request({
    url: "oauth/mail/mailConfig/" + companyId + "/" + to,
    method: "get",
  });
}

/**
 * 保存发信箱配置
 * @param data 发信箱配置
 * @return {AxiosPromise}
 */
export function saveEmailConfig(data) {
  return request({
    url: "oauth/mail/mailConfig",
    method: "post",
    headers: { "Content-Type": "application/json" },
    data: data,
  });
}

/**
 * 删除发信箱配置
 * @param id 发信箱配置ID
 * @return {AxiosPromise}
 */
export function deleteEmailConfig(id) {
  return request({
    url: "oauth/mail/mailConfig/" + id,
    method: "delete",
  });
}
