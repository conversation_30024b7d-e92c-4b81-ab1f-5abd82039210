import request from '@/utils/request';
import $qs from 'qs';

//获取终端列表(包括睿景终端与ideahub)
export function getCandidateTerminal(data) {
  return request({
    url: '/webapp/terminal/getCandidateTerminal',
    method: 'get',
    params: data,
  });
}

//获取终端列表(根据机构名称查询)
export function getTerminalList(data) {
  return request({
    url: '/webapp/terminal/getTerminalCfgLst',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//获取终端列表(根据机构id查询)
export function getTerminalListV2(data) {
  return request({
    url: '/webapp/terminal/getTerminalCfgLst2',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//获取单个终端信息
export function getTerminal(data) {
  return request({
    url: '/webapp/terminal/getTerminalCfg',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//设置单个终端信息
export function setTerminal(data) {
  return request({
    url: '/webapp/terminal/saveTerminalConfig',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//删除终端
export function deleteTerminal(data) {
  return request({
    url: '/webapp/terminal/delTerminal',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//上传终端注册信息
export function uploadTerminal(data) {
  let file = new FormData();
  file.append('file', data);
  return request({
    url: '/webapp/terminal/importTerminal',
    method: 'post',
    data: file,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

//批量设置终端定时开关机
export function setTerminalPower(data) {
  return request({
    url: '/webapp/terminal/updatePower',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//批量设置终端定时重启
export function setTerminalReboot(data) {
  return request({
    url: '/webapp/terminal/reboot',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//批量设置终端音量
export function setTerminalVol(data) {
  return request({
    url: '/webapp/terminal/updateVol',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//批量设置终端亮度
export function setTerminalBrightness(data) {
  return request({
    url: '/webapp/terminal/setBrightness',
    method: 'post',
    // headers: {
    //   'Content-Type': 'application/json;',
    // },
    // data,
    data: $qs.stringify(data),
  });
}

//批量设置终端旋转度
export function setTerminalRotation(data) {
  return request({
    url: '/webapp/terminal/updateRotation',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//批量设置终端媒体服务器
export function setTerminalMedia(data) {
  return request({
    url: '/webapp/terminal/setMedia',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//批量设置终端管理服务器
export function setTerminalServer(data) {
  return request({
    url: '/webapp/terminal/setServer',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//批量设置终端语言
export function setTerminalLanguage(data) {
  return request({
    url: '/webapp/terminal/setLanguage',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//批量设置终端日志级别
export function setTerminalLogLevel(data) {
  return request({
    url: '/webapp/terminal/logLevel',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//批量设置终端密码
export function setTerminalPwd(data) {
  return request({
    url: '/webapp/terminal/webPwd',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//批量设置终端NTP服务器
export function setTerminalNtp(data) {
  return request({
    url: '/webapp/terminal/ntp',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//批量设置终端HDMI
export function setTerminalHdmi(data) {
  return request({
    url: '/webapp/terminal/updateHdmi',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//批量获取终端状态
export function getTerminalStatus(data) {
  return request({
    url: '/webapp/terminal/getStatus',
    method: 'post',
    data: $qs.stringify(data),
  });
}

// 批量设置终端机构
export function setTerminalDept(data) {
  return request({
    url: '/webapp/terminal/setDept',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//批量设置终端城市
export function setTerminalCity(data) {
  return request({
    url: '/webapp/terminal/setCity',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//批量设置终端授权码
export function setTerminalLicenseKey(data) {
  return request({
    url: '/webapp/terminal/setlic',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//获取所有媒体服务器
export function getMediaList(data) {
  return request({
    url: '/webapp/media/getAll',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//设置终端管理-终端监控列表显示的表头
export function setUserColumn(data) {
  return request({
    url: '/webapp/terminal/setUserColumn',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//获取终端管理-终端监控列表显示的表头
export function getUserColumn(data) {
  return request({
    url: '/webapp/terminal/getUserColumn',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//向终端发送命令
/*
 *   终端控制命令
 *   terminalIds  ----  批量操作的终端id，逗号隔开
 *   cmd          ----  操作命令
 *          1     ----  休眠
 *          2     ----  激活
 *          3     ----  重启
 *          6     ----  显示信息
 *          11    ----  清空节目
 *          42    ----  取消下载
 *          46    ----  同步时间
 *          65    ----  挂断
 *          111   ----  删除实时信息
 *          10    ----  清空日志
 *          12    ----  开启终端通讯抓包
 *          13    ----  停止终端通讯抓包
 *          14    ----  开启终端音频抓包
 *          15    ----  停止终端音频抓包
 * */
export function setCmd(data) {
  return request({
    url: '/webapp/terminal/setCmd',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//删除节目单
export function deletePlayList(data) {
  return request({
    url: '/webapp/terminal/delPlaylist',
    method: 'post',
    data: $qs.stringify(data),
  });
}

/*
 *   电视机操作
 *   terminalIds  ----  批量操作的终端id，逗号隔开
 *   cmd          ----  操作命令
 *    startTv     ----  开机
 *    stopTv      ----  关机
 *    muteTv      ----  静音
 *    volTv       ----  音量
 *    switchTv    ----  视频源切换
 *    screenTv    ----  画面比率
 *    tvinfo      ----  电视机状态
 * */
// export function operateTv(data) {
//     return request({
//         url: '/control/tvControl.action',
//         method: 'post',
//         data: $qs.stringify(data)
//     })
// }

// 呼叫批量配置
export function operateTv(url, data) {
  return request({
    url: `/webapp/terminal/control/${url}.action`,
    method: 'post',
    data: $qs.stringify(data),
  });
}

// 获得内核列表
export function getCoreList(data) {
  return request({
    url: '/media/upgrade/getCore',
    method: 'post',
    data: $qs.stringify(data),
  });
}

// 获得其他公司内核列表
export function getOtherCoreList(data) {
  return request({
    url: '/media/upgrade/getCore/other',
    method: 'post',
    data: $qs.stringify(data),
  });
}

// 获得内核版本列表
export function getCoreVersionList(data) {
  return request({
    url: '/media/upgrade/getCoreVersions',
    method: 'post',
    data: $qs.stringify(data),
  });
}

// 获得其他公司内核版本列表
export function getOtherCoreVersionList(data) {
  return request({
    url: '/media/upgrade/getCoreVersions/other',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//升级内核版本
export function upgradeCoreVersion(data) {
  return request({
    url: '/webapp/terminal/publishUpgradePackage',
    method: 'post',
    data: $qs.stringify(data),
  });
}

// 删除内核
export function deleteCoreVersion(data) {
  return request({
    url: '/media/upgrade/delete',
    method: 'post',
    data: $qs.stringify(data),
  });
}

// 获得内核/应用历史
export function getCoreHistoryList(data) {
  return request({
    url: '/webapp/terminal/getUpgradeHisLst',
    method: 'post',
    data: $qs.stringify(data),
  });
}

// 升级内核/应用
export function updateCore(data) {
  return request({
    url: '/webapp/terminal/publishUpgradePackage',
    method: 'post',
    data: $qs.stringify(data),
  });
}

// 取消下载内核/应用
export function cancelDownload(data) {
  return request({
    url: '/media/upgrade/cancelDownload',
    method: 'post',
    data: $qs.stringify(data),
  });
}

// 获得应用列表
export function getApkList(data) {
  return request({
    url: '/media/upgrade/getAllApks',
    method: 'post',
    data: $qs.stringify(data),
  });
}

// 获得其他公司应用列表
export function getOtherApkList(data) {
  return request({
    url: '/media/upgrade/getAllApks/other',
    method: 'post',
    data: $qs.stringify(data),
  });
}

// 获得终端已安装的应用列表
export function getInstalledApkList(data) {
  return request({
    url: '/webapp/terminal/getInstalledApk',
    method: 'post',
    data: $qs.stringify(data),
  });
}

// 获得当前应用安装的所有终端
export function getApkInstalledList(data) {
  return request({
    url: '/webapp/terminal/getApkInstalled',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//卸载应用列表
export function uninstallApkList(data) {
  return request({
    url: '/webapp/terminal/uninstallApk',
    method: 'post',
    data: $qs.stringify(data),
  });
}

// 获得应用版本列表
export function getApkVersionList(data) {
  return request({
    url: '/media/upgrade/getApkVersions',
    method: 'post',
    data: $qs.stringify(data),
  });
}

// 获得其他公司应用版本列表
export function getOtherApkVersionList(data) {
  return request({
    url: '/media/upgrade/getApkVersions/other',
    method: 'post',
    data: $qs.stringify(data),
  });
}

// 删除应用
export function deleteApk(data) {
  return request({
    url: '/webapp/media/upgrade/delete',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//启动应用
export function startApk(data) {
  return request({
    url: '/webapp/terminal/startApk',
    method: 'post',
    data: $qs.stringify(data),
  });
}

/**
 * 停止应用
 */
export function stopApk(data) {
  return request({
    url: '/webapp/terminal/stopApk',
    method: 'post',
    data: $qs.stringify(data),
  });
}

// 是否可以上传
export function canUpload() {
  return request({
    url: '/webapp/terminal/canUploadUpgradeInfo',
    method: 'post',
  });
}

//上传内核
export function uploadApk(data, onUploadProgress) {
  let file = new FormData();
  file.append('file', data.file);
  file.append('desc', data.desc);
  file.append('type', data.type);
  file.append('auth', data.auth);
  data.companyIds && file.append('companyIds', data.companyIds);
  return request({
    url: '/media/upgrade/save',
    method: 'post',
    data: file,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    onUploadProgress,
  });
}

//编辑内核
export function editApk(data) {
  return request({
    url: '/media/upgrade/edit',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//获得视频会议终端列表
export function getVcTerminalList(data) {
  return request({
    url: '/webapp/vc/getVcTerminalLst',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//挂断视频会议
export function handVc(data) {
  return request({
    url: '/webapp/vc/handUp',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//
export function startVcAuxiliary(data) {
  return request({
    url: '/webapp/vc/startAuxiliary',
    method: 'post',
    data: $qs.stringify(data),
  });
}

export function stopVcAuxiliary(data) {
  return request({
    url: '/webapp/vc/stopAuxiliary',
    method: 'post',
    data: $qs.stringify(data),
  });
}

export function getTerminalGroup(data) {
  return request({
    url: '/webapp/terminalGroup',
    method: 'get',
    params: data,
  });
}

/**
 * 获取终端组中的终端列表
 */
export function getGroupTerminals(data) {
  return request({
    url: '/webapp/terminalGroup/getTerminals',
    method: 'get',
    params: data,
  });
}

export function addTerminalGroup(data) {
  return request({
    url: '/webapp/terminalGroup',
    method: 'put',
    data: data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

export function deleteTerminalGroup(data) {
  return request({
    url: '/webapp/terminalGroup',
    method: 'delete',
    params: data,
  });
}

export function editTerminalGroup(data) {
  return request({
    url: '/webapp/terminalGroup',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

export function publishPlaylistWithTerminalGroup(data) {
  return request({
    url: '/webapp/terminalGroup/publishPlaylist',
    method: 'get',
    params: data,
  });
}

//向终端下发截屏命令
export function startProgramCapture(data) {
  return request({
    url: '/webapp/terminal/startScreenshot',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//获取节目截屏
/*{
    macs: [Array]
    type: 0 节目截屏
          1 摄像头快照
}*/
export function getProgramCapture(data) {
  return request({
    url: 'noauth/getCapture',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//清除节目截屏数据
export function clearCapture(data) {
  return request({
    url: '/webapp/terminal/clearImages',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//发送功能键
export function setKey(macs, keyname) {
  return request({
    url: `/webapp/terminal/functionkey?macs=${macs}&keyname=${keyname}`,
    method: 'get',
  });
}

//审核终端信息
export function verifyTerminal(data) {
  return request({
    url: '/webapp/terminal/updtTerminal?approved=1',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//更新终端信息
export function updateTerminal(data) {
  return request({
    url: '/webapp/terminal/updtTerminal',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//获取终端操作历史
export function getTerminalOpHistory(data) {
  return request({
    url: '/webapp/terminal/getOpHistoryList',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//收集日志
export function fetchLog(data) {
  return request({
    url: '/webapp/log/fetch',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//获取终端日志列表
export function getTerminalLog(mac) {
  return request({
    url: `/webapp/log?mac=${mac}`,
    method: 'get',
  });
}

//删除终端日志列表
export function deleteTerminalLog(data) {
  return request({
    url: `webapp/log`,
    method: 'delete',
    params: data,
  });
}

//清空终端日志列表
export function clearTerminalLog(data) {
  return request({
    url: `webapp/log/clear`,
    method: 'delete',
    params: data,
  });
}

/**
 * 获取终端日志列表
 * @param macs 终端mac列表
 * @param keyName 按键名称
 * @param keyCode 按键的值
 */
export function setTerminalRemoteControl(data) {
  return request({
    url: `/webapp/terminal/remoteControl`,
    method: 'get',
    params: data,
  });
}

/**
 * ideahub回到主页
 * @param {*} data
 * @returns
 */
export function backToHome(data) {
  return request({
    url: '/webapp/terminal/backToHome',
    method: 'post',
    data: $qs.stringify(data),
  });
}

/**
 * 查询指定终端的MQTT通信数据
 * @param {*} mac
 * @returns
 */
export function getTerminalEmqxProfile(mac) {
  return request({
    url: `/webapp/terminal/emqx/${mac}/profile`,
    method: 'get',
  });
}

/**
 * 查询接收数据量的时序值
 * @param {*} mac
 * @param {*} data-clientid MQTT客户端ID
 * @param {*} data-from 开始时间戳，为空时返回最近的100条数据
 * @param {*} data-to 结束时间戳，为空时返回最近的100条数据
 * @returns
 */
export function getTerminalEmqxRecvMsg(mac, data) {
  return request({
    url: `/webapp/terminal/emqx/${mac}/sendMsg`,
    method: 'get',
    params: data
  });
}

/**
 * 查询发送数据量的时序值
 * @param {*} mac
 * @param {*} data-clientid MQTT客户端ID
 * @param {*} data-from 开始时间戳，为空时返回最近的100条数据
 * @param {*} data-to 结束时间戳，为空时返回最近的100条数据

 * @returns
 */
export function getTerminalEmqxSendMsg(mac, data) {
  return request({
    url: `/webapp/terminal/emqx/${mac}/recvMsg`,
    method: 'get',
    params: data
  });
}

/**
 * 清空终端保留消息
 * @param {*} mac
 * @returns
 */
export function clearTerminalEmqxRetainers(mac) {
  return request({
    url: `/webapp/terminal/emqx/${mac}/retainers`,
    method: 'delete',
  });
}
