<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Document</title>
  <link rel="stylesheet" href="40e93b11cbd4174c60b22cba0d630300.css" />
  <style>
    title,
    style,
    script {
      display: none;
    }

    body,
    html {
      height: 100%;
      margin: 0;
    }

    body {
      background-color: transparent;
    }

    .wrap {
      width: 100%;
      height: 100%;
      text-align: center;
    }

    .flex-wrap{
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }

    .wrap .time {
      font-size: 36vw;
    }

    .wrap .day {
      font-size: 7vw;
    }

    [v-cloak] {
      display: none;
    }
  </style>
  <script src="174365807c06490c848d7b1d45fdc348.js"></script>
  <script src="page.js"></script>
  </head>

<body>
  <div id="app" class="wrap" v-cloak>
    <div :style="style" class="wrap flex-wrap">
      <div class="time">{{time}}</div>
      <div class="day">{{day}}</div>
    </div>
    </div>
    </body>

<script>
  window.$page = page;
  const cssProperty = ["height", "width", "fontSize"];
  const weeks = [
    "星期日",
    "星期一",
    "星期二",
    "星期三",
    "星期四",
    "星期五",
    "星期六"
  ];

    const app = new Vue({
      el: "#app",
      data: {
        date: new Date(),
        props: page.props,
        timer: null
      },
      computed: {
        style() {
          let style = {};
          for (let prop of this.props) {
            let unit = cssProperty.includes(prop.field) ? "px" : "";
            style[prop.field] = prop.value + unit;
          }
          console.log(style);
          return style;
        },
        time() {
          return (
            this.formatValue(this.date.getHours()) +
            ":" +
            this.formatValue(this.date.getMinutes())
          );
        },
        day() {
          return (
            this.formatValue(this.date.getMonth() + 1) +
            "月" +
            this.formatValue(this.date.getDate()) +
            "日 " +
            this.getWeekDay(this.date.getDay())
          );
        }
      },
      created() {
        this.setTime();
      },
      mounted() {
        window["update"] = (val, mqtt = null) => {
          this.updateProps(val);
        };
      },
      beforeDestory() {
        clearInterval(this.timer);
      },
      methods: {
        setTime() {
          clearInterval(this.timer);
          this.timer = setInterval(() => {
            this.date = new Date();
          }, 1000);
        },
        updateProps(props) {
          for (let prop of props) {
            let index = this.getPropIndex(prop.field);
            if (index !== -1) {
              let data = this.props[index];
              data.value = prop.value;
              this.$set(this.props, index, data);
            }
          }
        },
        getPropIndex(name) {
          for (let i = 0; i < this.props.length; i++) {
            if (this.props[i].field === name) {
              return i;
            }
          }
          return -1;
        },
        getWeekDay(index) {
          return weeks[index];
        },
        formatValue(val) {
          if (val < 10) {
            return "0" + val;
          }
          return val;
        }
      }
    });
  </script>

</html>