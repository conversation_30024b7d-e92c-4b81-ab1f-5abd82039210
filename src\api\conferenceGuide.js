import request from '@/utils/request';
import $qs from 'qs';
import axios from 'axios';
import { getAccessToken } from '@/utils/auth';

//获取资源列表
export function getInfrastructure(data) {
  return request({
    url: '/weapp/equipment/getAllEquipments',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//删除资源
export function deleteInfrastructure(data) {
  return request({
    url: '/weapp/equipment/delEquipment',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//添加/更新资源列表
export function addInfrastructure(data) {
  return request({
    url: '/weapp/equipment/saveEquipment',
    method: 'post',
    data: $qs.stringify(data),
  });
}

/**
 * 标签绑定会议室
 * @param {*} data
 * @returns
 */
export function infrastructureBindRoom(data) {
  return request({
    url: '/weapp/equipment/bindRoom',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//获取会议室列表
export function getConferenceRoomList(data) {
  return request({
    url: '/weapp/getIdleRoomLst.json',
    method: 'get',
    params: data,
  });
}

//删除会议室列表
export function deleteConferenceRoom(data) {
  return request({
    url: '/weapp/delMeetingroom',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//会议指引------添加/更新会议室列表
export function addConferenceRoom(data) {
  let file = new FormData();
  Object.keys(data).forEach((key) => {
    data[key] && file.append(key, data[key]);
  });

  return request({
    url: '/weapp/saveMeetingroom',
    method: 'post',
    data: file,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

/**
 * 会议指引------获取会议室列表
 * @param page 页码
 * @param size 每页大小
 * @param nameToSearch 会议室名称
 * @param deptId 机构id
 * @param includeChild 是否包含下级
 * @param auth 权限（机构会议室/公共会议室）
 * @param roomtype 会议室名称
 * @param direction 升序ASC/降序DESC
 * @param property 排序属性（roomName）
 */
export function getMeetingRoomList(data) {
  data.direction = data.direction || 'DESC';
  data.property = data.property || 'roomName';

  return request({
    url: `/weapp/room`,
    method: 'get',
    params: data,
  });
}

/**
 * 会议指引------获取会议室列表的会议详情
 * @param page 页码
 * @param size 每页大小
 * @param nameToSearch 会议室名称
 * @param start 开始时间
 * @param deptId 机构id
 * @param includeChild 是否包含下级
 * @param capacity 容纳人数
 * @param equipment 设备
 */
export function getMeetingRoomAndConf(data) {
  return request({
    url: `/weapp/room/conf`,
    method: 'get',
    params: data,
  });
}

/**
 * 获取会议室当前状态
 * @param {*} roomId
 * @returns
 */
export function getRoomStatus(roomId) {
  return request({
    url: `weapp/room/status?roomId=${roomId}`,
    method: 'get',
  });
}

/**
 * 会议室终端状态
 * @param {*} id
 * @returns
 */
export function getTerminalStatus(id) {
  return request({
    url: `/weapp/videoterminal/status?id=${id}`,
    method: 'get',
  });
}

//会议指引------获取会议室详情
export function getMeetingRoom(id) {
  return request({
    url: `/weapp/room/find?id=${id}`,
    method: 'get',
  });
}

//会管-----添加/更新会议室
export function addMeetingRoom(data) {
  let file = new FormData();
  Object.keys(data).forEach((key) => {
    data[key] && file.append(key, data[key]);
  });

  return request({
    url: '/weapp/room',
    method: 'post',
    data: file,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

//把指定会议室设为所在机构的默认会议室
export function setRoomAsDeptDefault(id) {
  return request({
    url: '/weapp/asDefault?roomId=' + id,
    method: 'get',
  });
}

//取消指定会议室为所在机构的默认会议室
export function cancelRoomAsDeptDefault(id) {
  return request({
    url: '/weapp/cancelDefault?roomId=' + id,
    method: 'get',
  });
}

//获取未绑定会议室的终端列表
export function getUnBindTerminal(data) {
  return request({
    url: '/weapp/findUnBindTerminal',
    method: 'get',
    params: data,
  });
}

//获取人脸数据列表
export function getFaceList(data) {
  return request({
    url: '/webapp/faceManager/faceData',
    method: 'get',
    params: data,
  });
}

//上传人脸数据
export function uploadFace(data) {
  // let file = new FormData();
  // file.append("file", data.file);
  return request({
    url: '/webapp/faceManager/upload',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

/**
 * 批量上传/更新游客人脸数据
 * @param formData formData格式数据
 * @param expired 有效期
 * @param type 0 新建 1 更新
 * @param files 人脸数据图片
 * @param libId 人脸库id
 */
export function uploadGuestFace(data) {
  return request({
    url: '/webapp/faceManager/upload/guest',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

/**
 * 批量设置人脸有效期
 * @param expired 有效期
 * @param libId 人脸库id
 */
export function editFaceExpired(data) {
  return request({
    url: '/webapp/faceManager/expired',
    method: 'get',
    params: data,
  });
}

//上传带用户名参数的人脸数据
export function uploadUsernameFace(data, onUploadProgress) {
  let file = new FormData();
  Object.keys(data).forEach((key) => {
    file.append(key, data[key]);
  });
  return request({
    url: '/webapp/faceManager/upload/picture',
    method: 'post',
    data: file,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    onUploadProgress,
  });
}

//删除人脸数据
export function deleteFace(data) {
  return request({
    url: '/webapp/faceManager/faceData',
    method: 'delete',
    data: $qs.stringify(data),
  });
}

//向终端推送人脸库
export function pushAllFace(data) {
  return request({
    url: '/webapp/faceManager/pushAll',
    method: 'get',
    params: data,
  });
}

//向终端推送人脸库
export function pushFace(data) {
  return request({
    url: '/webapp/faceManager/pushOne',
    method: 'get',
    params: data,
  });
}

//向终端推送选定人脸
export function pushSelectedFace(data) {
  return request({
    url: '/webapp/faceManager/push',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

//向终端发送清空人脸库命令
export function clearTerminalFace(data) {
  return request({
    url: '/webapp/faceManager/empty',
    method: 'get',
    params: data,
  });
}

//查看人脸数据
export function getUserFace(username) {
  return request({
    url: '/webapp/faceManager/faceData/' + username,
    method: 'get',
  });
}

/**
 * 查询用户的人脸数据发布到的终端
 * @param username 用户名
 */
export function getFaceSyncInfo(data) {
  return request({
    url: '/webapp/faceManager/terminalFace/username',
    method: 'get',
    params: data,
  });
}

/**
 * 删除终端上人脸数据
 */
export function delTerminalFace(data) {
  return request({
    url: `/webapp/faceManager/terminalFace`,
    method: 'delete',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

/**
 * 查询终端上的人脸数据
 * @param mac 终端mac
 */
export function getTerminalFaceInfo(data) {
  return request({
    url: '/webapp/faceManager/terminalFace/mac',
    method: 'get',
    params: data,
  });
}

/**
 * 查询考勤打卡信息
 * @param name 用户名
 * @param deptId 部门id
 * @param includeChild 是否包含下级部门
 * @param start 开始时间
 * @param end 结束时间
 * @param twice 是否只查询最早最晚的两次
 */
export function getIdRecord(data) {
  return request({
    url: '/weapp/punch',
    method: 'get',
    params: data,
  });
}

/**
 * 查询识别记录的导出模版
 */
export function getIdRecordTemplate(data) {
  return request({
    url: '/weapp/punch/template',
    method: 'get',
    params: data,
  });
}

/**
 * 删除识别记录的导出模版
 */
export function delIdRecordTemplate(id) {
  return request({
    url: `/weapp/punch/template?id=${id}`,
    method: 'delete',
  });
}

/**
 * 添加识别记录的导出模版
 */
export function addIdRecordTemplate(data) {
  return request({
    url: '/weapp/punch/template',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

// 预约会议
export function addConference(data) {
  return request({
    url: '/weapp/saveConf',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

/**
 * 会议预置操作的预处理校验
 * @param {*} data
 * @returns
 */
export function preProcessConference(data) {
  return request({
    url: '/weapp/conference/preprocess',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

// 预约快速会议
export function fastConf(data) {
  return request({
    url: '/weapp/fastConf',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

/**
 * 会议是否开启
 * @param {*} id
 */
export function isConvokeSuccess(id) {
  return request({
    url: `/weapp/conference/control/isConvokeSuccess?conferenceId=${id}`,
    method: 'get',
  });
}

/**
 * 查询与自己相关的会议列表
 * @param page
 * @param rows
 * @param start 开始时间
 * @param end 结束时间
 * @param type 状态（CONFERENCE_STATUS）
 */
export function getMyConferences(data) {
  return request({
    url: '/weapp/getConfList.json',
    method: 'get',
    params: data,
  });
}

/**
 * 查询自己管理的会议室相关的会议列表
 * @param {*} data
 * @returns
 */
export function getMyRoomConferences(data) {
  return request({
    url: '/weapp/conf/list/room',
    method: 'get',
    params: data,
  });
}

/**
 * 获取我管理的会议室列表
 * @returns
 */
export function getMyRoom() {
  return request({
    url: '/weapp/room/my/manager',
    method: 'get',
  });
}

// 查询本公司的所有会议列表
export function getAllConferences(data) {
  return request({
    url: `/weapp/conference/all`,
    method: 'get',
    params: data,
  });
}

// 添加查询部门及子部门会议的接口 查询不到上级的会议
export function getDeptConferences(data) {
  return request({
    url: `/weapp/conference/dept`,
    method: 'get',
    params: data,
  });
}

// 查询与会人员列表
export function getParticipants(data) {
  return request({
    url: '/weapp/getParticipants.json',
    method: 'get',
    params: data,
  });
}

// 查询指定会议的详细信息
export function getConfDetail(data) {
  return request({
    url: '/weapp/getConfDetail.json',
    method: 'get',
    params: data,
  });
}

// 取消会议
export function delConf(data) {
  return request({
    url: '/weapp/delConf',
    method: 'get',
    params: data,
  });
}

// 延长正在召开的会议
export function delayConf(data) {
  return request({
    url: '/weapp/delayConf',
    method: 'get',
    params: data,
    headers: {
      noAlert: true,
    },
  });
}

// 提前结束正在召开的会议
export function stopConf(data) {
  return request({
    url: '/weapp/stopConf',
    method: 'get',
    params: data,
  });
}

// 会议室报修
export function addRepair(formData) {
  return request({
    url: '/weapp/repair/addRepair',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

// 查询与本人相关的报修
export function getMyRepair(data) {
  return request({
    url: '/weapp/repair/getMyRepair',
    method: 'get',
    params: data,
  });
}

// 查询本公司所有会议室报修
export function getAllRepair(data) {
  return request({
    url: '/weapp/repair/getAllRepair',
    method: 'get',
    params: data,
  });
}

// 处理会议室报修
export function edtRepair(data) {
  return request({
    url: '/weapp/repair/edtRepair',
    method: 'get',
    params: data,
  });
}

// 会议提交
export function commitConference(conferenceId) {
  return request({
    url: 'weapp/conference/commit/' + conferenceId,
    method: 'get',
  });
}

// 会议撤回
export function rollbackConference(conferenceId) {
  return request({
    url: 'weapp/conference/rollback/' + conferenceId,
    method: 'get',
  });
}

export function getLoopConferenceDetail(page, size, loopId) {
  return request({
    url: 'weapp/conference/loopDetail/' + page + '/' + size + '/' + loopId,
    method: 'get',
  });
}

//获取第三方会管系统列表
export function getThirdConfServer() {
  return request({
    url: '/weapp/dataSync',
    method: 'get',
  });
}

export function deleteThirdConfServer(ids) {
  return request({
    url: `/weapp/dataSync?ids=${ids}`,
    method: 'delete',
  });
}

//获取第三方会管类型列表
export function getThirdConfType() {
  return request({
    url: '/weapp/dataSync/apiType',
    method: 'get',
  });
}

//更新第三方会管系统
export function updateThirdConfServer(data) {
  return request({
    url: '/weapp/dataSync',
    method: 'put',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

//同步第三方会管数据
export function syncThirdConfServer(id) {
  return request({
    url: `/weapp/dataSync/liankun/call?id=${id}`,
    method: 'get',
  });
}

//停止同步第三方会管数据
export function shutdownThirdConfServer(id) {
  return request({
    url: `/weapp/dataSync/liankun/shutdown?id=${id}`,
    method: 'get',
  });
}

//获取服务区列表
export function getServiceAreaList() {
  return request({
    url: '/weapp/coverage',
    method: 'get',
  });
}

//获取服务区是否可用
export function getServiceAvailable(id) {
  return request({
    url: `/weapp/coverage/available?id=${id}`,
    method: 'get',
  });
}

//删除服务区
export function deleteServiceArea(ids) {
  return request({
    url: '/weapp/coverage',
    method: 'delete',
    data: $qs.stringify(ids),
  });
}

//添加服务区
export function addServiceArea(ids) {
  return request({
    url: '/weapp/coverage',
    method: 'post',
    data: $qs.stringify(ids),
  });
}

//编辑服务区
export function updateServiceArea(ids) {
  return request({
    url: '/weapp/coverage',
    method: 'put',
    data: $qs.stringify(ids),
  });
}

/**
 * 根据服务区id查询服务区是哪些机构的默认服务区
 * @param id 服务区id
 */
export function getDeptDefaultService(id) {
  return request({
    url: `/weapp/coverage/dept?coverageId=${id}`,
    method: 'get',
  });
}

//获取服务区中的视频终端
export function getServiceTerminal(id) {
  return request({
    url: `/weapp/coverage/terminal?coverageId=${id}`,
    method: 'get',
  });
}

/**
 * 导出视频终端模板
 * @param id    服务区id
 * @param bData 是否导出终端数据
 */
export function exportServiceTerminal(id, bData) {
  return axios({
    baseURL: process.env.VUE_APP_BASE_API,
    method: 'post',
    url: `/weapp/coverage/terminal/export?coverageId=${id}&hasData=${bData}`,
    responseType: 'blob',
    headers: {
      Authorization: 'Bearer ' + getAccessToken(),
      'Content-Type': 'application/json',
    },
  });
}

/**
 * 服务区导入终端
 * @param id    服务区id
 * @param file  文件
 */
export function uploadServiceTerminal(data, onUploadProgress) {
  let file = new FormData();
  Object.keys(data).forEach((key) => {
    data[key] && file.append(key, data[key]);
  });

  return request({
    url: `/weapp/coverage/terminal/import`,
    method: 'post',
    data: file,
    onUploadProgress,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

//获取服务区中的平台信息
export function getServiceMcu(id) {
  return request({
    url: `/weapp/coverage/mcu?coverageId=${id}`,
    method: 'get',
  });
}

//获取职场列表
export function getWorkplaceList(page, size, nameToSearch = '') {
  return request({
    url: `/weapp/workplace?page=${page}&size=${size}&nameToSearch=${nameToSearch}`,
    method: 'get',
  });
}

//删除职场列表
export function deleteWorkplace(ids) {
  return request({
    url: `/weapp/workplace`,
    method: 'delete',
    data: $qs.stringify(ids),
  });
}

//编辑职场列表
export function updateWorkplace(data) {
  return request({
    url: `/weapp/workplace`,
    method: 'put',
    data: $qs.stringify(data),
  });
}

//添加职场
export function addWorkplace(data) {
  return request({
    url: `/weapp/workplace`,
    method: 'post',
    data: $qs.stringify(data),
  });
}

//获取会议参数模板列表
export function getConferenceParamTemplateList(page, size, direction = 'DESC', property = 'name', nameToSearch = '') {
  return request({
    url: `/weapp/mcuTemplate/findAll?page=${page}&size=${size}&direction=${direction}&property=${property}&nameToSearch=${nameToSearch}`,
    method: 'get',
  });
}

//获取会议参数模板详情
export function getConferenceParamTemplate(id) {
  return request({
    url: `/weapp/mcuTemplate?id=${id}`,
    method: 'get',
  });
}

//删除会议参数模板
export function deleteConferenceParamTemplate(ids) {
  return request({
    url: '/weapp/mcuTemplate',
    method: 'delete',
    data: $qs.stringify(ids),
  });
}

//添加会议参数模板
export function addConferenceParamTemplate(data) {
  return request({
    url: '/weapp/mcuTemplate',
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

//编辑会议参数模板
export function updateConferenceParamTemplate(data) {
  return request({
    url: '/weapp/mcuTemplate',
    method: 'put',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

//获取平台模板列表
export function getMcuTemplateList(page, size, direction, property, nameToSearch, brandToSearch, isDefault) {
  let res = isDefault ? `&isDefault=${isDefault}` : '';
  return request({
    url: `/weapp/mcuParameter/findAll?page=${page}&size=${size}&direction=${direction}&property=${property}&nameToSearch=${nameToSearch}&brandToSearch=${brandToSearch}${res}`,
    method: 'get',
  });
}

// 获取平台模板详情
export function getMcuTemplate(id) {
  return request({
    url: `/weapp/mcuParameter?id=${id}`,
    method: 'get',
  });
}

//删除平台模板
export function deleteMcuTemplate(ids) {
  return request({
    url: '/weapp/mcuParameter',
    method: 'delete',
    data: $qs.stringify(ids),
  });
}

//添加平台模板
export function addMcuTemplate(data) {
  return request({
    url: '/weapp/mcuParameter',
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

//编辑平台模板
export function updateMcuTemplate(data) {
  return request({
    url: '/weapp/mcuParameter',
    method: 'put',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

//获取平台模板表单
export function getMcuTemplateForm(brand) {
  return request({
    url: `/weapp/mcuParameter/form?brand=${brand}`,
    method: 'get',
  });
}

//获取平台列表
export function getMcuList(nameToSearch = '', ipToSearch = '', deptId = '', coverageId = '', includeChild = true) {
  return request({
    url: `/weapp/mcu?nameToSearch=${nameToSearch}&ipToSearch=${ipToSearch}&deptId=${deptId}&coverageId=${coverageId}&includeChild=${includeChild}`,
    method: 'get',
  });
}

//获取平台是否可用
export function getMcuAvailable(id) {
  return request({
    url: `/weapp/mcu/available?id=${id}`,
    method: 'get',
  });
}

/**
 * 查询平台资源占用情况
 * @param id 平台id，为空时查询所有平台
 */
export function getMcuCapacity(data) {
  return request({
    url: `/weapp/mcu/capacity`,
    method: 'get',
    params: data,
  });
}

// 获取平台品牌
// 根据会管参数配置的品牌型号来显示，若未配置则显示所有的品牌型号
export function getMcuBrand() {
  let getBrandList = () => {
    return request({
      url: `/weapp/mcu/brand`,
      method: 'get',
    });
  };
  let getBrands = (data) => {
    if (!data.pageSetting || !data.pageSetting.brand) {
      return [];
    }
    let brand = data.pageSetting.brand.split(',').map(Number);
    return brand;
  };

  return Promise.all([getConfParam(), getBrandList()])
    .then((res) => {
      let brands = getBrands(res[0].data);
      if (!brands.length) {
        return res[1];
      }

      let brandList = res[1].data.filter((item) => brands.includes(item.code));
      res[1].data = brandList;
      return res[1];
    })
    .catch((err) => {
      console.error(err);
    });
}

//获取平台详情
export function getMcu(id) {
  return request({
    url: `/weapp/mcu/find?id=${id}`,
    method: 'get',
  });
}

//删除平台列表
export function deleteMcu(ids) {
  return request({
    url: `/weapp/mcu`,
    method: 'delete',
    data: $qs.stringify(ids),
  });
}

/**
 * 查询平台资源列表接口
 * @param {平台ID} id
 * @returns
 */
export function getMcuResource(id) {
  return request({
    url: `/weapp/mcu/resource?id=${id}`,
    method: 'get',
  });
}

/**
 * 获取资源图表数据
 * @param {*} id 平台id
 * @param {*} resourceId 资源id
 * @param {*} sortType 统计时段 SORT_BY_DAY， SORT_BY_WEEK， SORT_BY_MONTH
 * @returns
 */
export function getResourceChart(id, resourceId, sortType) {
  return request({
    url: `/weapp/mcu/resourceChart?id=${id}&resourceId=${resourceId}&sortType=${sortType}`,
    method: 'get',
  });
}

//获取平台表单
export function getMcuForm(id) {
  return request({
    url: `/weapp/mcu/parameters?brand=${id}`,
    method: 'get',
  });
}

// 添加/编辑平台
export function addMcu(data) {
  return request({
    url: `/weapp/mcu`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

/**
 * 切换平台
 * @param {*} srcId  源平台id
 * @param {*} destId 目标平台id
 * @returns 
 */
export function switchMcu(srcId, destId) {
  return request({
    url: `/weapp/mcu/changePlatform?srcId=${srcId}&destId=${destId}`,
    method: 'post'
  });
}

//获取视频终端列表
export function getVideoTerminalList(
  page,
  size,
  nameToSearch,
  deptId,
  coverageId,
  includeChild = false,
  bindRoom = '',
  direction = 'DESC',
  properties = 'name',
  room = '',
  user = '',
  ip = '',
  brandId = '',
  modelId = '',
  protocol = ''
) {
  return request({
    url: `/weapp/videoterminal?page=${page}&size=${size}&nameToSearch=${nameToSearch}&deptId=${deptId}&coverageId=${coverageId}&includeChild=${includeChild}&bindRoom=${bindRoom}&direction=${direction}&properties=${properties}&room=${room}&user=${user}&ip=${ip}&brandId=${brandId}&modelId=${modelId}&protocol=${protocol}`,
    method: 'get',
  });
}

//删除终端
export function deleteVideoTerminal(ids) {
  return request({
    url: `/weapp/videoterminal`,
    method: 'delete',
    data: $qs.stringify(ids),
  });
}

//新增/编辑终端列表
export function editVideoTerminal(data) {
  return request({
    url: `/weapp/videoterminal`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

export function terminalCreateRoom(data) {
  return request({
    url: `/weapp/videoterminal/createRoom`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

//获取终端详情
export function getVideoTerminal(id) {
  return request({
    url: `/weapp/videoterminal/find?id=${id}`,
    method: 'get',
  });
}

//获取终端表单
export function getVideoTerminalForm(modelId, coverageId) {
  return request({
    url: `/weapp/videoterminal/parameters?modelId=${modelId}&coverageId=${coverageId}`,
    method: 'get',
  });
}

// 可能需要更改 未使用
//获取终端品牌列表
export function getVideoTerminalBrand() {
  return request({
    url: `/weapp/videoterminal/manufacturer`,
    method: 'get',
  });
}

//获取终端型号列表
export function getVideoTerminalModel(brandId) {
  return request({
    url: `/weapp/videoterminal/model?brandId=${brandId}`,
    method: 'get',
  });
}

/**
 * 查询会场列表
 * @param {*} conferenceId 会议id
 * @param {*} nameToSearch 会场名称
 * @param {*} online 是否在线 boolean
 * @param {*} common 是否是常用会场
 * @param {*} deptId 二级机构id
 * @param {*} labelId 会场标签id
 * @param {*} direction 排序 asc desc
 * @param {*} properties 排序的属性
 * @returns
 */
export function getConfMeetingRoom(
  conferenceId,
  nameToSearch = '',
  online = '',
  common = '',
  deptId = '',
  labelId = '',
  direction = 'DESC',
  properties = 'online',
  bNoAlert = false
) {
  return request({
    url: `/weapp/room/conferenceRoom?conferenceId=${conferenceId}&nameToSearch=${nameToSearch}&online=${online}&common=${common}&deptId=${deptId}&labelId=${labelId}${direction ? `&direction=${direction}` : ''
      }${properties ? `&properties=${properties}` : ''}`,
    method: 'get',
    headers: {
      noAlert: bNoAlert,
    },
  });
}

/**
 * 添加了分页的会控会场查询接口
 * @param conferenceId
 * @param nameToSearch
 * @param online
 * @param common
 * @param deptId
 * @param labelId
 * @param direction
 * @param properties
 * @param page
 * @param size
 * @param bNoAlert
 * @param includeWild 是否包含外部终端
 * @returns {*}
 */
export function getConfMeetingRoomV2(
  conferenceId,
  nameToSearch = '',
  online = '',
  common = '',
  deptId = '',
  labelId = '',
  direction = 'DESC',
  properties = 'online',
  page = 1,
  size = 20,
  bNoAlert = false,
  includeWild = true,
) {
  return request({
    url: `/weapp/v2/room/conferenceRoom?conferenceId=${conferenceId}&nameToSearch=${nameToSearch}&online=${online}&common=${common}&deptId=${deptId}&labelId=${labelId}&includeWild=${includeWild}${direction ? `&direction=${direction}` : ''
      }${properties ? `&properties=${properties}` : ''}&page=${page}&size=${size}`,
    method: 'get',
    headers: {
      noAlert: bNoAlert,
    },
  });
}

export function getWaitingRoom(params) {
  return request({
    url: `/weapp/v2/room/waitingRoom`,
    method: 'get',
    params: params,
    headers: {
      noAlert: params?.bNoAlert,
    },
  });
}

/**
 * 查询会议中的野终端
 * @conferenceId 会议id
 * @ip 野终端ip
 */
export function getConfMeetingWildRoom(data, bNoAlert) {
  return request({
    url: `/weapp/conference/control/getWildTerminals`,
    method: 'get',
    params: data,
    headers: {
      noAlert: bNoAlert,
    },
  });
}

/**
 * 根据会议id和平台id去拿终端和级联点的接口，如果没有平台id则是主平台下的终端和级联点
 * @conferenceId 会议id
 * @platformId 平台id
 * @online 是否在线
 */
export function getTerminalInMcu(data) {
  return request({
    url: `/weapp/conference/control/terminalInMcu`,
    method: 'get',
    params: data,
  });
}

/**
 * 会控 --- 添加会场
 * @conferenceId 会议id
 * @venues 会场集合
 */
export function confControlAddTerminal(data) {
  return request({
    url: `/weapp/conference/control/invite`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

/**
 * 会控 --- 移除会场
 * @conferenceId 会议id
 * @venues 会场集合
 */
export function confControlDeleteTerminal(data) {
  return request({
    url: `/weapp/conference/control/expel`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

/**
 * 会控 --- 静音
 * @conferenceId 会议id
 * @venues 会场集合
 * @isMute true:开启静音；false:关闭静音
 */
export function confControlMute(data) {
  return request({
    url: `/weapp/conference/control/mute`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

/**
 * 会场闭音
 * @param {*} data
 * @returns
 */
export function confControlMuteLoudspeaker(data) {
  return request({
    url: `/weapp/conference/control/loudspeaker`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

/**
 * 会控 --- 扬声器
 * @conferenceId 会议id
 * @venues 会场集合
 * @isQuiet true:关闭扬声器；false:开启扬声器
 */
export function confControlSpkMute(data) {
  return request({
    url: `/weapp/conference/control/quiet`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

/**
 * 会控 --- 摄像头
 * @conferenceId 会议id
 * @venues 会场集合
 * @isVideoMute true:关闭摄像头；false:打开摄像头
 */
export function confControlVideoMute(data) {
  return request({
    url: `/weapp/conference/control/videoMute`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

/**
 * 会控 --- 呼起/挂断会场
 * @conferenceId 会议id
 * @venues 会场集合
 * @isOnline true:呼起会场；false:挂断会场
 */
export function confControlOnline(data) {
  return request({
    url: `/weapp/conference/control/online`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

/**
 * 会控 --- 设置会场音量
 * @conferenceId 会议id
 * @venues 会场集合
 * @volume 音量值
 */
export function confControlVolume(data) {
  return request({
    url: `/weapp/conference/control/volume`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

/**
 * 会控 --- 锁定/解锁视频源
 * @conferenceId 会议id
 * @venues 会场集合
 * @isLock true: 锁定, false: 解锁
 */
export function confControlVideoLock(data) {
  return request({
    url: `/weapp/conference/control/videoLock`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

/**
 * 会控 --- 设置演讲者
 * @conferenceId 会议id
 * @venues 会场集合, 该参数为null,取消广播会场
 */
export function confControlSpeaker(data) {
  return request({
    url: `/weapp/conference/control/speaker`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

/**
 * 会控 --- 设置多画面
 * @param data-conferenceId 会议id
 * @param data-unitId       单元会议id，不传则设置级联会议多画面
 * @param data-name         预设多画面名称
 * @param data-picNum       几画面
 * @param data-mode         画面样式
 * @param data-subPicList   多画面设置
 */
export function confControlMultiScreen(data) {
  return request({
    url: `/weapp/conference/control/multiPic`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

/**
 * 会控 --- 设置多画面轮询
 * @param data-conferenceId     会议id
 * @param data-unitId           单元会议id，不传则设置级联会议多画面轮询
 * @param data-name             预设多画面轮询名称
 * @param data-picNum           几画面
 * @param data-mode             画面样式
 * @param data-subPicPollList   多画面轮询设置
 */
export function confControlPollMultiScreen(data) {
  return request({
    url: `/weapp/conference/control/setPollPic`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

/**
 * 会控 --- 开始轮询(画面自动分屏)
 * @param data-conferenceId  会议id
 * @param data-unitId  单元会议id
 */
export function confControlStartPollPic(data) {
  return request({
    url: `/weapp/conference/control/startPollPic`,
    method: 'get',
    params: data,
  });
}

/**
 * 会控 --- 取消轮询(画面自动分屏)
 * @param data-conferenceId  会议id
 * @param data-unitId  单元会议id
 */
export function confControlCancelPollPic(data) {
  return request({
    url: `/weapp/conference/control/cancelPollPic`,
    method: 'get',
    params: data,
  });
}

/**
 * 会控 --- 停止轮询(画面停在当前)
 * @param data-conferenceId  会议id
 * @param data-unitId  单元会议id
 */
export function confControlStopPollPic(data) {
  return request({
    url: `/weapp/conference/control/stopPollPic`,
    method: 'get',
    params: data,
  });
}

/**
 * 会控 --- 获取单元会议多画面设置
 * @conferenceId 会议id
 */
export function getMultiScreen(conferenceId) {
  return request({
    url: `/weapp/conference/control/multiPic?conferenceId=${conferenceId}`,
    method: 'get',
  });
}

/**
 * 会控 --- 获取单元会议多画面轮询
 * @conferenceId 会议id
 */
export function getPollMultiScreen(conferenceId) {
  return request({
    url: `/weapp/conference/control/pollPic?conferenceId=${conferenceId}`,
    method: 'get',
  });
}

/**
 * 会控 --- 会议场景设置
 * @conferenceId 会议id
 * @sceneEnum SPEAKER:演讲者 DISSCUSSION：自由讨论
 * @speakerRoomId 演讲者
 */
export function setScene(data) {
  return request({
    url: `/weapp/conference/control/scene`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

/**
 * 会控 --- 字幕设置
 * @param conferenceId 会议id
 * @param text 字幕
 * @param position 位置 1：顶部 2:中部 3：底部
 * @param display 显示效果 1：从下到上 2：从右到左 3：靠左 4：居中 5：靠右
 * @param rooms 会场集合,该参数为null时,所有会场都显示字幕
 */
export function setSubtitle(data) {
  return request({
    url: `/weapp/conference/control/setTextTips`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

/**
 * 会控 --- 取消字幕设置
 * @param conferenceId 会议id
 * @param text 字幕
 * @param position 位置 1：顶部 2:中部 3：底部
 * @param display 显示效果 1：从下到上 2：从右到左 3：靠左 4：居中 5：靠右
 * @param rooms 会场集合,该参数为null时,所有会场都取消字幕
 */
export function cancelSubtitle(data) {
  return request({
    url: `/weapp/conference/control/cancelTextTips`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

/**
 * 会控 --- 查询会议/会场字幕
 * @conferenceId 会议id
 * @terminalId   会场id
 */
export function getSubtitle(conferenceId, terminalId = null) {
  return request({
    url: `/weapp/conference/control/textTips`,
    method: 'get',
    params: { conferenceId, terminalId }
  });
}

/**
 * 会议详情
 * @conferenceId 会议id
 */
export function getVcDetail(conferenceId, bNoAlert = false) {
  return request({
    url: `/weapp/conference/control/vcDetail?conferenceId=${conferenceId}`,
    method: 'get',
    headers: {
      noAlert: bNoAlert,
      rawError: true,
    },
  });
}

/**
 * 选看会场
 * @param {*} data
 * @returns
 */
export function confControlSelectPic(data) {
  return request({
    url: `/weapp/conference/control/selectPic`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

/**
 * 设置选定会场多画面轮询
 * 开始、暂停、取消会场多画面轮询
 * @param {*} data
 * @returns
 */
export function confControlSelectPicPoll(data) {
  return request({
    url: `/weapp/conference/control/participant/pollPic`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

/**
 * 获取选看会场/多画面的状态接口
 * @param {*} conferenceId
 * @param {*} terminalId
 * @returns
 */
export function getSelectPicStatus(conferenceId, terminalId) {
  return request({
    url: `/weapp/conference/control/selectPic?conferenceId=${conferenceId}&terminalId=${terminalId}`,
    method: 'get',
  });
}
/**
 * 获取选定会场多画面轮询的状态
 * @param {*} conferenceId
 * @param {*} terminalId
 * @returns
 */
export function getSelectPicPollStatus(conferenceId, terminalId) {
  return request({
    url: `/weapp/conference/control/${conferenceId}/getParticipantPollPic/${terminalId}`,
    method: 'get',
  });
}

/**
 * 设置联席主持人/取消联席主持人
 * @param {*} data-conferenceId  会议id
 * @param {*} data-venues        会场集合
 * @param {*} data-cohost        true:设置联席主持人 false:取消联席主持人
 * @returns
 */
export function setConfCohost(data) {
  return request({
    url: `/weapp/conference/control/cohost`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}


/**
 * 查询会议与会人
 * @param conferenceId 会议id
 * @param nameToSearch 与会人名称
 * @param bNoAlert     接口出错时是否显示提示框，false显示，true不显示
 */
export function getConfUsers(conferenceId, nameToSearch = '', bNoAlert = false) {
  return request({
    url: `/weapp/conference/participant?conferenceId=${conferenceId}&nameToSearch=${nameToSearch}`,
    method: 'get',
    headers: {
      noAlert: bNoAlert,
    },
  });
}

/**
 * 添加会议与会人
 * @param conferenceId 会议id
 * @param userIds 与会人id
 */
export function addConfUsers(data) {
  return request({
    url: `/weapp/conference/participant`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

/**
 * 删除会议与会人
 * @data conferenceId 会议id userIds 与会人id
 */
export function deleteConfUsers(data) {
  return request({
    url: `/weapp/conference/participant`,
    method: 'delete',
    data: $qs.stringify(data),
  });
}

/**
 * 历史会议另存为模板
 * @conferenceId 会议id
 */
export function saveAsTemplate(data) {
  return request({
    url: `/weapp/saveConfTemplate`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

/**
 * 添加模板
 */
export function addTemplate(data) {
  return request({
    url: `/weapp/template`,
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    data,
  });
}

/**
 * 设置默认会议模板
 */
export function setTemplateAsDefault(data) {
  return request({
    url: `/weapp/mcuTemplate/asDefault`,
    method: 'post',
    params: data,
  });
}

/**
 * 获取会议模板列表
 * @param page 页码
 * @param size 每页列表数量
 * @param nameToSearch 会议主题
 * @param deptId 机构id
 * @param includeChild 仅当前层级
 */
export function getTemplateList(page, size, nameToSearch = '', deptId = '', includeChild = true, permission = '', direction = '', properties = '') {
  return request({
    url: `/weapp/template?page=${page}&size=${size}&nameToSearch=${nameToSearch}&deptId=${deptId}&includeChild=${includeChild}&permission=${permission}&direction=${direction}&properties=${properties}`,
    method: 'get',
  });
}

/**
 * 删除会议模板
 */
export function deleteTemplate(ids) {
  return request({
    url: `/weapp/template`,
    method: 'delete',
    data: $qs.stringify(ids),
  });
}

/**
 * 获取会议模板详情
 */
export function getTemplateDetail({ confId }) {
  return request({
    url: `/weapp/template/${confId}`,
    method: 'get',
  });
}

/**
 * 设置常用会场
 * @param onconferenceId 会议id
 * @param venues 会场集合
 * @param common true:设置；false:取消
 */
export function collectRooms(data) {
  return request({
    url: `/weapp/conference/control/common`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

/**
 * 查询虚拟会议室
 * @param nameToSearch  根据名称搜索
 * @param numToSearch   根据号码搜索
 * @param vmrType       类型 1: 个人, 2: 机构
 * @param deptId        根据机构搜索 vmrType=2时有效
 * @param includeChild  是否包含下级 vmrType=2时有效
 * @param page          页码
 * @param size          每页数目
 * @param direction     排序
 * @param properties    排序
 */
export function getVirtualConfRoom(data) {
  data.direction = data.direction || 'DESC';
  data.properties = data.properties || 'name';
  return request({
    url: `/weapp/visualroom`,
    method: 'get',
    params: data,
  });
}

/**
 * 删除虚拟会议室
 * @ids   虚拟会议室id集合
 */
export function deleteVirtualConfRoom(ids) {
  return request({
    url: `/weapp/visualroom`,
    method: 'delete',
    data: $qs.stringify(ids),
  });
}

/**
 * 添加虚拟会议室
 * @param name      名称
 * @param vmrNumber 号码
 * @param vmrType   类型 1: 个人, 2: 机构
 * @param userId    个人，vmrType为1
 * @param deptId    机构，vmrType为2
 */
export function addVirtualConfRoom(data) {
  return request({
    url: `/weapp/visualroom`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

/**
 * 检查虚拟会议室状态
 * @returns
 */
export function checkVmrStatus(vmrNumber) {
  return request({
    url: `/weapp/visualroom/checkVmrStatus?vmrNumber=${vmrNumber}`,
    method: 'get',
  });
}

/**
 * 获取随机号码
 */
export function getVmrNumber() {
  return request({
    url: `/weapp/visualroom/randomVmr`,
    method: 'get',
  });
}

/**
 * 一键呼叫
 * @param conferenceId 会议id
 */
export function confControlOnlineAll(data) {
  return request({
    url: `/weapp/conference/control/onlineAll`,
    method: 'get',
    params: data,
  });
}

/**
 * 一键静音
 * @param conferenceId 会议id
 * @param isMute true:开启静音；false:关闭静音
 */
export function confControlMuteAll(data) {
  return request({
    url: `/weapp/conference/control/muteAll`,
    method: 'get',
    params: data,
  });
}

/**
 * 一键全体闭音
 * @param conferenceId 会议id
 * @param isMute true:闭音；false:关闭闭音
 */
export function confControlMuteLoudspeakerAll(data) {
  return request({
    url: `/weapp/conference/control/loudspeakerAll`,
    method: 'get',
    params: data,
  });
}

/**
 * 获取级联点
 * @param conferenceId 会议id
 */
export function getConfCascades(data, bNoAlert) {
  return request({
    url: `/weapp/conference/control/getCascades`,
    method: 'get',
    params: data,
    headers: {
      noAlert: bNoAlert,
    },
  });
}

/**
 * 获取未绑定终端的用户
 * @param deptId 机构id
 * @param includeChild 是否包含下级机构
 * @param nameToSearch 按用户名过滤
 */
export function getUnBindTerminalUser(data) {
  return request({
    url: `/weapp/videoterminal/unbindUser`,
    method: 'get',
    params: data,
  });
}

/**
 * 获取没绑定终端的会议室
 * @param deptId 机构id
 * @param includeChild 是否包含下级机构
 * @param nameToSearch 按会议室名过滤
 */
export function getUnBindTerminalRoom(data) {
  return request({
    url: `/weapp/videoterminal/unbindRoom`,
    method: 'get',
    params: data,
  });
}

/**
 * 获取会议拓扑图
 * @param conferenceId 会议id
 */
export function getConfTopology(data) {
  return request({
    url: `/weapp/vcTopology`,
    method: 'get',
    params: data,
  });
}

/**
 * 获取内部拓扑图
 * @param conferenceId 会议id
 * @param platformId 平台id
 */
export function getPlatformTopology(conferenceId, platformId) {
  return request({
    url: `/weapp/conference/control/platformTopo/${conferenceId}/${platformId}`,
    method: 'get',
  });
}

/**
 * 获取系统参数
 */
export function getSystemParam() {
  return request({
    url: `/weapp/systemparam`,
    method: 'get',
  });
}

/**
 * 设置系统参数
 */
export function setSystemParam(data) {
  let file = new FormData();
  Object.keys(data).forEach((key) => {
    data[key] && file.append(key, data[key]);
  });
  return request({
    url: `/weapp/systemparam`,
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    data: file,
  });
}

/**
 * 获取会管参数
 */
export function getConfParam() {
  return request({
    url: `/weapp/confparam`,
    method: 'get',
  });
}

/**
 * 设置会管参数
 */
export function setConfParam(data) {
  return request({
    url: `/weapp/confparam`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
    // data: $qs.stringify(data),
  });
}

/**
 * 会议重开
 * @param conferenceId 会议id
 */
export function confControlReCall(data) {
  return request({
    url: `weapp/conference/control/reCall`,
    method: 'get',
    params: data,
  });
}

/**
 * 会控授权
 * @param confId  会议id
 * @param auths   授权人员id列表
 */
export function setConfAuth(data) {
  return request({
    url: `/weapp/addAuths`,
    method: 'get',
    params: data,
  });
}

/**
 * 会控 --- 查询线下会议室（显示会议中所有被占用的会议室）
 * @param conferenceId  会议id
 */
export function getOfflineRoom(data, bNoAlert = false) {
  return request({
    url: `/weapp/room/offlineRoom`,
    method: 'get',
    params: data,
    headers: {
      noAlert: bNoAlert,
    },
  });
}

/**
 * 会控 --- 是否允许发送双流接口
 * @param conferenceId  会议id
 * @param venues 会场(venues为空, 取消双流发送者)
 */
export function confControlLockPresenter(data) {
  return request({
    url: `/weapp/conference/control/lockPresenter`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

/**
 * 会控 --- 自动分屏
 * @param conferenceId  会议id
 */
export function confControlAutoMultiPic(data) {
  return request({
    url: `/weapp/conference/control/autoMultiPic`,
    method: 'get',
    params: data,
  });
}

/**
 * 会控 --- 开始/取消录制
 * @conferenceId 会议id
 * @recordOpType 录制控制 START开始 PAUSE暂停 STOP停止
 */
export function confControlRecord(data) {
  return request({
    url: `/weapp/conference/control/record`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

/**
 * 查看会议录制列表
 * @conferenceId 会议id
 */
export function getConfRecords(conferenceId) {
  return request({
    url: `/weapp/conference/control/record?conferenceId=${conferenceId}`,
    method: 'get',
  });
}

/**
 * 获取会议直播文件列表
 */
export function getConfLive(conferenceId) {
  return request({
    url: `/weapp/conference/live/${conferenceId}`,
    method: 'get',
  });
}

/**
 * 直播文件的发布/下架
 */
export function changePublishState(conferenceId, publishStateInfo) {
  return request({
    url: `/weapp/conference/live/${conferenceId}/edit`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
    data: publishStateInfo,
  });
}

/**
 * 删除会议录制信息
 * @conferenceId 会议id
 */
export function deleteConfRecords(data) {
  return request({
    url: `/weapp/conference/control/record`,
    method: 'delete',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

/**
 * 统计 --- 会议
 * @param type  统计区间类型,STATISTIC_TYPE
 * @param value 基准时间
 * @param deptId 指定的部门
 * @param includeChild 是否包含下级部门
 * @param creatorId 指定的预约人Id
 */
export function getConfStatistic(data) {
  return request({
    url: `/weapp/confstatistic`,
    method: 'get',
    params: data,
  });
}

/**
 * 统计 --- 会议明细
 * @param type  统计区间类型,STATISTIC_TYPE
 * @param value 基准时间
 * @param deptId 指定的部门
 * @param includeChild 是否包含下级部门
 * @param creatorId 指定的预约人Id
 */
export function getConfStatisticDetail(data) {
  return request({
    url: `/weapp/confstatistic/detail`,
    method: 'get',
    params: data,
  });
}

/**
 * 统计 --- 会议室使用率
 * @param type  统计区间类型,STATISTIC_TYPE
 * @param value 基准时间
 * @param deptId 指定的部门
 * @param includeChild 是否包含下级部门
 * @param creatorId 指定的预约人Id
 */
export function getRoomUsage(data) {
  return request({
    url: `/weapp/confstatistic/usage/room`,
    method: 'get',
    params: data,
  });
}

/**
 * 统计 --- 平台使用率
 * @param type  统计区间类型,STATISTIC_TYPE
 * @param value 基准时间
 * @param deptId 指定的部门
 * @param creatorId 指定的预约人Id
 * @param includeChild 是否包含下级部门
 */
export function getMcuUsage(data) {
  return request({
    url: `/weapp/confstatistic/usage/mcu`,
    method: 'get',
    params: data,
  });
}

/**
 * 统计 --- 各类排名
 * @param type  统计区间类型,STATISTIC_TYPE
 * @param value 基准时间
 * @param deptId 指定的部门
 * @param includeChild 是否包含下级部门
 * @param sort 排名类型 0按次数 1按时长
 * @param direction 排名方式 ASC升序 DESC降序
 * @param rankItem  排名项目: 1.个人参会排名,  2. 个人迟到排名,  3. 个人缺席排名, 4.部门参会, 5.部门迟到 6.部门缺席
 * @param num 返回的数据个数
 */
export function getStatisticRank(data) {
  return request({
    url: `/weapp/confstatistic/ranking`,
    method: 'get',
    params: data,
  });
}

/**
 * 统计 --- 会议失败
 * @param type  统计区间类型,STATISTIC_TYPE
 * @param value 基准时间
 * @param deptId 指定的部门
 * @param includeChild 是否包含下级部门
 * @param creatorId 指定的预约人Id
 */
export function getStatisticFail(data) {
  return request({
    url: `/weapp/confstatistic/fail`,
    method: 'get',
    params: data,
  });
}

/**
 * 添加品牌
 * @param {*} data
 * @returns
 */
export function addBrand(name) {
  return request({
    url: `/weapp/brand?name=${name}`,
    method: 'post',
  });
}

/**
 * 导出统计分析数据
 * @param {*} data
 * @returns
 */
export const exportStatistic = (data, url = `/weapp/confstatistic/export`) => {
  return axios({
    baseURL: process.env.VUE_APP_BASE_API,
    method: 'post',
    url,
    data,
    responseType: 'blob',
    headers: {
      Authorization: 'Bearer ' + getAccessToken(),
      'Content-Type': 'application/json',
    },
  })
    .then((res) => {
      let link = document.createElement('a');
      let blob = new Blob([res.data], {
        type: res.data.type,
      });
      link.style.display = 'none';
      link.href = URL.createObjectURL(blob);
      link.download = data.filename || res.headers['content-disposition']; //下载后文件名
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    })
    .catch((error) => { });
};

/**
 * 查询品牌
 * @returns
 */
export function getBrand() {
  return request({
    url: '/weapp/brand',
    method: 'get',
  });
}

/**
 * 删除品牌
 * @param {*} ids  品牌id列表
 * @returns
 */
export function deleteBrand(id) {
  return request({
    url: `/weapp/brand?ids=${id}`,
    method: 'delete',
  });
}

/**
 * 添加终端型号
 * @param {*} data
 * brandId 所属品牌
 * name  型号名称
 * @returns
 */
export function addModel(params) {
  return request({
    url: '/weapp/model',
    method: 'post',
    params,
  });
}

/**
 * 查询终端型号
 * @param {*} id brandId 所属品牌
 * @returns
 */
export function getModel(id) {
  return request({
    url: `/weapp/model?brandId=${id}`,
    method: 'get',
  });
}

/**
 * 删除终端型号
 * @param {*} ids 型号Id列表
 * @returns
 */
export function deleteModel(params) {
  return request({
    url: '/weapp/model',
    method: 'delete',
    params,
  });
}

/**
 * 添加检测会议接口
 * @param {*} data
 * @returns
 */
export function chkBusy(data) {
  return request({
    url: '/weapp/chkBusy',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

/**
 * 一个查询渲染参数的接口
 * @param {*} id
 * @returns
 */
export function getParameters(id) {
  return request({
    url: `/weapp/videoterminal/renderParameters?coverageId=${id}`,
    method: 'get',
  });
}

/**
 * 会场列表排序
 * @param {*} data
 * @returns
 */
export function saveConfSort(data) {
  return request({
    url: '/weapp/room/savesort',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

/**
 * 查看二次预约列表
 * @param {*} data
 * @returns
 */
export function getSecondConf(data) {
  return request({
    url: '/weapp/confdept',
    method: 'get',
    params: data,
  });
}

/**
 * 二次预约添加用户
 * @param {*} data
 * @returns
 */
export function addSecondConfUser(data) {
  return request({
    url: '/weapp/confdept/addUser',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

/**
 * 二次预约添加会议室
 * @param {*} data
 * @returns
 */
export function addSecondConfRoom(data) {
  return request({
    url: '/weapp/confdept/addRoom',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

/**
 * 二次预约删除用户
 * @param {*} data
 * @returns
 */
export function deleteSecondConfUser(data) {
  return request({
    url: '/weapp/confdept/delUser',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

/**
 * 二次预约删除会议室
 * @param {*} data
 * @returns
 */
export function deleteSecondConfRoom(data) {
  return request({
    url: '/weapp/confdept/delRoom',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

/**
 * 查询指定会议对当前用户的可见会议室和与会人
 * @param confId 会议id
 * @returns
 */
export function getSecondConfRoomAndUser(confId) {
  return request({
    url: `/weapp/confdept/getRoomAndParticipant?confId=${confId}`,
    method: 'get',
  });
}

/**
 * 查询二级机构的接口
 * @returns
 */
export function getChildLevelDept(params, bNoAlert = false) {
  return request({
    url: `/weapp/room/findDept`,
    method: 'get',
    params,
    headers: {
      noAlert: bNoAlert,
    },
  });
}

/**
 * 查询会议室绑定的空间
 * @param {Object} roomId
 */
export function getRoomSpace(roomId) {
  return request({
    url: `/weapp/room/space?roomId=${roomId}`,
    method: 'get',
  });
}

/**
 * 演讲者模式下设置发言者
 * @param {*} data
 * @returns
 */
export function setTalker(data) {
  return request({
    url: '/weapp/conference/control/talker',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

/**
 * 设置会场点名
 * @param {*} data
 * @returns
 */
export function setRollCall(data) {
  return request({
    url: '/weapp/conference/control/rollCall',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

/**
 * 确认点名状态
 * @param {*} data
 * @returns
 */
export function setRollCallConfirm(data) {
  return request({
    url: '/weapp/conference/control/rollCallConfirm',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

/**
 * 查看会空中会场的标签
 * @param {*} confId
 * @returns
 */
export function getConfRoomLabel(confId, bNoAlert = false) {
  return request({
    url: `/weapp/room/findLabel?confId=${confId}`,
    method: 'get',
    headers: {
      noAlert: bNoAlert,
    },
  });
}

/**
 * 会中换人
 * 仅双屏对话模式下，可对副屏进行换人
 * @param  conferenceId 会议id
 * @param  after        替换的终端
 * @param  before   被替换的终端
 * @returns
 */
export function confControlReplaceTerminal(data) {
  return request({
    url: `/weapp/conference/control/replace`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

/**
 * 强制替换会场、不受限与双屏对话模式
 * @param {*} data
 * @returns
 */
export function confControlForceReplaceTerminal(data) {
  return request({
    url: `/weapp/conference/control/change`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

//获取动态账号列表
export function getDynamicAccountList(data) {
  return request({
    url: `weapp/dynamicAccount`,
    method: 'get',
    params: data,
  });
}

/**
 * 获取用户会议设置 主席密码 来宾密码
 */
export function getAppConfig() {
  return request({
    url: '/weapp/appconfig',
    method: 'get',
  });
}

/**
 * 查询所有会议级别
 * @returns
 */
export function getMeetingLevel() {
  return request({
    url: '/weapp/conderence/rank',
    method: 'get',
  });
}

/**
 * 获取我可用的会议级别
 * 查询当前用户可用的会议级别
 * @returns
 */
export function getMyMeetingLevel() {
  return request({
    url: '/weapp/conderence/rank//usable',
    method: 'get',
  });
}

/**
 * 删除会议级别
 * @param {*} data
 * @returns
 */
export function deleteMeetingLevel(data) {
  return request({
    url: '/weapp/conderence/rank',
    method: 'delete',
    data: $qs.stringify(data),
  });
}

/**
 * 保存会议级别
 * @param {*} data
 * @returns
 */
export function saveMeetingLevel(data) {
  return request({
    url: `/weapp/conderence/rank`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

/**
 * 查询会议室绑定的终端接口
 * @param {*} id
 * @returns
 */
export function getBackupVideoTerminalByRoom(id) {
  return request({
    url: `/weapp/videoterminal/backup?roomId=${id}`,
    method: 'get',
  });
}

/**
 * 会控中切换备份终端
 * @param {*} data
 * @returns
 */
export function confExchangeBackup(data) {
  return request({
    url: `/weapp/conference/control/exchangeBackup`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

/**
 * 确认延长会议（有冲突时也要延长）
 * @param {*} data
 * @returns
 */
export function delayConfAnyway(data) {
  return request({
    url: `/weapp/delayAnyway`,
    method: 'get',
    params: data,
  });
}

/**
 * 呼叫外部终端
 * @param {*} data
 * @returns
 */
export function callTempRoom(data) {
  return request({
    url: `/weapp/conference/control/temporaryCall`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

/**
 * 查询会场实时信息
 * @param conferenceId 会议ID
 * @param vcTerminalId 会场绑定的终端ID
 * @returns
 */
export function confControlTerminalRealInfo(conferenceId, vcTerminalId) {
  return request({
    url: `/weapp/ops/participant/${conferenceId}/${vcTerminalId}/latest`,
    method: 'get',
  });
}

/**
 * 查询会场能力
 * @param conferenceId 会议ID
 * @param vcTerminalId 会场绑定的终端ID
 * @returns
 */
export function confControlTerminalAbility(conferenceId, vcTerminalId) {
  return request({
    url: `/weapp/ops/participant/${conferenceId}/${vcTerminalId}/ability`,
    method: 'get',
  });
}

/**
 * 查询会场历史数据
 * @param conferenceId 会议ID
 * @param vcTerminalId 会场绑定的终端ID
 * @param params-target 监控对象,TARGET_OPTIONS
 * @param params-indicator 监控指标,INDICATOR_OPTIONS
 * @param params-from 开始时间
 * @param params-to 结束时间
 * @returns
 */
export function confControlTerminalHistoryInfo(conferenceId, vcTerminalId, params) {
  return request({
    url: `/weapp/ops/participant/${conferenceId}/${vcTerminalId}/history`,
    method: 'get',
    params
  });
}

/**
 * 查询会场所有历史数据
 * @param conferenceId 会议ID
 * @param vcTerminalId 会场绑定的终端ID
 * @param params-from 开始时间
 * @param params-to 结束时间
 * @returns
 */
export function confControlTerminalAllHistoryInfo(conferenceId, vcTerminalId, params) {
  return request({
    url: `/weapp/ops/participant/${conferenceId}/${vcTerminalId}/allHistory`,
    method: 'get',
    params
  });
}

/**
 * 查看会场预览图
 */
export function previewImage(conferenceId, data) {
  return request({
    url: `/weapp/conference/control/previewImage/${conferenceId}`,
    method: 'get',
    params: data,
  });
}

/**
 * 会控中锁定会议
 * @param {*} data
 * @returns
 */
export function lockConf(data) {
  return request({
    url: `/weapp/conference/control/lockConference`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

/**
 * 多画面快捷设置
 * @param {*} data
 * @returns
 */
export function quickScene(data) {
  return request({
    url: `/weapp/conference/control/multiPicModeQuickSet`,
    method: 'get',
    params: data,
  });
}

/**
 * 获取会议日志
 * @param {*} data
 * @returns
 */
export function getConferenceLog(data) {
  return request({
    url: '/weapp/conference/log',
    method: 'get',
    params: data,
  });
}

/**
 * 下载会议操作日志
 * @param {*} data
 * @returns
 */
export function downloadConfLog(data) {
  return request({
    url: '/weapp/conference/log/download',
    method: 'get',
    params: data,
  });
}

/**
 * 指定终端发送双流
 * @param {*} confId
 * @param {*} terminalId
 * @returns
 */
export function sendAuxiliaryFlow(confId, terminalId) {
  return request({
    url: `/weapp/conference/control/${confId}/sendAuxiliaryFlow/${terminalId}`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

/**
 * 取消会议双流
 * @param {*} confId
 * @returns
 */
export function stopAuxiliaryFlow(confId) {
  return request({
    url: `/weapp/conference/control/${confId}/stopAuxiliaryFlow`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

/**
 * 会议等候室控制
 * @param {*} data
 * @returns
 */
export function waitingRoomControl(data) {
  return request({
    url: `/weapp/conference/control/waitingRoom`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

/**
 * 获取会议中处于等候室的会场列表
 * @param {*} params conferenceId nameToSearch direction properties page size
 * @returns
 */
export function getWaitingRoomMember(params) {
  return request({
    url: '/weapp/v2/room/waitingRoom',
    method: 'get',
    params,
  });
}

/**
 * 设置/取消设置预监终端
 * @param {*} data
 * @returns
 */
export function setConferencePreMonitor(data) {
  return request({
    url: `/weapp/conference/control/preMonitor`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

/**
 * 修改会议密码
 * @param {*} conferenceId 会议ID
 * @param {*} data-guestPassword    来宾密码,null为删除 
 * @param {*} data-chairmanPassword 主席密码,null为删除 
 * @param {*} data-cmdType:modify
 * @returns
 */
export function confControlModifyPassword(conferenceId, data) {
  return request({
    url: `/weapp/conference/control/modifyPassword/${conferenceId}`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

/**
 * 修改会场名称
 * @param {*} data-conferenceId 会议ID
 * @param {*} data-guestPassword    来宾密码,null为删除 
 * @param {*} data-chairmanPassword 主席密码,null为删除 
 * @param {*} data-cmdType:modify
 * @returns
 */
export function confControlEditConfName(data) {
  return request({
    url: `/weapp/conference/control/venue/rename`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

/**
 * 查询聚合会议信息
 * @param {*} conferenceId 会议ID
 * @param {*} params-online 是否在线
 * @returns
 */
export function getMultiConfInfo(conferenceId, params) {
  return request({
    url: `/weapp/vcc/info/${conferenceId}`,
    method: 'get',
    params
  });
}







