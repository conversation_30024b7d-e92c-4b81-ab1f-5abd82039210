<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Document</title>
  <link rel="stylesheet" href="40e93b11cbd4174c60b22cba0d630300.css" />
  <style>
    ul {
      margin: 0;
      padding: 0;
    }

    ul li {
      list-style: none;
    }

    body,
    html {
      height: 100%;
      margin: 0;
    }

    .wrap {
      width: 100%;
      height: 100%;
      position: relative;
    }

    .wrap img {
      position: absolute;
      top: 0;
      left: 0;
      z-index: -1;
      width: 100%;
      height: 100%;
    }

    .content-wrap {
      box-sizing: border-box;
      width: 100%;
      height: 100%;
      padding: 0 8vmin 6.8vmin;
      display: flex;
      flex-direction: column;
    }

    .content-wrap .title {
      font-size: 6vmin;
      padding: 2vmin;
    }

    .header-wrap {
      display: grid;
      grid-template-columns: repeat(3, 33.33%);
      font-size: 3vmin;
      padding: 1.7vmin 0;
      background-color: #29abe2;
    }

    .header-wrap div {
      text-align: center;
    }

    .content {
      flex: 1;
      overflow: hidden;
      background-color: rgba(41, 171, 226, 0.43);
    }

    .content li {
      display: grid;
      grid-template-columns: repeat(3, 33.33%);
      font-size: 2.5vmin;
      line-height: 2.5;
      box-sizing: border-box;
    }

    .content li+li {
      border-top: 0.1vmin solid rgba(255, 255, 255, 0.5);
    }

    .content li div {
      text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    [v-cloak] {
      display: none;
    }
  </style>
  <!-- vue -->
  <script src="174365807c06490c848d7b1d45fdc348.js"></script>
  <!-- jquery.js -->
  <script src="13c0a5055cca7b2463b2f73701960b9e.js"></script>
  <!-- mqtt -->
  <script src="d4bfcfa9de50875589eaab322a015441.js"></script>
  <script src="page.js"></script>
</head>

<body>
  <div id="app" class="wrap" v-cloak>
    <div :style="style" class="wrap">
      <img src="./062d4669bebbe598c10528617ed85609.jpg" alt="" />
      <div class="content-wrap">
        <div class="title">会议信息一览</div>
        <div class="header-wrap">
          <div>会议时间</div>
          <div>会议主题</div>
          <div>会议室</div>
        </div>
        <div class="content" id="scrollWrap">
          <ul>
            <template v-for="room in conferenceList">
              <li v-for="item in room.data" :key="item.confId">
                <div>{{item.time}}</div>
                <div>{{item.subject}}</div>
                <div>{{item.roomName}}</div>
              </li>
            </template>
          </ul>
        </div>
      </div>
    </div>
  </div>
</body>

<script>
  window.$page = page;
  const cssProperty = ["height", "width", "fontSize"];
  const STYLE = ["color", "backgroundColor"];
  const PROP = ["conferenceRoom"];
  const topicPrefix = "dss2/web/conference/room/";

  const app = new Vue({
    el: "#app",
    data: {
      props: page.props,
      mqttClient: null,
      timer: null,
      roomIdList: new Set(),
      conferenceList: [],
      mqtt: {}
    },
    computed: {
      style() {
        let style = {};
        for (let prop of this.props) {
          let unit = cssProperty.includes(prop.field) ? "px" : "";
          style[prop.field] = prop.value + unit;
        }
        return style;
      }
    },
    created() {
      for (let i = 1; i <= 13; i++) {
        this.roomIdList.add(-i);
        this.conferenceList.push({
          roomId: -i,
          data: [
            {
              id: -i,
              roomId: -i,
              subject: "研究会议" + i,
              roomName: "会议室" + i,
              time: "2025/3/13 10:00-2025/3/13 12:00"
            }
          ]
        });
      }
      // 调试
      if (this.isWindows()) {
        // this.roomIdList.clear();
        // this.conferenceList = [];
        // let ws = "ws://192.168.89.251:8080/mqtt";
        // let username = "admin";
        // let password = "nst@aliyun";
        // this.getMqttServerAndConnect({ ws, username, password });
        return;
      }

      this.roomIdList.clear();
      this.conferenceList = [];
      var data = window.DSS20AndroidJS.getWebMqttWs();
      var info = JSON.parse(JSON.parse(data));
      this.getMqttServerAndConnect(info);
    },
    mounted() {
      this.scroll();
      window["update"] = (val, mqtt = null) => {
        let styles = [],
          props = [];
        for (let i = 0; i < val.length; i++) {
          let item = val[i];
          if (STYLE.includes(item.field)) {
            styles.push(item);
          } else if (PROP.includes(item.field)) {
            props.push(item);
            let index = this.getPropIndex(item.field);
            this.props[index].value = item.value;
          }
        }

        styles.length && this.updateStyles(styles);
        if (mqtt) {
          this.getMqttServerAndConnect(mqtt);
        } else {
          this.updateProps();
        }
      };

      window["setMqttParam"] = param => {
        this.getMqttServerAndConnect(param);
      };

      window["updateMqtt"] = param => {
        this.updateMqtt(param);
      };
    },

    beforeDestory() {
      this.timer && clearInterval(this.timer);
      this.mqttClose();
    },
    methods: {
      /*
       * 终端打印
       */
      log(msg) {
        window.DSS20AndroidJS &&
          window.DSS20AndroidJS.htmlLogcat("汇总屏：" + msg);
      },
      updateMqtt(mqtt) {
        if (!mqtt) {
          return;
        }
        mqtt = JSON.parse(mqtt);

        if (
          (this.mqttClient && this.mqtt.ws !== mqtt.ws) ||
          (!this.mqttClient && !Object.keys(this.mqtt).length)
        ) {
          this.log("mqtt重连");
          this.mqttClose();
          this.roomIdList.clear();
          this.conferenceList = [];
          this.mqtt = mqtt;
          this.getMqttServerAndConnect(mqtt);
        }
      },
      isWindows() {
        // var userAgent = navigator.userAgent;
        // return userAgent.indexOf("Windows") > -1;
        return window.DSS20AndroidJS === undefined;
      },
      getMac() {
        if(this.isWindows()){
          return ''
        }
        var data = window.DSS20AndroidJS.terminalInfo();
        var info = JSON.parse(data);
        return info.mac;
      },
      scroll() {
        let wrapper = $("#scrollWrap");
        let ulWrapper = $("#scrollWrap ul");
        function Marquee() {
          if (ulWrapper.height() <= wrapper.height()) {
            return;
          }
          let scrollHeight = ulWrapper.find("li").height();
          ulWrapper.stop().animate(
            {
              marginTop: -scrollHeight
            },
            1600,
            () => {
              ulWrapper
                .css({ marginTop: 0 })
                .find("li:first")
                .appendTo(ulWrapper);
            }
          );
        }
        this.timer && clearInterval(this.timer);
        this.timer = setInterval(Marquee, 2000);
      },
      updateProps() {
        for (let prop of this.props) {
          if (prop.field === "conferenceRoom") {
            if (!prop.value.length && this.isWindows()) {
              return;
            }

            //删除之前的订阅列表及会议信息
            this.roomIdList.forEach(id => {
              let topic = this.getTopic(id);
              this.mqttClient.unsubscribe(topic);
            });
            this.conferenceList = [];
            this.roomIdList.clear();

            //订阅当前的会议室
            this.roomIdList = new Set(
              prop.value.map(item => {
                this.mqttRefreshSubscribe(item.roomId);
                return item.roomId;
              })
            );
          }
        }
      },
      updateStyles(styles) {
        for (let style of styles) {
          let index = this.getPropIndex(style.field);
          if (index !== -1) {
            let data = this.props[index];
            data.value = style.value;
            this.$set(this.props, index, data);
          }
        }
      },
      getPropIndex(name) {
        for (let i = 0; i < this.props.length; i++) {
          if (this.props[i].field === name) {
            return i;
          }
        }
        return -1;
      },
      formateData(dstList, roomId) {
        let index = this.conferenceList.findIndex(
          item => item.roomId === roomId
        );
        if (index !== -1) {
          this.conferenceList[index].data = dstList;
        } else {
          this.conferenceList.push({ data: dstList, roomId });
        }
      },
      deleteRoomById(roomId) {
        let srcList = JSON.parse(JSON.stringify(this.conferenceList));
        return srcList.filter(item => item.roomId !== roomId);
      },
      /**
       * 随机ID
       * @param {*} len
       * @param {*} radix
       * @returns
       */
      randomId(len, radix) {
        var chars =
          '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('')
        var uuid = [],
          i
        radix = radix || chars.length
        if (len) {
          // Compact form
          for (i = 0; i < len; i++) uuid[i] = chars[0 | (Math.random() * radix)]
        } else {
          // rfc4122, version 4 form
          var r
          // rfc4122 requires these characters
          uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-'
          uuid[14] = '4'
          // Fill in random data.  At i==19 set the high bits of clock sequence as
          // per rfc4122, sec. 4.1.5
          for (i = 0; i < 36; i++) {
            if (!uuid[i]) {
              r = 0 | (Math.random() * 16)
              uuid[i] = chars[i == 19 ? (r & 0x3) | 0x8 : r]
            }
          }
        }
        return uuid.join('')
      },
      /**
       * 连接到MQTT服务器并订阅
       */
      getMqttServerAndConnect(param) {
        this.log("mqtt参数：" + JSON.stringify(param));

        const that = this;
        let { ws, username, password } = param;
        let mac = this.getMac() || ''
        // 客户端ID
        let clientId = `js_summaryScreen1_${mac}_${this.randomId(16, 16)}`;
        that.mqttClient = mqtt.connect(ws, {
          clientId,
          username,
          password,
          clean: true,
          connectTimeout: 5 * 1000,
          keepalive: 30
        });
        that.mqttClient.on("connect", () => {
          this.log("mqtt连接成功");
          this.updateProps();
        });
        that.mqttClient.on("error", error => {
          this.log("mqtt连接失败:" + error);
        });
        //监听接收消息事件
        that.mqttClient.on("message", (topic, message) => {
          message = JSON.parse(message.toString());
          this.log("获取mqtt消息：" + JSON.stringify(message));
          const { meetingroom, confDetailDTOList } = message;

          // 会议室被删除后，返回的roomName为空，删除该会议室的所有会议
          if (!meetingroom.roomName) {
            this.log("会议室被删除");
            this.conferenceList = this.deleteRoomById(meetingroom.id);
            return;
          }

          const { id, roomName } = meetingroom;
          let res = [];
          if (confDetailDTOList.length) {
            res = confDetailDTOList.filter(item => {
              if (item.approved !== 4) {
                return false;
              }
              item.time = item.period

              // 已经结束的会议不显示
              if (item.end < +new Date()) {
                return false
              }

              return true;
            });
          }

          if (!res.length) {
            res = [
              {
                roomName,
                subject: "",
                time: "",
                confId: Math.random(),
                roomId: id
              }
            ];
          }
          this.log("格式化后数据：" + JSON.stringify(res));
          this.formateData(res, id);
        });
      },

      /**
       * 发布MQTT主题
       */
      mqttPublish(id) {
        let topic = `dss2/web/conference/room/${id}`;
        let message = `{"command":"WEATHER","parameters":{${id}:"${id}"}}`;
        this.log("mqtt发布主题:" + message);
        this.mqttClient.publish(
          topic,
          message,
          { qos: 1, retain: true },
          (err, res) => {
            if (err) {
              this.log("mqtt发布主题失败", err);
              return;
            }
            this.mqttRefreshSubscribe(id);
          }
        );
      },
      /**
       * 订阅MQTT主题
       */
      mqttRefreshSubscribe(id) {
        const that = this;
        let topic = topicPrefix + id;
        this.log("mqtt订阅主题:" + topic);
        that.mqttClient.subscribe(topic, { qos: 1 }, (err, res) => {
          if (err) {
            this.log("mqtt订阅主题失败:", err);
            return;
          }
        });
      },
      /**
       * 释放MQTT客户端
       */
      mqttClose() {
        this.mqtt = {};
        if (this.mqttClient) {
          this.roomIdList.forEach(id => {
            let topic = this.getTopic(id);
            this.mqttClient.unsubscribe(topic);
          });
          this.mqttClient.end();
        }
      },
      getTopic(id) {
        return topicPrefix + id;
      }
    }
  });
</script>

</html>