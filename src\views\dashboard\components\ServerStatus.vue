<template>
  <div class="service-wrap" v-loading="loading">
    <div class="service-wrap" v-loading="loading">
      <div
        class="card-content"
        v-for="item in serviceList"
        :key="item.name"
        :class="{ danger: !item.alive }"
        @click="viewDetail(item)"
        :title="item.description"
      >
        <div class="icon" :style="{ color: item.iconColor }">
          <svg-icon :icon-class="item.icon" />
        </div>
        <div class="txt">
          <div class="name">{{ item.displayName }}</div>
          <div class="status">{{ item.alive ? '正常' : '异常' }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { getServiceStatus } from '@/api/dashboard';

  export default {
    data() {
      return {
        serviceList: [],
        loading: false,
        COLOR: ['#328EE1', '#32C5E1', '#FFB248', '#7B85F7'],
        ICON: {
          oauth: 'dashboard@serverSystem',
          dashboard: 'dashboard@serverDashboard',
          iot: 'dashboard@serverIot',
          office: 'dashboard@serverDocument',
          puppeteer: 'dashboard@serverChrome',
          stream: 'dashboard@serverLive',
          webapp: 'dashboard@server',
          wx: 'dashboard@serverConf',
          'smc-190': 'dashboard@serverSmc',
        },
      };
    },
    created() {
      this.getData();
    },
    methods: {
      getData() {
        this.loading = true;
        getServiceStatus()
          .then(({ data }) => {
            let colorIndex = 0;
            this.serviceList = data.map((item, index) => {
              if (item.alive) {
                item.icon = this.ICON[item.name] ? this.ICON[item.name] : 'dashboard@server';
                item.iconColor = this.COLOR[colorIndex];
                colorIndex = (colorIndex + 1) % 4;
              } else {
                item.icon = 'dashboard@serverError';
              }

              return item;
            });
          })
          .finally(() => (this.loading = false));
      },
      viewDetail(item) {
        const h = this.$createElement;

        let message = '';
        if (!item.alive && item.reason) {
          message = h('p', null, [
            h('div', null, `详情：${item.description}`),
            h('div', null, `实例数：${item.instanceNumber}`),
            h('div', null, `状态：${item.alive ? '正常' : '异常'}`),
            h('div', null, `异常原因：${item.reason}`),
          ]);
        } else {
          message = h('p', null, [
            h('div', null, `详情：${item.description}`),
            h('div', null, `实例数：${item.instanceNumber}`),
            h('div', null, `状态：${item.alive ? '正常' : '异常'}`),
          ]);
        }

        this.$msgbox({
          title: item.displayName,
          message,
          showCancelButton: false,
          showConfirmButton: false,
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .service-wrap {
    display: flex;
    justify-content: space-around;
    gap: 15px;
    overflow-x: auto;
    width: 100%;
    padding: 0 5px;

    .card-content {
      width: 25%;
      min-height: 75px;
      border-radius: 5px;
      min-width: 173px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 10px;
      cursor: pointer;
      border: 2px solid #30c790;

      .icon {
        text-align: center;
        border-radius: 50%;
        font-size: 38px;
        color: rgb(115, 222, 179);
      }

      .txt {
        margin-left: 8px;
        color: #333;

        .name {
          font-size: 18px;
          font-family: 'Microsoft YaHei', 'SansSerif', serif;
          font-weight: 800;
        }

        .status {
          font-size: 14px;
          white-space: nowrap;
          padding-top: 5px;
          color: #333;
          margin-top: 5px;
          color: #30c790;
          text-align: left;
        }
      }

      &.danger {
        color: #e52c54 !important;
        border-color: #e52c54 !important;
        .icon {
          color: #e52c54 !important;
          animation: flickerAnimation 1s infinite; /* 1秒完成一次动画，无限循环 */
        }
        .status {
          color: #e52c54 !important;
        }
      }
    }
  }

  /* 定义闪烁动画 */
  @keyframes flickerAnimation {
    0% {
      opacity: 1;
    }
    50% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }
</style>
