<template>
  <el-dialog :title="title" :visible.sync="visible" width="860px" :close-on-click-modal="false" @close="close">
    <el-input
      size="small"
      class="mr-10"
      style="width: 150px"
      v-model="filter.name"
      placeholder="产品名称"
      v-debounce="[
        () => {
          productPage.current = 1;
          getProductPageable();
        },
      ]"
    ></el-input>
    <el-input
      size="small"
      class="mr-10"
      style="width: 150px"
      v-model="filter.code"
      placeholder="产品编码"
      v-debounce="[
        () => {
          productPage.current = 1;
          getProductPageable();
        },
      ]"
    ></el-input>
    <el-input
      size="small"
      class="mr-10"
      style="width: 150px"
      v-model="filter.model"
      placeholder="产品型号"
      v-debounce="[
        () => {
          productPage.current = 1;
          getProductPageable();
        },
      ]"
    ></el-input>
    <el-cascader
      size="small"
      class="mr-10"
      v-model="filter.deviceType"
      :options="cascaderData"
      placeholder="产品类型"
      @change="
        {
          productPage.current = 1;
          getProductPageable();
        }
      "
      clearable
      filterable
      :props="{ checkStrictly: true }"
    ></el-cascader>
    <el-table :data="productList" ref="table" @selection-change="handleSelectionChange" height="450px" v-loading="loading" row-key="id">
      <el-table-column type="selection" width="35" align="center" :selectable="checkSelectable" reserve-selection> </el-table-column>
      <el-table-column prop="name" label="产品名称" :show-overflow-tooltip="true" min-width="180"> </el-table-column>
      <el-table-column prop="code" label="产品编码" :show-overflow-tooltip="true" min-width="150"> </el-table-column>
      <el-table-column prop="model" label="型号" min-width="150" :show-overflow-tooltip="true"> </el-table-column>
      <el-table-column prop="deviceTypeId" label="产品类型" :formatter="formatDeviceType" min-width="140" :show-overflow-tooltip="true">
      </el-table-column>
      <el-table-column
        prop="deviceSubTypeId"
        label="产品子类型"
        :formatter="formatDeviceSubType"
        min-width="140"
        :show-overflow-tooltip="true"
      ></el-table-column>
    </el-table>
    <pagination :total="productPage.total" :page.sync="productPage.current" :limit.sync="productPage.size" @pagination="getProductPageable" />
    <div slot="footer" class="dialog-footer">
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="handleOk">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import dlg from '@/mixins/dlg';
  import { getProductPageable, getAllProductTypes } from '@/api/iotControl';
  export default {
    props: {
      selectedProducts: {
        type: Array,
        default: () => [],
      },
    },
    mixins: [dlg],
    data() {
      return {
        title: '选择产品',
        productList: [],
        //加载状态
        loading: false,
        multipleSelection: [],
        productPage: {
          total: 0,
          current: 1,
          size: 20,
        },
        filter: {
          name: '',
          code: '',
          deviceType: [],
          model: '',
        },
        cascaderData: [],
        productTypeTree: [],
      };
    },
    created() {
      this.getProductPageable();
      getAllProductTypes()
        .then(({ data }) => {
          this.productTypeTree = data;
          this.cascaderData = data.map((item) => ({
            label: item.name,
            value: item.id,
            children: item.deviceSubTypes.map((subItem) => ({
              label: subItem.name,
              value: subItem.id,
            })),
          }));
        })
        .finally(() => {});
    },
    methods: {
      formatDeviceType(row) {
        return this.productTypeTree.find((item) => item.id === row.deviceTypeId)
          ? this.productTypeTree.find((item) => item.id === row.deviceTypeId).name
          : row.deviceTypeId;
      },
      formatDeviceSubType(row) {
        let deviceType = this.productTypeTree.find((item) => item.id === row.deviceTypeId);
        if (deviceType) {
          return deviceType.deviceSubTypes.find((item) => item.id === row.deviceSubTypeId)
            ? deviceType.deviceSubTypes.find((item) => item.id === row.deviceSubTypeId).name
            : row.deviceSubTypeId;
        } else {
          return row.deviceSubTypeId;
        }
      },
      /**
       * 获取产品列表
       */
      getProductPageable() {
        this.loading = true;
        getProductPageable({
          page: this.productPage.current,
          size: this.productPage.size,
          properties: 'id',
          direction: 'DESC',
          name: this.filter.name,
          code: this.filter.code,
          deviceSubTypeId: this.filter.deviceType[1],
          deviceTypeId: this.filter.deviceType[0],
          model: this.filter.model,
        })
          .then((res) => {
            this.productList = res.data.rows;
            this.productPage.total = res.data.total;
            this.setSelectedRows();
          })
          .finally(() => {
            this.loading = false;
          });
      },
      // 设置已选行
      setSelectedRows() {
        //注意：这里需要使用nextTick，否则无法选中
        this.$nextTick(() => {
          this.productList.forEach((row) => {
            if (this.selectedProducts.some((product) => product.productCode === row.code)) {
              this.$refs.table.toggleRowSelection(row, true);
            }
          });
        });
      },
      /**
       * table选中改变
       */
      handleSelectionChange(val) {
        this.multipleSelection = val;
      },
      /**
       * 确定
       */
      handleOk() {
        this.$emit('handleSelect', this.multipleSelection);
        this.close();
      },
      // 检查是否可选
      checkSelectable(row) {
        // 如果已经在已选列表中，则不可选
        return !this.selectedProducts.some((product) => product.productCode === row.code);
      },
    },
  };
</script>

<style scoped lang="scss"></style>
