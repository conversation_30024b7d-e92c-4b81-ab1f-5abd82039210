<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Document</title>
  <link rel="stylesheet" href="40e93b11cbd4174c60b22cba0d630300.css" />
  <link rel="stylesheet" href="133e6f6888709a53e833618d0cd0b864.css" />
  <style>
    .svg-icon {
      width: 1em;
      height: 1em;
      vertical-align: -0.15em;
      fill: currentColor;
      overflow: hidden;
    }

    ul {
      margin: 0;
      padding: 0;
    }

    ul li {
      list-style: none;
    }

    body,
    html {
      height: 100%;
      margin: 0;
    }

    .wrap {
      width: 100%;
      height: 100%;
      padding: 0 2%;
      box-sizing: border-box;
      overflow: hidden;
      color: white;
      background: rgb(215, 20, 24);
      position: relative;
    }

    .wrap .middle-wrap {
      width: 100%;
      height: 99%;
    }

    .middle-wrap .header-wrap {
      width: 100%;
      height: 12vh;
      padding-left: 2%;
      box-sizing: border-box;
      font-weight: bold;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .middle-wrap .header-wrap .title {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-size: 8vh;
    }

    .middle-wrap .header-wrap .qrcode-wrap {
      height: 100%;
      display: flex;
      /* flex-direction: ; */
      align-items: center;
      /* margin-top: 1vh; */
    }

    .middle-wrap .header-wrap .qrcode-wrap img {
      height: 10vh;
    }

    .middle-wrap .header-wrap .qrcode-wrap .desc {
      display: inline-block;
      border-radius: 1vmin;
      margin-top: 0.5vh;
      padding: 0.5vh;
      letter-spacing: 0.5vmin;
      box-sizing: border-box;
      font-size: 2vh;
      writing-mode: vertical-lr;
    }

    .middle-wrap .content-wrap {
      height: calc(100% - 12vh);
      padding-top: 2%;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
    }

    .content-wrap .top-wrap {
      height: 35%;
      box-sizing: border-box;
      overflow: hidden;
      border: 4px solid #fff;
      padding: 0 2% 1% 2%;
      border-radius: 9px;
    }

    .content-wrap .top-wrap .title {
      margin: 2vh 0 1.5vh;
      font-size: 3vh;
      font-weight: bold;
    }

    .content-wrap .top-wrap .empty-wrap {
      font-size: 6vh;
      line-height: 13vh;
      text-align: center;
    }

    .content-wrap .top-wrap .scroll-wrap {
      height: 20.16vh;
      overflow: hidden;
    }

    .content-wrap .top-wrap ul {
      width: 100%;
    }

    .content-wrap .top-wrap li {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      line-height: 1.8;
      font-size: 2.8vh;
    }

    .content-wrap .top-wrap li .left {
      display: flex;
      flex-direction: row;
      flex-wrap: nowrap;
      overflow: hidden;
    }

    .content-wrap .top-wrap li .left .time {
      padding-right: 1.5vmin;
      font-weight: bold;
      position: relative;
      flex-shrink: 0;
      /* width: 24vmin; */
    }

    .content-wrap .top-wrap li .left .theme {
      overflow: hidden;
      white-space: nowrap;
      display: flex;
      align-items: center;
    }

    .content-wrap .top-wrap li .right {
      flex-shrink: 0;
    }

    .content-wrap .bottom-wrap {
      /* height: calc(64% - 7vh); */
      flex: 1;
      box-sizing: border-box;
      padding: 8vh 0 4% 2%;
      display: flex;
    }

    .content-wrap .bottom-wrap .left-wrap {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }

    .content-wrap .bottom-wrap .cur-wrap {
      display: flex;
      flex-direction: column;
      height: 100%;
    }

    .content-wrap .bottom-wrap .status {
      position: relative;
      font-size: 4vh;
      /* padding-bottom: 3vh; */
      /* margin-bottom: 2vh; */
      letter-spacing: 0.3vmin;
      font-weight: bold;
    }

    .content-wrap .bottom-wrap .cur-wrap {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: flex-start;
    }

    .content-wrap .bottom-wrap .cur {
      font-size: 8vh;
      font-weight: bold;
      /* margin-bottom: 6vh; */
      letter-spacing: 0.3vmin;
      overflow: hidden;
      word-break: break-all;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .content-wrap .bottom-wrap .reservation {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      font-size: 4vh;
      margin-bottom: 1.5vh;
    }

    .content-wrap .bottom-wrap .reservation .title {
      display: inline-block;
      max-width: 17.5vh;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-left: 0.5vmin;
      margin-right: 1vmin;
      padding-right: 1vmin;
      border-right: 1px solid white;
    }

    .content-wrap>.empty-wrap {
      font-size: 8vh;
      height: calc(64% - 7vh);
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .bottom-wrap .right-wrap {
      align-self: flex-end;
      width: 29vh;
      border: 4px solid white;
      border-radius: 6px;
    }

    .bottom-wrap .right-wrap .title {
      font-size: 2.8vh;
      height: 4.5vh;
      line-height: 4.5vh;
      margin-left: 1vmin;
    }

    .bottom-wrap .right-wrap .empty-wrap {
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 3vh;
      height: 18vh;
      width: 100%;
    }

    .bottom-wrap .right-wrap .scroll-wrap {
      height: 18vh;
      overflow: hidden;
    }

    .bottom-wrap .right-wrap li {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      line-height: 1.8;
      font-size: 2.5vh;
      margin-right: 1vh;
    }

    .bottom-wrap .right-wrap .user-wrap {
      flex: 1;
      margin-right: 1vh;
      display: flex;
      align-items: center;
      overflow: hidden;
      white-space: nowrap;
    }

    .sigin-wrap {
      color: rgb(0, 176, 80);
    }

    .sigin {
      color: yellow;
    }

    .footer-wrap {
      height: 7vh;
      align-items: center;
    }

    .footer-wrap .time-line {
      display: flex;
      flex-direction: row;
      margin-bottom: 0.5vh;
      font-size: 2.5vh;
    }

    .footer-wrap .time-line .blocks {
      display: inline-block;
      text-align: left;
      width: 24%;
    }

    .footer-wrap .usage-wrap {
      display: flex;
      flex-direction: row;
      border-radius: 2vmin;
      overflow: hidden;
      width: 100%;
      position: relative;
      overflow: hidden;
      height: 3.5vmin;
    }

    .absolute-time-axis {
      position: absolute;
      height: 3.5vmin
    }

    [v-cloak] {
      display: none;
    }

  </style>
  <!-- vue -->
  <script src="174365807c06490c848d7b1d45fdc348.js"></script>
  <!-- jquery.js -->
  <script src="13c0a5055cca7b2463b2f73701960b9e.js"></script>
  <!-- mqtt -->
  <script src="d4bfcfa9de50875589eaab322a015441.js"></script>
  <!-- 文字横向滚动组件 -->
  <script src="59e4fb71c5c2fa1dba4969d59dcad854.js"></script>
  <!-- 列表向上滚动组件 -->
  <script src="c2cafcfcea4bfe9b3aafc8c8c03ea071.js"></script>
  <script src="page.js"></script>
  <script>
    // 将时间字符串转换为分钟数
    function convertToMinutes(time) {
      const [hours, minutes] = time.split(':');
      return parseInt(hours) * 60 + parseInt(minutes);
    }

    // 将分钟数转换为时间字符串
    function convertToTimeString(minutes) {
      const hours = Math.floor(minutes / 60);
      const mins = minutes % 60;
      return `${String(hours).padStart(2, '0')}:${String(mins).padStart(2, '0')}`;
    }

    /**
     * 根据一天内占用时间段，求解一天内未占用的时间段
     * @param {*} occupiedIntervals const occupiedIntervals = [['10:20', '11:00'],['16:20', '16:40'],['10:30', '10:45'],['10:30', '11:30']];
     * @returns [['00:00', '10:20'],['11:30', '16:20'],['16:40', '24:00']]
     */

    function findAvailableTimeIntervals(occupiedIntervals) {
      // 1、遍历 occupiedIntervals 可能会出现 ['10:00': '08:00'] 的情况，表示当天10:00 - 次日 08:00 这时需要将 ['10:00': '08:00'] 转化为 ['10:00', '24:00']

      let formateIntervals = [];
      occupiedIntervals.forEach(intervals => {
        const start = intervals[0];
        const end = intervals[1];
        const startNum = convertToMinutes(start);
        const endNum = convertToMinutes(end);
        // 表示有跨天，
        if (startNum > endNum) {
          intervals[1] = '24:00';
        }
        formateIntervals.push(intervals);
      });

      // 将时间区间转换为分钟数表示的区间
      const occupiedMinutes = formateIntervals.map((interval) => [convertToMinutes(interval[0]), convertToMinutes(
        interval[1])]);

      // 按照开始时间排序
      occupiedMinutes.sort((a, b) => a[0] - b[0]);
      // 寻找未占用的时间区间
      const availableIntervals = [];
      let lastEnd = 0;
      for (const interval of occupiedMinutes) {
        const start = interval[0];
        if (start > lastEnd) {
          availableIntervals.push([lastEnd, start]);
        }
        const end = interval[1];
        lastEnd = Math.max(lastEnd, end);
      }
      if (lastEnd < 24 * 60) {
        // 当天结束时间按照 23:59计算 不然会出现 当天0:00 到24:00 都是 00:00 - 00:00
        availableIntervals.push([lastEnd, 23 * 60 + 59]);
      }

      // 将分钟数表示的区间转换为时间字符串表示的区间
      const result = availableIntervals.map((interval) => [convertToTimeString(interval[0]), convertToTimeString(
        interval[1])]);
      return result;
    }

  </script>
</head>

<body>
  <div id="app" class="wrap" v-cloak :style="{
      backgroundColor:bConfs?style.backgroundActiveColor:style.backgroundColor,
      color:style.color}">
    <!-- <img src="./f64bbff3ab87e4d06fb387b50451ba61.png" alt="" /> -->
    <div class="middle-wrap" :style="{width:style.width}">
      <div class="header-wrap">
        <div class="title">
          {{conferenceRoom.roomName||terminalName}}
        </div>
        <div class="qrcode-wrap" v-if="style.bShowSign">
          <img :src="curConference.qrCode" alt="" />
          <span v-if="curConference.qrCode" class="desc">扫码签到</span>
        </div>
      </div>
      <div class="content-wrap">
        <div class="top-wrap">
          <div class="title">会议列表</div>
          <div v-if="!conferenceInfo.length" class="empty-wrap">无后续会议</div>
          <scroll-list v-else>
            <template v-slot:content="{curPage}">
              <li v-for="(item,index) in conferenceInfo" :key="item.confId" @click="clickConference(item)">
                <div class="left">
                  <div class="time">
                    {{item.period}}
                  </div>
                  <div class="theme">
                    <marquee :val="item.subject" :scroll="parseInt(index / 4) === curPage"></marquee>
                  </div>
                </div>
                <div class="right" v-if="style.bShowReserver">
                  {{item.nickname || item.contactPerson}}
                </div>
              </li>
            </template>
        </div>
        <div class="bottom-wrap" v-if="Object.keys(curConference).length">
          <div class="left-wrap">
            <div class="status">{{curConference.status}}</div>
            <div class="cur">{{curConference.subject}}</div>
            <div class="reservation">
              <div class="title" v-if="style.bShowReserver">{{curConference.nickname || curConference.contactPerson}}</div>
              <div class="time">{{curConference.period}}</div>
            </div>
          </div>
          <div class="right-wrap" v-if="style.bShowSign">
            <div class="title">签到情况</div>
            <div v-if="!curConference.participant.length" class="empty-wrap">无参会人</div>
            <scroll-list v-else>
              <template v-slot:content="{curPage}">
                <li v-for="(item,index) in curConference.participant" :key="item.username">
                  <div class="user-wrap">
                    <marquee :val="item.nickname || item.username" :scroll="parseInt(index / 4) === curPage"></marquee>
                  </div>
                  <div :class="[{sigin:item.signIn || item.signin},'sigin-wrap']">
                    {{item.signIn || item.signin ? '已签到' : '未签到'}}
                  </div>
                </li>
              </template>
            </scroll-list>
          </div>

        </div>
        <div v-else class="empty-wrap">当前无会议</div>
        <div class="footer-wrap" v-if="style.bShowTimeLine">
          <div class="time-line">
            <div class="blocks">0</div>
            <div class="blocks">03</div>
            <div class="blocks">06</div>
            <div class="blocks">09</div>
            <div class="blocks">12</div>
            <div class="blocks">15</div>
            <div class="blocks">18</div>
            <div class="blocks">21</div>
            <div class="midnight">24</div>
          </div>
          <div class="usage-wrap">
            <div v-for="item in timeAxis" class="absolute-time-axis" :key="item.key" :style="{
              width: `${item.width}%`,
              left: `${item.left}%`,
              'background-color':
                item.status === 0 ? status[0].color : status[1].color
            }"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>

<script>
  window.$page = page;
  const cssProperty = ["height", "width", "fontSize"];
  const app = new Vue({
    el: "#app",
    data: {
      props: page.props,
      mqttClient: null,
      mqttTopic: "",
      mac: null,
      curConference: {
        status: "正在会议中",
        subject: "第三季度经营情况分析大会",
        contactPerson: "刘勇",
        period: "2023/09/18 16:57-2023/09/18 17:27",
        periodArr: ["2021-03-04", "09:00-10:30"],
        qrCode: "./b947d49f014c663d02583f0f6cfc141f.png",
        participant: [{
            username: "张三丰",
            signIn: true
          },
          {
            username: "李四水",
            signIn: false
          },
          {
            username: "王五",
            signIn: false
          },
          {
            username: "刘勇",
            signIn: true
          },
          {
            username: "莹莹",
            signIn: false
          },
          {
            username: "于光",
            signIn: false
          }
        ]
      },
      conferenceList: [],
      conferenceInfo: [{
          confId: 1,
          contactPerson: "张三丰",
          participant: [{
            username: "张三丰",
            signIn: false
          }],
          period: "2023/09/18 16:57-2023/09/18 17:27",
          periodArr: ["2021-03-04", "11:00-12:00"],
          qrCode: "./7a8606304f019293184038e341f24d85.jpg",
          subject: "全国密码学与自主系统智能控制专题研讨会"
        },
        {
          confId: 2,
          contactPerson: "李四水",
          participant: [{
            username: "李四水",
            signIn: false
          }],
          period: "2023/09/18 16:57-2023/09/18 17:27",
          periodArr: ["2021-03-04", "12:30-13:30"],
          qrCode: "./7a8606304f019293184038e341f24d85.jpg",
          subject: "智能计算及应用国际会议"
        },
        {
          confId: 4,
          contactPerson: "刘小勇",
          participant: [{
            username: "刘小勇",
            signIn: false
          }],
          period: "2023/09/18 16:57-2023/09/18 17:27",
          periodArr: ["2021-03-04", "14:00-15:00"],
          qrCode: "./7a8606304f019293184038e341f24d85.jpg",
          subject: "干旱气候变化与可持续性发展学术研讨会"
        },
        {
          confId: 5,
          contactPerson: "方莹莹",
          participant: [{
            username: "方莹莹",
            signIn: false
          }],
          period: "2023/09/18 16:57-2023/09/18 17:27",
          periodArr: ["2021-03-04", "15:20-16:00"],
          qrCode: "./7a8606304f019293184038e341f24d85.jpg",
          subject: "中国IT、网络、信息技术、电子、仪器仪表创新学术会议"
        },
        {
          confId: 3,
          contactPerson: "王五",
          participant: [{
            username: "王五",
            signIn: false
          }],
          period: "2023/09/18 16:57-2023/09/18 17:27",
          periodArr: ["2021-03-04", "17:30-19:00"],
          qrCode: "./7a8606304f019293184038e341f24d85.jpg",
          subject: "国际气体净化技术研讨会"
        },
        {
          confId: 6,
          contactPerson: "余秋水",
          participant: [{
            username: "余秋水",
            signIn: false
          }],
          period: "2023/09/18 16:57-2023/09/18 17:27",
          periodArr: ["2021-03-04", "20:00-22:00"],
          qrCode: "./7a8606304f019293184038e341f24d85.jpg",
          subject: "图像处理与模式识别在工业工程中的应用国际学术研讨会"
        }
      ],
      conferenceRoom: {
        roomName: "A217陆家嘴会议室"
      },
      terminalName: "",
      // 会议室状态，0为空闲，1为占用
      status: [{
          label: "空闲中",
          color: "#2c9678"
        },
        {
          label: "已预约",
          color: "yellow"
        }
      ],
      timeAxis: [{
        key: Math.random(),
        width: "100%",
        status: 0
      }],
      timer: null,
      statusTimer: null,
      mqtt: {}
    },
    computed: {
      style() {
        let style = {};
        for (let prop of this.props) {
          if (cssProperty.includes(prop.field)) {
            style[prop.field] = prop.value + "%"
          } else {
            style[prop.field] = prop.value
          }
        }
        return style;
      },
      bConfs() {
        if (Object.keys(this.curConference).length) {
          return true
        } else {
          return false
        }
      }
    },
    created() {
      if (!this.isWindows()) {
        this.curConference = {};
        this.conferenceInfo = [];
        this.conferenceRoom = {
          roomName: ""
        };
        const {
          mac,
          name
        } = this.getTerminalInfo();
        if (mac) {
          this.mac = mac;
          this.terminalName = name
          this.conferenceRoom = {
            roomName: ""
          }
          //初始化终端灯状态
          this.setTerminalStatus(0);
          //刷新终端灯的状态
          this.statusTimer && clearInterval(this.statusTimer);
          this.statusTimer = setInterval(this.refreshTerminalStatus, 5000);
        } else {
          this.log("终端信息获取失败");
        }
        let mqtt = this.getMqtt();
        if (mqtt) {
          this.mqtt = mqtt;
          this.getMqttServerAndConnect(mqtt);
        } else {
          this.log("mqtt地址获取失败");
        }
      } else {
        this.initTimeline()
      }
      // let ws = "wss://rjms.putian-nst.com:443/mqtt/";
      // let username = "admin";
      // let password = "mqttServer";
      // let ws = "ws://218.94.61.65:8083/mqtt";
      // let username = "admin";
      // let password = "mqttServer";
      // let ws = "ws://192.168.89.153:8083/mqtt";
      // let username = "admin";
      // let password = "admin";
      // let ws = "ws://192.168.88.121:8080/mqtt";
      // let username = "admin";
      // let password = "";
      // let ws = "ws://192.168.90.1:8080/mqtt";
      // let username = "admin";
      // let password = "nst@aliyun";
      // this.getMqttServerAndConnect({ ws, username, password });
    },
    beforeDestory() {
      //关闭会议室状态
      this.setTerminalStatus(-1);
      this.mqttClose();
      this.IntervalClose();
    },
    mounted() {
      window["update"] = (val, mqtt = null) => {
        this.updateProps(val);
      };

      window["updateMqtt"] = param => {
        this.updateMqtt(param);
      };
    },
    methods: {
      initTimeline() {
        let confs = [this.curConference, ...this.conferenceInfo]
        let timeArr = confs.map(item => {
          let time = item.periodArr[1].split("-");
          startTimeArr = time[0].split(":").map(Number);
          endTimeArr = time[1].split(":").map(Number);
          return {
            startTimeArr,
            endTimeArr
          };
        });
        this.timeAxis = this.convertTimes2Pulses(timeArr);
      },
      updateMqtt(mqtt) {
        if (!mqtt) {
          return;
        }
        mqtt = JSON.parse(mqtt);

        if (
          (this.mqttClient && this.mqtt.ws !== mqtt.ws) ||
          (!this.mqttClient && !Object.keys(this.mqtt).length)
        ) {
          this.log("mqtt重连");
          this.mqttClose();
          this.curConference = {};
          this.conferenceInfo = [];
          this.conferenceRoom = {
            roomName: ""
          };
          this.mqtt = mqtt;
          this.getMqttServerAndConnect(mqtt);
        }
      },
      /*
       * 当前运行环境是终端还是pc
       */
      isWindows() {
        return window.DSS20AndroidJS === undefined;
      },
      /*
       * state -1:关闭 0:空闲 1:会议中 2:签到
       */
      setTerminalStatus(state) {
        if (window.DSS20AndroidJS) {
          // -1 关闭
          if (state === -1) {
            window.DSS20AndroidJS.updateLedStatusByWeb(0);
          }

          // 状态灯设置
          let temp = this.getProp("statusLight");
          if (temp) {
            // 开启
            if (temp.value) {
              let fieldKey = "";
              if (state === 0) {
                // 空闲 0
                fieldKey = "freeLightColor";
              } else if (state === 1) {
                // 会中 1
                fieldKey = "meetingLightColor";
              } else if (state === 2) {
                // 签到 2
                fieldKey = "signLightColor";
              }
              if (fieldKey) {
                let tempObj = this.getProp(fieldKey);
                if (tempObj) {
                  let lightVal = tempObj.value;
                  lightVal = Number(lightVal);
                  window.DSS20AndroidJS.updateLedStatusByWeb(lightVal);
                }
              }
            } else {
              // 关闭状态灯
              window.DSS20AndroidJS.updateLedStatusByWeb(0);
            }
          }
        }
      },
      /*
       * 终端打印
       */
      log(msg) {
        window.DSS20AndroidJS &&
          window.DSS20AndroidJS.htmlLogcat("会议指引：" + msg);
      },
      clickConference(item) {
        this.log('conferenceInfo')
        window.DSS20AndroidJS &&
          window.DSS20AndroidJS.conferenceInfo(JSON.stringify(item));
      },
      getTerminalInfo() {
        var data = window.DSS20AndroidJS.terminalInfo();
        var info = JSON.parse(data);
        return info;
      },
      getMqtt() {
        var data = window.DSS20AndroidJS.getWebMqttWs();
        var info = {};
        try {
          info = data && JSON.parse(JSON.parse(data));
        } catch (err) {
          this.log("mqtt地址解析失败！");
        }

        return info;
      },
      updateProps(props) {
        for (let prop of props) {
          let index = this.getPropIndex(prop.field);
          if (index !== -1) {
            let data = this.props[index];
            data.value = prop.value;
            this.$set(this.props, index, data);
          }
        }
      },
      getPropIndex(name) {
        for (let i = 0; i < this.props.length; i++) {
          if (this.props[i].field === name) {
            return i;
          }
        }
        return -1;
      },
      /**
       * 更新数据
       */
      updateConfByTime() {
        if (!this.conferenceList.length && this.isWindows()) {
          return;
        }
        const cur = +new Date();

        let bProcessing = false,
          siginConferenceList = [];
        let res = this.conferenceList.filter(item => {
          if (item.approved !== 4) {
            return false;
          }
          const {
            start,
            end
          } = item;
          if (end < cur) {
            return false;
          } else if (start <= cur && end >= cur) {
            bProcessing = true;
          } else if (start - cur <= 15 * 1000 * 60) {
            siginConferenceList.push(item);
          }
          return true;
        });
        //更新会议信息
        if (bProcessing) {
          this.curConference = {
            ...res.shift(),
            status: "正在会议中"
          };
        } else if (siginConferenceList.length) {
          this.curConference = {
            ...siginConferenceList[0],
            status: "签到中"
          };
        } else {
          this.curConference = {};
        }

        this.conferenceInfo = res;

        // 这里需要过滤出时间是当天的会议 支持跨天
        const tempConfList = JSON.parse(JSON.stringify(this.conferenceList));
        // 需要针对跨天进行处理
        tempConfList.forEach(item => {
          // 创建一个新的日期对象
          const now = new Date();
          // 设置时间为凌晨 00:00:00
          now.setHours(0, 0, 0, 0);
          const todayStartTimestamp = now.getTime();
          const todayEndTimestamp = todayStartTimestamp + 1000 * 3600 * 24;
          // 开始时间在今天，结束时间不在今天 endTimeArr => [24, 0]
          if (item.start >= todayStartTimestamp && item.start <= todayEndTimestamp && item.end >=
            todayEndTimestamp) {
            item.endTimeArr = [24, 0];
            item.bInToday = true;
          }

          // 开始时间不在今天、结束时间在今天 startTimeArr => [0, 0]
          if (item.start <= todayStartTimestamp && item.end >= todayStartTimestamp && item.end <=
            todayEndTimestamp) {
            item.startTimeArr = [0, 0];
            item.bInToday = true;
          }

          // 时间包括当天 开始时间小于当天0：00 结束时间大于 当天23：59
          if (item.start <= todayStartTimestamp && item.end >= todayEndTimestamp) {
            item.startTimeArr = [0, 0];
            item.endTimeArr = [24, 0];
            item.bInToday = true;
          }

          // 开始时间在今天 结束时间在今天
          if (item.start >= todayStartTimestamp && item.end <= todayEndTimestamp) {
            item.bInToday = true;
          }
        });


        let timeArr = tempConfList.filter(item => item.bInToday).map(item => {
          return {
            startTimeArr: item.startTimeArr,
            endTimeArr: item.endTimeArr
          };
        });
        this.timeAxis = this.convertTimes2Pulses(timeArr);
      },
      /**
       * 转换后端返回的会议时间段
       * @param times
       */
      convertTimes2Pulses(times = []) {
        let pulses = [];
        console.log(times)

        // 放入已被占用的时间段
        for (let occupied of times) {
          const {
            startTimeArr,
            endTimeArr
          } = occupied;
          // 起止时间横坐标
          let x0 = this.getCoordinateX(startTimeArr);
          let x1 = this.getCoordinateX(endTimeArr);
          let width = this.calculateWidth(x0, x1);
          let pulse = {
            period: occupied.period,
            // 宽度百分比，24小时共1440分钟，作为总宽度
            width,
            status: 1,
            // 左边距百分比
            left: Number(((x0 / 1440) * 100).toFixed(2)),
            x0: x0,
            x1: x1,
            key: Math.random(),
            dateRange: [startTimeArr.join(':'), endTimeArr.join(':')]
          };
          pulses.push(pulse);
        }

        // 补全没有被占用的时间段
        let result = [];
        if (pulses.length === 0) {
          // 全天没有会议
          result.push({
            period: "0:00-23:59",
            width: 100,
            status: 0,
            x0: 0,
            left: 0,
            x1: 1440,
            key: Math.random()
          });
        } else {
          // 需要找出空余的时间段区间 @update by Gong
          let occupiedIntervals = [];
          pulses.forEach((item) => {
            occupiedIntervals.push(item.dateRange);
            result.push(item);
          });

          try {
            let avaliableIntervals = findAvailableTimeIntervals(occupiedIntervals);
            // exp : [['00:00', '10:20'],['11:30', '16:20'],['16:40', '24:00']]
            avaliableIntervals.forEach((item) => {
              result.push({
                period: item.join('-'),
                width: ((convertToMinutes(item[1]) - convertToMinutes(item[0])) / 1440) *
                100, // 转换为分钟数 宽度百分比
                left: (convertToMinutes(item[0]) / 1440) * 100, // 左边界百分比
                status: 0,
                x0: convertToMinutes(item[0]),
                x1: convertToMinutes(item[1]),
                key: Math.random(),
              });
            });
          } catch (error) {
            // ignore error
          }

          // 放入原点
          // result.push({
          //   period: "0:00-0:00",
          //   width: 0,
          //   status: 0,
          //   x0: 0,
          //   x1: 0,
          //   key: Math.random()
          // });
          // pulses.forEach((pulse, index) => {
          //   let pre = result[result.length - 1];
          //   if (pulse.x0 !== pre.x1) {
          //     // 与前一段有间隔，补上空白时间
          //     result.push({
          //       width: this.calculateWidth(pre.x1, pulse.x0),
          //       status: 0,
          //       x0: pulse.x0,
          //       x1: pre.x1,
          //       key: Math.random()
          //     });
          //   }
          //   result.push(pulse);

          //   if (index === pulses.length - 1 && pulse.x1 !== 1440) {
          //     // 最后一个元素，检查是否要在结尾追加
          //     result.push({
          //       width: this.calculateWidth(pulse.x1, 1440),
          //       status: 0,
          //       x0: pulse.x1,
          //       x1: 1440,
          //       key: Math.random()
          //     });
          //   }
          // });
          // // 删除原点
          // result.shift();
        }
        return result;
      },
      /**
       * 根据坐标算出宽度
       * @param coordinateX0 开始时间坐标
       * @param coordinateX1 结束时间坐标
       * @return string 时间段宽度占比，e.g. 10%
       */
      calculateWidth(coordinateX0, coordinateX1) {
        return Number((((coordinateX1 - coordinateX0) / 1440) * 100).toFixed(2));
      },

      /**
       * 获取时间点在一天中的横坐标，刻度为分钟
       * @param time e.g. 8:00
       */
      getCoordinateX(timeSplits) {
        let hour = parseInt(timeSplits[0]);
        let minute = parseInt(timeSplits[1]);
        // 起点横坐标
        return hour * 60 + minute;
      },
      //刷新终端灯的状态
      refreshTerminalStatus() {
        // 空闲
        let state = 0;

        // 会中
        if (
          Object.keys(this.curConference).length &&
          this.curConference.status === "正在会议中"
        ) {
          state = 1;
        }

        // 签到
        if (Object.keys(this.curConference).length && this.curConference.status === '签到中') {
          state = 2;
        }
        this.setTerminalStatus(state);
      },
      /**
       * 从 prop 中获取 val
       */
      getProp(field) {
        for (let prop of this.props) {
          if (prop.field == field) {
            return prop;
          }
        }
        return null;
      },
      /**
       * 随机ID
       * @param {*} len
       * @param {*} radix
       * @returns
       */
      randomId(len, radix) {
        var chars =
          '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('')
        var uuid = [],
          i
        radix = radix || chars.length
        if (len) {
          // Compact form
          for (i = 0; i < len; i++) uuid[i] = chars[0 | (Math.random() * radix)]
        } else {
          // rfc4122, version 4 form
          var r
          // rfc4122 requires these characters
          uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-'
          uuid[14] = '4'
          // Fill in random data.  At i==19 set the high bits of clock sequence as
          // per rfc4122, sec. 4.1.5
          for (i = 0; i < 36; i++) {
            if (!uuid[i]) {
              r = 0 | (Math.random() * 16)
              uuid[i] = chars[i == 19 ? (r & 0x3) | 0x8 : r]
            }
          }
        }
        return uuid.join('')
      },
      /**
       * 连接到MQTT服务器并订阅
       */
      getMqttServerAndConnect(param) {
        this.log("mqtt参数：" + JSON.stringify(param));

        const that = this;
        let {
          ws,
          username,
          password
        } = param;
        let mac = this.mac || ''
        // 客户端ID
        let clientId = `js_conferenceGuide4_${mac}_${this.randomId(16, 16)}`;

        that.mqttClient = mqtt.connect(ws, {
          clientId,
          username,
          password,
          clean: true,
          connectTimeout: 5 * 1000,
          keepalive: 30
        });
        that.mqttClient.on("connect", () => {
          this.log("mqtt连接成功");
          this.mqttRefreshSubscribe();
        });
        that.mqttClient.on("error", error => {
          this.log("mqtt连接失败：" + JSON.stringify(error));
          this.conferenceRoom = {};
          this.curConference = {};
          this.conferenceInfo = [];
        });
        //监听接收消息事件
        that.mqttClient.on("message", (topic, message) => {
          message = JSON.parse(message.toString());
          console.log(message)
          this.log("获取mqtt消息" + JSON.stringify(message));
          const {
            meetingroom,
            confDetailDTOList
          } = message;
          this.conferenceRoom = meetingroom;
          this.conferenceList = confDetailDTOList.map(item => {
            return this.getFormatData(item)
          });
          this.updateConfByTime();
        });
      },
      getFormatData(item) {
        let periodArr = item.period.split("-");
        let start, end, startTimeArr, endTimeArr
        // 获取startTimeArr、endTimeArr （[hh,mm]）
        // 会议可能跨天
        if (item.start && item.end) {
          start = item.start
          end = item.end

          const startTime = new Date(start)
          startTimeArr = [startTime.getHours(), startTime.getMinutes()]

          let curEndTime = new Date(new Date().setHours(23, 59, 59, 999)).getTime()
          let endTime = end > curEndTime ? new Date(curEndTime) : new Date(end)
          endTimeArr = [endTime.getHours(), endTime.getMinutes()]
        } else {
          // 获取start、end (时间戳)
          // 兼容之前的写法（会议不可能跨天）
          let timeArr = periodArr[1].split("-");
          startTimeArr = timeArr[0].split(":").map(Number);
          endTimeArr = timeArr[1].split(":").map(Number);

          start = new Date(new Date().setHours(startTimeArr[0], startTimeArr[1])).getTime()
          end = new Date(new Date().setHours(endTimeArr[0], endTimeArr[1])).getTime()
        }
        return {
          period: item.period,
          periodArr,
          start,
          end,
          startTimeArr,
          endTimeArr,
          confId: item.confId,
          subject: item.subject,
          nickname: item.nickname,
          contactPerson: item.contactPerson,
          participant: item.participant,
          approved: item.approved,
          qrCode: item.qrCode
        }
      },

      /**
       * 发布MQTT主题
       */
      mqttPublish(address) {
        let topic = "dss2/terminal/conference";
        let message = `{"command":"WEATHER","parameters":{${param}:"${address}"}}`;
        this.log("mqtt发布主题: " + message);
        this.mqttClient.publish(
          topic,
          message, {
            qos: 1,
            retain: true
          },
          (err, res) => {
            if (err) {
              this.log("mqtt发布主题失败：", err);
              return;
            }
            this.mqttRefreshSubscribe(address);
          }
        );
      },
      /**
       * 订阅MQTT主题
       */
      mqttRefreshSubscribe() {
        // this.mac = "00073D9001D7";
        // this.mac = "00073D900053";
        const that = this;
        if (that.mqttTopic) {
          that.mqttClient.unsubscribe(that.mqttTopic);
        }
        that.mqttTopic = `dss2/web/conference/mac/${this.mac}`;
        this.log("mqtt订阅主题：" + that.mqttTopic);
        that.mqttClient.subscribe(
          that.mqttTopic, {
            qos: 1,
            rap: true
          },
          (err, res) => {
            if (err) {
              this.log("mqtt订阅主题失败：", err);
              return;
            }

            //刷新数据
            that.timer && clearInterval(that.timer);
            that.timer = setInterval(() => {
              that.updateConfByTime();
            }, 60000);
          }
        );
      },
      /**
       * 释放MQTT客户端
       */
      mqttClose() {
        this.mqtt = {};
        if (this.mqttClient) {
          this.mqttClient.unsubscribe(this.mqttTopic);
          this.mqttClient.end();
        }
      },
      IntervalClose() {
        this.timer && clearInterval(this.timer);
        this.statusTimer && clearInterval(this.statusTimer);
      }
    }
  });

</script>

</html>
