<template>
  <div class="send-sms-test">
    <el-dialog
      :title="`测试发送短信 - ${smsConfig.companyName || ''}`"
      :visible.sync="visible"
      width="30%"
      :close-on-click-modal="false"
    >
      <el-form :model="formData" :rules="rules" ref="ruleForm" label-width="100px">
        <el-form-item label="短信平台">
          <el-input size="small" :value="smsConfig.platform" disabled />
        </el-form-item>
        <el-form-item label="短信签名">
          <el-input size="small" :value="smsConfig.signName" disabled />
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input
            size="small"
            placeholder="请输入手机号"
            v-model="formData.phone"
          />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="doSend('ruleForm')">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "sendSmsTest",

  data() {
    // 校验手机号
    const checkPhone = (rule, value, callback) => {
      const phoneReg = /^1[3-9]\d{9}$/
      if (!value) {
        return callback(new Error('手机号不能为空'))
      }
      setTimeout(() => {
        if (phoneReg.test(value)) {
          callback()
        } else {
          callback(new Error('请输入正确的手机号格式'))
        }
      }, 100)
    }

    return {
      // 弹窗是否可见
      visible: false,
      // 表单数据
      formData: {
        phone: ''
      },
      // 表单校验
      rules: {
        phone: [
          { required: true, message: "请输入手机号", trigger: "blur" },
          { validator: checkPhone, trigger: "blur" },
        ],
      },
      // 公司ID
      smsConfig: null
    }
  },

  methods: {
    /**
     * 显示对话框
     */
    showDialog(smsConfig) {
      this.smsConfig = smsConfig
      this.visible = true
      this.formData.phone = ''
      if (this.$refs.ruleForm) {
        this.$refs.ruleForm.clearValidate()
      }
    },

    /**
     * 关闭对话框
     */
    closeDialog() {
      this.visible = false
      this.formData.phone = ''
      if (this.$refs.ruleForm) {
        this.$refs.ruleForm.clearValidate()
      }
    },

    /**
     * 测试发送
     */
    doSend(formName) {
      this.$refs[formName].validate(valid => {
        if(valid) {
          this.$emit("beforeSend")
          // 模拟发送操作
          setTimeout(() => {
            this.$message.success(`已向 ${this.formData.phone} 发送测试短信`)
            this.$emit("afterSend")
            this.closeDialog()
          }, 1000)
        }
      })
    }
  }
}
</script>

<style scoped>

</style>
