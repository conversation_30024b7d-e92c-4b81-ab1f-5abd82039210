<template>
  <div class="alarm-record-detail-wrap">
    <el-page-header @back="$router.go(-1)">
      <template slot="content">
        {{ alarmRecordDetail.name }}
      </template>
    </el-page-header>
    <el-descriptions :column="1" :labelStyle="{ width: '150px' }" size="medium" class="alarm-record-detail" border>
      <el-descriptions-item label="告警名称">{{ alarmRecordDetail.name }}</el-descriptions-item>
      <el-descriptions-item label="告警级别">
        <el-tag :type="getAlarmLevelTagType(alarmRecordDetail)" v-if="alarmRecordDetail.type === 'platform_automation_alarm'">
          {{ formatRuleLevel(alarmRecordDetail) }}
        </el-tag>
        <span v-else>--</span>
      </el-descriptions-item>
      <el-descriptions-item label="告警类别">
        <el-tag :type="getAlarmTypeTag(alarmRecordDetail)" v-if="alarmRecordDetail.type">
          {{ ALARM_ENUM.find((item) => alarmRecordDetail.type === item.value).label }}
        </el-tag>
        <el-button
          class="view-rule-btn"
          size="mini"
          type="primary"
          @click="viewRule()"
          plain
          v-if="alarmRecordDetail.type === 'platform_automation_alarm'"
          >查看告警规则</el-button
        >
      </el-descriptions-item>
      <el-descriptions-item label="所属空间">{{
        alarmRecordDetail.spaceList ? alarmRecordDetail.spaceList.map((item) => item.spaceName).join(',') : '--'
      }}</el-descriptions-item>
      <el-descriptions-item label="告警设备">
        {{ alarmRecordDetail.deviceList ? alarmRecordDetail.deviceList.map((item) => item.deviceName).join(',') : '--' }}</el-descriptions-item
      >
      <el-descriptions-item label="告警描述">
        <span style="white-space: pre-wrap">{{ alarmRecordDetail.description }}</span>
      </el-descriptions-item>
      <el-descriptions-item label="是否确认">
        <svg-icon icon-class="circle" :style="{ color: alarmRecordDetail.confirmed ? '#1ba784' : '#fb8b05' }" class="circle-icon" />
        <span> {{ alarmRecordDetail.confirmed ? '已确认' : '未确认' }}</span>
      </el-descriptions-item>
      <el-descriptions-item label="确认人"> {{ alarmRecordDetail.confirmName ? alarmRecordDetail.confirmName : '--' }}</el-descriptions-item>
      <el-descriptions-item label="告警时间">
        {{ alarmRecordDetail.alarmTime ? new Date(alarmRecordDetail.alarmTime).Format('yyyy-MM-dd hh:mm') : '--' }}</el-descriptions-item
      >
    </el-descriptions>
    <div class="btn-wrap">
      <el-button class="mr-10" type="primary" @click="confirmRecord()" plain v-if="!alarmRecordDetail.confirmed && showConfirmBtn">确认</el-button>
      <el-button class="mr-10" type="danger" @click="deleteRecord()" plain v-if="showDeleteBtn">删除</el-button>
    </div>
  </div>
</template>

<script>
  import { ALARM_LEVEL, ALARM_ENUM } from '../../enum';
  import { getAlarmRecordDetail, deleteAlarmRecord, confirmAlarm } from '@/api/iotControl';
  import { hasPermission } from '@/directive/permission/index.js';

  export default {
    props: {},
    data() {
      return {
        ALARM_LEVEL,
        ALARM_ENUM,
        alarmRecordDetail: {},
        alarmRecordId: '',
      };
    },
    created() {
      this.alarmRecordId = this.$route.params.id;
      this.getAlarmRecordDetail();
    },
    methods: {
      getAlarmRecordDetail() {
        getAlarmRecordDetail(this.alarmRecordId).then(({ data }) => {
          this.alarmRecordDetail = data;
        });
      },
      formatRuleLevel(row) {
        let level = this.ALARM_LEVEL.find((item) => item.value === row.level);
        if (level) {
          return level.label;
        }
        return '--';
      },
      getAlarmLevelTagType(row) {
        if (row.level == 'ERROR') {
          return 'info';
        } else if (row.level == 'WARN') {
          return 'warning';
        } else if (row.level == 'FATAL') {
          return 'danger';
        }
      },
      getAlarmTypeTag(row) {
        if (row.type == 'platform_automation_alarm') {
          return '';
        } else if (row.type == 'device_alarm_event') {
          return 'danger';
        }
      },
      doCancle() {
        this.$router.go(-1);
      },
      confirmRecord() {
        confirmAlarm([this.alarmRecordId]).then(() => {
          this.$message.success('操作成功!');
          this.getAlarmRecordDetail();
        });
      },
      deleteRecord() {
        this.$confirm('确定要删除当前告警记录吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          deleteAlarmRecord([this.alarmRecordId]).then(() => {
            this.$message.success('删除成功!');
            this.$router.go(-1);
          });
        });
      },
      viewRule() {
        this.$router.push({ name: 'editAlarmRule', params: { id: this.alarmRecordDetail.alarmId } });
      },
    },
    watch: {
      //监测路由变化
      $route: function (to, from) {
        if (to.path !== from.path) {
          this.alarmRecordId = this.$route.params.id;
          this.getAlarmRecordDetail();
        }
      },
    },
    computed: {
      showConfirmBtn() {
        return hasPermission(undefined, 'IotAlarmCenter', 'confirmAlarm');
      },
      showDeleteBtn() {
        return hasPermission(undefined, 'IotAlarmCenter', 'deleteAlarmRule');
      },
    },
  };
</script>
<style lang="scss" scoped>
  .alarm-record-detail-wrap {
    margin-top: 20px;
    margin-left: 10px;
  }
  .el-page-header {
    background-color: #f5f7fa;
    line-height: 40px;
    padding: 5px 18px;
    border-top: 1px solid #ebeef5;
    border-left: 1px solid #ebeef5;
    border-right: 1px solid #ebeef5;
    ::v-deep {
      .el-page-header__left {
        width: 70px;
        align-items: center;
      }
      .el-page-header__title {
        width: 30px;
      }
      .el-page-header__title,
      .el-icon-back,
      .el-page-header__content,
      .el-icon-edit-outline {
        color: rgb(60, 67, 83);
        font-weight: bold;
      }
      .el-page-header__left:hover {
        .el-page-header__title,
        .el-icon-back {
          color: #409eff;
        }
      }
    }
  }
  .alarm-record-detail {
    user-select: text;
  }
  ::v-deep .el-descriptions-item__cell {
    line-height: 2 !important;
  }
  .btn-wrap {
    margin: 20px 0;
  }
  .view-rule-btn {
    margin-left: 20px;
  }
</style>
