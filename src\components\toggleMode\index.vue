<template>
  <div>
    <el-radio-group v-model="currentMode" size="mini">
      <el-radio-button v-for="(item,index) in modeOptions" :key="index" :label="item">
        <el-tooltip
          class="item"
          effect="dark"
          :content="MODE[item].title"
          placement="bottom"
        >
          <svg-icon :icon-class="MODE[item].icon"></svg-icon>
        </el-tooltip>
      </el-radio-button>
    </el-radio-group>
  </div>
</template>

<script>
const MODE = {
  list: {
    title: '表格视图',
    icon: 'list',
  },
  card: {
    title: '卡片视图',
    icon: 'card',
  },
  gplot: {
    title: '拓扑图',
    icon: 'gplot',
  },
}
export default {
  props: {
    active: {
      type: String,
      default: 'card',
    },
    modeOptions: {
      type: Array,
      default: () => (['list', 'card']),
    },
  },
  data() {
    return {
      MODE
    }
  },
  computed: {
    currentMode: {
      get() {
        return this.active
      },
      set(val) {
        this.$emit('toggle', val)
      },
    },
  },
  methods: {
  },
}
</script>
<style lang="scss" scoped>
.svg-icon {
  font-size: 17px;
  cursor: pointer;
}
</style>
