<template>
  <div class="">
    <el-select
      v-model="active"
      placeholder="请选择"
      :style="{ '--BgColor': BgColor, '--Color': Color }"
    >
      <el-option
        v-for="item in Options"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      >
        <div :style="item.style" class="select-item">{{ item.label }}</div>
      </el-option>
    </el-select>
  </div>
</template>

<script>
export default {
  props: {
    //预定义
    value: String | Array,
    options: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return { };
  },
  computed: {
    Options() {
      return this.options.map(item => {
        return {
          ...item,
          style: JSON.parse(item.style)
        };
      });
    },
    active: {
      get() {
        return this.value;
      },
      set(newValue) {
        //当组件值发生变化后,通过 input 事件更新值
        this.$emit("input", newValue);
      }
    },
    BgColor() {
      let res = this.Options.find(item => item.value === this.active);
      return (res && res.style && res.style.backgroundColor) || "";
    },
    Color() {
      let res = this.Options.find(item => item.value === this.active);
      return (res && res.style && res.style.color) || "";
    }
  }
};
</script>
<style lang="scss" scoped>
::v-deep .el-input__inner {
  background-color: var(--BgColor);
  color: var(--Color);
}
::v-deep.el-select .el-input .el-select__caret {
  color: var(--Color);
}
.el-select-dropdown__item {
  padding: 0;
  .select-item {
    padding: 0 20px;
  }
}
</style>
