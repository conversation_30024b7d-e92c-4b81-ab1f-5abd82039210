<template>
  <div>
    <div v-for="item in SCREEN_ENUM" :key="item.key" class="screen-wrap">
      <div class="title">{{ item.key }}</div>
      <div class="img-wrap">
        <img
          v-for="item in item.layout"
          :key="item.value"
          :src="require(`./img/${item.img}`)"
          alt=""
          @click="changeSelect(item)"
          :class="{ active: active === item.value }"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { SCREEN_ENUM } from "./enum";
export default {
  props: {
    //预定义
    value: String | Array
  },
  data() {
    return { SCREEN_ENUM };
  },
  computed: {
    active: {
      get() {
        return this.value;
      },
      set(newValue) {
        //当组件值发生变化后,通过 input 事件更新值
        this.$emit("input", newValue);
      }
    }
  },
  methods: {
    changeSelect(item) {
      if (this.active === item.value) {
        return;
      }
      this.active = item.value;
    }
  }
};
</script>
<style lang="scss" scoped>
.screen-wrap {
  display: flex;
  flex-direction: row;
  align-items: center;
  .title {
    width: 20px;
  }
  .img-wrap {
    margin-left: 10px;
    display: flex;
    flex-direction: row;
    img {
      padding: 5px;
      margin-right: 10px;
      cursor: pointer;
      &.active {
        background-color: #f81919;
      }
    }
  }
}
</style>
