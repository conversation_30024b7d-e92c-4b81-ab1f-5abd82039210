<template>
  <div id="materialList">
    <el-dialog
      :title="element.label || '素材'"
      :visible.sync="bDlgMaterialVisible"
      width="870px"
      :close-on-click-modal="false"
      @close="closeDialog"
      append-to-body
    >
      <div class="search-wrap">
        <el-input
          placeholder="素材名称"
          suffix-icon="el-icon-search"
          v-model="szInputMaterialName"
          v-debounce="[e=>{pagination.curPage = 1;getSearchList(e);}]"
          size="small"
        ></el-input>
        <div class="filter-wrap">
          <div class="search" v-if="!bSelectSingle">
            <el-checkbox v-model="checkAll" @change="handleCheckAll"
              >全选</el-checkbox
            >
          </div>
          <div class="search" @click="sortChange('name')">
            名称<span
              :class="{
                'caret-wrapper': true,
                ascending: nameSort === 0,
                descending: nameSort === 1
              }"
              ><i class="sort-caret ascending"></i
              ><i class="sort-caret descending"></i
            ></span>
          </div>
          <div class="search" @click="sortChange('createTime')">
            创建时间<span
              :class="{
                'caret-wrapper': true,
                ascending: createTimeSort === 0,
                descending: createTimeSort === 1
              }"
              ><i class="sort-caret ascending"></i
              ><i class="sort-caret descending"></i
            ></span>
          </div>
        </div>
      </div>
      <div class="picture-area">
        <div class="material-tree">
          <el-tree
            ref="tree"
            highlight-current
            node-key="id"
            :props="{ label: 'name' }"
            :load="loadNode"
            @node-click="nodeClick"
            :default-expanded-keys="defaultExpandKeys"
            lazy
          >
            <div class="custom-tree-node" slot-scope="{ node }">
              <i v-if="node.expanded" class="el-icon-folder-opened"></i>
              <i v-else class="el-icon-folder"></i>

              <span class="material-type">{{ node.label }}</span>
            </div>
          </el-tree>
        </div>
        <div class="material-list" v-loading="bShowLoading">
          <div class="img-list" v-if="aFileList.length > 0">
            <div
              class="hover_area"
              :style="{
                height:
                  item.type == 'pdf' || item.type == 'office'
                    ? '212px'
                    : '165px'
              }"
              v-for="(item, index) in aFileList"
              :key="item.id"
              @click.stop="selectMaterial(item)"
              :class="item.bSelected ? 'selected' : ''"
              @mouseenter="setHoverState(index, true)"
              @mouseleave="setHoverState(index, false)"
            >
              <div class="hover_img">
                <i class="el-icon-check check" v-if="item.bSelected"></i>
                <img
                  :src="item.url"
                  alt=""
                  v-if="
                    'staticPicture' === item.type ||
                      'dynamicPicture' === item.type
                  "
                />
                <div
                  style="width: 150px;height: 130px;background: black;"
                  v-if="'txt' === item.type"
                >
                  <div class="txt-default"></div>
                </div>
                <div v-if="'video' === item.type">
                  <img :src="item.cover" class="image" v-show="!item.hover" />
                  <img
                    :src="item.preview"
                    class="image"
                    v-show="item.hover"
                    v-if="'video' === item.type"
                  />
                </div>
                <img
                  :src="item.cover"
                  alt=""
                  v-if="'pdf' === item.type || 'office' === item.type"
                />
                <div
                  style="width: 150px;height: 130px;background: black;"
                  v-if="'audio' === item.type"
                >
                  <div class="music"></div>
                </div>
                <iframe
                  :src="item.url"
                  width="1920px"
                  height="1080px"
                  frameborder="0"
                  scrolling="no"
                  ALLOWTRANSPARENCY="true"
                  style="transform-origin: 0 0;"
                  :style="{
                    transform: 'scale(' + 150 / 1920 + ',' + 125 / 1080 + ')'
                  }"
                  v-if="'website' === item.type"
                ></iframe>
                <div class="name" :title="item.name">
                  {{ item.name }}
                </div>
                <div class="hover_overlay"></div>
                <div class="hover_content">
                  <el-tooltip
                    class="item"
                    effect="dark"
                    :content="item.name"
                    placement="top"
                  >
                    <div class="item">
                      <span class="material-attr">文件名:</span>
                      <span class="material-name">{{ item.name }}</span>
                    </div>
                  </el-tooltip>

                  <div class="item">
                    <span class="material-attr">大小:</span>
                    <span class="material-name">{{ item.sizeWithUnit }}</span>
                  </div>
                  <div
                    class="item"
                    v-if="item.type === 'video' || item.type === 'audio'"
                  >
                    <span class="material-attr">时长:</span>
                    <span class="material-name">{{ item.duration }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="empty-wrap" v-else>
            暂无数据
          </div>
          <pagination
            v-show="pagination.total > 0 && bSearch"
            :total="pagination.total"
            :page.sync="pagination.curPage"
            :limit.sync="pagination.size"
            @pagination="getSearchList()"
            layout="prev, pager, next"
            :autoScroll="false"
          />
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button
          class="okBtn"
          @click="addComponent"
          :loading="bShowBtnLoading"
          >确定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script>
import pdf from "vue-pdf";
import { ROOT_NODE } from "@/utils/enum";
import {
  getSearchMaterial,
  getMaterialsByFolder,
  getFoldersByParent,
  getMineRootFolderId
} from "@/api/info";
const ELEMENTS = [
  {
    label: "视频",
    value: "video",
    id: 1
  },
  {
    label: "视频",
    value: "videoSyncForFile",
    id: 1
  },
  {
    label: "图片",
    value: "staticPicture",
    id: 2
  },
  {
    label: "背景图片",
    value: "background",
    id: 2
  },
  {
    label: "图片",
    value: "pictureSync",
    id: 2
  },
  {
    label: "水印",
    value: "watermark",
    id: 2
  },
  {
    label: "动态图片",
    value: "dynamicPicture",
    id: 3
  },
  {
    label: "文本",
    value: "marquee",
    id: 4
  },
  {
    label: "文本",
    value: "text",
    id: 4
  },
  {
    label: "PDF文档",
    value: "pdf",
    id: 5
  },
  {
    label: "音频",
    value: "audio",
    id: 6
  },
  {
    label: "OFFICE文档",
    value: "office",
    id: 7
  }
];

export default {
  name: "materialList",
  props: ["oParam"],
  components: { pdf },
  data() {
    return {
      bDlgMaterialVisible: true,
      bSelectSingle: this.oParam.bSelectSingle,
      aFileList: [],
      defaultExpandKeys: [],
      bShowLoading: false,
      bShowBtnLoading: false,
      rootNode: ROOT_NODE,
      szInputMaterialName: "",
      bSearch: false,
      pagination: {
        total: 0,
        size: 20,
        curPage: 1
      },
      sort: {
        direction: "ASC",
        properties: "name"
      },
      nameSort: 0,
      createTimeSort: 2,
      dir: {},
      checkAll: false
    };
  },
  computed: {
    element() {
      let res = ELEMENTS.find(ele => ele.value === this.oParam.type);
      return res || {};
    }
  },
  methods: {
    //鼠标放到视频上时，播放视频的gif
    setHoverState(index, state) {
      let material = this.aFileList[index];
      if (material.type !== "video") {
        return;
      }

      let data = { ...material, hover: state };
      this.$set(this.aFileList, index, data);
    },
    /**
     * 获取我的文件夹ID
     */
    getMyDiskId() {
      return new Promise((resolve, reject) => {
        getMineRootFolderId().then(res => {
          resolve(res.data);
        });
      });
    },
    loadNode(node, resolve) {
      if (node.data) {
        var { children, ...dir } = node.data;
      } else {
        dir = this.rootNode[0];
      }

      this.getMaterialGroup(dir)
        .then(res => {
          if (node.level === 0) {
            this.getMyDiskId().then(id => {
              this.defaultExpandKeys = [id];
              this.rootNode[0].id = id;
              this.rootNode[0].children = res;
              this.dir = this.rootNode[0];
              this.searchMaterial();

              return resolve(this.rootNode);
            });
          } else {
            return resolve(res);
          }
        })
        .catch(err => {
          console.log(err);
        });
    },
    nodeClick(nodes = {}, node = {}, self = {}) {
      let { children, ...dir } = node.data;
      this.dir = dir;
      this.searchMaterial();
    },
    getMaterialGroup(dir) {
      return new Promise((resolve, reject) => {
        getFoldersByParent(dir)
          .then(res => {
            resolve(JSON.parse(JSON.stringify(res.data)));
          })
          .catch(err => {
            reject(err);
          });
      });
    },
    // 按标签页（分组）搜索素材
    searchMaterial() {
      this.bShowLoading = true;
      this.bSearch = false;
      let param = {
        materialType: this.element.id || "",
        isExpired: false,
        direction: this.sort.direction,
        properties: this.sort.properties,
        approved: 4
      };
      getMaterialsByFolder({ ...this.dir }, param)
        .then(res => {
          this.aFileList = res.data.rows;
          this.checkAll = false;
          for (let i = 0; i < this.aFileList.length; i++) {
            let oFile = this.aFileList[i];
            this.$set(oFile, "bSelected", false);
            oFile.duration = this.transferTime(oFile.duration);
            oFile.url = oFile.url;
          }
        })
        .finally(() => {
          this.bShowLoading = false;
        });
    },
    selectMaterial(item) {
      if (this.bSelectSingle) {
        for (let i = 0; i < this.aFileList.length; i++) {
          this.aFileList[i].bSelected = false;
        }
      }
      item.bSelected = !item.bSelected;

      this.setCheckAllStatus();
    },
    handleCheckAll() {
      const newFileList = this.aFileList.map(file => {
        return { ...file, bSelected: this.checkAll };
      });
      this.aFileList = newFileList;
    },
    setCheckAllStatus() {
      if (this.bSelectSingle) {
        return;
      }
      let checkedCount = this.aFileList.reduce((prev, file) => {
        return prev + (file.bSelected ? 1 : 0);
      }, 0);
      if (
        checkedCount === this.aFileList.length &&
        this.aFileList.length !== 0
      ) {
        this.checkAll = true;
      } else {
        this.checkAll = false;
      }
    },
    getSearchList() {
      this.bShowLoading = true;
      this.bSearch = true;
      getSearchMaterial({
        nameToSearch: this.szInputMaterialName,
        materialType: this.element.id || "",
        size: this.pagination.size,
        page: this.pagination.curPage,
        isExpired: false,
        direction: this.sort.direction,
        properties: this.sort.properties
      })
        .then(res => {
          this.aFileList = res.data.rows;
          this.pagination.total = res.data.total;
          for (let i = 0; i < this.aFileList.length; i++) {
            let oFile = this.aFileList[i];
            this.$set(oFile, "bSelected", false);
            oFile.duration = this.transferTime(oFile.duration);
            oFile.url = oFile.url;
          }
        })
        .catch(err => {
          console.log(err);
        })
        .finally(() => {
          this.bShowLoading = false;
        });
    },
    closeDialog() {
      this.$emit("closeDialog");
    },
    addComponent() {
      this.bShowBtnLoading = true;
      this.$emit("addMaterial", this.aFileList, this.oParam);
    },
    sortChange(properties) {
      let direction;
      if (properties === "name") {
        this.nameSort = this.nameSort === 0 ? 1 : 0;
        this.createTimeSort = 2;
        direction = this.nameSort;
      } else {
        this.createTimeSort = this.createTimeSort === 0 ? 1 : 0;
        direction = this.createTimeSort;
        this.nameSort = 2;
      }
      this.sort.direction = direction === 0 ? "ASC" : "DESC";
      this.sort.properties = properties;
      if (this.bSearch) {
        this.getSearchList();
      } else {
        this.searchMaterial();
      }
    }
  }
};
</script>

<style scoped lang="scss">
.picture-area {
  position: relative;
  width: 815px;
  height: 400px;
  display: flex;
  flex-direction: row;
}
.material-tree {
  width: 250px;
  border-right: 1px solid #eeeeee;
  height: 100%;
  overflow: auto;
  flex-shrink: 0;
}
.material-type {
  display: inline-block;
  // margin-right: 75px;
  margin-left: 2px;
}
.material-list {
  flex: 1;
  height: 100%;
  overflow: auto;
  position: relative;
  display: flex;
  flex-direction: column;
}
.pagination-position {
  position: absolute;
  height: 30px;
  left: 250px;
  right: 0;
  bottom: 15px;
}
.img-list .hover_area {
  width: 150px;
  height: 150px;
  float: left;
  margin: 7px 15px;
  border: 1px solid #eee;
  position: relative;
  cursor: pointer;
  overflow: hidden;
  border-radius: 5px;
}
.img-list .hover_area:hover {
  border: 1px solid #67c23a;
}
.hover_area img {
  width: 150px;
  height: 125px;
}
.hover_area video {
  width: 150px;
  height: 97px;
  margin-top: 25px;
}
.hover_area .name {
  height: 35px;
  line-height: 35px;
  padding: 0 5px;
  box-sizing: border-box;
  width: 100%;
  background: #fafafa;
  color: #666666;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: center;
  position: absolute;
  bottom: 0;
}
.hover_area.selected {
  border: 1px solid #3c822e;
}
.hover_area.selected:after {
  width: 0;
  height: 0;
  border-top: 40px solid #3c822e;
  border-left: 61px solid transparent;
  position: absolute;
  display: block;
  right: 0;
  content: ".";
  color: transparent;
  top: 0;
  z-index: 1000;
}
.hover_area .item {
  height: 25px;
  line-height: 25px;
  color: white;
  font-size: 12px;
}
.hover_img {
  width: 100%;
  height: 100%;
  position: relative;
}
.check {
  position: absolute;
  right: 5px;
  font-size: 25px;
  color: white;
  z-index: 1001;
}
.hover_img img {
  transition: all 0.5s;
  object-fit: contain;
}
.hover_area:hover .hover_content {
  opacity: 1;
  visibility: visible;
}
.hover_area:hover .hover_overlay {
  opacity: 0.3;
  visibility: visible;
}
.hover_overlay {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  opacity: 0;
  visibility: hidden;
  background: black;
  transition: all 0.3s;
}
.hover_content {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  opacity: 0;
  visibility: hidden;
  text-align: center;
  transition: all 0.5s;
}
.material-name {
  overflow: hidden;
  display: inline-block;
  float: left;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100px;
  text-align: left;
  margin-left: 5px;
}
.material-attr {
  display: inline-block;
  float: left;
  text-align: left;
}
.el-icon-folder,
.el-icon-folder-opened {
  color: rgb(248, 211, 100);
  font-size: 16px;
}
.empty-wrap {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
.search-wrap {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin: 0 15px 5px 6px;
  .el-input {
    width: 50%;
  }
  .filter-wrap {
    .search {
      display: inline-block;
      margin-left: 4px;
      .caret-wrapper {
        display: inline-flex;
        flex-direction: column;
        align-items: center;
        height: 34px;
        width: 24px;
        vertical-align: middle;
        cursor: pointer;
        overflow: initial;
        position: relative;
        .sort-caret {
          width: 0;
          height: 0;
          border: 5px solid transparent;
          position: absolute;
          left: 7px;
          &.ascending {
            border-bottom-color: #c0c4cc;
            top: 5px;
          }
          &.descending {
            border-top-color: #c0c4cc;
            bottom: 7px;
          }
        }
        &.ascending .sort-caret.ascending {
          border-bottom-color: #409eff;
        }
        &.descending .sort-caret.descending {
          border-top-color: #409eff;
        }
      }
    }
  }
}
</style>
