<template>
  <el-dialog
    :title="title"
    class="iot-device"
    :visible.sync="show"
    :close-on-click-modal="false"
    width="45%"
    @close="closeDialog"
    :before-close="dialogBeforeClose"
  >
    <div class="iot-device-container">
      <el-table :data="deviceList" style="width: 100%" height="100%">
        <el-table-column type="index" width="80" label="序号" align="center"></el-table-column>
        <el-table-column prop="deviceName" label="设备名称" min-width="auto" show-overflow-tooltip> </el-table-column>
        <el-table-column prop="brand" label="设备品牌" min-width="auto" align="center" show-overflow-tooltip> </el-table-column>
        <el-table-column prop="model" label="设备型号" min-width="auto" align="center" show-overflow-tooltip> </el-table-column>
        <el-table-column prop="online" label="在线状态" min-width="60" align="center" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <div class="status-wrap">
              <svg-icon class="status" :icon-class="row.onlineStatus && row.onlineStatus.icon" />
                {{ row.onlineStatus && row.onlineStatus.label }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="告警状态" min-width="60" align="center" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <div class="status-wrap">
              <svg-icon class="status" :icon-class="row.alarmStatus && row.alarmStatus.icon" />
              <el-link v-if="row.alarmStatus.value == 'alarm'" type="primary" @click="openAlarmDlg(row.deviceId, row.deviceName)">
                {{ row.alarmStatus && row.alarmStatus.label }}
              </el-link>
              <div v-else>
                {{ row.alarmStatus && row.alarmStatus.label }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="80" align="center" show-overflow-tooltip v-if="hasOperatePermission">
          <template slot-scope="{ row }">
            <el-link @click="checkAction(row)" class="mr-10">操作</el-link>
            <el-link @click="handleCheckDeviceDetail(row)">查看详情</el-link>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </el-dialog>
</template>

<script>
  import { deviceGetAllList } from '@/api/iotControl';
  import { DEVICE_ALARM_STATUS,DEVICE_ONLINE_STATUS } from '@/views/iotControl/enum.js';
  import transform from '@/utils/transform';
  import alarm from '@/views/iotControl/deviceMaintenance/intelligentMonitor/modules/alarm.vue';
  import { hasPermissionWithoutStore } from '@/directive/permission';
  export default {
    props: {
      // 网关ID
      gatewayId: {
        type: String | Number,
        default: '',
      },
      // 空间ID
      spaceId: {
        type: String | Number,
        default: '',
      },
      visible: {
        type: Boolean,
        default: false,
      },
      title: {
        type: String,
        default: '设备',
      },
    },
    data() {
      return {
        show: false,
        deviceList: [],
        DEVICE_ALARM_STATUS,
        DEVICE_ONLINE_STATUS
      };
    },
    methods: {
      getDevice() {
        let params = {};
        let func;
        if (this.gatewayId) {
          params = {
            gatewayId: this.gatewayId,
          };
          func = deviceGetAllList;
        } else if (this.spaceId) {
          params = {
            spaceId: this.spaceId,
          };
          func = deviceGetAllList;
        }

        func(params).then(({ data }) => {
          this.deviceList = data.map((item) => {
            let alarmStatus = {};
            alarmStatus = item.status ==='alarm' ? DEVICE_ALARM_STATUS[1] : DEVICE_ALARM_STATUS[0];
            let onlineStatus = {};
            onlineStatus = item.online ? DEVICE_ONLINE_STATUS[0] : DEVICE_ONLINE_STATUS[1];
            return {
              ...item,
              onlineStatus,
              alarmStatus,
            };
          });
        });
      },
      alarmDlg: transform(alarm),
      openAlarmDlg(deviceId, deviceName) {
        this.alarmDlg({
          propsData: {
            deviceId: deviceId,
            deviceName: deviceName,
          },
          methods: {
            refresh: () => {
              this.getDevice();
            },
          },
        });
      },
      closeDialog() {
        this.show = false;
        this.$emit('update:visible', false);
      },
      dialogBeforeClose(done) {
        this.deviceList = [];
        this.$emit('update:userSpaceId', '');
        this.$emit('update:spaceId', '');
        done();
      },
      handleCheckDeviceDetail(row) {
        this.$router.push({
          name: 'IotDeviceInfo',
          params: {
            id: row.deviceId,
          },
        });
      },
      /**
       * 操作设备
       * @param {*} row
       */
      checkAction(row) {
        this.$router.push({
          name: 'IotDeviceAction',
          params: {
            id: row.deviceId,
            deviceType: row.deviceType,
            devSubType: row.devSubType,
            model: row.model,
          },
        });
      },
    },
    watch: {
      visible(val) {
        this.show = val;
        if (val) {
          this.getDevice();
        }
      },
    },
    computed: {
      hasOperatePermission() {
        // 物联组网
        // 系统运维 | 设备列表
        // 物联空间 | 设备操作
        return (
          hasPermissionWithoutStore('IotManage', undefined) ||
          hasPermissionWithoutStore('deviceMaintenance', 'intelligentMonitor') ||
          hasPermissionWithoutStore('IotCustomSpace', 'deviceOperate')
        );
      },
    },
  };
</script>

<style lang="scss" scoped>
  .iot-device {
  }

  .iot-device-container {
    width: 100%;
    height: 500px;
    overflow-y: auto;
  }

  .el-link {
    color: #1f7bc1;
  }
  .status-wrap {
    display: flex;
    width: 100%;
    justify-content: center;
    align-items: center;
  }
  .status {
    margin-right: 0.5em;
  }
</style>
