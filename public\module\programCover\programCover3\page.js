let page = {
  width: 350,
  height: 180,
  props: [
    {
      type: "el-color-picker",
      title: "文字颜色",
      field: "color",
      value: "#000000",
      props: {
        predefine: [
          "#FFFFFF",
          "#000000",
          "#C00000",
          "#FF0000",
          "#FFC000",
          "#FFFF00",
          "#92D050",
          "#00B050",
          "#00B0F0",
          "#0070C0",
          "#002060",
          "#7030A0",
        ]
      }
    },
    {
      type: "el-color-picker",
      title: "背景颜色",
      field: "backgroundColor",
      value: "rgba(255,255,255,0)",
      props: {
        showAlpha: true,
        predefine: [
          "#FFFFFF",
          "#000000",
          "#C00000",
          "#FF0000",
          "#FFC000",
          "#FFFF00",
          "#92D050",
          "#00B050",
          "#00B0F0",
          "rgb(91,155,213)",
          "#002060",
          "#7030A0",
        ]
      }
    }],
};
