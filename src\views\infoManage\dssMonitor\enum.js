import i18n from '@/i18n'
//信发终端状态
export const DSS_TERMINAL_STATUS = [
  {
    value: 'all',
    label: '所有',
  },
  {
    value: 'online',
    label: '在线',
  },
  {
    value: 'playing',
    label: '播放中',
  },
  {
    value: 'downloading',
    label: '下载中',
  },
  {
    value: 'updating',
    label: '升级中',
  },
  {
    value: 'idle',
    label: '空闲',
  },
  {
    value: 'notRunning',
    label: '未运行',
  },
  {
    value: 'hibernate',
    label: '休眠',
  },
  {
    value: 'offline',
    label: '离线',
  },
  {
    value: 'notInstalled',
    label: '未安装',
  },
  {
    value: 'foreRunning',
    label: '前台运行',
  },
  {
    value: 'backRunning',
    label: '后台运行',
  },
];

//信发终端红外状态
export const INFRARED_STATUS = ['未知', '没人', '有人'];

// 默认显示列
export const DEFAULT_COLUMN = ['terminalId', 'terminalName', 'approveState', 'apkState', 'runState', 'terminalIp', 'dssVersion'];

// 列显示下拉框
export const ALL_COLUMN = [
  {
    value: 'all',
    label: '所有',
  },
  {
    value: 'terminalId',
    label: '终端ID',
    key: 'bShowId',
  },
  {
    value: 'terminalName',
    label: '终端名称',
    key: 'bShowName',
  },
  {
    value: 'approveState',
    label: '系统状态',
    key: 'bShowApproved',
  },
  {
    value: 'apkState',
    label: '应用状态',
    key: 'bShowApkState',
  },
  {
    value: 'runState',
    label: '业务状态',
    key: 'bShowRunStatus',
  },
  {
    value: 'playlistNumber',
    label: '播放频道',
    key: 'bShowPlaylistNumber',
  },
  {
    value: 'terminalIp',
    label: '终端IP地址',
    key: 'bShowIp',
  },
  {
    value: 'dssVersion',
    label: '软件版本',
    key: 'bShowVersion',
  },
  {
    value: 'deptName',
    label: `所属${i18n.t('deptLabel')}`,
    key: 'bShowDept',
  },
  {
    value: 'bandwidth',
    label: '平均带宽',
    key: 'bShowBandwidth',
  },
  {
    value: 'diskSpace',
    label: '磁盘使用率',
    key: 'bShowDiskSpace',
  },
  {
    value: 'Tf',
    label: 'TF卡使用率',
    key: 'bShowTf',
  },
  {
    value: 'singleScreen',
    label: '屏幕数量',
    key: 'bShowScreen',
  },
  {
    value: 'city',
    label: '所在城市',
    key: 'bShowCity',
  },
  {
    value: 'infraredStatus',
    label: '红外状态',
    key: 'bShowInfrared',
  },
  {
    value: 'downloadStatus',
    label: '下载进度',
    key: 'bShowDownloadStatus',
  }
];

// 控制列显示数值
export const SHOW_COLUMN = {
  bShowId: true,
  bShowApproved: true,
  bShowState: true,
  bShowName: true,
  bShowPlaylistNumber: false,
  bShowIp: true,
  bShowVersion: true,
  bShowRunStatus: true,
  bShowDownloadStatus: true,
  bShowDept: false,
  bShowBandwidth: false,
  bShowCity: false,
  bShowDiskSpace: false,
  bShowScreen: false,
  bShowInfrared: false,
};

// 默认显示列
export const IDEAHUB_DEFAULT_COLUMN = ['terminalId', 'terminalName', 'approveState', 'terminalIp', 'omsdkAuth', 'httpApiAuth', 'deptName'];

// 列显示下拉框
export const IDEAHUB_ALL_COLUMN = [
  {
    value: 'terminalId',
    label: '终端ID',
    key: 'bShowId',
  },
  {
    value: 'terminalName',
    label: '终端名称',
    key: 'bShowName',
  },
  {
    value: 'approveState',
    label: '系统状态',
    key: 'bShowApproved',
  },
  {
    value: 'terminalIp',
    label: '终端IP地址',
    key: 'bShowIp',
  },
  {
    value: 'omsdkAuth',
    label: '账户鉴权',
    key: 'bShowOMAuth',
  },
  {
    value: 'httpApiAuth',
    label: '应用鉴权',
    key: 'bShowAppAuth',
  },
  // {
  //   value: "dssVersion",
  //   label: "软件版本",
  //   key: "bShowVersion"
  // },
  {
    value: 'deptName',
    label: `所属${i18n.t('deptLabel')}`,
    key: 'bShowDept',
  },
  // {
  //   value: "city",
  //   label: "所在城市",
  //   key: "bShowCity"
  // }
];

export const IDEAHUB_SHOW_COLUMN = {
  bShowId: true,
  bShowApproved: true,
  bShowName: true,
  bShowIp: true,
  // bShowVersion: false,
  bShowDept: true,
  // bShowCity: false,
  // app 授权
  bShowAppAuth: true,
  // 账户鉴权
  bShowOMAuth: true,
};

export const IDEAHUB_BASE_TERMINAL_STATUS = [
  {
    value: 'all',
    label: '所有',
  },
  {
    value: 'online',
    label: '在线',
    icon: 'status@online',
  },
  {
    value: 'updating',
    label: '升级中',
    icon: 'status@updating',
  },
  {
    value: 'hibernate',
    label: '休眠',
    icon: 'status@hibernate',
  },
  {
    value: 'offline',
    label: '离线',
    icon: 'status@offline',
  },
];
