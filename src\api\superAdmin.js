/**
 *
 * @author: <PERSON><PERSON><PERSON>
 * @create: 2020-12-16
 */

import request from "@/utils/request";

/**
 * 查询所有公司
 * @param page
 * @param size
 * @param direction
 * @param property
 * @return {AxiosPromise}
 */
export function getCompanies(page, size, direction, property) {
  return request({
    url:
      "/oauth/company/" + page + "/" + size + "/" + direction + "/" + property,
    method: "get"
  });
}

/**
 * 删除指定公司
 * @param companyId
 * @return {AxiosPromise}
 */
export function deleteCompany(companyId) {
  return request({
    url: "/oauth/company/" + companyId,
    method: "delete"
  });
}

/**
 * 查询所有权限
 * @return {AxiosPromise}
 */
export function getPermissions() {
  return request({
    url: "/oauth/permission",
    method: "get"
  });
}

/**
 * 添加权限
 * @return {AxiosPromise}
 */
export function addPermission(data) {
  return request({
    url: "/oauth/permission",
    method: "post",
    data,
    headers: {
      "Content-Type": "application/json;"
    }
  });
}

/**
 * 更新权限
 * @return {AxiosPromise}
 */
export function updatePermission(data) {
  return request({
    url: "/oauth/permission",
    method: "put",
    data,
    headers: {
      "Content-Type": "application/json;"
    }
  });
}

/**
 * 删除权限
 * @param id
 * @return {AxiosPromise}
 */
export function deletePermission(id) {
  return request({
    url: "/oauth/permission/" + id,
    method: "delete"
  });
}
