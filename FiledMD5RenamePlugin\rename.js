const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const process = require('process');
const UglifyJS = require('uglify-js');
const moment = require('moment');

// 只修改 xxxxx/dist/module 目录下
// rootPath xxxxx/dist/module
exports.rename = (rootPath, config) => {
  const whiteList = config.whiteList;
  const modulePath = config.modulePath;
  // 更新 module.json 文件
  const moduleJsonPath = path.join(rootPath, config.moduleJsonPath);

  for (let dirPath of modulePath) {
    batchRename(path.join(rootPath, dirPath), whiteList);
  }

  let moduleJson = fs.readFileSync(moduleJsonPath, 'utf-8');
  let sourceJson = JSON.parse(moduleJson);
  let outputJson = [];
  sourceJson.forEach((module) => {
    module.children.forEach((subModule) => {
      subModule.attr = moment().format('YYYYMMDDHHmm');
      // rootPath 'E:\\Workspace\\dss20_web\\dist\\module' 'module/timer/timer1/index.html'
      let rawPath = path.join(rootPath.replace(/module/g, ''), subModule.html);
      let temp = path.parse(rawPath);
      let subModulePath = temp.dir;
      let webFiles = fs.readdirSync(subModulePath);
      let webAsset = [];
      webFiles.forEach((file) => {
        let outputFileInfo = {};
        if (!whiteList.includes(file)) {
          let fileStat = fs.statSync(path.join(subModulePath, file));
          outputFileInfo.size = String(fileStat.size);
          outputFileInfo.md5 = chkMd5(path.join(subModulePath, file));
          outputFileInfo.pathname = path.parse(subModule.html).dir + '/' + file;
          outputFileInfo.name = file;
          webAsset.push(outputFileInfo);
        }
      });
      subModule.webAsset = webAsset;
    });
    outputJson.push(module);
  });

  // 更新module.json文件
  fs.writeFileSync(moduleJsonPath, JSON.stringify(outputJson, null, '  '), 'utf-8');

  console.log('module.json updated success!');
};

/**
 * 读取文件内容并替换其中的换行符
 * @param {*} filename
 */
function replaceLineBreaks(filename) {
  const contents = fs.readFileSync(filename, 'utf-8');
  const unixContents = contents.replace(/\r\n/g, '\n');
  // console.log('change file line breaks', filename);

  // 将替换后的内容写回到原文件
  fs.writeFileSync(filename, unixContents);
}

/**
 * 校验文件MD5
 * @param {*} filepath
 * @returns
 */
function chkMd5(filepath) {
  const buffer = fs.readFileSync(filepath);
  const hash = crypto.createHash('md5');
  hash.update(buffer, 'utf8');
  const md5 = hash.digest('hex');
  return md5;
}

/**
 * 列出模块下所有文件
 * @param {*} modulePath
 */
function batchRename(modulePath, whiteList) {
  let isChanged = false;
  // 入口文件
  let entryHtml = null;
  // 模块下所有文件
  let allFiles = [];

  function listDir(dir) {
    let arr = fs.readdirSync(dir);
    arr.forEach((item) => {
      let fullPath = path.join(dir, item);
      let stats = fs.statSync(fullPath);
      if (stats.isDirectory()) {
        listDir(fullPath);
      } else {
        // // 暂时不更改文件名称，下面会进行变更
        // const extName = path.extname(fullPath);
        // if (['.css', '.js'].includes(extName)) {
        //   // 统一 换行符为 /n
        //   replaceLineBreaks(fullPath);
        // }

        let tmpFile = path.parse(fullPath);
        tmpFile.md5 = chkMd5(fullPath);
        tmpFile.targetName = tmpFile.md5 + tmpFile.ext;
        allFiles.push(tmpFile);
        if (tmpFile.base == 'index.html') {
          entryHtml = tmpFile;
        }
      }
    });
  }

  listDir(modulePath);

  let entryHtmlPath = path.join(entryHtml.dir, entryHtml.base || 'index.html');
  console.log('');
  console.log('detected entry html ====> ', entryHtmlPath);

  let entryHtmlText = fs.readFileSync(entryHtmlPath, 'utf-8');

  // 去除 html 注释
  let htmlText = entryHtmlText.replace(/<!--[\s\S]*?-->/g, '');
  // 去除 JavaScript 注释
  // htmlText = htmlText.replace(/\/\*[\s\S]*?\*\/|\/\/.*/g, '');
  // 去除 css 注释
  // htmlText = htmlText.replace(/\/\*[\s\S]*?\*\//g, '');

  // 全局匹配
  allFiles.forEach((file) => {
    // 忽略白名单
    if (!whiteList.includes(file.base)) {
      // 1、读取入口index.html文件内容
      // 2、匹配index.html是否引入该文件
      //  == (1) html有引用该文件
      //  == (2) 且该文件的md5不是文件名
      //  == (3) 则标识改文件有改动
      if (htmlText.indexOf(file.name) != -1 && file.name != file.md5) {
        entryHtmlText = entryHtmlText.replaceAll(file.base, file.targetName);
        console.log(file.dir + '\\' + file.base, '===========>', file.targetName);
        isChanged = true;
        // 3、将引用的文件改名为 新的md5文件名
        fs.rename(file.dir + '\\' + file.base, file.dir + '\\' + file.targetName, (error) => {
          if (error) {
            console.log(error);
          }
        });
      }
    }
  });
  // 4、写回 html 内容中
  fs.writeFileSync(entryHtmlPath, entryHtmlText);

  // 模块下文件是否有更改
  return isChanged;
}
