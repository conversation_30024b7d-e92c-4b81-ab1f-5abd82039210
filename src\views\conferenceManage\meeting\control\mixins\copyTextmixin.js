import clipboard from '@/utils/clipboard';
export default {
  methods: {
    /**
     * 复制文本
     * @param {*} val
     * @param {*} event
     */
    copyText(val, event, bAlert) {
      clipboard(val, event, () => {
        if (bAlert) {
          this.$message.success('已复制');
        }
      });
    },
    /**
     * 终端ip跳转
     * @param {*} row
     */
    handleJump(ip) {
      const ipRegex = /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;

      if (ip) {
        const isIp = ipRegex.test(ip);
        if (isIp) {
          window.open(`http://${ip}`, '__blank');
        } else {
          this.$message.warning('不是合法的IP地址, 暂不支持跳转!');
        }
      }
    },
  },
};
