/// <reference types="node" />
const path = require('path');
const rename = require('../FiledMD5RenamePlugin/rename').rename;

module.exports = class FiledMD5RenamePlugin {
  constructor(options) {
    if (!options || !options.sourceFolder) {
      options = {};
      options.sourceFolder = '/public/module';
    }
    // 插件的配置选项可以通过构造函数传递
    this.options = options;
  }

  apply(compiler) {
    // 在这里编写插件逻辑
    // compiler 参数是 Webpack 编译器实例

    compiler.plugin('after-emit', (compilation, callback) => {
      const outputPath = compilation.getPath(compiler.options.output.path || '');
      const outputModulePath = path.join(outputPath, 'module');

      // 执行自定义逻辑 outputModulePath
      this.doCustomLogic(outputModulePath);

      callback();
    });
  }

  doCustomLogic(outputPath) {
    console.log('ready to rename module files, please wait 5s!');
    setTimeout(() => {
      console.log('start rename module files!');
      try {
        // 在这里根据自己的需求编写判断逻辑
        rename(outputPath, {
          // 静态文件白名单
          whiteList: ['capture.jpg', 'capture.png', 'index.html', 'page.js'],
          // 每个子项的方式进行遍历，不使用循环遍历，防止特殊情况下 文件目录格式不符
          modulePath: [
            'conferenceGuide/conferenceGuide1',
            'conferenceGuide/conferenceGuide2',
            'conferenceGuide/conferenceGuide3',
            'conferenceGuide/conferenceGuide4',
            'conferenceGuide/conferenceGuide5',
            'conferenceGuide/conferenceGuide6',
            'programCover/programCover1',
            'stock/stock1',
            'stock/stock2',
            'summaryScreen/summaryScreen1',
            'timer/timer1',
            'timer/timer2',
            'timer/timer3',
            'timer/timer4',
            'timer/timer5',
            'weather/weather1',
            'weather/weather2',
            'weather/weather3',
            'weather/weather4',
            'weather/weather5',
            'weather/weather6',
          ],
          moduleJsonPath: 'module.json',
        });
      } catch (error) {
        // ignore
        console.error('Module文件更新失败,请手动更新!');
      }
    }, 5 * 1000);
  }
};


