import axios from 'axios';
import moment from 'moment';
import {getAccessToken} from './auth';
import {TERMINAL_ERROR_CODE} from './enum';

Date.prototype.Format = function (fmt) {
  let o = {
    'M+': this.getMonth() + 1,
    'd+': this.getDate(),
    'h+': this.getHours(),
    'm+': this.getMinutes(),
    's+': this.getSeconds(),
    'q+': Math.floor((this.getMonth() + 3) / 3),
    S: this.getMilliseconds(),
  };
  if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (this.getFullYear() + '').substr(4 - RegExp.$1.length));
  for (let k in o)
    if (new RegExp('(' + k + ')').test(fmt)) fmt = fmt.replace(RegExp.$1, RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length));
  return fmt;
};

export function formatTime(times, pattern) {
  let d = new Date(times).Format('yyyy-MM-dd hh:mm:ss');
  if (pattern) {
    d = new Date(times).Format(pattern);
  }
  return d.toLocaleString();
}

const VIDEO = ['wmv', 'asf', 'asx', 'rm', 'rmvb', 'mp4', '3gp', 'mov', 'm4v', 'avi', 'dat', 'mkv', 'flv', 'vob', 'swf'];
const IMAGE = ['png', 'jpg', 'bmp'];
const GIF = ['gif'];
const TXT = ['txt'];
const PDF = ['pdf'];
const AUDIO = ['mp3'];
const OFFICE = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'];
const ZIP = ['zip'];
const APK = ['apk'];
const H5 = ['ph', 'html', 'htm'];

/**
 * 根据文件名获取素材类型
 * @param filename 文件名
 * @return
 *      <ul>
 *          <li>
 *              {type: 'video', code: 1}
 *          </li>
 *          <li>
 *              {type: 'staticPicture', code: 2}
 *          </li>
 *          <li>
 *              {type: 'dynamicPicture', code: 3}
 *          </li>
 *          <li>
 *              {type: 'txt', code: 4}
 *          </li>
 *          <li>
 *              {type: 'pdf', code: 5}
 *          </li>
 *          <li>
 *              {type: 'audio', code: 6}
 *          </li>
 *          <li>
 *              {office: 'office', code: 7}
 *          </li>
 *          <li>
 *              {type: 'website', code: 8}
 *          </li>
 *      </ul>
 *      <strong>若文件类型受支持，则返回类型，否则返回空值</strong>
 */
export function getMaterialType(filename = '') {
  if (VIDEO.indexOf(filename) !== -1) {
    return {type: 'video', code: 1};
  }
  if (IMAGE.indexOf(filename) !== -1) {
    return {type: 'staticPicture', code: 2};
  }
  if (GIF.indexOf(filename) !== -1) {
    return {type: 'dynamicPicture', code: 3};
  }
  if (TXT.indexOf(filename) !== -1) {
    return {type: 'txt', code: 4};
  }
  if (PDF.indexOf(filename) !== -1) {
    return {type: 'pdf', code: 5};
  }
  if (AUDIO.indexOf(filename) !== -1) {
    return {type: 'audio', code: 6};
  }
  if (OFFICE.indexOf(filename) !== -1) {
    return {office: 'office', code: 7};
  }
  if (H5.indexOf(filename) !== -1) {
    return {type: 'website', code: 8};
  }
  return null;
}

export function IsObjectEmpty(data) {
  return Object.keys(data).length === 0;
}

export function IsStrEmpty(data) {
  if (data === '' || data === null || data === undefined || data === 'null') {
    return true;
  }
  return false;
}

export function arrItemFirst(arr, index) {
  if (index === 0 || index < 0) {
    return;
  }
  let curIndex = 0;
  arr.unshift(arr.splice(index, 1)[0]);
  return curIndex;
}

export function arrItemUp(arr, index) {
  if (index === 0 || index < 0) {
    return;
  }
  let curIndex = index - 1;
  arr[index] = arr.splice(index - 1, 1, arr[index])[0];
  return curIndex;
}

export function arrItemDown(arr, index) {
  if (index === arr.length - 1 || index < 0) {
    return;
  }
  let curIndex = index + 1;
  arr[index] = arr.splice(index + 1, 1, arr[index])[0];
  return curIndex;
}

export function arrItemEnd(arr, index) {
  if (index === arr.length - 1 || index < 0) {
    return;
  }

  let curIndex = arr.length - 1;
  arr.push(arr.splice(index, 1)[0]);
  return curIndex;
}

export function getErrorMsgByCode(code) {
  let res = TERMINAL_ERROR_CODE.find((item) => item.code === Number(code));
  return (res && res.label) || code;
}

const SIZE_UNIT = {
  b: 1,
  kb: 1 << 10,
  mb: 1 << 20,
  gb: 1 << 30,
  tb: Math.pow(1024, 4),
  pb: Math.pow(1024, 5),
};

export function sizeToBytes(val, unit) {
  return Math.floor(SIZE_UNIT[unit] * val);
}

export function bytesToSize(val) {
  if (!val) {
    return {value: val, unit: 'b'};
  }
  let unit = '';
  if (val >= SIZE_UNIT.pb) {
    unit = 'pb';
  } else if (val >= SIZE_UNIT.tb) {
    unit = 'tb';
  } else if (val >= SIZE_UNIT.gb) {
    unit = 'gb';
  } else if (val >= SIZE_UNIT.mb) {
    unit = 'mb';
  } else if (val >= SIZE_UNIT.kb) {
    unit = 'kb';
  } else {
    unit = 'b';
  }
  let value = (val / SIZE_UNIT[unit]).toFixed(2);
  return {value, unit};
}

export function treeToList(tree, list = []) {
  if (!(tree && tree.length)) return;
  tree.forEach((node) => {
    list.push(node);
    node.children && node.children.length && treeToList(node.children, list);
  });
  return list;
}

/**
 * 树形结构数据转换为对象
 * @param tree 树形数据
 * @param param 对象的key
 * @param list 返回的对象
 */
export function treeToObj(tree, param = 'id', list = {}) {
  if (!(tree && tree.length)) return;
  tree.forEach((node) => {
    list[node[param]] = {...node};
    node.children && node.children.length && treeToObj(node.children, param, list);
  });
  return list;
}

/**
 * 格式化 ISO 8601 格式的时间
 * @param isoString 2025-05-21T22:46:05.587848+00:00
 * @returns {string} 2025-05-22 06:46:05
 */
export function formatISODate(isoString) {
  const date = new Date(isoString);

  // 定义格式化选项
  const options = {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false,
    timeZone: 'Asia/Shanghai'
  };

  const formatter = new Intl.DateTimeFormat('zh-CN', options);
  // 将默认的斜杠替换为横杠，以匹配 "YYYY-MM-DD" 格式
  return formatter.format(date).replace(/\//g, '-');
}

//将时间转换为yyyy-MM-dd hh:mm:ss
export function formatTableTime(time) {
  if (!time) {
    return null;
  }
  return new Date(time).Format('yyyy-MM-dd hh:mm:ss');
}

/**
 * @param duration 时长
 * @param unit 单位,默认为毫秒milliseconds
 * @param bShowSecond 是否显示秒，默认显示
 * @return 天时分秒
 */
export function durationToTime(duration, unit = 'milliseconds', bShowSecond = true) {
  if (isNaN(duration)) {
    return;
  }
  if (duration <= 0) {
    return duration;
  }

  let d = moment.duration(duration, unit);
  const day = Math.floor(d.asDays());
  const hour = d.hours();
  const minute = d.minutes();
  const second = d.seconds();

  let str = day > 0 ? `${day}天` : '';
  str += hour > 0 ? `${hour}小时` : '';
  str += minute > 0 ? `${minute}分钟` : '';
  if (bShowSecond) {
    str += second > 0 ? `${second}秒` : '';
  }

  return str;
}

// 导出文件
export function exportFile(data) {
  axios({
    baseURL: process.env.VUE_APP_BASE_API,
    method: data.method || 'get',
    url: `${data.url}`,
    params: data.params,
    data: data.method === 'POST' ? data.data : {},
    responseType: 'blob',
    headers: {Authorization: 'Bearer ' + getAccessToken()},
  })
    .then((res) => {
      const link = document.createElement('a');
      let blob = new Blob([res.data], {type: res.data.type});
      link.style.display = 'none';
      link.href = URL.createObjectURL(blob);
      let downloadName = data.name || res.headers['content-disposition'];
      try {
        downloadName = decodeURI(downloadName);
      } catch {
        // ignored
      }
      link.download = downloadName; //下载后文件名
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    })
    .catch((error) => {
      this.$message.error({
        title: error || '错误',
        desc: '网络连接错误',
      });
    });
}

/**
 * 下载文件
 * @param {*} name 文件名
 * @param {*} url  下载路径
 */
export function downloadFile(name, url) {
  const link = document.createElement('a');
  link.style.display = 'none';
  link.href = url;
  link.setAttribute('download', name);
  document.body.appendChild(link);
  link.click();
}

/**
 * 交换数组中元素位置
 * @param arr 数组
 * @param index1 元素1位置
 * @param index2 元素2位置
 */
export function swapArray(arr, index1, index2) {
  let length = arr.length - 1;
  if (index1 > length || index2 > length) {
    console.error('元素位置超过数组长度，无法交换！');
    return;
  }
  arr[index1] = arr.splice(index2, 1, arr[index1])[0];
  return arr;
}

/**
 * 在新进程中打开标签页
 * @param url 链接
 */
export function openTabsWithNewProcess(routeUrl) {
  let link = document.createElement('a'); //创建a标签
  link.style.display = 'none';
  link.setAttribute('href', routeUrl); //url即为需要打开的新页面的url
  link.setAttribute('target', '_blank'); //_blank新窗口打开
  link.setAttribute('rel', 'noopener noreferrer'); //添加rel
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

/**
 * Blob转File
 * @param blob     {Blob}   blob
 * @param fileName {String} 文件名
 * @param mimeType {String} 文件类型
 * @return {File}
 */
export function blobToFile(blob, fileName, mimeType) {
  return new File([blob], fileName, {type: mimeType});
}

/**
 * 树形结构数据根据条件获取所有父亲节点直到跟节点
 * @param tree 树形结构数据
 * @param func 需要满足的条件
 * @return 满足条件的路径
 */
export function getTreePath(tree, func, path = []) {
  if (!tree) return [];
  for (const data of tree) {
    // 这里按照你的需求来存放最后返回的内容吧
    path.push(data);
    if (func(data)) return path;
    if (data.children) {
      const findChildren = getTreePath(data.children, func, path);
      if (findChildren.length) return findChildren;
    }
    path.pop();
  }
  return [];
}

/**
 * 遍历树形结构数据
 * @param tree 树形结构数据，支持对象或数组，[{xx,xx,children}]或{xx,xx,children}
 * @param cb 回调函数
 * @param parent 父级节点
 * @param key 子级的键，默认为children
 */
export function reverseTree(tree, cb, parent = null, key = 'children') {
  if (!tree) return; // 处理 null 或 undefined

  // 将对象类型转换为数组（单个元素）
  let nodes = Array.isArray(tree) ? tree : [tree];

  // 如果 nodes 是空数组，直接返回
  if (nodes.length === 0) return;

  for (const [index, item] of nodes.entries()) {
    cb(item, parent, index);
    const children = item[key] || [];
    if (children.length) reverseTree(children, cb, item, key);
  }
}

/**
 * 格式化树形结构数据，生成一个新的树
 * @param node 树形数据 {xx,xx,children}
 * @param transformFunc 转换函数
 * @param key 子级的键，默认为children
 */
export function transformTree(node, transformFunc, key = 'children') {
  if (!node) return null;

  // 转换当前节点
  const transformedNode = transformFunc(node);

  // 递归转换子节点
  if (node[key]) {
    let children = node[key].map((child) => transformTree(child, transformFunc, key)).filter((child) => child !== null); // 可选：过滤空节点

    // 转换后节点transformedNode存在children时，合并二者
    if (transformedNode.children) {
      transformedNode.children = transformedNode.children.concat(children);
    } else {
      transformedNode.children = children;
    }
  }

  return transformedNode;
}

export function file2Blob(file) {
  let reader = new FileReader();
  reader.readAsArrayBuffer(file);
  let blob = null;
  reader.onload = (e) => {
    if (typeof e.target.result === 'object') {
      blob = new Blob([e.target.result]);
    } else {
      blob = e.target.result;
    }
    return blob;
  };
}

/**
 * 中文排序
 * @param {*} arr 源数组
 * @param {*} dataLeven 排序属性名
 */
export function sortChinese(arr, dataLeven) {
  // 参数：arr 排序的数组; dataLeven 数组内的需要比较的元素属性
  /* 获取数组元素内需要比较的值 */
  function getValue(option) {
    // 参数： option 数组元素
    if (!dataLeven) return option;
    var data = option;
    dataLeven.split('.').filter(function (item) {
      data = data[item];
    });
    return data + '';
  }

  arr.sort(function (item1, item2) {
    return getValue(item1).localeCompare(getValue(item2), 'zh-CN');
  });
}

// 手动添加mate标签
export function addMeta(name, content) {
  const meta = document.createElement('meta');
  meta.content = content;
  meta.name = name;
  document.getElementsByTagName('head')[0].appendChild(meta);
}

// 将时间字符串转换为分钟数
export function convertToMinutes(time) {
  const [hours, minutes] = time.split(':');
  return parseInt(hours) * 60 + parseInt(minutes);
}

// 将分钟数转换为时间字符串
export function convertToTimeString(minutes) {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  return `${String(hours).padStart(2, '0')}:${String(mins).padStart(2, '0')}`;
}

/**
 * 根据一天内占用时间段，求解一天内未占用的时间段
 * @param {*} occupiedIntervals const occupiedIntervals = [['10:20', '11:00'],['16:20', '16:40'],['10:30', '10:45'],['10:30', '11:30']];
 * @returns [['00:00', '10:20'],['11:30', '16:20'],['16:40', '24:00']]
 */

export function findAvailableTimeIntervals(occupiedIntervals) {
  // 1、遍历 occupiedIntervals 可能会出现 ['10:00': '08:00'] 的情况，表示当天10:00 - 次日 08:00 这时需要将 ['10:00': '08:00'] 转化为 ['10:00', '24:00']

  let formateIntervals = [];
  occupiedIntervals.forEach((intervals) => {
    const start = intervals[0];
    const end = intervals[1];
    const startNum = convertToMinutes(start);
    const endNum = convertToMinutes(end);
    // 表示有跨天，
    if (startNum > endNum) {
      intervals[1] = '24:00';
    }
    formateIntervals.push(intervals);
  });

  // 将时间区间转换为分钟数表示的区间
  const occupiedMinutes = formateIntervals.map((interval) => [convertToMinutes(interval[0]), convertToMinutes(interval[1])]);

  // 按照开始时间排序
  occupiedMinutes.sort((a, b) => a[0] - b[0]);

  // 寻找未占用的时间区间
  const availableIntervals = [];
  let lastEnd = 0;
  for (const interval of occupiedMinutes) {
    const start = interval[0];
    if (start > lastEnd) {
      availableIntervals.push([lastEnd, start]);
    }
    const end = interval[1];
    lastEnd = Math.max(lastEnd, end);
  }
  if (lastEnd < 24 * 60) {
    // 当天结束时间按照 23:59计算 不然会出现 当天0:00 到24:00 都是 00:00 - 00:00
    availableIntervals.push([lastEnd, 23 * 60 + 59]);
  }

  // 将分钟数表示的区间转换为时间字符串表示的区间
  const result = availableIntervals.map((interval) => [convertToTimeString(interval[0]), convertToTimeString(interval[1])]);

  return result;
}

/**
 * 在树结构中查找目标节点及其所有子节点的ID集合
 * @param {Array|Object} treeData - 树形结构数据（支持数组或对象形式）
 * @param {number} targetId - 要查找的目标节点ID
 * @returns {Array} 包含目标节点及其所有子节点ID的数组
 */
export function getNodeAndChildIds(treeData, targetId) {
  // 递归查找目标节点
  const findNode = (nodes) => {
    for (const node of nodes) {
      if (node.id === targetId) return node;
      if (node.children?.length) {
        const found = findNode(node.children);
        if (found) return found;
      }
    }
    return null;
  };

  // 递归收集节点及子节点ID
  const collectIds = (node) => {
    if (!node) return [];
    return [node.id, ...(node.children?.flatMap((child) => collectIds(child)) || [])];
  };

  // 统一处理数据结构
  const rootNodes = Array.isArray(treeData) ? treeData : [treeData];
  const targetNode = findNode(rootNodes);
  return targetNode ? collectIds(targetNode) : [];
}

/**
 * 时间时长转换工具
 * @param {number} duration 时间长度
 * @param {'ms'|'s'|'m'|'h'|'d'} [unit='ms'] 输入单位（默认毫秒）
 * @returns {Object} 包含天数(days)和小时数(hours)的对象
 */
export function calculateDuration(duration, unit = 'ms') {
  // 单位转换映射表（毫秒基准）
  const unitMap = {
    d: 24 * 60 * 60 * 1000,
    h: 60 * 60 * 1000,
    m: 60 * 1000,
    s: 1000,
    ms: 1,
  };

  // 统一转换为毫秒计算
  const totalMs = duration * (unitMap[unit] || 1);

  // 计算天数和剩余时间
  const days = Math.floor(totalMs / unitMap.d);
  const remainingMs = totalMs % unitMap.d;

  // 计算小时数（向下取整）
  const hours = Math.floor(remainingMs / unitMap.h);

  return {days, hours};
}

/**
 * 格式化输出时长字符串
 * @param {number} duration 时间长度
 * @param {'ms'|'s'|'m'|'h'|'d'} [unit='ms'] 输入单位
 * @returns {string} 去零值后的字符串，如"3天5小时"
 */
function formatDuration(duration, unit = 'ms') {
  const {days, hours} = calculateDuration(duration, unit);
  const parts = [];

  // 智能过滤零值：仅当有天数时才显示，小时在无天数时显示
  if (days > 0) parts.push(`${days}天`);
  if (hours > 0 || days === 0) parts.push(`${hours}小时`);

  return parts.join('') || '0小时'; // 全零值兜底
}
