<template>
  <el-dialog :title="groupName" :visible.sync="visible" width="900px" :close-on-click-modal="false" @close="close">
    <div class="life-dialog-container">
      <el-table :data="list" height="100%" @row-click="handleRowClick">
        <el-table-column type="index" label="序号" width="50" show-overflow-tooltip fixed="left"></el-table-column>
        <el-table-column prop="lifeStatus" label="寿命状态" min-width="180" :show-overflow-tooltip="true" fixed="left" align="center">
          <template slot-scope="{ row }">
            <div class="life-status-wrap">
              <div class="life-tag">
                <el-tag :class="[row.lifeStatus, 'tag']" v-if="row.lifeStatus">{{ getLifeStatusType(row).label }}</el-tag>
              </div>
              <el-progress
                :percentage="getLifeProgress(row)"
                :text-inside="true"
                :stroke-width="18"
                class="life-progress"
                :color="getLifeProgressColor(row)"
              ></el-progress>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="deviceName" label="设备名称" min-width="180" :show-overflow-tooltip="true" fixed="left"> </el-table-column>
        <el-table-column prop="commissioningDate" label="投入使用时间" min-width="160" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            {{ scope.row.commissioningDate ? new Date(scope.row.commissioningDate).Format('yyyy-MM-dd hh:mm') : '--' }}
          </template>
        </el-table-column>
        <el-table-column label="投入使用时长" min-width="150" align="center" :show-overflow-tooltip="true">
          <template slot-scope="{ row }">
            {{ row.commissioningDateDayHour || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="workTime" label="实际使用时长" min-width="130" align="center" :show-overflow-tooltip="true">
          <template slot-scope="{ row }"> {{ row.workTimeDayHour || '-' }} </template>
        </el-table-column>
        <el-table-column label="在线时长" min-width="130" align="center" :show-overflow-tooltip="true">
          <template slot-scope="{ row }">
            {{ row.onlineDayHour || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="offlineNum" label="离线次数" align="center" min-width="100" :show-overflow-tooltip="true">
          <template slot-scope="{ row }"> {{ row.offlineNum === null || row.offlineNum === undefined ? '-' : row.offlineNum }} </template>
        </el-table-column>
        <el-table-column prop="spaceName" label="所属空间" min-width="140" :show-overflow-tooltip="true"> </el-table-column>
        <el-table-column prop="gatewayName" label="所属智慧中控" min-width="120" :show-overflow-tooltip="true"> </el-table-column>
        <el-table-column prop="typeName" label="设备类型" min-width="160" :show-overflow-tooltip="true"> </el-table-column>
        <el-table-column prop="model" label="设备型号" min-width="140" :show-overflow-tooltip="true"> </el-table-column>
        <el-table-column prop="brand" label="设备品牌" min-width="120" :show-overflow-tooltip="true"> </el-table-column>
        <el-table-column label="操作" fixed="right" width="100" align="center">
          <template slot-scope="{ row }">
            <el-button size="mini" class="edit-btn" icon="el-icon-edit" @click.stop="editDevice(row)"> 编辑 </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </el-dialog>
</template>

<script>
  import dlg from '@/mixins/dlg';
  import { LIFE_STATUS } from '@/views/iotControl/enum.js';
  import deviceEditDialog from './deviceEditDialog.vue';
  import { getLifeStatusInGroup } from '@/api/iotControl';
  import transform from '@/utils/transform';
  import { calculateDuration } from '@/utils/utils';
  export default {
    mixins: [dlg],
    components: {
      deviceEditDialog,
    },
    props: {
      groupId: {
        type: String,
        default: '',
      },
      groupName: {
        type: String,
        default: '',
      },
    },
    data() {
      return {
        LIFE_STATUS,
        list: [],
      };
    },
    created() {
      this.getLifeStatusInGroup();
    },

    methods: {
      getLifeStatusInGroup() {
        let params = {
          groupId: this.groupId,
        };
        getLifeStatusInGroup(params)
          .then(({ data }) => {
            data.forEach((item) => {
              // 投入使用时长
              item.commissioningDateDayHour = '';
              if (item.commissioningDate) {
                const { days, hours } = calculateDuration(new Date().getTime() - item.commissioningDate);
                item.commissioningDateDayHour = `${days}天${hours}小时`;
              }

              // 实际使用时长
              item.workTimeDayHour = '';
              if (item.workTime) {
                const { days, hours } = calculateDuration(item.workTime, 's');
                item.workTimeDayHour = `${days}天${hours}小时`;
              }

              // 在线时长
              item.onlineDayHour = '';
              if (item.onlineDuration) {
                const { days, hours } = calculateDuration(item.onlineDuration, 's');
                item.onlineDayHour = `${days}天${hours}小时`;
              }
            });
            this.list = data;
          })
          .catch((e) => {});
      },
      getLifeStatusType(row) {
        return this.LIFE_STATUS.find((item) => item.value === row.lifeStatus);
      },
      deviceEditDlg: transform(deviceEditDialog),
      editDevice(row) {
        let that = this;
        this.deviceEditDlg({
          propsData: {
            deviceObj: row,
          },
          methods: {
            refresh() {
              that.getLifeStatusInGroup();
            },
          },
        });
      },
      handleRowClick(row) {
        this.$emit('rowClick', row);
      },
      getLifeProgress(row) {
        // workTime 单位秒 theoreticalLifespan 单位小时
        const progress = 100 - Math.trunc((row.workTime / 3600 / row.theoreticalLifespan) * 100);
        if (progress < 0) {
          return 0;
        }
        // console.log('progress', progress);
        return Number(progress.toFixed(2));
      },
      getLifeProgressColor(row) {
        if (row.lifeStatus === 'fresh') {
          return '#3dcca6';
        } else if (row.lifeStatus === 'good') {
          return '#87CEFA';
        } else if (row.lifeStatus === 'renew') {
          return '#fabe46';
        } else if (row.lifeStatus === 'overdue') {
          return '#f56c6c';
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .life-dialog-container {
    height: 380px;
  }
  .tag {
    &.fresh {
      color: #808080;
      background-color: rgba(61, 204, 166, 0.3);
      border: 1px solid #3dcca6;
    }
    &.good,
    &.under {
      color: #808080;
      background-color: rgba(135, 206, 250, 0.3);
      border: 1px solid #87cefa;
    }
    &.renew,
    &.expiring {
      color: #808080;
      background-color: rgba(250, 190, 70, 0.3);
      border: 1px solid #fabe46;
    }
    &.overdue {
      color: #808080;
      background-color: rgba(245, 108, 108, 0.3);
      border: 1px solid #f56c6c;
    }
  }
  .life-status-wrap {
    width: 180px;
    display: flex;
    align-items: center;
    .life-tag {
      width: 71px;
    }
    .life-progress {
      width: 60px;
      margin-left: 10px;
    }
  }
</style>
