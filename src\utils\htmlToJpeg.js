import html2Canvas from 'html2canvas';

// domElement 页面dom , fileName 文件名
export function htmlToJpeg(domElement, fileName = '巡检报表') {
  html2Canvas(domElement, {
    useCORS: true, //是否尝试使用 CORS 从服务器加载图片
    allowTaint: true, //是否允许不同源的图片污染画布
    scale: 3, // 用于渲染的比例
  })
    .then((canvas) => {
      const link = document.createElement('a');
      link.download = `${fileName}.jpg`;
      link.href = canvas.toDataURL('image/jpeg', 1.0);
      link.click();
    })
    .catch((err) => {
      console.log(err);
    });
}
