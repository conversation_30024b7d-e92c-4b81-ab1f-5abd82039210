import Layout from '@/layout';
import BlankLayout from '@/layout/BlankLayout.vue';
import RouteView from '@/layout/RouteView.vue';
import store from '@/store';
import i18n from '@/i18n';
import { reverseTree } from '@/utils/utils';
import { getExternalSystemLink } from '@/api/system';

// 前端路由表
const constantRouterComponents = {
  // 基础页面 layout 必须引入
  Layout: Layout,
  BlankLayout: BlankLayout,
  RouteView: RouteView,
  401: () => import(/* webpackChunkName: "error" */ '@/views/error-page/401'),
  404: () => import(/* webpackChunkName: "error" */ '@/views/error-page/404'),

  // 你需要动态引入的页面组件
  dashboard: () => import('@/views/dashboard/index'),

  // 系统管理
  divisionManagement: () => import('@/views/systemManage/divisionManagement/index'),
  roleManagement: () => import('@/views/systemManage/roleManagement/index'),
  userManagement: () => import('@/views/systemManage/userManagement/index'),
  operateLog: () => import('@/views/systemManage/operateLog'),
  loginLog: () => import('@/views/systemManage/loginLog'),
  systemSetting: () => import('@/views/systemManage/systemSetting/index'),
  LDAP: () => import('@/views/systemManage/LDAP/index'),
  serverManage: () => import('@/views/systemManage/serverManage/index'),
  // 外部系统
  externalSystem: () => import('@/views/systemManage/externalSystem/index'),

  // 终端管理
  terminalManagement: () => import('@/views/terminalManage/terminalManagement/index'),
  terminalUpgrade: () => import('@/views/terminalManage/apkPackage/terminalUpgrade'),
  applicationManage: () => import('@/views/terminalManage/apkPackage/applicationManage'),
  appDetails: () => import('@/views/terminalManage/apkPackage/appDetails'),
  // idea hub管理
  hubMonitor: () => import('@/views/hubManage/hubMonitor/index'),
  // hubDetail: () => import("@/views/hubManage/hubMonitor/detail"),
  // 信发管理
  dssMonitor: () => import('@/views/infoManage/dssMonitor/index'),
  myDssMonitor: () => import('@/views/infoManage/dssMonitor/my'),
  terminalGroup: () => import('@/views/infoManage/terminalGroup/index'),
  materialManage: () => import('@/views/infoManage/materialManage/index'),
  fileHome: () => import('@/views/infoManage/materialManage/fileHome'),
  fileExplorer: () => import('@/views/infoManage/materialManage/fileExplorer'),
  fileProperties: () => import('@/views/infoManage/materialManage/fileProperties'),
  fileSearch: () => import('@/views/infoManage/materialManage/fileSearch'),
  materialAudit: () => import('@/views/infoManage/materialAudit'),
  materialStatistic: () => import('@/views/infoManage/materialManage/materialStatistic/index'),
  myAudit: () => import('@/views/infoManage/myAudit'),
  programTemplate: () => import('@/views/infoManage/programTemplate/index'),
  programManagement: () => import('@/views/infoManage/programManagement/index'),
  programAudit: () => import('@/views/infoManage/programAudit'),
  playManagement: () => import('@/views/infoManage/playManagement/index'),
  realTimeInfo: () => import('@/views/infoManage/realTimeInfo/index'),
  playlistNumber: () => import('@/views/infoManage/playManagement/playlistNumber'),
  weatherSource: () => import('@/views/infoManage/weatherSource/index'),
  dssDataBoard: () => import('@/views/infoManage/dataBoard/index'),

  //节目制作
  programCreate: () => import('@/views/infoManage/programCreate/index'),

  // 视频会议
  terminalList: () => import('@/views/videoConference/terminalList'),

  // 会议管理 - 会议预约
  createMeeting: () => import('@/views/conferenceManage/meeting/create/index'),
  roomUsage: () => import('@/views/conferenceManage/meeting/roomUsage/index'),
  confTemplate: () => import('@/views/conferenceManage/meeting/template/index'),
  myConference: () => import('@/views/conferenceManage/meeting/myConf/index'),
  conferenceLib: () => import('@/views/conferenceManage/meeting/confLib/index'),
  // 机构日程
  deptConferenceLib: () => import('@/views/conferenceManage/meeting/confLib/deptConfLib.vue'),
  joinConf: () => import('@/views/conferenceManage/meeting/joinConf/index'),
  quickCreate: () => import('@/views/conferenceManage/meeting/create/quick'),
  secondCreate: () => import('@/views/conferenceManage/meeting/second/index'),
  myRoomConference: () => import('@/views/conferenceManage/meeting/myRoomConf/index'),

  // 会议列表控制
  myConfControl: () => import('@/views/conferenceManage/meeting/myConf/myConfControl'),
  confLibControl: () => import('@/views/conferenceManage/meeting/confLib/confLibControl'),
  deptConfLibControl: () => import('@/views/conferenceManage/meeting/confLib/deptConfLibControl.vue'),
  myRoomConfControl: () => import('@/views/conferenceManage/meeting/myRoomConf/myRoomConfControl'),

  // 会议管理 - 会议审核
  auditProcess: () => import('@/views/conferenceManage/auditManage/auditProcess/index'),
  conferenceAudit: () => import('@/views/conferenceManage/auditManage/conferenceAudit/index'),

  // 会议管理 - 会议配置
  mcuTemplate: () => import('@/views/conferenceManage/confSetting/mcuTemplate/index'),
  conferenceParamsTemplate: () => import('@/views/conferenceManage/confSetting/conferenceTemplate/index'),
  personalRoom: () => import('@/views/conferenceManage/confSetting/virtual/personalRoom/index'),
  publicRoom: () => import('@/views/conferenceManage/confSetting/virtual/publicRoom/index'),
  conferenceLevel: () => import('@/views/conferenceManage/confSetting/meetingLevel/meetingLevel'),

  // 会议管理 - 会管运维
  roomManage: () => import('@/views/conferenceManage/roomSetting/roomManage/index'),
  infrastructure: () => import('@/views/conferenceManage/roomSetting/infrastructure/index'),
  myRepair: () => import('@/views/conferenceManage/roomSetting/myRepair'),
  systemNotice: () => import('@/views/advanceSetting/systemNotice/list'),
  appFeedback: () => import('@/views/conferenceManage/roomSetting/appFeedback/index'),
  repairManage: () => import('@/views/conferenceManage/roomSetting/repairManage'),
  workplace: () => import('@/views/conferenceManage/roomSetting/workplace/index'),
  systemParam: () => import('@/views/conferenceManage/roomSetting/systemParam/index'),

  //会议管理 - 统计分析
  confStatistic: () => import('@/views/conferenceManage/statistics/conf/index'),
  usageRate: () => import('@/views/conferenceManage/statistics/usageRate/index'),
  rank: () => import('@/views/conferenceManage/statistics/rank/index'),

  //会议管理 - 设备组网
  serviceArea: () => import('@/views/conferenceManage/deviceManage/serviceArea/index'),
  videoTerminal: () => import('@/views/conferenceManage/deviceManage/videoTerminal/index'),
  mcu: () => import('@/views/conferenceManage/deviceManage/mcu/index'),
  dynamicAccount: () => import('@/views/conferenceManage/deviceManage/dynamicAccount/index'),

  // 监控中心
  // 系统监控
  systemMonitor: () => import('@/views/monitor/systemMonitor/index'),
  // 终端监控
  terminalMonitor: () => import('@/views/monitor/terminalMonitor/index'),
  // 呼叫记录
  callRecords: () => import('@/views/monitor/callRecords/index'),
  // 阈值管理
  thresholdManage: () => import('@/views/monitor/configurationCenter/index'),
  // 告警配置
  alarmConfiguration: () => import('@/views/monitor/configurationCenter/alarm'),

  // 系统通知
  emailNotice: () => import('@/views/systemManage/systemNotice/index'),
  // 短信配置
  smsConfig: () => import('@/views/systemManage/smsConfig/index'),
  //授权信息
  authorization: () => import('@/views/dataManage/authorization/index'),

  //智能网关
  integrated: () => import('@/views/dataManage/integrated/index'),

  //网络诊断
  callServer: () => import('@/views/ndt/callServer/index'),
  callTerminal: () => import('@/views/ndt/callTerminal/index'),
  testTemplate: () => import('@/views/ndt/testTemplate/index'),
  callParamTemplate: () => import('@/views/ndt/callParamTemplate'),
  // addTestTemplate:()=>import("@/views/ndt/testTemplate/addTemplate"),

  universalObjectModel: () => import('@/views/iotControl/productConfig/universalObjectModel/index'),
  productType: () => import('@/views/iotControl/productConfig/productType/index'),
  productManagement: () => import('@/views/iotControl/productConfig/productManagement/index'),
  configManagement: () => import('@/views/iotControl/productConfig/configManagement/index'),
  brandManagement: () => import('@/views/iotControl/productConfig/brandManagement/index'),
  // 物联集控
  IotGateway: () => import('@/views/iotControl/iotManage/gateway/index'),
  IotCustomSpace: () => import('@/views/iotControl/iotManage/customSpace/index'),
  IotLabel: () => import('@/views/iotControl/iotManage/label/index'),
  IotSceneMode: () => import('@/views/iotControl/IotScenelinkage/IotSceneMode/index'),
  IotIntelligentLinkage: () => import('@/views/iotControl/IotScenelinkage/IotIntelligentLinkage/index'),
  IotSceneModeNew: () => import('@/views/iotControl/IotScenelinkageNew/IotSceneModeNew/index'),
  IotIntelligentLinkageNew: () => import('@/views/iotControl/IotScenelinkageNew/IotIntelligentLinkageNew/index'),
  // 物联集控固件管理
  IotFireware: () => import('@/views/iotControl/iotUpgrade/iotFireware/index'),
  IotGatewayUpgrade: () => import('@/views/iotControl/iotUpgrade/gatewayUpgrade/index'),

  // 直播服务
  // 直播频道
  channel: () => import('@/views/live/channelManage/channel/index'),
  // 直播监控
  channelStream: () => import('@/views/live/channelManage/channelStream/index'),
  // 直播统计
  liveStatistic: () => import('@/views/live/liveManage/statistic/index'),
  // 服务器监控
  sys: () => import('@/views/live/liveManage/sys/index'),
  // 区域管理
  area: () => import('@/views/live/liveManage/area/index'),
  // 路由管理
  router: () => import('@/views/live/liveManage/router/index'),
  // 域名管理
  domain: () => import('@/views/live/liveManage/domain/index'),
  // 推流管理
  sourceDevice: () => import('@/views/live/liveManage/sourceDevice/index'),
  // 权限管理
  authManage: () => import('@/views/live/liveManage/auth/index'),

  //设备运维
  IotAlarmCenter: () => import('@/views/iotControl/deviceMaintenance/IotAlarmCenter/index'),
  intelligentMonitor: () => import('@/views/iotControl/deviceMaintenance/intelligentMonitor/index'),
  deviceStatistic: () => import('@/views/iotControl/deviceMaintenance/deviceStatistic/index'),
  // devicelnspection: () => import('@/views/iotControl/deviceMaintenance/devicelnspection/index'),
  iotLogCenter: () => import('@/views/iotControl/deviceMaintenance/iotLogCenter/index'),
  inspectTask: () => import('@/views/iotControl/deviceMaintenance/deviceInspect/index'),
  inspectRecord: () => import('@/views/iotControl/deviceMaintenance/inspectRecord/index'),
  //工单管理
  workorderList: () => import('@/views/iotControl/workorderManage/workorderList/index'),
  myWorkorder: () => import('@/views/iotControl/workorderManage/myWorkorder/index'),
  //智慧能耗
  intelligentEnergy: () => import('@/views/iotControl/intelligentEnergy/index'),
  bigData: () => import('@/views/iotControl/bigData/index'),
  //超级管理
  adminManage: () => import('@/views/superAdmin/adminManage'),
  companyManage: () => import('@/views/superAdmin/companyManage'),
  permissionManage: () => import('@/views/superAdmin/permissionManage/index'),

  // AI
  // 知识库
  knowledgeLibrary: () => import('@/views/ai/knowledgeCenter/library/index'),
  // 知识图谱
  knowledgeGraph: () => import('@/views/ai/knowledgeCenter/graph/index'),
  // AI助手
  AIHelper: () => import('@/views/ai/aiHelper/index'),

  // exception
  Exception401: () => import(/* webpackChunkName: "fail" */ '@/views/error-page/401'),
  Exception404: () => import(/* webpackChunkName: "fail" */ '@/views/error-page/404'),

  // 登录
  Login: () => import('@/views/login/index'),
  // 'TestWork': () => import(/* webpackChunkName: "TestWork" */ '@/views/dashboard/TestWork')
};

// 前端未找到页面路由（固定不用改）
const notFoundRouter = {
  path: '*',
  redirect: '/404',
  hidden: true,
  name: '404',
};

const advanceSettingRouter = {
  path: '/advanceSetting',
  component: Layout,
  redirect: '/advanceSetting/serverDns',
  name: 'AdvanceSetting',
  meta: {
    title: '专家配置',
    icon: 'el-icon-warning-outline',
  },
  children: [
    {
      path: 'serverDns',
      component: () => import('@/views/advanceSetting/serverDns/index'),
      name: 'ServerDns',
      meta: { title: '服务通信' },
    },
    {
      path: 'terminalDns',
      component: () => import('@/views/advanceSetting/terminalDns/index'),
      name: 'TerminalDns',
      meta: { title: '终端通信' },
    },
    {
      path: 'loginPageSetting',
      component: () => import('@/views/advanceSetting/loginSetting/index'),
      name: 'loginPageSetting',
      meta: { title: '系统设置' },
    },
    {
      path: 'runtimeLog',
      component: () => import('@/views/advanceSetting/runtimeLog/index'),
      name: 'runtimeLog',
      meta: { title: '运行日志' },
    },
    {
      path: 'thirdPartyAccount',
      component: () => import('@/views/advanceSetting/thirdPartyAccount/index'),
      name: 'ThirdPartyAccount',
      meta: { title: '第三方应用' },
    },
    {
      path: 'confModule',
      component: () => import('@/views/advanceSetting/confModule/index'),
      name: 'ConfModule',
      meta: { title: '会议指引' },
      licenseKey: 'max',
    },
  ],
};

const superRouter = {
  path: '/super',
  component: Layout,
  redirect: '/super/permissionManage',
  name: 'Super',
  meta: {
    title: '超级管理',
    icon: 'el-icon-help',
    breadcrumb: false,
  },
  children: [
    // {
    //   path: 'adminManage',
    //   component: () => import('@/views/superAdmin/adminManage'),
    //   name: 'AdminManage',
    //   meta: { title: '超级管理员' }
    // },
    {
      path: 'permissionManage',
      component: () => import('@/views/superAdmin/permissionManage/index'),
      name: 'PermissionManage',
      meta: { title: '权限管理' },
    },
  ],
};

// 根级菜单
const rootRouter = [
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        component: () => import('@/views/dashboard/index'),
        name: 'Dashboard',
        meta: { title: '首页', icon: 'el-icon-s-home', affix: true },
      },
      {
        path: 'screenCapture/:id',
        component: () => import('@/views/terminalManage/screenCapture/index'),
        name: 'ScreenCapture',
        hidden: true,
        meta: { title: '截屏' },
      },
      // 物联设备的操作页面，用户可能没有物联集控权限，也可能没有会议室权限，故放在首页下
      {
        path: 'IotDeviceAction/:id/:deviceType/:devSubType/:model',
        component: () => import('@/views/iotControl/iotManage/device/deviceAction'),
        name: 'IotDeviceAction',
        hidden: true,
        meta: { title: '设备操作' },
      },
      {
        path: 'emqx/:mac/:name',
        name: 'Emqx',
        component: () => import('@/views/infoManage/dssMonitor/emqx'),
        hidden: true,
        meta: { title: '通讯数据' },
      },
    ],
  },
  // 全屏页面
  {
    path: 'modifyPassword/',
    component: () => import('@/views/systemManage/modifyPassword/index'),
    name: 'ModifyPassword',
    hidden: true,
    meta: { title: '修改密码' },
  },
  {
    path: '/programCreate',
    component: () => import('@/views/infoManage/programCreate/index'),
    name: 'ProgramCreate',
    hidden: true,
  },
  {
    path: '/dataBoard/:testId/:historyId',
    component: () => import('@/views/ndt/dataBoard/index'),
    name: 'DataBoard',
    hidden: true,
  },
  {
    path: '/chart/:component/:testId/:historyId/:title',
    component: () => import('@/views/ndt/dataBoard/chart'),
    name: 'Chart',
    hidden: true,
  },
  {
    path: '/conferenceControl/:id',
    name: 'ConferenceControl',
    component: () => import('@/views/conferenceManage/meeting/control/index'),
    hidden: true,
  },
  {
    path: '/confDag/:confId',
    component: () => import('@/views/conferenceManage/meeting/confDag/index'),
    name: 'ConfDag',
    hidden: true,
  },
  {
    path: '/bigDataReal',
    component: () => import('@/views/iotControl/bigData/main.vue'),
    name: 'bigDataReal',
    hidden: true,
  },
  {
    path: '/dssDataBoard',
    component: () => import('@/views/infoManage/dataBoard/index'),
    name: 'DssDataBoard',
    hidden: true,
  },
  {
    path: '/topoPage/:spaceId',
    component: () => import('@/views/iotControl/iotManage/customSpace/spaceControl/topoPage.vue'),
    name: 'topoPage',
    hidden: true,
  },
];

const hideRouter = [
  // 机构详情
  {
    parent: 'Organization',
    router: {
      path: 'deptDetail/:id',
      component: () => import('@/views/systemManage/divisionManagement/detail/index'),
      name: 'DeptDetails',
      hidden: true,
      meta: { title: `${i18n.t('deptLabel')}详情` },
    },
  },
  // 添加角色
  {
    parent: 'Organization',
    router: {
      path: 'addRole/:id?',
      component: () => import('@/views/systemManage/roleManagement/add'),
      name: 'AddRole',
      hidden: true,
      meta: { title: '添加角色' },
    },
  },
  {
    parent: 'TerminalManage',
    router: {
      path: 'appDetails/:type/:name/:pkgType/:other',
      component: () => import('@/views/terminalManage/apkPackage/appDetails'),
      name: 'AppDetails',
      hidden: true,
      meta: { title: '应用详情' },
    },
  },
  // 物联集控模块 start
  {
    parent: 'IotUpgrade',
    router: {
      path: 'fireware/:name/:pkgType/:other',
      component: () => import('@/views/iotControl/iotUpgrade/iotFireware/appDetails'),
      name: 'IotFirewareDetails',
      hidden: true,
      meta: { title: '固件详情' },
    },
  },
  {
    parent: 'IotUpgrade',
    router: {
      path: 'IotGatewayUpgrade/:name/:other',
      component: () => import('@/views/iotControl/iotUpgrade/gatewayUpgrade/appDetails.vue'),
      name: 'IotAppDetails',
      hidden: true,
      meta: { title: '物联网关应用' },
    },
  },
  // 物联集控模块 end
  {
    parent: 'MaterialManage',
    router: {
      path: 'explorer',
      name: 'FileExplorer',
      component: () => import('@/views/infoManage/materialManage/fileExplorer'),
      hidden: true,
      meta: { breadcrumb: false },
    },
  },
  {
    parent: 'MaterialManage',
    router: {
      path: 'properties',
      name: 'FileProperties',
      component: () => import('@/views/infoManage/materialManage/fileProperties'),
      hidden: true,
      meta: { breadcrumb: false },
    },
  },
  {
    parent: 'MaterialManage',
    router: {
      path: 'search',
      name: 'FileSearch',
      component: () => import('@/views/infoManage/materialManage/fileSearch'),
      hidden: true,
      meta: { breadcrumb: false },
    },
  },
  {
    parent: 'Play',
    router: {
      path: 'addProgramme/:type?/:id?',
      name: 'AddProgramme',
      component: () => import('@/views/infoManage/playManagement/add'),
      hidden: true,
      meta: { title: '新建节目单' },
    },
  },
  {
    parent: 'Ndt',
    router: {
      path: 'add',
      name: 'AddTestTemplate',
      component: () => import('@/views/ndt/testTemplate/addTemplate'),
      hidden: true,
      meta: { title: '新建' },
    },
  },
  {
    parent: 'Ndt',
    router: {
      path: 'analysis/:testId/:historyId/:activeTab',
      name: 'TestAnalysis',
      component: () => import('@/views/ndt/testTemplate/testAnalysis/index'),
      hidden: true,
      meta: { title: '探测诊断' },
    },
  },
  {
    parent: 'Ndt',
    router: {
      path: '/chartAnalysis/:historyId/:edgeId/:timestamp?/:eleType?/:dataType?',
      name: 'ChartAnalysis',
      component: () => import('@/views/ndt/testTemplate/chartAnalysis'),
      hidden: true,
      meta: { title: '图表' },
    },
  },
  {
    parent: 'Ndt',
    router: {
      path: '/ndt/testTemplate/detection/:testCaseId',
      name: 'TestCaseDetection',
      component: () => import('@/views/ndt/testTemplate/detection'),
      hidden: true,
      meta: { title: '链路检测' },
    },
  },
  {
    parent: 'DeviceManage',
    router: {
      path: 'addTerminal/:id?/:action?',
      name: 'AddTerminal',
      component: () => import('@/views/conferenceManage/deviceManage/addTerminal/index'),
      hidden: true,
      meta: { title: '视频终端管理' },
    },
  },
  {
    parent: 'DeviceManage',
    router: {
      path: 'addMcu/:id?',
      name: 'AddMcu',
      component: () => import('@/views/conferenceManage/deviceManage/mcu/add/index'),
      hidden: true,
      meta: { title: '平台管理' },
    },
  },
  // MCU列表页
  {
    parent: 'DeviceManage',
    router: {
      path: 'mcuList/:id?',
      name: 'McuList',
      component: () => import('@/views/conferenceManage/deviceManage/mcu/add/mcu-list'),
      hidden: true,
      meta: { title: '资源列表' },
    },
  },
  {
    parent: 'DeviceManage',
    router: {
      path: 'dataSync/:id?/:mcuName?',
      name: 'DataSync',
      component: () => import('@/views/conferenceManage/deviceManage/mcu/add/data-sync'),
      hidden: true,
      meta: { title: `${i18n.t('deptLabel')}同步` },
    },
  },
  {
    parent: 'DeviceManage',
    router: {
      path: 'terminalSync/:id?',
      name: 'TerminalSync',
      component: () => import('@/views/conferenceManage/deviceManage/mcu/add/terminal-sync'),
      hidden: true,
      meta: { title: '终端同步' },
    },
  },
  //
  {
    parent: 'DeviceManage',
    router: {
      path: 'addServiceArea/:type/:id?',
      name: 'AddServiceArea',
      component: () => import('@/views/conferenceManage/deviceManage/serviceArea/add/index'),
      hidden: true,
      meta: { title: `${i18n.t('serviceArea')}管理` },
    },
  },
  {
    parent: 'RoomSetting',
    router: {
      path: 'addMcuTemplate',
      name: 'AddMcuTemplate',
      component: () => import('@/views/conferenceManage/confSetting/mcuTemplate/add/index'),
      hidden: true,
      meta: { title: '参数模板' },
    },
  },
  {
    parent: 'ConferenceGuide',
    router: {
      path: 'addConferenceTemplate/:id?',
      name: 'AddConferenceTemplate',
      component: () => import('@/views/conferenceManage/confSetting/conferenceTemplate/add/index'),
      hidden: true,
      meta: { title: '会议参数' },
    },
  },
  {
    parent: 'Room',
    router: {
      path: 'addConferenceRoom/:id?',
      name: 'AddConferenceRoom',
      component: () => import('@/views/conferenceManage/roomSetting/roomManage/add/index'),
      hidden: true,
      meta: { title: '会议室' },
    },
  },
  {
    parent: 'Room',
    router: {
      path: 'roomIotDevice/:id?',
      name: 'RoomIotDevice',
      component: () => import('@/views/conferenceManage/roomSetting/roomManage/iotDevice/index'),
      hidden: true,
      meta: { title: '物联设备' },
    },
  },
  {
    parent: 'ConferenceManage',
    router: {
      path: 'conferenceDetail/:id/:action?',
      name: 'ConferenceDetail',
      component: () => import('@/views/conferenceManage/meeting/detail/index'),
      hidden: true,
      meta: { title: '会议详情' },
    },
  },
  {
    parent: 'ConferenceManage',
    router: {
      path: 'add/:id',
      name: 'AddSecondCreate',
      component: () => import('@/views/conferenceManage/meeting/second/add'),
      hidden: true,
      meta: { title: '设置二次预约' },
    },
  },
  {
    parent: 'DeviceMaintenance',
    router: {
      path: 'IotDeviceInfo/:id',
      name: 'IotDeviceInfo',
      component: () => import('@/views/iotControl/iotManage/device/detail'),
      hidden: true,
      meta: { title: '设备详情' },
    },
  },
  {
    parent: 'IotManage',
    router: {
      path: 'IotSpaceScendMode/:spaceId/:spaceName',
      name: 'IotSpaceScendMode',
      component: () => import('@/views/iotControl/iotManage/space/spaceSceneMode'),
      hidden: true,
      meta: { title: '空间场景' },
    },
  },
  {
    parent: 'IotManage',
    router: {
      path: 'spaceSceneLinkage/:spaceId/:spaceName',
      name: 'spaceSceneLinkage',
      component: () => import('@/views/iotControl/iotManage/space/spaceSceneLinkage'),
      hidden: true,
      meta: { title: '设备联动' },
    },
  },
  {
    parent: 'IotGateway',
    router: {
      path: 'addGateway',
      name: 'AddGateway',
      component: () => import('@/views/iotControl/iotManage/gateway/add/index'),
      hidden: true,
      meta: { title: '添加网关' },
    },
  },
  {
    parent: 'Organization',
    router: {
      path: 'visitorFace',
      name: 'visitorFace',
      component: () => import('@/views/systemManage/userManagement/visitorFace/index'),
      hidden: true,
      meta: { title: '游客人脸' },
    },
  },
  {
    parent: 'Organization',
    router: {
      path: 'identificationRecord',
      name: 'identificationRecord',
      component: () => import('@/views/systemManage/userManagement/identificationRecord/index'),
      hidden: true,
      meta: { title: '识别记录' },
    },
  },
  {
    parent: 'Organization',
    router: {
      path: 'idRecordExport',
      name: 'idRecordExport',
      component: () => import('@/views/systemManage/userManagement/identificationRecord/templateExport/index'),
      hidden: true,
      meta: { title: '识别记录导出' },
    },
  },
  {
    parent: 'ChannelManage',
    router: {
      path: 'add/:id?/:deptId?',
      name: 'AddChannel',
      component: () => import('@/views/live/channelManage/channel/add'),
      hidden: true,
      meta: { title: '新建' },
    },
  },
  {
    parent: 'ZteOps',
    router: {
      path: 'systemMonitorDetail/:id/:activeTab?',
      name: 'SystemMonitorDetail',
      component: () => import('@/views/monitor/systemMonitor/monitor'),
      hidden: true,
      meta: { title: '系统监控' },
    },
  },
  {
    parent: 'ZteOps',
    router: {
      path: 'terminalMonitorDetail/:id/:activeTab?',
      name: 'TerminalMonitorDetail',
      component: () => import('@/views/monitor/terminalMonitor/terminal'),
      hidden: true,
      meta: { title: '终端监控' },
    },
  },
  {
    parent: 'ZteOps',
    router: {
      path: 'callRecordDetail/:id?/:deviceId?',
      name: 'CallRecordDetail',
      component: () => import('@/views/monitor/callRecords/call'),
      hidden: true,
      meta: { title: '呼叫记录' },
    },
  },
  {
    parent: 'IotCustomSpace',
    router: {
      path: 'spaceControl/:id',
      name: 'spaceControl',
      component: () => import('@/views/iotControl/iotManage/customSpace/spaceControl/index'),
      hidden: true,
      meta: { title: '空间控制' },
    },
  },
  {
    parent: 'WorkorderManage',
    router: {
      path: 'addWorkorder/:id?',
      name: 'addWorkorder',
      component: () => import('@/views/iotControl/workorderManage/addWorkorder/index'),
      hidden: true,
      meta: { title: '工单编辑' },
    },
  },
  {
    parent: 'WorkorderManage',
    router: {
      path: 'workorderDetail/:id?',
      name: 'workorderDetail',
      component: () => import('@/views/iotControl/workorderManage/workorderDetail/index'),
      hidden: true,
      meta: { title: '工单详情' },
    },
  },
  {
    parent: 'IotCustomSpace',
    router: {
      path: 'addSpace/:id?',
      name: 'addSpace',
      component: () => import('@/views/iotControl/iotManage/customSpace/addSpace/index'),
      hidden: true,
      meta: { title: '空间编辑' },
    },
  },
  {
    parent: 'ProductConfig',
    router: {
      path: 'editProduct/:id?',
      name: 'editProduct',
      component: () => import('@/views/iotControl/productConfig/productManagement/editProduct'),
      hidden: true,
      meta: { title: '编辑产品' },
    },
  },
  {
    parent: 'IotScenelinkageNew',
    router: {
      path: 'editRule/:type?/:id?',
      name: 'editRule',
      component: () => import('@/views/iotControl/IotScenelinkageNew/editRule/index'),
      hidden: true,
      meta: { title: '规则编辑' },
    },
  },
  {
    parent: 'DeviceMaintenance',
    router: {
      path: 'editAlarmRule/:id?',
      name: 'editAlarmRule',
      component: () => import('@/views/iotControl/deviceMaintenance/editAlarmRule/index'),
      hidden: true,
      meta: { title: '告警规则编辑' },
    },
  },
  {
    parent: 'DeviceInspect',
    router: {
      path: 'viewEditInspect/:type?/:id?',
      name: 'viewEditInspect',
      component: () => import('@/views/iotControl/deviceMaintenance/viewEditInspect/index'),
      hidden: true,
      meta: { title: '巡检计划详情' },
    },
  },
  {
    parent: 'DeviceInspect',
    router: {
      path: 'viewInspectRecordDetail/:id?',
      name: 'viewInspectRecordDetail',
      component: () => import('@/views/iotControl/deviceMaintenance/viewInspectRecordDetail/index'),
      hidden: true,
      meta: { title: '巡检记录详情' },
    },
  },
  {
    parent: 'DeviceInspect',
    router: {
      path: 'viewEditInspectTemplate/:type?/:id?',
      name: 'viewEditInspectTemplate',
      component: () => import('@/views/iotControl/deviceMaintenance/viewEditInspectTemplate/index'),
      hidden: true,
      meta: { title: '巡检模板详情' },
    },
  },
  {
    parent: 'DeviceMaintenance',
    router: {
      path: 'alarmRecordDetail/:id?',
      name: 'alarmRecordDetail',
      component: () => import('@/views/iotControl/deviceMaintenance/alarmRecordDetail/index'),
      hidden: true,
      meta: { title: '告警记录详情' },
    },
  },
  {
    parent: 'DeviceInspect',
    router: {
      path: 'viewEditProductAlarm/:type?/:id?',
      name: 'viewEditProductAlarm',
      component: () => import('@/views/iotControl/deviceMaintenance/viewEditProductAlarm/index'),
      hidden: true,
      meta: { title: '产品告警详情' },
    },
  },
];

window.hideRouter = hideRouter;

/**
 * 获取外部应用的子菜单
 */
const getExternalSystemChileMenu = async (item) => {
  return new Promise((resolve, reject) => {
    getExternalSystemLink().then(({ data }) => {
      if (!data || !data.length) {
        // 没有子菜单时隐藏页面
        item.isShow = false;
        item.children = [];
        resolve(item);
        return;
      }
      item.children = data.map((link) => ({
        path: link.url,
        permissionName: link.name,
        permissionKey: '',
        isShow: true,
      }));
      resolve(item);
    }).catch(() => resolve());
  });
};
/**
 * 动态生成菜单
 * @param token
 * @returns {Promise<Router>}
 */
export const generatorDynamicRouter = ({ commit }) => {
  let routers = [];
  let licenseRes = [];
  let resList = null;
  return new Promise((resolve, reject) => {
    Promise.all([store.dispatch('user/getPermissions'), store.dispatch('user/getLicense'), store.dispatch('user/getIsRootCompany')])
      .then((res) => {
        resList = res;

        routers = [...rootRouter];
        licenseRes = verifyLicense(res[0], res[1]);
        // 信发无授权时隐藏会管中的审核界面
        licenseRes = verifyAuditLicense(licenseRes, res[1]);
        // 根据授权码保存授权页面
        store.commit('user/SET_PERMISSIONS', licenseRes);

        // 获取外部应用的子菜单
        let externalSystemItem = licenseRes.find((item) => item.permissionKey === 'externalSystem');
        if (externalSystemItem) {
          return getExternalSystemChileMenu(externalSystemItem);
        }
      })
      .then(() => {
        // 生成路由
        routers = routers.concat(generator(licenseRes));

        if (resList[2]) {
          // 验证页面授权,没有信发授权时隐藏会议指引页面
          verifyPageLicense(advanceSettingRouter, resList[1])
          // 专家配置页面只有根机构下的用户才能看到
          routers.push(advanceSettingRouter);
          // 超级管理只有开发环境可以看到
          if (process.env.VUE_APP_ENV === 'development'){
            routers.push(superRouter);
          }
          
        }
        routers.push(notFoundRouter);
        resolve(routers);
      })
      .catch((err) => {
        reject(err);
      });
  });
};

/**
 * 根据授权判断是否显示终端管理/信发管理/视频会议/会议管理/网络诊断/物联集控
 * PERMISSION_PAGE
 *
 * @param tree  页面tree
 * @param licenseArr 权限列表
 * @returns {*}
 */
const verifyLicense = (tree, licenseArr) => {
  return tree.filter((item) => {
    let res = licenseArr.find((license) => license.page.includes(item.permissionKey));
    if (res) {
      return res.value;
    }
    return true;
  });
};

/**
 * 信发无授权（max）时隐藏会管中的审核界面（auditManage）
 * @param tree  页面tree
 * @param licenseArr 权限列表
 * @returns {*}
 */
const verifyAuditLicense = (tree, licenseArr) => {
  // 判断是否有信发权限
  let bHasInfoPremission = false;
  licenseArr.forEach((auth) => {
    if (auth.key === 'max') {
      bHasInfoPremission = auth.value;
    }
  });
  // 没有信发授权时隐藏会管中的审核界面
  reverseTree(tree, (item) => {
    if (item.permissionKey === 'auditManage' && !bHasInfoPremission) {
      item.isShow = false;
    }
  });

  return tree;
};

/**
 * 无授权时隐藏相关界面
 * @param tree  页面tree，对象或数组
 * @param licenseArr 权限列表
 * @returns {*}
 */
const verifyPageLicense = (tree, licenseArr) => {
  let licenseMap = licenseArr.filter(item => item.value).map(item => item.key)
  // 没有授权时隐藏相关界面
  reverseTree(tree, (item) => {
    if (item.licenseKey && !licenseMap.includes(item.licenseKey)) {
      item.hidden = true;
    }
  });
};

/**
 * 格式化树形结构数据 生成 vue-router 层级路由表
 *
 * @param routerMap
 * @param parent
 * @returns {*}
 */
export const generator = (routerMap, parent) => {
  return routerMap.map((item) => {
    const permissionKey = sliceStr(item.permissionKey);
    const currentRouter = {
      // 如果路由设置了 path，则作为默认 path，否则 路由地址 动态拼接生成如 /dashboard/workplace
      path: item.path || `${(parent && parent.path) || ''}/${item.permissionKey}`,
      // 路由名称，建议唯一
      name: capitalizeFirstLetter(permissionKey) || '',
      // 该路由对应页面的 组件 :方案2 (动态加载)
      component: constantRouterComponents[item.component || permissionKey] || BlankLayout,

      // meta: 页面标题, 菜单图标, 页面权限(供指令权限用，可去掉)
      meta: {
        title: item.permissionName,
        icon: item.icon || undefined,
        // hiddenHeaderContent: hiddenHeaderContent,
        // target: item.target,
        // permission: item.name
      },
    };

    // 是否设置了隐藏菜单
    currentRouter.hidden = item.isShow ? false : true;

    // 是否设置了一直显示菜单
    currentRouter.alwaysShow = item.alwaysShow || false;

    // 是否设置了隐藏子菜单
    // if (hideChildren) {
    //   currentRouter.hideChildrenInMenu = true
    // }
    // 为了防止出现后端返回结果不规范，处理有可能出现拼接出两个 反斜杠
    if (!currentRouter.path.startsWith('http')) {
      currentRouter.path = currentRouter.path.replace('//', '/');
    }

    //层级
    currentRouter.level = (parent && parent.level + 1) || 1;
    if (currentRouter.level >= 3 && parent) {
      currentRouter.meta.activeMenu = (parent.meta && parent.meta.activeMenu) || parent.path;
    }
    // 是否有子菜单，并递归处理
    if (item.children && item.children.length > 0) {
      // Recursion
      currentRouter.children = generator(item.children, currentRouter);
    }

    // 重定向
    // console.log("Current Router: ", currentRouter);
    // 需要先判断下级路由是否有该跳转的页面权限，如果没有，则返回拥有的第一个页面
    if (item.redirect) {
      let hasAuth = false;
      if (currentRouter.children) {
        let index = currentRouter.children.findIndex((item) => item.path.includes(item.redirect));
        hasAuth = index != -1;
      }
      // 有权限
      if (hasAuth) {
        currentRouter.redirect = item.redirect;
      } else {
        if (currentRouter.children && currentRouter.children.length > 0) {
          // 没有权限
          currentRouter.redirect = currentRouter.children[0].path;
        }
      }
    }

    hideRouter.map((item) => {
      if (item.parent === currentRouter.name) {
        if (!currentRouter.children) {
          currentRouter.children = [];
        }
        if (item.router.meta) {
          item.router.meta.activeMenu = currentRouter.path;
        }
        item.router.level = currentRouter.level + 1;
        currentRouter.children.push(item.router);
      }
    });

    return currentRouter;
  });
};

/**
 * 数组转树形结构
 * @param list 源数组
 * @param tree 树
 * @param parentId 父ID
 */
const listToTree = (list, tree, parentId) => {
  list.forEach((item) => {
    // 判断是否为父级菜单
    if (item.parentId === parentId) {
      const child = {
        ...item,
        key: item.permissionKey || item.name,
        children: [],
      };
      // 迭代 list， 找到当前菜单相符合的所有子菜单
      listToTree(list, child.children, item.id);
      // 删掉不存在 children 值的属性
      if (child.children.length <= 0) {
        delete child.children;
      }
      // 加入到树中
      tree.push(child);
    }
  });
};

/**
 * 首字母大写
 * 去除 / 后面的字符
 * @param str 源字符串
 * @returns string
 */
const capitalizeFirstLetter = (str) => {
  return str.charAt(0).toUpperCase() + str.slice(1);
};

/**
 * 去除 / 后面的字符
 * @param str 源字符串
 * @returns string
 */
const sliceStr = (str) => {
  let index = str.indexOf('/');
  return index === -1 ? str : str.slice(0, index);
};
