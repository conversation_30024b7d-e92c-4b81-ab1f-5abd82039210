import request from "@/utils/request";

/**
 * 检测测试用例中的端口状态
 * @param data
 * @return {AxiosPromise}
 */
export function startDetectionPort(id) {
  return request({
    url: '/webapp/mcu/test/case/connect?id=' + id,
    method: 'post',
    headers: {'Content-Type': 'application/json'}
  })
}

/**
 * 查询测试用例中的端口状态
 * @param data
 * @return {AxiosPromise}
 */
export function getDetectionPort(id) {
  return request({
    url: '/webapp/mcu/test/case/connect?id=' + id,
    method: 'get',
    headers: {'Content-Type': 'application/json'}
  })
}

/**
 * 检测测试用例中的链路状态
 * @param data
 * @return {AxiosPromise}
 */
export function startDetectionTrace(id) {
  return request({
    url: '/webapp/mcu/test/case/traceroute?id=' + id,
    method: 'post',
    headers: {'Content-Type': 'application/json'}
  })
}

/**
 * 查询测试用例中的链路状态
 * @param data
 * @return {AxiosPromise}
 */
export function getDetectionTrace(id) {
  return request({
    url: '/webapp/mcu/test/case/traceroute?id=' + id,
    method: 'get',
    headers: {'Content-Type': 'application/json'}
  })
}
