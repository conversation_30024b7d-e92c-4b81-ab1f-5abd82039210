<template>
  <div class="ip-wrap">
    <el-autocomplete
      placeholder="ip"
      v-model="ip"
      class="input-with-select"
      size="small"
      @input="changeValue"
      :fetch-suggestions="querySearch"
    >
      <el-select
        v-model="protocol"
        slot="prepend"
        placeholder="协议"
        @input="changeValue"
      >
        <el-option
          v-for="item in protocolList"
          :key="item.label"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
    </el-autocomplete>
    <span class="colon">:</span>
    <el-input
      v-model="port"
      placeholder="端口"
      size="small"
      class="port-input"
      @input="changeValue"
      type="tel"
    ></el-input>
  </div>
</template>

<script>
import { getDefaultServer } from "@/api/advance";
export default {
  props: {
    value: {
      type: String,
      default: ""
    },
    protocolList: {
      type: Array,
      default: []
    },
    bShowSuggestions: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      protocol: "",
      ip: "",
      port: null,
      defaultServerList: []
    };
  },
  watch: {
    value() {
      this.setData();
    }
  },
  created() {
    this.setData();
    this.bShowSuggestions && this.getDefaultServer();
  },
  methods: {
    setData() {
      // 使用正则匹配，端口号有可能为空
      var reg = new RegExp(/(\w+:\/\/)([^/:]+)(:\d*)?/);
      var result = this.value.match(reg);
      if (result && result.length) {
        this.protocol = result[1];
        this.ip = result[2];
        this.port = result[3] ? result[3].substring(1) : result[3];
      } else {
        this.protocol = this.protocolList[0].value;
        this.ip = "";
        this.port = null;
      }
    },
    changeValue() {
      let port = this.port ? ":" + this.port : "";
      this.$emit("input", this.protocol + this.ip + port);
    },
    getDefaultServer() {
      getDefaultServer().then(res => {
        this.defaultServerList = res.data.map(item => ({ value: item }));
      });
    },
    querySearch(queryString, cb) {
      cb(this.defaultServerList);
    }
  }
};
</script>
<style lang="scss" scoped>
.ip-wrap {
  display: flex;
  align-items: center;
  .input-with-select {
    width: 80%;
    ::v-deep .el-select .el-input {
      width: 95px;
    }
    ::v-deep .el-input-group__prepend {
      background-color: #fff;
    }
  }
  .port-input {
    width: 20%;
  }
  .colon {
    margin: 0 5px;
    color: rgb(144, 147, 153);
  }
}
</style>
