<template>
  <div class="pt-directory-tree">
    <el-tree
      highlight-current
      node-key="id"
      :props="{ label: 'id' }"
      :load="loadNode"
      :expand-on-click-node="false"
      :default-expanded-keys="defaultExpandKeys"
      @node-click="nodeClick"
      lazy
      ref="tree"
    >
      <div slot-scope="{ node, data }" class="node-slot">
        <img :src="nodeIcon(node, data)" />
        <span class="material-type">{{ data.name }}</span>
      </div>
    </el-tree>
  </div>
</template>

<script>
import { getFoldersByParent, getMineRootFolderId } from "@/api/info";

export default {
  name: "PtDirectoryTree",
  props: {
    onlyMine: {
      type: Boolean,
      default: false
    },
    expanded: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      defaultExpandKeys: []
      // 根节点，直接显示
    };
  },
  mounted() {
    this.$root.EventBus.$on("refreshTree", id => this.refresh(id));
  },
  beforeDestroy() {
    this.$root.EventBus.$off("refreshTree");
  },
  methods: {
    /**
     * 懒加载节点
     */
    async loadNode(node, resolve) {
      const that = this;
      if (node.level === 0) {
        let { data: id } = await getMineRootFolderId();
        this.defaultExpandKeys = this.onlyMine ? [id] : null;
        this.rootNode[0].id = id;
        return resolve(that.rootNode);
      }
      that
        .getFoldersByParent(node.data)
        .then(res => {
          return resolve(res);
        })
        .catch(err => console.log(err));
    },
    /**
     * 获取子文件夹
     */
    getFoldersByParent(dir) {
      return new Promise((resolve, reject) => {
        getFoldersByParent(dir).then(res => {
          resolve(JSON.parse(JSON.stringify(res.data)));
        });
      });
    },
    /**
     * 节点点击事件
     */
    nodeClick(data) {
      this.$emit("node-click", data);
    },
    /**
     * 刷新某个树节点
     */
    refresh(id) {
      let node = this.$refs.tree.getNode(id); // 通过节点id找到对应树节点对象
      if (node) {
        node.loaded = false;
        node.expand();
      }
    }
  },
  computed: {
    /**
     * 标签图标
     * @return {function(...[*]=)}
     */
    nodeIcon() {
      return function(node, data) {
        switch (data.auth) {
          case 1:
            return require("@/assets/mkos/folder.svg");
          case 2:
            return require("@/assets/mkos/peer.svg");
          case 3:
            return require("@/assets/mkos/all.svg");
          default:
            return require("@/assets/mkos/folder.svg");
        }
      };
    },
    rootNode() {
      if (this.onlyMine) {
        return [
          {
            name: "我的文件",
            id: -101,
            auth: null,
            folderType: -101,
            rootFolderType: -101,
            createTime: null
          }
        ];
      } else {
        return [
          {
            name: "我的文件",
            id: -101,
            auth: null,
            folderType: -101,
            rootFolderType: -101,
            createTime: null
          },
          {
            name: `我的${this.$t('deptLabel')}`,
            id: -102,
            auth: null,
            folderType: -102,
            rootFolderType: -102,
            createTime: null
          },
          {
            name: `下级${this.$t('deptLabel')}`,
            id: -103,
            auth: null,
            folderType: -103,
            rootFolderType: -103,
            createTime: null
          },
          {
            name: "全局共享",
            id: -104,
            auth: null,
            folderType: -104,
            rootFolderType: -104,
            createTime: null
          }
        ];
      }
    }
    // expandedKeys() {
    //   return this.expanded ? [-101] : null;
    // }
  }
};
</script>

<style scoped lang="scss">
.pt-directory-tree {
  // 节点内容
  .node-slot {
    display: flex;
    align-items: center;
    img {
      width: 17px;
      margin-right: 2px;
    }
  }
}
</style>
