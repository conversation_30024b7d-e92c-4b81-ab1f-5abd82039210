<!-- 鼠标悬浮显示完整的机构名称的组件 -->
<template>
  <el-tooltip placement="top" v-if="!fullDeptName" :key="deptId + '1'" :enterable="false">
    <div slot="content">
      <i class="el-icon-loading"></i>
    </div>
    <div class="oneLine" @mouseover="getFullDepartmentName">{{ deptName }}</div>
  </el-tooltip>
  <el-tooltip placement="top" v-else :key="deptId + '2'" :enterable="false">
    <div slot="content">
      {{ fullDeptName }}
    </div>
    <div class="oneLine">{{ deptName }}</div>
  </el-tooltip>
</template>

<script>
import { getDeptById } from '@/api/system';

export default {
  props: {
    deptName: {
      type: String,
      default: '',
    },
    deptId: {
      type: Number,
      default: null,
    },
    bShowCompany:{
      type:Boolean,
      default:false
    }
  },
  data() {
    return {
      fullDeptName: '',
      loading: false,
    };
  },
  watch: {
    deptId(newVal, oldVal) {
      if (newVal !== oldVal) {
        this.fullDeptName = '';
      }
    },
  },
  methods: {
    getFullDepartmentName() {
      if (this.fullDeptName || !this.deptId || this.loading) {
        return;
      }
      this.loading = true;
      getDeptById(this.deptId)
        .then(({ data }) => {
          let depts = data.map((item) => item.deptName);
          if (depts.length > 1 && !this.bShowCompany) {
            depts.shift();
          }
          this.fullDeptName = depts.join(' / ');
          this.loading = false
        })
    },
  },
};
</script>

<style>
</style>