export default {
  data() {
    return {
      timer: null,
    };
  },
  mounted() {
    this.getData();
    this.startInterval();

    window.addEventListener('resize', this.resizeHandle)
  },
  beforeDestroy() {
    this.closeInterval();

    window.removeEventListener('resize', this.resizeHandle)
  },
  methods: {
    resizeHandle() {
      this.chart && this.chart.resize()
      this.chart1 && this.chart1.resize()
      this.chart2 && this.chart2.resize()
    },
    startInterval() {
      if (this.timer) {
        return;
      }
      this.timer = setInterval(() => {
        this.getData(false);
      }, 60 * 1000);
    },
    closeInterval() {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }
    }
  }
};
