<!-- 设备穿梭框 -->
<!-- 物联集控中使用 -->
<template>
  <el-dialog title="选择设备" :visible.sync="visible" width="1200px" custom-class="device-dialog" :close-on-click-modal="false" @close="close">
    <div class="margin-bottom10 search-wrap">
      <el-input
        placeholder="设备名称"
        suffix-icon="el-icon-search"
        size="small"
        class="mr-10"
        v-model="deviceName"
        clearable
        style="width: 150px"
        v-debounce="[
          () => {
            pagination.curPage = 1;
            getDeviceList();
          },
        ]"
        @clear="
          () => {
            pagination.curPage = 1;
            getDeviceList();
          }
        "
      />
      <el-select
        v-model="gatewayId"
        class="mr-10"
        size="small"
        placeholder="智慧中控"
        @change="
          {
            pagination.curPage = 1;
            getDeviceList();
          }
        "
        clearable
        style="width: 150px"
      >
        <el-option v-for="item in gatewayList" :key="item.mac" :label="item.name" :value="item.mac"> </el-option>
      </el-select>
      <el-cascader
        class="mr-10"
        :options="labelList"
        v-model="labelId"
        placeholder="空间标签"
        ref="cascaderSelector"
        :props="{ label: 'name', value: 'id', checkStrictly: true }"
        clearable
        size="small"
        style="width: 150px"
      >
      </el-cascader>
      <el-select
        class="mr-10"
        size="small"
        style="width: 150px"
        v-model="spaceId"
        placeholder="空间"
        @change="
          {
            pagination.curPage = 1;
            getDeviceList();
          }
        "
        clearable
      >
        <el-option v-for="(item, index) in spaceList" :key="index" :label="item.spaceName" :value="item.id"> </el-option>
      </el-select>
    </div>
    <transfer
      ref="transfer"
      keyword="id"
      :list="deviceList"
      :chooseList="chooseDeviceList"
      class="device-transfer"
      :defaultSort="{ prop: sort.prop, order: 'ascending' }"
      @sortChange="sortChange"
      :isMulti="isMulti"
      v-loading="loading"
    >
      <div slot="titleLeft">候选设备</div>
      <template slot="leftTable">
        <el-table-column label="序号" width="50" show-overflow-tooltip align="center">
          <template slot-scope="scope">
            {{ scope.$index + (pagination.curPage - 1) * pagination.size + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="deviceName" sortable :show-overflow-tooltip="true" label="设备名称" min-width="80" />
        <el-table-column prop="gatewayName" sortable :show-overflow-tooltip="true" label="智慧中控名称" min-width="80"> </el-table-column>
        <el-table-column prop="spaceName" sortable :show-overflow-tooltip="true" label="空间名称" min-width="80"> </el-table-column>
      </template>
      <pagination
        slot="pagination"
        :total="pagination.total"
        :page.sync="pagination.curPage"
        :limit.sync="pagination.size"
        :layout="TRANSFER_PAGINATION_LAYOUT"
        @pagination="getDeviceList"
        :autoScroll="false"
      />
      <div slot="titleRight">已选设备</div>
      <template slot="rightTable">
        <el-table-column type="index" label="序号" min-width="50" align="center"> </el-table-column>
        <el-table-column prop="deviceName" sortable :show-overflow-tooltip="true" label="设备名称" min-width="80" />
        <el-table-column prop="gatewayName" sortable :show-overflow-tooltip="true" label="智慧中控名称" min-width="80"> </el-table-column>
        <el-table-column prop="spaceName" sortable :show-overflow-tooltip="true" label="空间名称" min-width="80"> </el-table-column>
      </template>
    </transfer>
    <div slot="footer" class="dialog-footer">
      <el-button @click="close">取消</el-button>
      <el-button class="okBtn" @click="handleOk">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import dlg from '@/mixins/dlg';
  import transfer from './index.vue';
  import { getSpaceLabel, deviceGetDeviceList, getAllGatewaySpace, getAllGatewayList } from '@/api/iotControl';
  import { TRANSFER_PAGINATION_LAYOUT } from '@/utils/enum';
  export default {
    mixins: [dlg],
    components: {
      transfer,
    },
    props: {
      devices: {
        type: Array,
        default: () => [],
        require: false,
      },
      // 是否多选
      isMulti: {
        type: Boolean,
        default: true,
      },
    },
    data() {
      return {
        loading: false,
        //空间标签
        labelList: [],
        spaceList: [],
        //选中的空间标签
        labelId: [],
        spaceId: '',
        // 设备名称搜索
        deviceName: '',
        // 设备列表
        deviceList: [],
        chooseDeviceList: [],
        gatewayId: '',
        // 分页参数
        pagination: {
          curPage: 1,
          size: 20,
          total: 0,
        },
        sort: {
          prop: 'deviceName',
          order: 'ASC',
        },
        TRANSFER_PAGINATION_LAYOUT,
        gatewayList: [],
      };
    },
    created() {
      getSpaceLabel().then((res) => {
        this.labelList = res.data;
        this.getSpaceList();
        this.getDeviceList();
        this.getGatewayList();
        if (this.devices && this.devices.length) {
          this.chooseDeviceList = this.devices;
        }
      });
    },
    watch: {
      labelId: {
        handler() {
          this.pagination.curPage = 1;
          this.spaceId = '';
          this.spaceList = [];
          this.getSpaceList();
          this.getDeviceList();
        },
        deep: true,
      },
    },
    methods: {
      getGatewayList() {
        getAllGatewayList({
          approved: true,
        }).then((response) => {
          this.gatewayList = response.data;
        });
      },
      handleOk(closeDlg = true) {
        let list = this.$refs.transfer.rightList;
        if (!this.isMulti && list.length > 1) {
          this.$message.warning('只能选择单个设备!');
          return;
        }
        this.$emit('handleSelect', list);
        if (closeDlg) {
          this.close();
        }
      },
      getSpaceList() {
        getAllGatewaySpace({ includeChild: true, labelId: this.labelId[this.labelId.length - 1] }).then((response) => {
          this.spaceList = response.data;
        });
      },
      /**
       * 获取设备列表
       */
      getDeviceList() {
        this.loading = true;
        deviceGetDeviceList({
          gatewayId: this.gatewayId,
          labelId: this.labelId[this.labelId.length - 1],
          spaceId: this.spaceId,
          page: this.pagination.curPage,
          size: this.pagination.size,
          direction: this.sort.order,
          properties: this.sort.prop,
          deviceName: this.deviceName,
        })
          .then((res) => {
            this.deviceList = res.data.rows;
            this.pagination.total = res.data.total;
          })
          .finally(() => (this.loading = false));
      },
      sortChange(col) {
        this.sort.order = 'ascending' === col.order ? 'ASC' : 'DESC';
        this.sort.prop = col.prop;
        this.getDeviceList();
      },
    },
  };
</script>
<style lang="scss" scoped></style>
