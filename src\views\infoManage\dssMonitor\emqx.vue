<template>
  <div>
    <div class="mt-10 flex-box">
      <el-page-header @back="goBack" :content="terminalName"></el-page-header>
      <el-button type="success" icon="el-icon-refresh" @click="doRefresh" size="small">刷新</el-button>
    </div>

    <div v-loading="loading">
      <div>
        <h3>客户端</h3>
        <el-collapse v-model="activeNames" @change="handleChangeCollapse" accordion>
          <el-collapse-item :title="client.clientid" :name="client.clientid" v-for="client in clients" :key="client.clientid">
            <div class="flex-box">
              <el-descriptions class="margin-top" title="连接信息" :column="1" size="small" border>
                <el-descriptions-item>
                  <template slot="label"> 状态 </template>
                  <i :class="client.connected ? 'el-icon-success' : 'el-icon-error'"></i>
                  {{ client.connected ? '已连接' : '未连接' }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label"> 节点 </template>
                  {{ client.node }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label"> 用户名 </template>
                  {{ client.username }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label"> 协议 </template>
                  {{ client.proto_name }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label"> 监听器 </template>
                  {{ client.listener }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label"> IP地址 </template>
                  {{ client.ip_address }}:{{ client.port }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label"> 心跳 </template>
                  {{ client.keepalive }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label"> 清除会话 </template>
                  {{ client.clean_start }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label"> 桥接标识 </template>
                  {{ client.is_bridge }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label"> 连接时间 </template>
                  {{ formatTime(client.created_at) }}
                </el-descriptions-item>
              </el-descriptions>

              <el-descriptions class="margin-top" title="会话信息" :column="1" size="small" border>
                <template slot="extra"> （当前值/最大值） </template>
                <el-descriptions-item>
                  <template slot="label"> 会话过期间隔 </template>
                  {{ client.expiry_interval }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label"> 会话创建时间 </template>
                  {{ formatTime(client.created_at) }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label"> 进程堆栈 </template>
                  {{ client.heap_size }} bytes
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label"> 订阅数 </template>
                  {{ client.subscriptions_cnt }}/{{ client.subscriptions_max }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label"> 消息队列 </template>
                  {{ client.mqueue_len }}/{{ client.mqueue_max }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label"> 飞行窗口 </template>
                  {{ client.inflight_cnt }}/{{ client.inflight_max }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label"> QoS 2 报文接收队列 </template>
                  {{ client.awaiting_rel_cnt }}/{{ client.awaiting_rel_max }}
                </el-descriptions-item>
              </el-descriptions>
            </div>

            <h3>指标</h3>
            <div class="flex-box">
              <div class="left-wrap">
                <el-descriptions class="margin-top" title="流量收发（字节）" :column="1" size="small" border>
                  <el-descriptions-item>
                    <template slot="label"> 接收字节数 </template>
                    {{ client.send_oct }}
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template slot="label"> 发送字节数 </template>
                    {{ client.recv_oct }}
                  </el-descriptions-item>
                </el-descriptions>
                <el-descriptions title="报文" :column="1" size="small" border style="margin-top: 30px">
                  <el-descriptions-item>
                    <template slot="label"> 接收的 TCP 报文数量 </template>
                    {{ client.send_cnt }}
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template slot="label"> 发送的 TCP 报文数量 </template>
                    {{ client.recv_cnt }}
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template slot="label"> 接收的 MQTT 报文数量 </template>
                    {{ client.send_pkt }}
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template slot="label"> 发送的 MQTT 报文数量 </template>
                    {{ client.recv_pkt }}
                  </el-descriptions-item>
                </el-descriptions>
              </div>
              <el-descriptions class="margin-top" title="消息数量" :column="1" size="small" border>
                <el-descriptions-item>
                  <template slot="label"> 消息发布总数 </template>
                  {{ client.recv_msg }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label"> QoS 0 消息发布 </template>
                  {{ client['recv_msg.qos0'] }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label"> QoS 1 消息发布 </template>
                  {{ client['recv_msg.qos1'] }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label"> QoS 2 消息发布 </template>
                  {{ client['recv_msg.qos2'] }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label"> 消息发布丢弃数 </template>
                  {{ client['recv_msg.dropped'] }}
                </el-descriptions-item>
                <!-- <el-descriptions-item>
                  <template slot="label"> 消息发布丢弃数（过期） </template>
                  {{ client['recv_msg.dropped.expired'] }}
                </el-descriptions-item> -->
                <el-descriptions-item>
                  <template slot="label"> 消息接收总数 </template>
                  {{ client.send_msg }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label"> QoS 0 消息接收 </template>
                  {{ client['send_msg.qos0'] }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label"> QoS 1 消息接收 </template>
                  {{ client['send_msg.qos1'] }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label"> QoS 2 消息接收 </template>
                  {{ client['send_msg.qos2'] }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label"> 消息接收丢弃数 </template>
                  {{ client['send_msg.dropped'] }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label"> 消息接收丢弃数（过期） </template>
                  {{ client['send_msg.dropped.expired'] }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label"> 消息接收丢弃数（队列已满） </template>
                  {{ client['send_msg.dropped.queue_full'] }}
                </el-descriptions-item>
                <el-descriptions-item>
                  <template slot="label"> 消息接收丢弃数（消息过大） </template>
                  {{ client['send_msg.dropped.too_large'] }}
                </el-descriptions-item>
              </el-descriptions>
            </div>

            <div class="chart-box">
              <div class="flex-box">
                消息时序值
                <el-date-picker
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  v-model="filterDate"
                  type="datetimerange"
                  value-format="timestamp"
                  :picker-options="datePickerOptions"
                  :clearable="true"
                  :default-time="['00:00:00', '23:59:59']"
                  @change="ChangeFilterDate"
                >
                </el-date-picker>
              </div>
              <div class="chart-wrap" :id="`chart-wrap-${client.clientid}`" style="width: 100%; margin-top: 30px" v-loading="chartLoading"></div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </div>

      <div>
        <h3>当前订阅</h3>
        <el-table :data="subscriptions" style="width: 100%">
          <el-table-column prop="topic" label="主题" width="auto"> </el-table-column>
          <el-table-column prop="qos" label="QoS" width="300"> </el-table-column>
          <el-table-column prop="clientid" label="客户端ID" width="300"> </el-table-column>
        </el-table>
      </div>

      <div>
        <div class="flex-box" style="padding: 20px 0">
          <h3 style="margin: 0">保留消息</h3>
          <el-button type="danger" icon="el-icon-delete" @click="doClear" size="small">清空</el-button>
        </div>
        <el-table :data="retainers" style="width: 100%">
          <el-table-column prop="topic" label="主题" width="auto"> </el-table-column>
          <el-table-column prop="qos" label="QoS" width="300"> </el-table-column>
          <el-table-column prop="from_clientid" label="客户端ID" width="500"> </el-table-column>
          <el-table-column prop="from_username" label="客户端" width="300"> </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<script>
  import { getTerminalEmqxProfile, getTerminalEmqxRecvMsg, getTerminalEmqxSendMsg, clearTerminalEmqxRetainers } from '@/api/terminal';

  export default {
    data() {
      return {
        // MQTT客户端信息
        clients: [],
        // MQTT订阅的主题
        subscriptions: [],
        // MQTT保留信息
        retainers: [],
        activeNames: '',
        loading: false,
        chartLoading: false,
        // 日期限制条件
        datePickerOptions: {
          disabledDate(time) {
            return time.getTime() >= +(new Date().setHours(0, 0, 0, 0) + 24 * 60 * 60 * 1000);
          },
          firstDayOfWeek: 1,
        },
        // 日期筛选
        filterDate: '',
        // 当前客户端id
        clientId: null,
        timer: null,
      };
    },
    computed: {
      mac() {
        return this.$route.params.mac;
      },
      terminalName() {
        return this.$route.params.name;
      },
    },
    mounted() {
      this.getTerminalEmqxProfile();
    },
    beforeDestroy() {
      this.closeTimer();
    },
    methods: {
      goBack() {
        this.$router.go(-1);
      },
      doRefresh() {
        this.getTerminalEmqxProfile();
      },
      formatTime(time) {
        return new Date(time).Format('yyyy-MM-dd hh:mm:ss');
      },
      ChangeFilterDate() {
        this.getTerminalEmqxMsg(this.clientId,true);
      },
      /**
       * 清空保留消息
       */
      doClear() {
        clearTerminalEmqxRetainers(this.mac).then((res) => {
          this.$message.success('操作成功！');
          this.doRefresh();
        });
      },
      handleChangeCollapse(val) {
        if (!val) {
          this.clientId = null;
          this.closeTimer();
          return;
        }
        this.clientId = val;
        this.getTerminalEmqxMsg(val,true);
        this.startInterval();
      },
      /**
       * 定时刷新图表
       */
      startInterval() {
        if (this.timer) {
          return;
        }
        this.timer = setInterval(() => {
          this.getTerminalEmqxMsg();
        }, 60 * 1000);
      },
      closeTimer() {
        clearInterval(this.timer);
        this.timer = null;
      },
      getTerminalEmqxProfile() {
        this.loading = true;
        getTerminalEmqxProfile(this.mac)
          .then(({ data }) => {
            Object.keys(data).forEach((key) => {
              if (data[key] !== null && data[key] !== undefined && this[key] !== undefined) {
                this[key] = data[key];
              }
            });
          })
          .finally(() => (this.loading = false));
      },
      getTerminalEmqxMsg(clientid = this.clientId, bShowLoading = false) {
        if (!clientid) {
          return;
        }
        let from = null,
          to = null;
        if (this.filterDate && this.filterDate.length && this.filterDate.length > 1) {
          from = this.filterDate[0];
          to = this.filterDate[1];
        }
        if (bShowLoading) {
          this.chartLoading = true;
        }

        Promise.all([getTerminalEmqxRecvMsg(this.mac, { clientid, from, to }), getTerminalEmqxSendMsg(this.mac, { clientid, from, to })])
          .then((res) => {
            let sendArr = [];
            let recvArr = [];
            let timeArr = [];

            res[0].data.forEach((element) => {
              recvArr.push(element.value);
              timeArr.push(new Date(element.key).Format('yy/MM/dd hh:mm:ss'));
            });

            res[1].data.forEach((element) => {
              sendArr.push(element.value);
            });

            this.initOption(timeArr, sendArr, recvArr, clientid);
          })
          .finally(() => (this.chartLoading = false));
      },
      initOption(timeArr, sendArr, recvArr, clientId) {
        let option = {
          color: ['rgb(61,127,249)', 'rgb(244,152,69)'],
          tooltip: {
            trigger: 'axis',
            position: function (pt) {
              return [pt[0], '10%'];
            },
            confine: true,
          },
          grid: {
            left: 50,
            right: 50,
            top: 50,
          },
          // title: {
          //   left: 'center',
          //   text: `消息时序值`,
          // },
          toolbox: {
            show: false,
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: timeArr,
          },
          yAxis: {
            type: 'value',
            name: '数量',
            boundaryGap: [0, '100%'],
          },
          dataZoom: [
            {
              start: 0,
              end: 100,
            },
          ],
          series: [
            {
              name: `发送`,
              type: 'line',
              smooth: true,
              symbol: 'none',
              areaStyle: {
                color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgba(61,127,249,0.5)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(61,127,249,0.1)',
                  },
                ]),
              },
              data: sendArr,
            },
            {
              name: `接收`,
              type: 'line',
              smooth: true,
              symbol: 'none',
              areaStyle: {
                color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgba(244,152,69,0.5)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(244,152,69,0.1)',
                  },
                ]),
              },
              data: recvArr,
            },
          ],
        };
        this.drawChart(option, `chart-wrap-${clientId}`);
      },
      /**
       * 绘制echarts图
       */
      drawChart(option, id) {
        setTimeout(() => {
          let dom = this.$el.querySelector(`#${id}`);
          if (dom) {
            let chart = this.$echarts.init(dom);
            chart.setOption(option, true);
          }
        }, 800);
      },
    },
  };
</script>

<style lang="scss" scoped>
  .flex-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    align-items: flex-start;
    width: 100%;
    .el-descriptions {
      width: 49%;
      border: 1px solid #eee;
      padding: 20px;
      border-top: 5px solid #00b173;
      border-radius: 10px;
    }

    &:nth-child(1) {
      .el-descriptions:nth-child(2) {
        border-top-color: #bf73ff;
      }
    }

    .left-wrap {
      width: 49%;
      .el-descriptions {
        width: 100%;
        &:nth-child(1) {
          border-top-color: #3d7ff9;
        }
        &:nth-child(2) {
          border-top-color: #f49845;
        }
      }
    }
  }

  .chart-box {
    border: 1px solid #eee;
    padding: 20px;
    border-top: 5px solid #bf73ff;
    border-radius: 10px;
    margin-top: 20px;
    font-size: 16px;
    font-weight: bold;
  }

  .chart-wrap {
    width: 100%;
    height: 400px;
  }

  .el-icon-success {
    color: #67c23a;
    font-size: 14px;
  }

  .el-icon-error {
    color: #f56c6c;
    font-size: 14px;
  }
</style>
