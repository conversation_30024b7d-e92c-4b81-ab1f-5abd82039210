<template>
  <el-dialog :title="title" :visible.sync="visible" width="800px" :close-on-click-modal="false" @close="close">
    <div style="height: 570px">
      <el-steps :active="curStep" finish-status="success" align-center>
        <el-step title="审核链应用对象"> </el-step>
        <el-step title="审核信息"> </el-step>
        <el-step title="应用到其他部门"> </el-step>
      </el-steps>
      <div v-show="curStep === 0" class="step1-wrap">
        <el-form label-width="100px" :model="form" :rules="step1Rule" ref="step1Form">
          <el-form-item :label="`所属${$t('deptLabel')}`" prop="dept">
            <el-cascader
              ref="cascader"
              :options="deptList"
              :show-all-levels="false"
              clearable
              @change="handleChangeDivision"
              placement="bottom"
              v-model="form.dept"
              :props="{
                value: 'id',
                label: 'deptName',
                checkStrictly: true,
                expandTrigger: 'hover',
              }"
            ></el-cascader>
          </el-form-item>
        </el-form>
      </div>
      <div v-show="curStep === 1">
        <el-form :model="form" :rules="step2Rule" label-width="80px" ref="step2Form">
          <el-form-item label="名称" prop="name">
            <el-input type="text" size="small" v-model="form.name" style="width: 200px"></el-input>
          </el-form-item>

          <el-form-item label="审核类型">
            <el-radio-group v-model="form.type">
              <el-radio :label="true">全签（所有审核人同意）</el-radio>
              <el-radio :label="false">单签（同级一名审核人同意即可）</el-radio>
            </el-radio-group>
          </el-form-item>
          <div class="step-wrap">
            <el-steps direction="vertical" process-status="finish" :active="form.activeMemberIndex">
              <el-step v-for="(item, index) in form.member" :key="index" title="审核人" :status="item.status" @click.native="selectUser(index)">
                <div slot="description">
                  {{ (item.users && item.users.length && item.users.map((user) => user.nickname || user.username).join(',')) || '点击选择审核人' }}
                </div>
              </el-step>
            </el-steps>
          </div>
          <div style="margin-left: 100px; margin-bottom: 20px">
            <el-tooltip effect="dark" content="添加" placement="bottom">
              <el-button type="primary" icon="el-icon-plus" size="small" @click="addLevel"></el-button>
            </el-tooltip>
            <el-tooltip effect="dark" content="删除" placement="bottom">
              <el-button type="primary" icon="el-icon-delete" size="small" @click="deleteLevel"></el-button>
            </el-tooltip>
          </div>
          <el-form-item label="会议方数" v-if="chainType === 2">
            <el-input-number v-model="form.party" controls-position="right" :min="1"></el-input-number>
          </el-form-item>
          <el-form-item label="描述">
            <el-input type="textarea" size="small" v-model="form.description" style="width: 300px"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div v-show="curStep === 2" v-loading="treeLoading">
        <el-tree
          ref="treeRole"
          :data="companyDeptList"
          show-checkbox
          check-strictly
          node-key="id"
          default-expand-all
          :check-on-click-node="true"
          :default-checked-keys="checkedDept"
          :props="{ label: 'deptName' }"
          :expand-on-click-node="false"
          @node-click="handleNodeClick"
          @check="handleCheckClick"
        >
          <span class="custom-tree-node" slot-scope="{ node, data }">
            <i class="el-icon-tickets"></i>
            {{ node.label }}
            <el-button type="text" @click.stop="doCheckAll(node, data)" v-show="node.childNodes && node.childNodes.length">
              {{ data.bCheckedAll ? '(取消全选)' : '(全选)' }}
            </el-button>
          </span>
        </el-tree>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="backward" v-if="curStep > 0" class="fl" type="primary">上一步</el-button>
      <el-button @click="close">取 消</el-button>
      <el-button type="primary" @click="handleNext(curStep)" :loading="loading">
        {{ (curStep === 2 && '完成') || '下一步' }}
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
  import { reverseTree } from '@/utils/utils';
  import dlg from '@/mixins/dlg';
  import userTransfer from './user.vue';
  import transform from '@/utils/transform';
  import { addChainList, updateChainList, getDepartementList, getOwnCompanyDept } from '@/api/system';

  export default {
    props: {
      chainType: {
        type: Number,
        default: 1,
      },
      companyId: {
        type: String | Number,
        default: '',
      },
      row: {
        type: Object,
        default: () => {},
      },
    },
    mixins: [dlg],
    components: {
      userTransfer,
    },
    data() {
      return {
        loading: false,
        treeLoading:false,
        curStep: 0,
        title: '添加审批流程',
        deptList: [],
        checkedDept: [],
        companyDeptList: [],
        id: 0,
        form: {
          name: '',
          description: '',
          //审核类型,true全签,false单签
          type: true,
          dept: [],
          activeMemberIndex: -1,
          member: [],
          //会议方数
          party: 20,
        },
        step1Rule: {
          dept: [{ required: true, message: `请选择${this.$t('deptLabel')}`, trigger: 'blur' }],
        },
        step2Rule: {
          name: [{ required: true, message: '请输入审核链名称', trigger: 'blur' }],
        },
      };
    },
    created() {
      this.getDept();
      this.form.member = new Array(3).fill(1).map((_) => ({ users: [] }));
      this.row && Object.keys(this.row).length && this.editForm();
    },
    methods: {
      editForm() {
        const { row } = this;
        this.title = '编辑审批流程';
        this.id = row.chainId;
        this.form.name = row.name;
        this.form.description = row.desc;
        this.form.type = row.type;
        this.checkedDept = [...row.deptIds, row.departmentId];
        this.form.dept = row.departmentId;
        this.form.member = [];
        this.form.party = row.party || 20;
        for (let i = 0; i < row.nodes.length; i++) {
          let users = row.nodes[i].auditors.map((item) => ({
            id: item.userId,
            username: item.userName,
            nickname: item.nickname,
          }));
          this.form.member.push({
            status: 'success',
            users,
          });
        }
      },
      backward() {
        this.curStep--;
      },
      handleNext(step) {
        const curStep = step + 1;
        if (curStep === 1) {
          this.$refs.step1Form.validate((valid) => {
            if (valid) {
              this.curStep = curStep;
            }
          });
          return;
        } else if (curStep === 2) {
          this.$refs.step2Form.validate((valid) => {
            if (valid) {
              let companyId = this.$refs.cascader.getCheckedNodes()[0].data.companyId;
              this.getCompanyDept(companyId);
              this.curStep = curStep;
            }
          });
          return;
        }
        this.addChain();
      },
      handleChangeDivision() {
        this.$refs.cascader.dropDownVisible = false;
        this.form.activeMemberIndex = -1;
        this.form.member = new Array(3).fill(1).map((_) => ({ users: [] }));
      },
      addLevel() {
        this.form.member.push({
          users: [],
        });
      },
      deleteLevel() {
        this.form.member.splice(this.form.member.length - 1, 1);
      },
      // 获取该角色下的所有部门列表
      getDept() {
        getDepartementList().then((response) => {
          this.deptList = response.data;
        });
      },
      // 当前节点以及所有子级节点是否被选中
      getAllChildrenChecked(node) {
        let bChecked = this.checkedDept.includes(node.id)
        if(!bChecked){
          return false
        }
        if (node.children && node.children.length > 0) {
          return node.children.every((child) => this.checkedDept.includes(child.id));
        }
        return true; // 如果没有子节点，则默认视为检查状态正确
      },
      // 获取该公司下的所有部门列表
      getCompanyDept(companyId) {
        this.treeLoading = true;
        getOwnCompanyDept(companyId).then((response) => {
          reverseTree(response.data, (item) => {
            item.bCheckedAll = this.getAllChildrenChecked(item);
          });
          this.companyDeptList = response.data;
          this.treeLoading = false;
        }).catch(()=>this.treeLoading = false);
      },
      /**
       * 审核人弹窗
       */
      userTransferDlg: transform(userTransfer),
      /**
       * 选择审核人
       */
      selectUser(index) {
        if (index < 0) {
          return;
        }
        let users = this.form.member.reduce((prev, item, i) => {
          if (i === index) {
            return prev;
          }
          return prev.concat(item.users);
        }, []);
        this.userTransferDlg({
          propsData: {
            users,
            companyId: this.companyId,
            curUsers: this.form.member[index].users,
          },
          methods: {
            handleSelect: (users) => {
              this.form.activeMemberIndex = index;
              this.form.member[index].users = users;
              this.form.member[index].status = 'finish';
            },
          },
        });
      },
      /**
       * 添加审核链
       */
      addChain() {
        let bEmpty = this.form.member.every((item) => item.users.length === 0);
        if (bEmpty) {
          this.$message.warning('审核人不能为空！');
          return;
        }

        let deptId = this.$refs.treeRole.getCheckedNodes().map((item) => item.id);
        let oData = {
          type: this.form.type,
          name: this.form.name,
          desc: this.form.description,

          deptId,
          nodes: [],
          chainType: this.chainType,
        };
        if (this.chainType === 2) {
          oData.party = this.form.party;
        }
        this.form.member.forEach((item) => {
          if (item.users.length) {
            oData.nodes.push({
              auditors: [],
            });
          }
          item.users.forEach((user) => {
            oData.nodes.at(-1).auditors.push({
              userId: user.id,
              userName: user.username,
            });
          });
        });
        oData.total = oData.nodes.length;

        let fn = addChainList;
        let szTip = '添加成功';

        if (this.row && Object.keys(this.row).length > 0) {
          oData.chainId = this.id;
          szTip = '编辑成功';
          fn = updateChainList;
        }

        let departmentId = this.form.dept;
        if (this.form.dept instanceof Array) {
          departmentId = this.form.dept[this.form.dept.length - 1];
        }

        oData.departmentId = departmentId;

        this.loading = true;
        fn(oData)
          .then(() => {
            this.$message.success(szTip);
            this.close();
            this.$emit('refresh');
          })
          .finally(() => {
            this.loading = false;
          });
      },
      /**
       * 点击全选/取消全选
       */
      doCheckAll(node, data) {
        let bCheckedAll = data.bCheckedAll;
        reverseTree(
          [node],
          (item) => {
            item.checked = !bCheckedAll;
            item.data.bCheckedAll = !bCheckedAll;
          },
          null,
          'childNodes'
        );
        // 更新所有上级节点的bCheckedAll
        node.parent && this.handleNodeCheckChange(node.parent);
      },
      /**
       * 点击节点
       */
      handleNodeClick(data, node, dom) {
        this.handleNodeCheckChange(node);
      },
      /**
       * 点击复选框
       */
      handleCheckClick(data) {
        let node = this.$refs.treeRole.getNode(data.id);
        node && this.handleNodeCheckChange(node);
      },
      /**
       * 节点选中/取消选中
       * 遍历所有父级节点，判断该节点以及该节点的下一级节点是否全选中，若选中，bCheckedAll为true
       */
      handleNodeCheckChange(node) {
        let fn = (node) => {
          if (!node) {
            return;
          }
          node.data.bCheckedAll = node.checked && node.childNodes.every((item) => item.data.bCheckedAll);
          node.parent && fn(node.parent);
        };

        fn(node);
      },
    },
  };
</script>

<style scoped lang="scss">
  .el-steps {
    margin: 0;
  }
  .step-wrap {
    height: 260px;
    margin-left: 100px;
    margin-top: 10px;
    margin-bottom: 10px;
    overflow: auto;
  }
  .step1-wrap {
    margin: 100px 20px;
  }
</style>
