<template>
  <el-dialog :title="title" :visible.sync="visible" width="1100px" :close-on-click-modal="false" @close="close">
    <div class="date-picker-wrap">
      <el-button type="success" plain :size="size" @click="getInspectRecord">刷新</el-button>
      <el-date-picker
        :size="size"
        v-model="inspectRecordFilter.timeRange"
        type="datetimerange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="timestamp"
        class="ml-10"
        @change="
          {
            inspectRecordPage.current = 1;
            getInspectRecord();
          }
        "
      >
      </el-date-picker>
      <el-select
        :size="size"
        v-model="inspectRecordFilter.taskStatus"
        placeholder="请选择执行状态"
        clearable
        class="ml-10"
        @change="
          {
            inspectRecordPage.current = 1;
            getInspectRecord();
          }
        "
      >
        <el-option v-for="item in INSPECT_RECORD_STATUS_ENUM" :key="item.value" :label="item.label" :value="item.value"></el-option>
      </el-select>
    </div>
    <el-table :data="inspectRecordList" highlight-current-row height="500px" v-loading="inspectRecordLoading">
      <el-table-column label="序号" width="70" align="center" fixed="left">
        <template slot-scope="scope">
          {{ scope.$index + (inspectRecordPage.current - 1) * inspectRecordPage.size + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="name" label="巡检计划名称" min-width="160" show-overflow-tooltip fixed="left"> </el-table-column>
      <el-table-column prop="remark" label="巡检结果描述" min-width="180" show-overflow-tooltip>
        <template slot-scope="{ row }">
          {{ row.remark }}
        </template>
      </el-table-column>
      <el-table-column prop="taskTime" label="巡检时间" min-width="120" align="center" show-overflow-tooltip>
        <template slot-scope="{ row }">
          {{ formatDate(row.taskTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="taskStatus" label="巡检状态" min-width="120" align="center" show-overflow-tooltip>
        <template slot-scope="{ row }">
          <el-tag :type="INSPECT_RECORD_STATUS_ENUM.find((obj) => obj.value === row.taskStatus).type">
            {{ INSPECT_RECORD_STATUS_ENUM.find((obj) => obj.value === row.taskStatus).label }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="taskStatus" label="操作" min-width="100" show-overflow-tooltip>
        <template slot-scope="{ row }">
          <el-button @click="openInspectRecordDetail(row.id)" type="primary" size="mini" plain>查看详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      :total="inspectRecordPage.total"
      :page.sync="inspectRecordPage.current"
      :limit.sync="inspectRecordPage.size"
      @pagination="getInspectRecord"
    />
  </el-dialog>
</template>

<script>
  import dlg from '@/mixins/dlg';
  import { getInspectRecord } from '@/api/iotControl';
  import { INSPECT_RECORD_STATUS_ENUM } from '@/views/iotControl/enum';
  export default {
    props: {
      inspectId: {
        type: String | Number,
        default: '',
      },
    },
    mixins: [dlg],
    data() {
      return {
        INSPECT_RECORD_STATUS_ENUM,
        size: 'small',
        inspectRecordFilter: {
          timeRange: [undefined, undefined],
          taskStatus: '',
        },
        inspectRecordPage: {
          total: 0,
          current: 1,
          size: 20,
        },
        inspectRecordList: [],
        inspectRecordLoading: false,
        inspectRecordSort: {
          direction: 'DESC',
          properties: 'id',
        },
      };
    },
    created() {
      this.getInspectRecord();
    },
    methods: {
      getInspectRecord() {
        this.inspectRecordLoading = true;
        let params = {
          id: this.inspectId,
          page: this.inspectRecordPage.current,
          size: this.inspectRecordPage.size,
          direction: this.inspectRecordSort.direction,
          properties: this.inspectRecordSort.properties,
          taskStatus: this.inspectRecordFilter.taskStatus,
          start: this.inspectRecordFilter.timeRange ? this.inspectRecordFilter.timeRange[0] : '',
          end: this.inspectRecordFilter.timeRange ? this.inspectRecordFilter.timeRange[1] : '',
          unpaged: false,
        };
        getInspectRecord(params).then(({ data }) => {
          this.inspectRecordList = data.rows;
          this.inspectRecordPage.total = data.total;
          this.inspectRecordLoading = false;
        });
      },
      formatDate(inputDate) {
        const date = new Date(inputDate);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        return `${year}-${month}-${day} ${hours}:${minutes}`;
      },
      openInspectRecordDetail(id) {
        this.$emit('handleViewDetail', id);
        this.close();
      },
    },
    computed: {
      title() {
        return '巡检记录';
      },
    },
  };
</script>

<style scoped lang="scss"></style>
