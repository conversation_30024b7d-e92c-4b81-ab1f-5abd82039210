<template>
  <el-dialog :close-on-click-modal="false" @close="close" title="终端信发配置" :visible.sync="visible" width="600px">
    <el-form :rules="rules" :model="model" ref="ruleForm" label-width="140px">
      <el-form-item label="单击事件" prop="">
        <el-switch v-model="model.enableSingleTap" active-color="#13ce66" inactive-color="#DCDFE6" active-text="开" inactive-text="关"> </el-switch>
      </el-form-item>
      <el-form-item label="播放器控制键" prop="" v-if="model.enableSingleTap">
        <el-radio-group v-model="model.singleTapPlayerMenuBackKeyFunction">
          <el-radio :label="1">显示返回按键，点击返回到封面</el-radio>
          <el-radio :label="0">不显示返回按键</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="播放器菜单键" prop="" v-if="model.enableSingleTap">
        <el-radio-group v-model="model.playerMenuKeysCtl">
          <el-radio :label="0">全打开</el-radio>
          <el-radio :label="1">隐藏控制按键</el-radio>
          <el-radio :label="2">隐藏进度条</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="双击事件" prop="enableDoubleTap">
        <el-switch v-model="model.enableDoubleTap" active-color="#13ce66" inactive-color="#DCDFE6" active-text="开" inactive-text="关"> </el-switch>
      </el-form-item>
      <el-form-item label="双击行为" prop="doubleTapFunction" v-if="model.enableDoubleTap">
        <el-radio-group v-model="model.doubleTapFunction">
          <el-radio :label="1">选择节目</el-radio>
          <el-radio :label="2">退出信发应用</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="替换节目单后删除之前节目单素材" prop="delMaterialIfReplacePlaylist">
        <el-switch v-model="model.delMaterialIfReplacePlaylist" active-color="#13ce66" inactive-color="#DCDFE6" active-text="开" inactive-text="关">
        </el-switch>
      </el-form-item>

      <el-form-item label="切换节目显示淡入淡出效果" prop="fadeInOutIfSwitchPrograms">
        <el-switch v-model="model.fadeInOutIfSwitchPrograms" active-color="#13ce66" inactive-color="#DCDFE6" active-text="开" inactive-text="关">
        </el-switch>
      </el-form-item>

      <el-form-item label="淡入时长" prop="" v-if="model.fadeInOutIfSwitchPrograms">
        <el-input placeholder="请输入内容" v-model.number="model.fadeInTimems" type="number">
          <template slot="append">ms</template>
        </el-input>
      </el-form-item>

      <el-form-item label="淡出时长" prop="" v-if="model.fadeInOutIfSwitchPrograms">
        <el-input placeholder="请输入内容" v-model.number="model.fadeOutTimems" type="number">
          <template slot="append">ms</template>
        </el-input>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="close">取消</el-button>
      <el-button class="okBtn" @click="save">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import dlg from '@/mixins/dlg';
  import { setDssTerminalClickControl, getDssTerminalClickControl } from '@/api/info';

  export default {
    mixins: [dlg],
    props: {
      macs: {
        type: Array,
        required: true,
      },
      bBatch: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        model: {
          // 启用双击事件
          enableDoubleTap: true,
          // 双击事件行为，1选择节目，2退出信发应用
          doubleTapFunction: 1,
          // 启用单击事件
          enableSingleTap: true,
          // 播放器控制键 0不显示返回按键 1显示返回按键，点击返回到封面
          singleTapPlayerMenuBackKeyFunction: 1,
          // 播放器菜单键，0全打开，1隐藏控制按键，2隐藏进度条
          playerMenuKeysCtl: 0,
          // 替换节目单后删除之前节目单素材
          delMaterialIfReplacePlaylist: false,
          // 切换节目显示淡入淡出效果
          fadeInOutIfSwitchPrograms: false,
          // 淡入时长，默认2000ms
          fadeInTimems: 2000,
          // 淡出时长，默认2000ms
          fadeOutTimems: 2000,
        },
        rules: {},
      };
    },
    created() {
      !this.bBatch && this.getDssTerminalClickControl();
    },
    methods: {
      /**
       * 查询终端的点击事件配置
       */
      getDssTerminalClickControl() {
        if (!this.macs || !this.macs.length > 0) {
          return;
        }
        getDssTerminalClickControl(this.macs[0]).then(({ data }) => {
          Object.keys(data).forEach((key) => {
            if (data[key] !== null && data[key] !== undefined  && this.model[key] !== undefined) {
              this.model[key] = data[key];
            }
          });
        });
      },
      /**
       * 保存
       */
      save() {
        this.$refs.ruleForm.validate((valid) => {
          if (valid) {
            setDssTerminalClickControl({ macs: this.macs, config: this.model }).then((res) => {
              this.$message.success('设置成功！');
              this.close();
            });
          }
        });
      },
    },
  };
</script>
<style lang="scss" scoped>
  ::v-deep .el-select .el-input {
    width: 80px;
  }
  .input-with-select ::v-deep .el-input-group__prepend {
    background-color: #fff;
  }
  .input-with-select {
    padding: 10px 0;
  }
</style>
