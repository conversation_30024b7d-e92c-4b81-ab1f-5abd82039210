<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
    <link rel="stylesheet" href="40e93b11cbd4174c60b22cba0d630300.css" />
    <style>
      body,
      html {
        height: 100%;
        margin: 0;
      }
      .wrap {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      section {
        width: 80vmin;
        height: 80vmin;
        margin: auto;
        border: 3vmin solid #000;
        border-radius: 50%;
        box-shadow: inset 12vmin 12vmin 27vmin rgba(0, 0, 0, 0.2),
          inset 3vmin 3vmin 9vmin rgba(0, 0, 0, 0.5),
          1.5vmin 1.5vmin 2vmin rgba(0, 0, 0, 0.4), 3vmin 3vmin 4.5vmin rgba(0, 0, 0, 0.4);
        position: relative;
        z-index: 1;
      }

      section:before {
        content: "";
        width: 95%;
        height: 95%;
        border-radius: 50%;
        display: block;
        background: transparent;
        border: 2vmin solid white;
      }

      /* highlight at top left of black outline */
      section:after {
        content: "";
        width: 105%;
        height: 105%;
        border-radius: 50%;
        display: block;
        background: transparent;
        position: absolute;
        top: -2.5%;
        left: -2.5%;
        box-shadow: -3px -3px 9px rgba(255, 255, 255, 0.8);
      }

      .label {
        position: absolute;
        top: 19vmin;
        left: 46%;
        font-size: 2.5vmin;
      }

      .hourhand,
      .secondhand,
      .minutehand {
        width: 25vmin;
        height: 2vmin;
        background: #000;
        position: absolute;
        top: 40vmin;
        left: calc(50% - 3.5vmin);
        z-index: 2;
        transform-origin: 16%;
      }

      .hourhand:after,
      .secondhand:after,
      .minutehand:after {
        content: "";
        background: #000;
        width: 5vmin;
        height: 5vmin;
        border-radius: 50%;
        z-index: 3;
        position: absolute;
        top: -1.5vmin;
        left: 1.5vmin;
      }

      .hourhand {
        border-top-right-radius: 20%;
        border-bottom-right-radius: 20%;
        box-shadow: -10px 0px 10px rgba(0, 0, 0, 0.4);
      }

      .minutehand {
        width: 40vmin;
        height: 1vmin;
        top: 40.5vmin;
        transform-origin: 10%;
        border-top-right-radius: 30%;
        border-bottom-right-radius: 30%;
        box-shadow: -10px 10px 10px rgba(0, 0, 0, 0.4);
      }

      .minutehand:before {
        content: "";
        width: 4.5vmin;
        height: 4.5vmin;
        border-radius: 50%;
        z-index: 99;
        position: absolute;
        top: -1.7vmin;
        left: 1.7vmin;
        box-shadow: -2px -2px 7px rgba(255, 255, 255, 0.6);
      }

      .minutehand:after {
        top: -2vmin;
      }

      .secondhand {
        width: 35vmin;
        height: 0.5vmin;
        top: 40.75vmin;
        transform-origin: 11.5%;
        box-shadow: -10px -10px 10px rgba(0, 0, 0, 0.4);
      }
      .secondhand:after {
        top: -2.25vmin;
      }

      .hour12,
      .hour1,
      .hour2,
      .hour3,
      .hour4,
      .hour5 {
        height: 1vmin;
        width: 55vmin;
        background: transparent;
        border-left: 7vmin solid #000;
        border-right: 7vmin solid #000;
        transform: translate(-50%, -50%);
        top: 50%;
        left: 50%;
        position: absolute;
      }

      .hour3 {
        transform: rotate(90deg) translate(0, 34vmin);
      }

      .hour1 {
        transform: rotate(120deg) translate(17vmin, 30vmin);
      }

      .hour2 {
        transform: rotate(150deg) translate(29vmin, 18vmin);
      }

      .hour4 {
        transform: rotate(210deg) translate(30vmin, -17vmin);
      }

      .hour5 {
        transform: rotate(240deg) translate(17vmin, -30vmin);
      }
      .date-info,
      .time-info {
        position: absolute;
        left: 50%;
        transform: translate(-50%, -50%);
      }
      .date-info {
        top: 70%;
        font-size: 4vmin;
      }
      .time-info {
        top: 80%;
        display: flex;
        flex-direction: row;
        color: white;
      }
      .time-info .time {
        margin-left: 2px;
        background-color: #000;
        padding: 0.5vmin;
        font-size: 2.8vmin;
      }
      [v-cloak] {
        display: none;
      }
    </style>
    <script src="174365807c06490c848d7b1d45fdc348.js"></script>
    <script src="page.js"></script>
  </head>

  <body>
    <div id="app" class="wrap">
      <section :style="style" v-cloak>
        <div class="hourhand"></div>
        <div class="secondhand"></div>
        <div class="minutehand"></div>
        <div class="hour12"></div>
        <div class="hour1"></div>
        <div class="hour2"></div>
        <div class="hour3"></div>
        <div class="hour4"></div>
        <div class="hour5"></div>
        <div class="date-info">{{dateInfo}}</div>
        <div class="time-info">
          <div class="time">{{hour}}</div>
          <div class="time">{{minute}}</div>
          <div class="time">{{second}}</div>
        </div>
      </section>
    </div>
  </body>

  <script>
    window.$page = page;
    const cssProperty = ["height", "width", "fontSize"];
    const weeks = [
      "星期日",
      "星期一",
      "星期二",
      "星期三",
      "星期四",
      "星期五",
      "星期六"
    ];

    const app = new Vue({
      el: "#app",
      data: {
        date: new Date(),
        hour: 0,
        minute: 0,
        second: 0,
        props: page.props,
        timer: null
      },
      computed: {
        style() {
          let style = {};
          for (let prop of this.props) {
            let unit = cssProperty.includes(prop.field) ? "px" : "";
            style[prop.field] = prop.value + unit;
          }
          return style;
        },
        dateInfo() {
          return (
            this.formatValue(this.date.getMonth() + 1) +
            "月" +
            this.formatValue(this.date.getDate()) +
            "日 " +
            weeks[this.date.getDay()]
          );
        }
      },
      created() {
        this.runClock();
        this.setTime();
      },
      mounted() {
        window["update"] = (val, mqtt = null) => {
          this.updateProps(val);
        };
      },
      beforeDestory() {
        clearInterval(this.timer);
      },
      methods: {
        updateProps(props) {
          for (let prop of props) {
            let index = this.getPropIndex(prop.field);
            if (index !== -1) {
              let data = this.props[index];
              data.value = prop.value;
              this.$set(this.props, index, data);
            }
          }
        },
        getPropIndex(name) {
          for (let i = 0; i < this.props.length; i++) {
            if (this.props[i].field === name) {
              return i;
            }
          }
          return -1;
        },
        setTime() {
          clearInterval(this.timer);
          this.timer = setInterval(() => {
            this.runClock();
          }, 1000);
        },
        runClock() {
          this.date = new Date();

          var hours = this.date.getHours();
          var minutes = this.date.getMinutes();
          var seconds = this.date.getSeconds();

          var clock = {
            hours: document.querySelector(".hourhand"),
            minutes: document.querySelector(".minutehand"),
            seconds: document.querySelector(".secondhand")
          };
          var deg = {
            hours: 30 * hours + 0.5 * minutes,
            minutes: 6 * minutes + 0.1 * seconds,
            seconds: 6 * seconds
          };

          deg.hours += 360 / 43200 - 90;
          deg.minutes += 360 / 3600 - 90;
          deg.seconds -= 90;
          

          clock.hours.style.transform = "rotate(" + deg.hours + "deg)";
          clock.minutes.style.transform = "rotate(" + deg.minutes + "deg)";
          clock.seconds.style.transform = "rotate(" + deg.seconds + "deg)";
          this.setTimeInfo(hours, minutes, seconds);
        },
        setTimeInfo(h, min, sec) {
          this.hour = this.formatValue(h);
          this.minute = this.formatValue(min);
          this.second = this.formatValue(sec);
        },
        formatValue(val) {
          if (val < 10) {
            return "0" + val;
          }
          return val;
        }
      }
    });
  </script>
</html>
