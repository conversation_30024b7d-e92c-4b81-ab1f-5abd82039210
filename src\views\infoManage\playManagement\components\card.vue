<template>
  <el-row>
    <el-col :xs="8" :sm="8" :md="6" :lg="5" :xl="4" v-for="item in newList" :key="item.id">
      <el-card :body-style="{ padding: '0px' }" :class="{ checked: item.checked }">
        <div class="img-wrap" @click="handleClick($event, item)">
          <playlistCover :url="item.cover || item.programCover || defaultCover" :programSize="item.programSize" class="image">
            <template slot="hover">
              <el-checkbox v-model="item.checked" @change="selectChange(item)" class="checkbox" @click.stop></el-checkbox>
            </template>
          </playlistCover>
        </div>
        <div class="middle-wrap border-T">
          <el-tooltip effect="dark" content="缺失节目或素材" placement="top" v-if="item.isLackP || item.isLackM" class="center-icon">
            <i class="el-icon-info" style="color: #c6c6c6; cursor: pointer" @click.stop="$emit('viewLostInfo', item)"></i>
          </el-tooltip>
          <el-tooltip class="item" effect="dark" :content="item.name" placement="top-start">
            <span class="title" @click.stop="$emit('watchHistory', item)">{{ item.name }}</span>
          </el-tooltip>
          <el-tooltip class="item" effect="dark" :content="item.creator" placement="top">
            <span class="creator">{{ item.creator }}</span>
          </el-tooltip>
          <el-dropdown placement="top">
            <el-button type="text">
              <i class="el-icon-more-outline el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item @click.native="$emit('edit', item)" v-action:playManagement|operate>
                <i class="el-icon-edit-outline"></i>
                编辑
              </el-dropdown-item>
              <el-dropdown-item @click.native="$emit('publishTerminal', item)">
                <i class="el-icon-monitor"></i>
                发布
              </el-dropdown-item>
              <el-dropdown-item
                :disabled="item.isLackP || item.isLackM"
                @click.native="$emit('doExport', item, 'playDownloadPath', 2)"
                v-action:playManagement|udisk
              >
                <svg-icon :icon-class="item.playDownloadPath ? 'download' : 'zip'"></svg-icon>
                仅播放
              </el-dropdown-item>
              <el-dropdown-item
                :disabled="item.isLackP || item.isLackM"
                @click.native="$emit('doExport', item, 'importDownloadPath', 1)"
                v-action:playManagement|udisk
              >
                <svg-icon :icon-class="item.importDownloadPath ? 'download' : 'zip'"></svg-icon>
                仅导入终端
              </el-dropdown-item>
              <el-dropdown-item
                :disabled="item.isLackP || item.isLackM"
                @click.native="$emit('doExport', item, 'importPlayDownloadPath', 3)"
                v-action:playManagement|udisk
              >
                <svg-icon :icon-class="item.importPlayDownloadPath ? 'download' : 'zip'"></svg-icon>
                导入终端并播放
              </el-dropdown-item>
              <el-dropdown-item @click.native="$emit('deletePlaylist', item)" v-if="bShowAddBtn" v-action:playManagement|operate>
                <i class="el-icon-delete"></i>删除</el-dropdown-item
              >
              <el-dropdown-item @click.native.stop="$emit('stopPlay', item)" v-action:playManagement|operate>
                <i class="el-icon-video-pause"></i>
                停止</el-dropdown-item
              >
            </el-dropdown-menu>
          </el-dropdown>
        </div>
      </el-card>
    </el-col>
  </el-row>
</template>

<script>
  import _ from 'lodash';
  import playlistCover from '../modules/playlistCover.vue';
  import { PLAY_TYPE } from '@/utils/enum';
  import { hasPermission } from '@/directive/permission/index.js';

  export default {
    props: {
      list: {
        type: Array,
        default() {
          return [];
        },
      },
      mulSelect: {
        type: Array,
        default() {
          return [];
        },
      },
    },
    components: {
      playlistCover,
    },
    data() {
      return {
        newList: _.cloneDeep(this.list),
        defaultCover: require('@/assets/cover.png'),
      };
    },
    computed: {
      bShowAddBtn() {
        return PLAY_TYPE.some((item) => {
          return hasPermission(undefined, 'playManagement', item.key);
        });
      },
    },
    watch: {
      list(newVal) {
        //父组件中数据改变后更新列表数据并将状态置为未选中
        this.newList = _.cloneDeep(newVal).map((item) => ({
          ...item,
          checked: false,
        }));
        this.$emit('update:mulSelect', []);
      },
    },
    methods: {
      handleClick(event, item) {
        if (event.target.closest('.checkbox')) {
          return;
        }
        this.$emit('publishTerminal', item);
      },
      selectChange(row) {
        let mul = [];
        if (row.checked) {
          mul = [...this.mulSelect, row];
        } else {
          mul = this.mulSelect.filter((item) => item.id !== row.id);
        }
        this.$emit('update:mulSelect', mul);
      },
    },
  };
</script>
<style lang="scss" scoped>
  @media only screen and (min-width: 1200px) and (max-width: 1919px) {
    .el-col-lg-5 {
      width: 20% !important;
    }
  }
  .center-icon {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .border-T {
    border-top: 1px solid #e6ebed;
  }
  .el-col {
    transition: all 0.5s ease-in-out;
    &:hover {
      transform: translate3d(0, -5px, 0);
    }
    .el-card {
      cursor: pointer;
      margin: 0 20px 30px 20px;
      &:hover .header-mask {
        opacity: 1 !important;
      }
      &.checked .el-checkbox,
      &:hover .el-checkbox {
        opacity: 1 !important;
      }
      .img-wrap {
        position: relative;
        width: 100%;
        padding-bottom: 56.25%;
        box-sizing: border-box;
        .image {
          width: 100%;
          height: 100%;
          position: absolute;
        }
        .status {
          position: absolute;
          top: 0;
          left: 0;
          font-size: 12px;
          display: block;
          padding: 0 10px;
          height: 30px;
          line-height: 30px;
          background-color: #76838f;
          color: #fff;
          border-top-left-radius: 4px;
          border-bottom-right-radius: 12px;
        }
        .el-checkbox {
          opacity: 0;
          position: absolute;
          right: 10px;
          top: 10px;
          z-index: 10;
          ::v-deep .el-checkbox__input {
            .el-checkbox__inner {
              border-radius: 50%;
              width: 18px;
              height: 18px;
              &::after {
                left: 6px;
                top: 3px;
              }
            }
            &.is-checked {
              .el-checkbox__inner {
                background-color: #09aaff;
                border-color: #09aaff;
              }
            }
            &:hover {
              .el-checkbox__inner {
                border-color: rgb(157, 221, 255);
              }
            }
          }
        }
        .header-mask {
          position: absolute;
          top: 0;
          left: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0;
          background-color: rgba(0, 0, 0, 0.3);
          width: 100%;
          height: 100%;
          border-radius: 4px 4px 0 0;
          transition: top 0.28s ease, opacity 0.28s ease, height 0.28s ease;
          z-index: 6;
          .details-btn {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            display: inline-block;
            width: 40%;
            height: 44px;
            font-size: 18px;
            line-height: 44px;
            border-radius: 22px;
            border: 1px solid #fff;
            color: #fff;
            cursor: pointer;
            text-align: center;
            transition: all 0.2s;
          }
        }
      }
      .middle-wrap {
        height: 36px;
        line-height: 36px;
        padding: 0 8px;
        display: flex;
        justify-content: space-between;
        .title {
          padding-left: 4px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          flex: 1;
        }
        .creator {
          color: #999;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          flex-grow: 0;
          max-width: 50px;
          margin-left: 5px;
          margin-right: 5px;
          font-size: 14px;
        }
      }
      .bottom-wrap {
        width: 100%;
        display: flex;
        justify-content: space-between;
        padding: 0 20px;
      }
    }
  }
</style>
