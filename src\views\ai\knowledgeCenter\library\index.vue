<template>
  <div id="kgLibrary" v-loading="loading">
    <div class="content-top">
      <div class="btn-area">
        <el-upload
          action="https://jsonplaceholder.typicode.com/posts/"
          multiple
          :limit="20">
          <el-button
            size="small"
            type="primary"
            icon="el-icon-upload2">
            上传
          </el-button>

          <el-button
            size="small"
            type="danger"
            icon="el-icon-delete">
            清空
          </el-button>
        </el-upload>

        <el-radio-group v-model="statusFilter" size="small" style="margin-left: 10px">
          <el-radio-button label="all">所有({{ allNum }})</el-radio-button>
          <el-radio-button label="pending">等待中({{ pendingNum }})</el-radio-button>
          <el-radio-button label="processing">处理中({{ processingNum }})</el-radio-button>
          <el-radio-button label="processed">已完成({{ proceedNum }})</el-radio-button>
          <el-radio-button label="failed">失败({{ failedNum }})</el-radio-button>
        </el-radio-group>
      </div>
    </div>

    <div class="content-body">
      <div class="content-detail">
        <div class="content-detail-border">
          <div class="table-position-pagination">
            <el-table
              :data="documents"
              highlight-current-row
            >
              <el-table-column
                prop="file_path"
                label="文件名"
                align="left"
                :show-overflow-tooltip="true"
                min-width="200" fixed="left">
                <template slot-scope="scope">
                  <div class="document-name">
                    <el-image
                      :src="scope.row.icon"
                      lazy
                      class="icon"
                      fit="cover"
                    >
                      <div slot="error" class="image-slot">
                        <i class="el-icon-picture-outline"></i>
                      </div>
                      <div slot="placeholder" class="image-slot">
                        <i class="el-icon-loading"></i>
                      </div>
                    </el-image>
                    <span>{{ scope.row.file_path }}</span>
                  </div>
                </template>
              </el-table-column>

              <el-table-column
                prop="content_summary"
                label="摘要"
                align="center"
                :show-overflow-tooltip="true"
                min-width="200" fixed="left"/>

              <el-table-column
                prop="status"
                label="状态"
                align="center"
                min-width="50"
              >
                <template slot-scope="scope">
                  <svg-icon
                    icon-class="circle"
                    :style="{color: DOCUMENT_STATUS[scope.row.status].color}"
                    class="circle-icon"
                  />
                  <span>{{ DOCUMENT_STATUS[scope.row.status].label }}</span>
                </template>
              </el-table-column>

              <el-table-column
                prop="content_length"
                label="长度"
                min-width="50"
                align="center"
                :show-overflow-tooltip="true"
              />

              <el-table-column
                prop="chunks_count"
                label="分块"
                min-width="50"
                align="center"
                :show-overflow-tooltip="true"
              />

              <el-table-column
                prop="created_at"
                label="创建时间"
                min-width="100"
                align="center"
                :show-overflow-tooltip="true"
              />

              <el-table-column
                prop="updated_at"
                label="更新时间"
                min-width="100"
                align="center"
                :show-overflow-tooltip="true"
              />

            </el-table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {getDocuments} from "@/api/knowledgeGraph";
import {formatISODate} from "@/utils/utils";

export default {
  name: "kgLibrary",
  components: {},
  data() {
    return {
      /**
       * 文档状态与对应的描述、颜色
       * @type {{}}
       */
      DOCUMENT_STATUS: {
        pending: {
          color: 'rgb(238,168,63)',
          label: '等待中'
        },
        processing: {
          color: 'rgb(63,145,238)',
          label: '处理中'
        },
        processed: {
          color: 'rgb(113, 179, 69)',
          label: '已完成'
        },
        failed: {
          color: 'rgb(238, 63, 77)',
          label: '失败'
        }
      },
      loading: false,
      // 文档列表
      documents: [],
      // 文档状态筛选
      statusFilter: 'all',
      // 各文档状态的数量
      allNum: 0,
      pendingNum: 0,
      processingNum: 0,
      processedNum: 0,
      failedNum: 0
    }
  },

  watch: {
    statusFilter() {
      this.getDocumentsList()
    }
  },

  methods: {
    /**
     * 获取文件的图标
     * @param filename 文件名
     */
    getDocumentIcon(filename) {
      let ext = ''
      const parts = filename.split('.');
      if (parts.length === 1 && !filename.startsWith('.')) {
        ext = '';
      } else if (parts.length === 0 || (parts.length === 1 && filename.startsWith('.'))) {
        ext = '';
      } else {
        ext = parts.pop().toLowerCase()
      }
      switch (ext) {
        case 'pdf':
          return require('@/assets/mkos/gnome-mime-application-pdf.svg')
        case 'docx':
          return require('@/assets/mkos/application-vnd.openxmlformats-officedocument.wordprocessingml.document.svg')
        case 'xlsx':
          return require('@/assets/mkos/application-vnd.openxmlformats-officedocument.presentationml.presentation.svg')
        case 'txt':
          return require('@/assets/mkos/text-x-preview.png')
        default:
          return require('@/assets/mkos/text-x-preview.svg')
      }
    },
    /**
     * 加载文档列表
     */
    getDocumentsList() {
      this.loading = true
      getDocuments().then(res => {
        const pending = res.statuses.pending
        const processing = res.statuses.processing
        const processed = res.statuses.processed
        const failed = res.statuses.failed
        let documents = [];
        if (this.statusFilter === 'all') {
          documents = [
            ...(pending || []),
            ...(processing || []),
            ...(processed || []),
            ...(failed || [])
          ];
        } else {
          documents = res.statuses[this.statusFilter] || [];
        }
        documents.map(item => {
          item.created_at = formatISODate(item.created_at)
          item.updated_at = formatISODate(item.updated_at)
          item.icon = this.getDocumentIcon(item.file_path)
        })
        this.documents = documents
        // 各状态文档数量
        this.pendingNum = pending ? pending.length : 0
        this.processingNum = processing ? processing.length : 0
        this.processedNum = processed ? processed.length : 0
        this.failedNum = failed ? failed.length : 0
        this.allNum = this.pendingNum + this.processingNum + this.processedNum + this.failedNum
      }).finally(() => {
        this.loading = false
      })
    }
  },

  mounted() {
    this.getDocumentsList()
  }
}
</script>

<style scoped lang="scss">
.table-position-pagination {
  height: 100%;
}

#kgLibrary {
  .el-input {
    width: 400px;
  }

  .circle-icon {
    font-size: 18px;
    margin-right: 3px;
    vertical-align: middle;
  }

  .document-name {
    display: flex;
    align-items: center;

    .icon {
      height: 30px;
      margin-right: 10px;
    }
  }
}
</style>
