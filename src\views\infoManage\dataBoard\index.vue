<template>
  <div class="data-board">
    <div class="title-wrap">
      <div class="svg-wrap" @click="toggleScreen">
        <svg-icon icon-class="ndt@screen" v-if="!fullScreen"></svg-icon>
        <svg-icon icon-class="ndt@exitScreen" v-else></svg-icon>
      </div>
      <div class="title">信息发布可视化运维平台</div>
      <div class="time-wrap">
        <div class="date">{{ date }}</div>
        <div class="time">{{ time }}</div>
      </div>
    </div>

    <div class="data-wrap">
      <div class="left-wrap">
        <today-overview></today-overview>
        <play-rank></play-rank>
        <play-count></play-count>
      </div>
      <div class="middle-wrap">
        <dss-summary></dss-summary>
        <terminal-list></terminal-list>
      </div>
      <div class="right-wrap">
        <log></log>
        <program-rank></program-rank>
        <material-storage></material-storage>
      </div>
    </div>
  </div>
</template>

<script>
  import moment from 'moment';
  import screenfull from 'screenfull';
  import { refreshToken } from '@/api/user';

  import TodayOverview from './components/TodayOverview.vue';
  import DssSummary from './components/Summary.vue';
  import TerminalList from './components/TerminalList.vue';
  import Log from './components/Log.vue';
  import PlayRank from './components/PlayRank.vue';
  import PlayCount from './components/PlayCount.vue';
  import ProgramRank from './components/ProgramRank.vue';
  import MaterialStorage from './components/MaterialStorage.vue';

  moment.locale('zh-cn', {
    weekdays: '周日_周一_周二_周三_周四_周五_周六'.split('_'),
  });

  export default {
    components: {
      TodayOverview,
      DssSummary,
      TerminalList,
      Log,
      PlayRank,
      PlayCount,
      ProgramRank,
      MaterialStorage,
    },
    data() {
      return {
        bRefresh: false,
        user: '',
        testName: '',
        time: '',
        date: '',
        timer: null,
        tokenTimer: null,
        fullScreen: false,
      };
    },
    created() {
      this.initTime();
      this.startUpdateTimeInterval();
      this.startUpdateTokenInterval();
    },
    beforeDestroy() {
      this.closeUpdateTimeInterval();
      this.closeUpdateTokenInterval();
    },
    methods: {
      toggleScreen() {
        if (!screenfull.enabled) {
          alert('当前浏览器不支持全屏！');
          this.$message.warning('当前浏览器不支持全屏！');
          return false;
        }
        // alert('toggleScreen',this.fullScreen)
        screenfull.toggle();
        this.fullScreen = !this.fullScreen;
      },
      initTime() {
        let t = new Date();
        this.date = moment(t).format('YYYY-MM-DD dddd');
        this.time = moment(t).format('HH:mm:ss');
      },
      startUpdateTimeInterval() {
        if (this.timer) {
          return;
        }
        this.timer = setInterval(() => {
          this.initTime();
        }, 1000);
      },
      closeUpdateTimeInterval() {
        if (this.timer) {
          clearInterval(this.timer);
          this.timer = null;
        }
      },
      updateToken() {
        localStorage.setItem('lastTime', +new Date());
        refreshToken();
      },
      startUpdateTokenInterval() {
        if (this.tokenTimer) {
          return;
        }
        this.tokenTimer = setInterval(() => {
          this.updateToken();
        }, 20 * 60 * 1000);
      },
      closeUpdateTokenInterval() {
        if (this.tokenTimer) {
          clearInterval(this.tokenTimer);
          this.tokenTimer = null;
        }
      },
    },
  };
</script>
<style scoped lang="scss">
  @font-face {
    font-family: 'YouSheBiaoTiHei'; /* 定义一个新的字体名称 */
    src: url('../../../assets/fonts/YouSheBiaoTiHei.woff') format('woff');
    font-weight: normal; /* 设置字体的粗细 */
  }

  .data-board {
    width: 100%;
    height: 100%;
    background: url('./img/bg.png') center center;
    background-size: cover;
    color: #e2e1e4;
    .title-wrap {
      height: 100px;
      width: 100%;
      background: url('./img/header.png') center center;
      background-size: cover;
      display: flex;
      justify-content: center;
      position: relative;
      .title {
        font-family: 'YouSheBiaoTiHei';
        margin-top: 44px;
        font-size: 30px;
        letter-spacing: 6px;
        filter: drop-shadow(rgba(14, 39, 62, 0.57) 0px 6px 2px) drop-shadow(rgba(0, 178, 255, 0.8) 0px 0px 8px);
        font-family: YouSheBiaoTiHei;
      }
      .svg-wrap {
        position: absolute;
        left: 1%;
        top: 14%;
      }
      .time-wrap {
        position: absolute;
        right: 1%;
        top: 15%;
        display: flex;
        align-items: center;
        .time {
          font-size: 24px;
          letter-spacing: 2px;
          padding-left: 5%;
        }
        .date {
          font-size: 14px;
          white-space: nowrap;
        }
      }
    }
    .data-wrap {
      display: flex;
      height: calc(100% - 100px);
      box-sizing: border-box;
      padding: 0 1% 1%;
      .left-wrap,
      .right-wrap {
        width: 25%;
        .log-wrap {
          height: 35%;
          margin-bottom: 2vh;
        }
        .play-rank-wrap,
        .today-overview-wrap,
        .program-rank-wrap {
          margin-bottom: 2vh;
        }
        .program-rank-wrap {
          height: calc((65% - 4vh) / 2);
          margin-bottom: 2vh;
        }
        .material-storage-wrap {
          height: calc((65% - 4vh) / 2);
        }
      }
      .left-wrap {
        display: flex;
        flex-direction: column;
        .play-count-wrap,
        .play-rank-wrap {
          flex: 1;
        }
      }
      .middle-wrap {
        display: flex;
        flex-direction: column;
        width: 50%;
        padding: 0 1%;
        box-sizing: border-box;
        .summary-wrap {
          height: 60%;
        }
        .terminal-list-wrap {
          height: 40%;
          margin-top: 2vh;
        }
      }
    }
  }

  .svg-wrap {
    // position: absolute;
    right: 60px;
    top: 0px;
    // padding: 24px 50px 24px 50px;
    .svg-icon {
      color: white;
      font-size: 30px;
      cursor: pointer;
      &:hover {
        color: rgb(215, 219, 221);
      }
    }
  }
</style>
