<!-- 选择终端弹窗,支持机构筛选 -->
<template>
  <el-dialog :title="szTitle" :visible.sync="visible" width="1050px" :close-on-click-modal="false" @close="close">
    <tree-layout @handleTreeChange="handleTreeChange" :treeOptions="['dept']" :bCompanyDept="bCompanyDept" :treebOwn="false">
      <div class="mt-10 search-wrap">
        <div>
          <el-input
            placeholder="终端名称"
            suffix-icon="el-icon-search"
            size="small"
            v-model="szTerminalName"
            v-debounce="[
              (e) => {
                iCurrentPage = 1;
                getTerminalList(e);
              },
            ]"
            style="width: 120px"
            class="mr-10"
          ></el-input>
          <el-input
            placeholder="终端id"
            suffix-icon="el-icon-search"
            size="small"
            v-model="macToSearch"
            v-debounce="[
              (e) => {
                iCurrentPage = 1;
                getTerminalList(e);
              },
            ]"
            style="width: 120px"
            class="mr-10"
          ></el-input>
          <el-select placeholder="终端状态" v-model="terminalStatus" class="mr-10" style="width: 120px" size="small" clearable>
            <el-option v-for="item in terminalStatusOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
          </el-select>
          <el-checkbox v-model="excludeChild" class="ml-10">仅当前层级</el-checkbox>
        </div>
      </div>
      <el-table
        :data="aTerminalList"
        ref="leftTable"
        @selection-change="handleLeftSelectionChange"
        @row-click="clickLeftRow"
        @sort-change="sortChange"
        :default-sort="{
          prop: 'terminalName',
          order: 'descending',
        }"
        size="mini"
        height="300px"
        row-key="terminalId"
        v-loading="loading"
      >
        <el-table-column type="selection" width="45" :selectable="isDisabledSelected" reserve-selection></el-table-column>
        <el-table-column label="序号" width="50">
          <template slot-scope="scope">
            {{ scope.$index + (iCurrentPage - 1) * iPageSize + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="terminalName" :show-overflow-tooltip="true" label="终端名称" sortable="custom" width="180"> </el-table-column>
        <el-table-column prop="mac" :show-overflow-tooltip="true" sortable="custom" label="终端id" width="200"> </el-table-column>
        <el-table-column prop="terminalStatus" :show-overflow-tooltip="true" label="状态" width="100" :formatter="formatStatus"> </el-table-column>
        <el-table-column prop="deptName" :show-overflow-tooltip="true" :label="`所属${$t('deptLabel')}`"> </el-table-column>
      </el-table>
      <pagination
        :total="iTotal"
        :page.sync="iCurrentPage"
        :limit.sync="iPageSize"
        @pagination="getTerminalList"
        :autoScroll="false"
        :layout="TRANSFER_PAGINATION_LAYOUT"
      />
    </tree-layout>
    <el-table :data="leftSelection" ref="rightTable" size="mini" key="rightTable">
      <el-table-column type="index" label="序号" width="70"> </el-table-column>
      <el-table-column prop="terminalName" :show-overflow-tooltip="true" sortable label="终端名称" width="200"> </el-table-column>
      <el-table-column prop="mac" sortable :show-overflow-tooltip="true" label="终端id" width="200"> </el-table-column>
      <el-table-column prop="terminalStatus" :show-overflow-tooltip="true" label="状态" width="100" :formatter="formatStatus"> </el-table-column>
      <el-table-column prop="deptName" :show-overflow-tooltip="true" :label="`所属${$t('deptLabel')}`"> </el-table-column>
      <el-table-column label="操作" width="70" key="operate">
        <template slot-scope="{ row, index }">
          <i class="el-icon-delete" @click="doDelete(row, index)" />
        </template>
      </el-table-column>
    </el-table>
    <div slot="footer" style="margin-bottom: 40px; margin-top: 10px">
      <div class="right">
        <el-button @click="close">取消</el-button>
        <el-button class="okBtn" @click="submitOk">确定</el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
  import { excludeChildMixin } from '@/mixins/prefer';
  import treeLayout from '@/components/treeLayout';
  import dlgMixin from '@/mixins/dlg';
  import { getTerminalList } from '@/api/info';
  import { TRANSFER_PAGINATION_LAYOUT, SYSTEM_STATUS } from '@/utils/enum';
  import { DSS_TERMINAL_STATUS } from '@/views/infoManage/dssMonitor/enum';

  export default {
    name: 'terminalSelect',
    mixins: [excludeChildMixin,dlgMixin],
    components: { treeLayout },
    data() {
      return {
        terminalStatusOptions: DSS_TERMINAL_STATUS,
        bDialogShow: true,
        szTitle: '选择终端',
        iId: this.id,
        szTerminalName: '',
        macToSearch: '',
        terminalStatus: 'all',
        aTerminalList: [],
        iCurrentPage: 1,
        iPageSize: 20,
        iTotal: 0,
        TRANSFER_PAGINATION_LAYOUT,
        sort: {
          prop: 'mac',
          order: 'DESC',
        },
        // 选中的终端
        leftSelection: [],
        // 机构id查询
        searchDeptId: null,
        // 仅当前层级
        excludeChild: false,
        bCompanyDept: false,
        loading: false,
      };
    },
    computed: {},
    watch: {
      terminalStatus() {
        this.iCurrentPage = 1;
        this.iPageSize = 20;
        this.getTerminalList();
      },
      excludeChild() {
        if (!this.searchDeptId) {
          return;
        }
        this.iCurrentPage = 1;
        this.iPageSize = 20;
        this.getTerminalList();
      },
    },
    methods: {
      clickLeftRow(row) {
        if (row.disabled) {
          return;
        }
        this.$refs.leftTable.toggleRowSelection(row);
      },
      handleLeftSelectionChange(val) {
        this.leftSelection = val;
      },
      /**
       * 格式化状态
       */
      formatStatus(row) {
        let res = SYSTEM_STATUS.find((item) => row.terminalStatus === item.value);
        return (res && res.label) || '--';
      },
      //设置该行数据是否可用
      isDisabledSelected(row) {
        return !row.disabled;
      },
      /**
       * 删除选中的终端
       */
      doDelete(row, index) {
        this.leftSelection.splice(index, 1);
        this.$refs.leftTable.toggleRowSelection(row, false);
      },
      // 树控件选项改变
      handleTreeChange({ node, type }) {
        this.searchDeptId = node.id;
        this.getTerminalList();
      },
      sortChange(col) {
        this.sort.order = 'ascending' === col.order ? 'ASC' : 'DESC';
        this.sort.prop = col.prop;
        this.getTerminalList();
      },
      getTerminalList() {
        this.loading = true;
        let that = this;
        let param = {
          terminalStatus: that.terminalStatus,
          terminalName: that.szTerminalName,
          deptId: that.searchDeptId,
          includeChild: !this.excludeChild,
          mac: that.macToSearch,
          page: that.iCurrentPage,
          rows: that.iPageSize,
          direction: this.sort.order,
          properties: this.sort.prop,
        };
        getTerminalList(param)
          .then((response) => {
            that.aTerminalList = response.data.rows;
            that.iTotal = response.data.total;
          })
          .finally(() => (this.loading = false));
      },
      submitOk() {
        if (!this.leftSelection.length) {
          this.$message.warning('请选择终端！');
          return;
        }
        this.$emit('handleSelect', this.leftSelection);
        this.close();
      },
    },
  };
</script>

<style scoped lang="scss">
  .search-wrap {
    display: flex;
    align-items: center;
  }
  ::v-deep .el-dialog {
    height: 700px;
    .layout {
      height: 404px;
      .left-wrap,
      .middle-wrap {
        display: var(--block);
      }
    }
    .el-dialog__body {
      padding: 10px 20px 0;
    }
  }
  .el-icon-delete {
    font-size: 18px;
    cursor: pointer;
    color: #1f7bc1;
  }
</style>
