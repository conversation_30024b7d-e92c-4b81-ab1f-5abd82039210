<template>
  <div class="terminal-list-wrap">
    <div class="title-wrap"> 终端列表 </div>
    <div class="terminal-list">
      <el-table
        :data="list"
        style="width: 100%; overflow-y: scroll"
        height="100%"
        :row-style="{ height: '32px' }"
        :cell-style="{ textAlign: 'center' }"
        :header-cell-style="{ textAlign: 'center' }"
        size="small"
      >
        <el-table-column prop="name" label="名称" width="180" show-overflow-tooltip></el-table-column>
        <el-table-column prop="status" label="状态" width="180" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <svg-icon icon-class="circle" :style="{ color: 'rgb(63, 252, 200)' }" class="circle-icon" />
            {{ row.status }}
          </template>
        </el-table-column>
        <el-table-column prop="playTime" label="播放时长" width="180" show-overflow-tooltip>
          <template slot-scope="{ row }">
            {{ row.playTime + 'h' }}
          </template>
        </el-table-column>
        <el-table-column prop="material" label="素材" min-width="200">
          <template slot-scope="{ row }">
            <stacked-progress-bar :list="row.material" :title="row.name"></stacked-progress-bar>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
  import StackedProgressBar from './StackedProgressBar.vue';
  export default {
    components: {
      StackedProgressBar,
    },
    data() {
      return {
        list: [
          {
            name: '终端一',
            status: '在线',
            playTime: 3200,
            material: [
              { type: 'staticPicture', value: 200 },
              { type: 'dynamicPicture', value: 20 },
              { type: 'video', value: 50 },
              { type: 'pdf', value: 2 },
              { type: 'office', value: 10 },
            ],
          },
          {
            name: '终端二',
            status: '在线',
            playTime: 3200,
            material: [
              { type: 'staticPicture', value: 20 },
              { type: 'dynamicPicture', value: 20 },
              { type: 'video', value: 50 },
              { type: 'pdf', value: 2 },
              { type: 'office', value: 10 },
            ],
          },
          {
            name: '终端三',
            status: '在线',
            playTime: 3200,
            material: [
              { type: 'staticPicture', value: 100 },
              { type: 'dynamicPicture', value: 20 },
              { type: 'video', value: 50 },
              { type: 'pdf', value: 2 },
              { type: 'office', value: 10 },
            ],
          },
          {
            name: '终端四',
            status: '在线',
            playTime: 3200,
            material: [
              { type: 'staticPicture', value: 80 },
              { type: 'dynamicPicture', value: 20 },
              { type: 'video', value: 50 },
              { type: 'pdf', value: 2 },
              { type: 'office', value: 10 },
            ],
          },
          {
            name: '终端五',
            status: '在线',
            playTime: 3200,
            material: [
              { type: 'staticPicture', value: 60 },
              { type: 'dynamicPicture', value: 20 },
              { type: 'video', value: 50 },
              { type: 'pdf', value: 2 },
              { type: 'office', value: 10 },
            ],
          },
          {
            name: '终端六',
            status: '在线',
            playTime: 3200,
            material: [
              { type: 'staticPicture', value: 120 },
              { type: 'dynamicPicture', value: 20 },
              { type: 'video', value: 50 },
              { type: 'pdf', value: 2 },
              { type: 'office', value: 10 },
            ],
          },
          {
            name: '终端七',
            status: '在线',
            playTime: 3200,
            material: [
              { type: 'staticPicture', value: 140 },
              { type: 'dynamicPicture', value: 20 },
              { type: 'video', value: 50 },
              { type: 'pdf', value: 2 },
              { type: 'office', value: 10 },
            ],
          },
          {
            name: '终端八',
            status: '在线',
            playTime: 3200,
            material: [
              { type: 'staticPicture', value: 50 },
              { type: 'dynamicPicture', value: 20 },
              { type: 'video', value: 50 },
              { type: 'pdf', value: 2 },
              { type: 'office', value: 10 },
            ],
          },
        ],
      };
    },
  };
</script>

<style lang="scss" scoped>
  .terminal-list-wrap {
    background: url(../img/box-center-bg.png) no-repeat;
    box-sizing: border-box;
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    .title-wrap {
      font-family: 'YouSheBiaoTiHei';
      font-size: 18px;
      padding: 15px 22px 15px 0px;
      text-align: center;
    }
    .terminal-list {
      padding: 10px 30px;
      flex: 1;
      overflow: hidden;
      .el-table {
        height: 100%;
        background-color: transparent;
        ::v-deep {
          th.el-table__cell {
            background-color: rgb(26, 49, 77);
            border-bottom: 1px solid rgb(26, 49, 77);
          }

          thead {
            color: white;
          }

          tr {
            background-color: transparent;
            td.el-table__cell {
              background-color: transparent;
              color: rgb(217, 217, 217);
              border-bottom-color: transparent;
            }
            &:nth-child(2n) {
              background-color: rgb(21, 37, 55);
              border-bottom-color: rgb(21, 37, 55);
            }
          }
        }
      }
    }
  }

  ::v-deep .el-table__body-wrapper::-webkit-scrollbar {
    width: 24px;
  }
  /*定义滚动条轨道 内阴影+圆角*/
  ::v-deep .el-table__body-wrapper::-webkit-scrollbar-track {
    border-radius: 24px;
    border: 9px solid rgba(0, 0, 0, 0);
    box-shadow: 8px 0 0 rgba(125, 125, 125, 0.2) inset;
  }
  /*定义滑块 内阴影+圆角*/
  ::v-deep .el-table__body-wrapper::-webkit-scrollbar-thumb {
    border-radius: 24px;
    border: 9px solid rgba(0, 0, 0, 0);
    box-shadow: 8px 0 0 rgba(12, 113, 197, 0.8) inset;
    background-color: transparent;
    outline: 0px solid #fff;
  }
  ::v-deep .el-table__body-wrapper::-webkit-scrollbar-track-piece {
    background-color: transparent;
  }

  .bar {
    margin-right: 20px;
  }
</style>
