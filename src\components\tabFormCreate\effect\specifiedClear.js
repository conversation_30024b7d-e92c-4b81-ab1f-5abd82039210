/**
 * 自定义属性
 * 当前组件的值变化为指定值时清空指定组件的值
 * 数据格式
 * [
     {
       "clear": [
         "uri"
       ],
       "value": "1"
     }
  ]
 */
export default {
  name: "specifiedClear",
  value({ value: clears }, rule, fapi) {
    console.log(rule)
    for (let link of clears) {
      const { clear, value } = link;
      if (rule.value === value) {
        
        clear.map((item) => {
          console.log(fapi.getValue(item))
          fapi.setValue({ [item]: "" })
        });
      }
    }
  },
};
