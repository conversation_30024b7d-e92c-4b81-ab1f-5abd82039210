<template>
  <div class="log-wrap">
    <div class="title-wrap"> 监控告警 </div>
    <ul
      class="list-wrap"
      v-infinite-scroll="() => getData(false, true)"
      infinite-scroll-disabled="bDisabledInfiniteScroll"
      v-loading="loading"
      element-loading-spinner="el-icon-loading"
      element-loading-background="transparent"
      @mouseenter="closeInterval"
      @mouseleave="startInterval"
    >
      <li v-for="item in list" :key="item.id" :style="{ color: COLOR[item.levelIndex] }">
        {{ item.str }}
      </li>
    </ul>
  </div>
</template>

<script>
  import refreshMixin from '../mixins/refresh';

  const COLOR = ['#3FFCC8', '#3FFCC8', 'rgb(255,156,0)', '#D94883'];
  const LEVEL = ['NONE', 'MINOR', 'MAJOR', 'FATAL'];

  export default {
    mixins: [refreshMixin],
    data() {
      return {
        loading: false,
        list: [
          {
            str: '[2025/02/24 10:42:17]: 终端【会议室1终端】离线',
            levelIndex: 3,
          },
          {
            str: '[2025/02/24 10:40:17]: tom上传素材【logo.png】',
            levelIndex: 0,
          },
          {
            str: '[2025/02/24 10:40:17]: tom制作节目【会议室1会议指引】',
            levelIndex: 1,
          },
          {
            str: '[2025/02/24 10:40:17]: 系统同步时间失败',
            levelIndex: 2,
          },
          {
            str: '[2025/02/24 10:41:17]: tom下发节目单【会议室1会议指引】失败',
            levelIndex: 3,
          },
          {
            str: '[2025/02/24 10:42:17]: 终端【会议室1终端】离线',
            levelIndex: 3,
          },
          {
            str: '[2025/02/24 10:40:17]: tom上传素材【logo.png】',
            levelIndex: 0,
          },
          {
            str: '[2025/02/24 10:40:17]: tom制作节目【会议室1会议指引】',
            levelIndex: 1,
          },
          {
            str: '[2025/02/24 10:40:17]: 系统同步时间失败',
            levelIndex: 2,
          },
          {
            str: '[2025/02/24 10:41:17]: tom下发节目单【会议室1会议指引】失败',
            levelIndex: 3,
          },
          {
            str: '[2025/02/24 10:42:17]: 终端【会议室1终端】离线',
            levelIndex: 3,
          },
          {
            str: '[2025/02/24 10:40:17]: tom上传素材【logo.png】',
            levelIndex: 0,
          },
          {
            str: '[2025/02/24 10:40:17]: tom制作节目【会议室1会议指引】',
            levelIndex: 1,
          },
          {
            str: '[2025/02/24 10:40:17]: 系统同步时间失败',
            levelIndex: 2,
          },
          {
            str: '[2025/02/24 10:41:17]: tom下发节目单【会议室1会议指引】失败',
            levelIndex: 3,
          },
        ],
        COLOR,
        page: 0,
        size: 20,
        total: 0,
        // 是否禁用无限滚动加载
        bDisabledInfiniteScroll: false,
      };
    },
    methods: {
      getData(bShowLoading = true, bNext = false) {
        // 加载下一页
        // if (bNext) {
        //   if (this.list.length === this.total) {
        //     return;
        //   }
        //   this.bDisabledInfiniteScroll = true;
        //   this.page++;
        // } else {
        //   // 初始化或刷新列表
        //   this.bDisabledInfiniteScroll = true;
        //   this.list = [];
        //   this.page = 0;
        // }   

        // this.getMcuAlert(bShowLoading).then(() => (this.bDisabledInfiniteScroll = false));
      },
    },
  };
</script>

<style lang="scss" scoped>
  .log-wrap {
    background: url(../img/box-right-bg.png) no-repeat;
    box-sizing: border-box;
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    .title-wrap {
      font-family: 'YouSheBiaoTiHei';
      font-size: 18px;
      padding: 15px 22px 15px 0px;
      text-align: right;
    }

    .list-wrap {
      width: 100%;
      flex: 1;
      list-style: none;
      overflow-y: scroll;
      margin: 10px 0;
      padding: 0 15px;
      li {
        font-size: 12px;
        line-height: 14px;
        cursor: pointer;
        & + li {
          margin-top: 8px;
        }
      }
      &::-webkit-scrollbar {
        width: 24px;
      }
      /*定义滚动条轨道 内阴影+圆角*/
      &::-webkit-scrollbar-track {
        border-radius: 24px;
        border: 9px solid rgba(0, 0, 0, 0);
        box-shadow: 8px 0 0 rgba(125, 125, 125, 0.2) inset;
      }
      /*定义滑块 内阴影+圆角*/
      &::-webkit-scrollbar-thumb {
        border-radius: 24px;
        border: 9px solid rgba(0, 0, 0, 0);
        box-shadow: 8px 0 0 rgba(12, 113, 197, 0.8) inset;
        background-color: transparent;
        outline: 0px solid #fff;
      }
      &::-webkit-scrollbar-track-piece {
        background-color: transparent;
      }
    }
  }
</style>
