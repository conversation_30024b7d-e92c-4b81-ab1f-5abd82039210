server_name  _;

proxy_set_header    X-Forwarded-For $proxy_add_x_forwarded_for;
charset utf-8;
client_max_body_size     4096m;

location / {
    root   /usr/share/nginx/html;
    index  index.html;
}

location /base/puppeteer/ {
    proxy_pass http://172.110.220.54:10024/puppeteer/;
}

location /base/webapp {
    proxy_pass http://**************:10022/webapp;
}

location /base/media {
    proxy_pass http://**************:10022/media;
}

location /base/h5 {
    proxy_pass http://**************:10022/h5;
}

location /base/storage {
    proxy_pass http://**************:10022/storage;
}

location /base/noauth {
    proxy_pass http://**************:10022/noauth;
}

location /base/oauth {
    proxy_pass http://172.110.220.51:10021/oauth;
}

location /base/license {
    proxy_pass http://172.110.220.51:10021/license;
}

location /base/sysManagement {
    proxy_pass http://172.110.220.51:10021/sysManagement;
}

location /base/oplog {
    proxy_pass http://172.110.220.51:10021/oplog;
}

location /base/weapp {
    proxy_pass http://**************:10025/weapp;
}

location /base/iot {
    proxy_pass http://172.110.220.55:10026/iot;
}

location /api {
    proxy_pass http://**************:10025;
}

location /auth {
    proxy_pass http://**************:10025;
}

location /peimc-chb {
    proxy_pass http://**************:10025;
}

location /base/dashboard {
    proxy_pass http://**************:10027/dashboard;
}

location /base/stream {
    proxy_pass http://**************:10028/stream;
}

location /webapp/ {
    proxy_pass http://**************:10022/webapp/;
}

location /storage/ {
    root /home/<USER>/repository;
    autoindex off;
}

location /distribution {
    alias /distribution;
}

location /mqtt {
    proxy_pass http://**************:8083;
    proxy_redirect off;
    proxy_set_header Host nginx:8083;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
}
