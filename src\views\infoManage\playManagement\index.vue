<template>
  <div id="playManagement" v-loading="loading">
    <div class="content-top">
      <div class="btn-area">
        <el-menu
          mode="horizontal"
          background-color="#1F7BC1"
          text-color="#fff"
          active-text-color="#fff"
          class="mr-10"
          v-action:playManagement|operate
          v-if="bShowAddBtn"
        >
          <el-submenu index="1" popper-class="add-program-menu" @click.native="add(1)">
            <template slot="title">添加</template>
            <el-submenu index="1-1">
              <template slot="title">单屏</template>
              <el-menu-item index="1-1-1" @click.native="add(1)">普通</el-menu-item>
              <el-menu-item index="1-1-2" @click.native="add(4)">拼接</el-menu-item>
            </el-submenu>
            <el-submenu index="1-2">
              <template slot="title">双屏</template>
              <el-menu-item index="1-2-1" @click.native="add(2)">普通</el-menu-item>
              <el-menu-item index="1-2-2" @click.native="add(3)">拼接</el-menu-item>
            </el-submenu>
          </el-submenu>
        </el-menu>
        <el-button icon="el-icon-delete" size="small" class="mr-10" @click="deleteBatch" type="danger" v-action:playManagement|operate
          >删除</el-button
        >
        <el-button icon="el-icon-time" size="small" @click="viewPackProgress" class="mr-10" type="primary" v-action:playManagement|udisk
          >打包进度
        </el-button>
        <el-button icon="el-icon-download" size="small" @click="exportExcel" class="mr-10" type="primary" v-action:playManagement|export
          >导出节目单信息</el-button
        >
        <div class="mr-10">
          <el-input
            style="width: 140px"
            placeholder="节目单名称"
            suffix-icon="el-icon-search"
            v-model="szInputProgrammeName"
            size="small"
            v-debounce="[
              (e) => {
                pagination.iCurrentPage = 1;
                getProgrammeList(e);
              },
            ]"
          >
          </el-input>
        </div>
        <div>
          <fuzzy-query-user-input
            placeholder="创建人"
            style="width: 140px"
            class="mr-10"
            labelType="username"
            secondLabelType="nickname"
            @handleSelect="
              (e) => {
                creatorToSearch = e ? e.username : '';
                pagination.iCurrentPage = 1;
                getProgrammeList();
              }
            "
          >
          </fuzzy-query-user-input>
        </div>
        <el-select
          v-model="playlistNumber"
          v-action:playManagement|channel
          size="small"
          placeholder="请选择播放频道"
          clearable
          style="width: 140px"
          class="mr-10"
          @change="
            () => {
              pagination.iCurrentPage = 1;
              getProgrammeList();
            }
          "
        >
          <el-option
            v-for="item in numberedPlaylist"
            :key="item.playlistNumber"
            :label="item.playlistNumber + ': ' + item.playlistName"
            :value="item.playlistNumber"
          >
          </el-option>
        </el-select>
        <el-select
          v-model="playType"
          size="small"
          placeholder="请选择播放类型"
          clearable
          style="width: 140px"
          @change="
            () => {
              pagination.iCurrentPage = 1;
              getProgrammeList();
            }
          "
        >
          <el-option v-for="item in playTypeList" :key="item.label" :label="item.value" :value="item.label"> </el-option>
        </el-select>
        <div class="ml-10" v-show="active === 'list'">
          <el-select placeholder="显示列" style="width: 180px" v-model="columns" multiple collapse-tags size="small" @change="changeColumns">
            <el-option v-for="item in PLAYLIST_COLUMNS" :key="item.value" :label="item.label" :value="item.value"> </el-option>
          </el-select>
        </div>
      </div>
      <toggle-mode class="toggle-mode" @toggle="changMode" :active="active"></toggle-mode>
    </div>
    <div class="content-body">
      <div class="content-detail">
        <div class="content-detail-border">
          <transition name="fade" mode="out-in">
            <component
              class="com-content"
              :is="mode"
              :list="aProgrammeList"
              :pagination="pagination"
              :pageSize="pageSize"
              :mulSelect.sync="aMultipleSelection"
              @viewLostInfo="viewLostInfo"
              @watchHistory="watchHistory"
              @publishTerminal="publishTerminal"
              @doExport="doExport"
              @deletePlaylist="deletePlaylist"
              @edit="edit"
              @stopPlay="stopPlay"
              @sortChange="sortChange"
              :columnsKey="columnsKey"
              ref="component"
            ></component>
          </transition>
          <pagination :total="pagination.iTotal" :page.sync="pagination.iCurrentPage" :limit.sync="pageSize" @pagination="getProgrammeList" />
        </div>
      </div>
    </div>
    <!--发布到终端弹框-->
    <TerminalSelect
      :bDialogSelectTerminalVisible="bDialogPublishVisible"
      v-if="bDialogPublishVisible"
      :title="szTitle"
      :id="iId"
      :programmeType="programmeType"
      :bScheduleTime="true"
      @closeDialog="bDialogPublishVisible = false"
    >
    </TerminalSelect>
    <add-programme ref="addProgramme" @getProgrammeList="getProgrammeList"></add-programme>
    <!-- 打包进度进度弹窗 -->
    <progress-dlg
      ref="packProgrammeDlg"
      :getProgressInterface="getPackProgrammeProgress"
      :deleteProgressInterface="deletePackProgrammeProgress"
      strAction="打包"
      strValue="节目单"
      @refresh="getProgrammeList"
    >
    </progress-dlg>
    
    <!--节目单终端下载记录-->
    <el-dialog
      :title="oDownloadHistory.szTitle"
      :visible.sync="bDialogDownloadVisible"
      width="1150px"
      :close-on-click-modal="false"
      @close="closeDialog"
    >
      <el-button size="small" icon="el-icon-refresh-left" @click="rePublish">重发</el-button>
      <el-button size="small" icon="el-icon-close" @click="rePublish(false)">取消下载</el-button>
      <div class="margin-top10" style="border: 1px solid #eee">
        <el-table
          :data="oDownloadHistory.aTerminalList"
          ref="multipleTableDownload"
          height="300"
          @selection-change="handleSelectionChange1"
          @row-click="clickRowHistory"
          :row-key="getRowKeys"
        >
          <el-table-column type="selection" min-width="40" align="center" reserve-selection :selectable="checkIsCanSelected" />
          <el-table-column prop="terminalName" label="终端名称" :show-overflow-tooltip="true" width="100" />
          <el-table-column prop="mac" label="终端ID" width="120" :show-overflow-tooltip="true" />
          <el-table-column prop="publisher" label="发布者" width="100" :show-overflow-tooltip="true" />
          <el-table-column prop="receive" :formatter="formatterChinese" label="接收状态" width="100" />
          <el-table-column prop="download" label="下载状态" width="150" :show-overflow-tooltip="true" />
          <el-table-column prop="bandwidth" label="下载速率" width="110" :show-overflow-tooltip="true" />
          <el-table-column prop="gmtCreate" label="发布时间" width="160" :show-overflow-tooltip="true" :formatter="formatCreateTime"/>
          <el-table-column label="操作" min-width="200">
            <template slot-scope="scope">
              <el-button
                size="mini"
                class="add-btn"
                icon="el-icon-refresh-left"
                @click.stop="rePublish(true, scope.row)"
                :disabled="bOnlyHasMyTerminalAuth && !scope.row.myTerminal"
                >重发</el-button
              >
              <el-button
                size="mini"
                class="delete-btn"
                icon="el-icon-close"
                @click.stop="rePublish(false, scope.row)"
                :disabled="bOnlyHasMyTerminalAuth && !scope.row.myTerminal"
              >
                取消下载</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
      <pagination
        :total="oDownloadHistory.iTotal"
        :page.sync="oDownloadHistory.iCurrentPage"
        :limit.sync="oDownloadHistory.iPageSize"
        @pagination="getDownloadHistory(oDownloadHistory.iCurrentId)"
        layout="total,prev,pager,next"
        :autoScroll="false"
      />
    </el-dialog>
    <!--查看节目单删除的节目-->
    <el-dialog title="缺失节目或素材" :visible.sync="bDialogLostVisible" width="500px" :close-on-click-modal="false">
      <div style="height: 400px">
        <el-table :data="oLostProgram.aProgramList" height="50%">
          <el-table-column label="序号" type="index" width="60"> </el-table-column>
          <el-table-column prop="programName" :show-overflow-tooltip="true" label="已删除的节目名称" width="auto"> </el-table-column>
        </el-table>
        <el-table :data="oLostMaterial.aProgramList" height="50%">
          <el-table-column label="序号" type="index" width="60"> </el-table-column>
          <el-table-column prop="name" :show-overflow-tooltip="true" label="已删除的素材名称" width="auto"> </el-table-column>
        </el-table>
      </div>
      <pagination
        :total="oLostMaterial.iTotal"
        :page.sync="oLostMaterial.iCurrentPage"
        :limit.sync="oLostMaterial.iPageSize"
        @pagination="viewLostInfo(oLostMaterial.iCurrentPlaylistId)"
        layout="prev, pager, next"
        :autoScroll="false"
      />
    </el-dialog>
  </div>
</template>

<script>
  import TerminalSelect from '@/views/infoManage/modules/terminalSelect.vue';
  import ProgressDlg from '@/components/progressDlg.vue';
  import addProgramme from './modules/addProgramme';
  import ToggleMode from '@/components/toggleMode/index.vue';
  import CardList from './components/card.vue';
  import TableList from './components/table.vue';
  import StopPlayTerminal from './modules/stopPlayTerminal.vue'
  import { PROGRAM_TYPE, PLAY_TYPE } from '@/utils/enum';
  import { exportFile, downloadFile } from '@/utils/utils';
  import {
    getProgrammeList,
    getLostProgram,
    getLostMaterial,
    getProgrammePlayList,
    publishPlaylist,
    canclePublishPlaylist,
    deleteProgrammeList,
    getAllExportLog,
    packProgramme,
    getPackProgrammeProgress,
    deletePackProgrammeProgress,
    canDownloadProgramme,
    getNumberedPlaylist,
  } from '@/api/info';
  import { tablePageSize as pageSize } from '@/mixins/prefer.js';
  import { hasPermission } from '@/directive/permission/index.js';
  import { setUserColumn, getUserColumn } from '@/api/terminal';
  import { PLAYLIST_COLUMNS, DEFAULT_PLAYLIST_COLUMNS, DEFAULT_PLAYLIST_COLUMNS_KEY } from '@/views/infoManage/enum';
  import { playlistModeMixin } from '@/mixins/prefer.js';
  import transform from '@/utils/transform';

  export default {
    name: 'playManagement',
    mixins: [pageSize, playlistModeMixin, ],
    components: { TerminalSelect, addProgramme, ProgressDlg, ToggleMode, CardList, TableList },
    data() {
      return {
        PLAYLIST_COLUMNS,
        columns: DEFAULT_PLAYLIST_COLUMNS,
        columnsKey: DEFAULT_PLAYLIST_COLUMNS_KEY,
        bShowAll: false,

        bDialogPublishVisible: false,
        bDialogPercentVisible: false,
        bDialogStopVisible: false,
        bDialogDownloadVisible: false,
        bDialogLostVisible: false,

        szInputProgrammeName: '',
        creatorToSearch: '',
        oInterval: null,
        szMediaServer: '',
        szTitlePlaylist: '',
        szTitle: '',
        iId: -1,
        aMultipleSelection: [],
        aProgrammeList: [],
        pagination: {
          iTotal: 0,
          iCurrentPage: 1,
        },
        szPublishType: '1',
        aTask: [],
        oSort: {
          direction: 'DESC',
          properties: 'gmtCreate',
        },
        iCurrentPlayListId: -1, // 当前停止播放或者取消下载的id
        oDownloadHistory: {
          szTitle: '',
          aMultipleSelection: [],
          iCurrentId: -1,
          aTerminalList: [],
          iPageSize: 20,
          iCurrentPage: 1,
          iTotal: 0,
        },
        oTimer: null,
        oLostProgram: {
          aProgramList: [],
          iCurrentPage: 1,
          iCurrentPlaylistId: -1,
          iPageSize: 20,
          iTotal: 0,
        },
        oLostMaterial: {
          aProgramList: [],
          iCurrentPage: 1,
          iCurrentPlaylistId: -1,
          iPageSize: 20,
          iTotal: 0,
        },
        loading: false,
        programmeType: PROGRAM_TYPE[0].key,
        numberedPlaylist: [],
        playlistNumber: null,
        mode: 'card-list',
        active: 'card',
        // 播放类型筛选
        playType: null,
      };
    },
    created() {
      // 根据用户设置显示页面视图
      this.active = this.playlistMode;
      this.mode = this.playlistMode === 'card' ? 'card-list' : 'table-list';

      this.getNumberedPlaylist();
    },
    mounted() {
      this.getProgrammeList();
      this.getColumn();
    },
    computed: {
      bShowAddBtn() {
        return PLAY_TYPE.some((item) => {
          return hasPermission(undefined, 'playManagement', item.key);
        });
      },
      // 是否有我的终端页面权限
      bOnlyHasMyTerminalAuth() {
        return hasPermission(undefined, 'myDssMonitor') && !hasPermission(undefined, 'dssMonitor');
      },
      // 播放类型列表
      playTypeList() {
        // 权限判断
        return PLAY_TYPE.filter((item) => {
          return hasPermission(undefined, 'playManagement', item.key);
        });
      },
    },
    watch: {
      bShowAll: function (newValue) {
        let that = this;
        if (newValue) {
          for (let i in this.columnsKey) {
            this.columnsKey[i] = true;
          }
          this.columns = PLAYLIST_COLUMNS.map((item) => item.value);
          that.saveColumn(that.columns);
        } else {
          for (let i in this.columnsKey) {
            this.columnsKey[i] = false;
          }
          this.columns = DEFAULT_PLAYLIST_COLUMNS;
          setTimeout(function () {
            that.saveColumn(that.columns);
          }, 200);
          this.changeColumns(this.columns);
        }
      }
    },
    methods: {
      changMode(mode) {
        this.active = mode;
        this.mode = mode === 'card' ? 'card-list' : 'table-list';
        // 记录设置
        this.playlistMode = mode;
      },
      //设置当前行数据是否可选中，返回true可选中，false禁止选中
      checkIsCanSelected(row) {
        if (!this.bOnlyHasMyTerminalAuth) {
          return true;
        }
        // 当用户有我的终端页面权限，但没有终端列表权限，仅可以操作属于我的终端，其他禁用
        return this.bOnlyHasMyTerminalAuth && row.myTerminal;
      },
      //获取节目单编号
      getNumberedPlaylist() {
        getNumberedPlaylist()
          .then(({ data }) => {
            this.numberedPlaylist = data;
          })
          .catch(() => {
            // do nothing
          });
      },
      getPackProgrammeProgress,
      deletePackProgrammeProgress,
      // 获取节目单列表
      getProgrammeList() {
        this.loading = true;
        let that = this;
        getProgrammeList({
          page: that.pagination.iCurrentPage,
          rows: that.pageSize,
          creator: this.creatorToSearch,
          name: that.szInputProgrammeName,
          direction: that.oSort.direction,
          properties: that.oSort.properties,
          number: this.playlistNumber,
          playType: this.playType,
        })
          .then((response) => {
            that.aProgrammeList = response.data.rows;
            that.pagination.iTotal = response.data.total;
          })
          .catch(() => {
            // do nothing
          })
          .finally(() => {
            this.loading = false;
          });
      },
      viewLostInfo(row) {
        let that = this;
        that.bDialogLostVisible = true;
        that.oLostProgram.iCurrentPlaylistId = row.id;
        that.oLostMaterial.iCurrentPlaylistId = row.id;
        getLostProgram({
          page: that.oLostProgram.iCurrentPage,
          size: that.oLostProgram.iPageSize,
          playlistId: row.id,
        }).then((response) => {
          that.oLostProgram.aProgramList = response.data.rows;
          that.oLostProgram.iTotal = response.data.total;
        });
        getLostMaterial({
          page: that.oLostMaterial.iCurrentPage,
          size: that.oLostMaterial.iPageSize,
          playlistId: row.id,
        }).then((response) => {
          that.oLostMaterial.aProgramList = response.data.rows;
          that.oLostMaterial.iTotal = response.data.total;
        });
      },
      //显示添加节目单对话框
      add(type) {
        console.log('add', type);
        this.$router.push({
          name: 'AddProgramme',
          params: { type },
        });
      },
      //显示编辑节目单对话框
      edit(row) {
        this.$router.push({
          name: 'AddProgramme',
          params: { id: row.id, type: 0 },
        });
      },

      isSame(arr1, arr2) {
        let bSame = false;
        for (let i = 0; i < arr1.length; i++) {
          if (-1 !== $.inArray(arr1[i], arr2)) {
            bSame = true;
            break;
          }
        }
        return bSame;
      },
      publishTerminal(row) {
        this.bDialogPublishVisible = true;
        this.szTitle = '发布';
        this.iId = row.id;
        this.programmeType = row.playProgramType;
      },
      //打包节目单
      doExport(row, param, type) {
        console.log('doExport');
        if (row[param]) {
          this.doDownload(row, param);
        } else {
          this.doPack(row, type);
        }
      },
      doDownload(row, param) {
        let name = PLAY_TYPE.find((item) => item.label === row.playType).zipName;
        //多个默认节目单的文件名
        name = row?.numberedPlaylist?.playlistNumber ? name + row?.numberedPlaylist?.playlistNumber : name;
        canDownloadProgramme(row.id).then(() => {
          downloadFile(name, row[param]);
        });
      },
      //导出节目单
      doPack(row, packageType) {
        packProgramme(row.id, packageType).then((res) => {
          this.$refs.packProgrammeDlg.show();
        });
      },
      viewPackProgress() {
        this.$refs.packProgrammeDlg.show();
      },
      // 停止播放弹窗
      createStopPlayTerminalDlg:transform(StopPlayTerminal),
      // 停止播放
      stopPlay(row) {
        this.createStopPlayTerminalDlg({
          propsData:{
            playlistId:row.id,
            playlistType:row.playType
          }
        })
      },
      watchHistory(row) {
        let that = this;
        that.bDialogDownloadVisible = true;
        that.oDownloadHistory.iCurrentId = row.id;
        that.oDownloadHistory.szTitle = `节目单【${row.name}】的发布历史`;
        that.clearSelection();
        that.getDownloadHistory(row.id);
        that.oInterval = setInterval(function () {
          that.getDownloadHistory(row.id);
        }, 2000);
      },
      clearSelection() {
        this.$nextTick(() => {
          if (this.$refs.multipleTableDownload) {
            this.$refs.multipleTableDownload.clearSelection();
          }
        });
      },
      getDownloadHistory(id) {
        let that = this;
        that.oDownloadHistory.aTerminalList.length = 0;
        getProgrammePlayList({
          page: that.oDownloadHistory.iCurrentPage,
          rows: that.oDownloadHistory.iPageSize,
          playlistId: id,
        })
          .then((response) => {
            that.oDownloadHistory.aTerminalList = response.data.rows;
            that.oDownloadHistory.iTotal = response.data.total;
          })
          .catch(() => {
            // do nothing
          });
      },
      // 重发
      rePublish(bPublish = true, row = null) {
        let that = this;
        let aTerminalIds = [];
        let aMacs = [];
        if (row) {
          aTerminalIds = [row.terminalId];
          aMacs = [row.mac];
        } else {
          if (!that.oDownloadHistory.aMultipleSelection.length) {
            that.$message.error('请选择要操作的终端！');
            return;
          } else {
            aTerminalIds = that.oDownloadHistory.aMultipleSelection.map((item) => item.terminalId);
            aMacs = that.oDownloadHistory.aMultipleSelection.map((item) => item.mac);
          }
        }
        let fn = bPublish ? publishPlaylist : canclePublishPlaylist;
        fn({
          macs: aMacs.join(','),
          terminalIds: aTerminalIds.join(','),
          playlistId: that.oDownloadHistory.iCurrentId,
        }).then(() => {
          this.$message.success('操作成功');
          that.getDownloadHistory(that.oDownloadHistory.iCurrentId);
          //that.bDialogDownloadVisible = false;
        });
      },
      getRowKeys(row) {
        return row.id;
      },
      getTaskStatus() {
        let that = this;
        that.bDialogPercentVisible = true;
        that.oInterval = setInterval(function () {
          getAllExportLog()
            .then((response) => {
              that.aTask = response.data.data;
            })
            .catch(function () {
              // do nothing
            });
        }, 1000);
      },
      exportExcel() {
        exportFile({ url: '/webapp/playlist/exportPlaylist' });
      },
      formatterChinese(row) {
        if (row.receive === 'rok') {
          return '接收成功';
        } else if ('rfailed' === row.receive) {
          return '接收失败';
        } else {
          return '';
        }
      },
      formatCreateTime(row){
        if (row.gmtCreate) {
          return new Date(row.gmtCreate).Format('yyyy-MM-dd hh:mm:ss');
        }
        return '';
      },
      download(url) {
        window.location.href = url;
      },
      closeDialog() {
        clearInterval(this.oInterval);
        this.oInterval = null;
      },
      deleteBatch() {
        let that = this;
        if (!that.aMultipleSelection.length) {
          let szHtml = '<p>' + '没有选中数据，请先选中一条数据' + '</p>';
          let szTitle = '操作失败';
          this.$alert(szHtml, szTitle, {
            dangerouslyUseHTMLString: true,
            confirmButtonText: '确定',
            type: 'error',
          });
        } else {
          let szHtml = '<p>' + '您确定要删除所选节目单吗？' + '</p>';
          that
            .$confirm(szHtml, '提示', {
              dangerouslyUseHTMLString: true,
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
            })
            .then(() => {
              let aIds = that.aMultipleSelection.map((item) => item.id);
              that.submitDeleteData(aIds);
            })
            .catch(() => {
              //do nothing
            });
        }
      },
      deletePlaylist(row) {
        let that = this;
        let szHtml = '<p>' + `您确定要删除【${row.name}】吗？` + '</p>';
        that
          .$confirm(szHtml, '提示', {
            dangerouslyUseHTMLString: true,
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
          .then(() => {
            that.submitDeleteData([row.id]);
          })
          .catch(() => {
            //do nothing
          });
      },
      submitDeleteData(aIds) {
        let that = this;
        deleteProgrammeList({
          ids: aIds.join(','),
        })
          .then(() => {
            this.$message.success('删除成功');
            that.getProgrammeList();
          })
          .catch(() => {
            // do nothing
          });
      },
      clickRowHistory(row) {
        this.$refs.multipleTableDownload.toggleRowSelection(row);
      },
      checkedChangeWeek(value) {
        let checkedCount = value.length;
        this.oProgramme.bWeekCheckAll = checkedCount === this.oProgramme.aWeeks.length;
        this.oProgramme.isWeekIndeterminate = checkedCount > 0 && checkedCount < this.oProgramme.aWeeks.length;
      },
      checkAllMonth(val) {
        this.oProgramme.aPlayMonth = val ? this.oProgramme.aMonth.map((item) => item.value) : [];
        this.oProgramme.isMonthIndeterminate = false;
      },
      checkedChangeMonth(value) {
        let checkedCount = value.length;
        this.oProgramme.bMonthCheckAll = checkedCount === this.oProgramme.aMonth.length;
        this.oProgramme.isMonthIndeterminate = checkedCount > 0 && checkedCount < this.oProgramme.aMonth.length;
      },
      sortChange(col) {
        this.oSort.direction = 'ascending' === col.order ? 'ASC' : 'DESC';
        this.oSort.properties = col.prop;
        this.getProgrammeList();
      },
      selectChange(val) {
        this.aMultipleSelection = val;
      },
      handleSelectionChange1(val) {
        this.oDownloadHistory.aMultipleSelection = val;
      },
      saveColumn(arr) {
        if (PLAYLIST_COLUMNS.length > arr.length) {
          arr = arr.filter((column) => column !== 'all');
        }
        setUserColumn({
          page: 4,
          columns: arr.join(','),
        });
      },
      getColumn() {
        let that = this;
        getUserColumn({
          page: 4,
        }).then((response) => {
          for (let j = 0; j < response.data.length; j++) {
            if ('all' === response.data[j]) {
              this.bShowAll = true;
              break;
            }
          }
          if (response.data.length) {
            that.columns = response.data;
          } else {
            that.columns = DEFAULT_PLAYLIST_COLUMNS;
          }
          that.changeColumns(that.columns);
        });
      },
      // 下拉框多选控制列显示隐藏
      changeColumns(arr) {
        for (let i in this.columnsKey) {
          this.columnsKey[i] = false;
        }
        this.bShowAll = false;
        for (let j = 0; j < arr.length; j++) {
          if ('all' === arr[j]) {
            this.bShowAll = true;
            break;
          }
        }
        this.showColumns(arr);
        this.saveColumn(arr);
      },
      showColumns(arr) {
        for (let i in this.columnsKey) {
          this.columnsKey[i] = false;
        }
        for (let i = 0; i < arr.length; i++) {
          let res = PLAYLIST_COLUMNS.find((column) => column.value === arr[i]);
          if (res && res.key) {
            this.columnsKey[res.key] = true;
          }
        }
        this.$nextTick(() => {
          if (this.$refs.component && this.$refs.component.$refs.table) {
            this.$refs.component.$refs.table.doLayout();
          }
        });
      },
    },
  };
</script>

<style scoped lang="scss">
  #playManagement {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
  .heightLight {
    color: #3a8ee6;
    cursor: pointer;
  }
  .btn-area > * {
    margin-bottom: 10px;
  }

  .toggle-mode {
    min-width: 97px;
  }
  .el-button {
    margin-right: 10px;
    margin-left: 0;
  }
  ::v-deep.el-menu {
    border-radius: 3px;
  }

  ::v-deep.el-menu--horizontal > .el-submenu .el-submenu__title {
    height: auto;
    line-height: normal;
    padding: 9px 15px;
    font-size: 12px;
    border-radius: 3px;
    border-bottom: none;

    i {
      color: white;
    }
  }
  .content-body {
    min-height: 0;
    display: flex;
    flex: 1;
    width: 100%;
  }
  .content-detail {
    width: 100%;
  }
  .content-detail-border {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .com-content {
    width: 100%;
    height: calc(100% - 48px);
    overflow-y: auto;
  }
  .content-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.5s;
  }
  .fade-enter,
  .fade-leave-out {
    opacity: 0;
  }
  .layout {
    margin-top: 0;
  }
</style>
