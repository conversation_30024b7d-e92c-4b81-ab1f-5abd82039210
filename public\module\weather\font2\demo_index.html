<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>IconFont Demo</title>
  <link rel="shortcut icon" href="https://img.alicdn.com/tps/i4/TB1_oz6GVXXXXaFXpXXJDFnIXXX-64-64.ico" type="image/x-icon"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">&#xe86b;</a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=2222852" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe616;</span>
                <div class="name">暴雨</div>
                <div class="code-name">&amp;#xe616;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe617;</span>
                <div class="name">大暴雨</div>
                <div class="code-name">&amp;#xe617;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe618;</span>
                <div class="name">大雨</div>
                <div class="code-name">&amp;#xe618;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe619;</span>
                <div class="name">多云</div>
                <div class="code-name">&amp;#xe619;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61a;</span>
                <div class="name">多云夜</div>
                <div class="code-name">&amp;#xe61a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61b;</span>
                <div class="name">大雪2</div>
                <div class="code-name">&amp;#xe61b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61d;</span>
                <div class="name">雷阵雨</div>
                <div class="code-name">&amp;#xe61d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61e;</span>
                <div class="name">雷阵雨伴冰雹</div>
                <div class="code-name">&amp;#xe61e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61f;</span>
                <div class="name">暴雪2</div>
                <div class="code-name">&amp;#xe61f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe621;</span>
                <div class="name">霾</div>
                <div class="code-name">&amp;#xe621;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe623;</span>
                <div class="name">强沙尘暴</div>
                <div class="code-name">&amp;#xe623;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe632;</span>
                <div class="name">浮尘</div>
                <div class="code-name">&amp;#xe632;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe634;</span>
                <div class="name">晴-夜</div>
                <div class="code-name">&amp;#xe634;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe636;</span>
                <div class="name">晴</div>
                <div class="code-name">&amp;#xe636;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe639;</span>
                <div class="name">沙尘暴</div>
                <div class="code-name">&amp;#xe639;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63a;</span>
                <div class="name">雾</div>
                <div class="code-name">&amp;#xe63a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63b;</span>
                <div class="name">小雨</div>
                <div class="code-name">&amp;#xe63b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63c;</span>
                <div class="name">特大暴雨</div>
                <div class="code-name">&amp;#xe63c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63e;</span>
                <div class="name">小雪2</div>
                <div class="code-name">&amp;#xe63e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe63f;</span>
                <div class="name">严重霾</div>
                <div class="code-name">&amp;#xe63f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe640;</span>
                <div class="name">扬沙</div>
                <div class="code-name">&amp;#xe640;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe641;</span>
                <div class="name">雨夹雪2</div>
                <div class="code-name">&amp;#xe641;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe642;</span>
                <div class="name">阵雪-夜2</div>
                <div class="code-name">&amp;#xe642;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe643;</span>
                <div class="name">阵雪2</div>
                <div class="code-name">&amp;#xe643;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe645;</span>
                <div class="name">阴</div>
                <div class="code-name">&amp;#xe645;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe646;</span>
                <div class="name">阵雨2</div>
                <div class="code-name">&amp;#xe646;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe647;</span>
                <div class="name">中度霾</div>
                <div class="code-name">&amp;#xe647;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe648;</span>
                <div class="name">中雨</div>
                <div class="code-name">&amp;#xe648;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe649;</span>
                <div class="name">重度霾</div>
                <div class="code-name">&amp;#xe649;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe64a;</span>
                <div class="name">中雪2</div>
                <div class="code-name">&amp;#xe64a;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>兼容性最好，支持 IE6+，及所有现代浏览器。</li>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>但是因为是字体，所以不支持多色。只能使用平台里单色的图标，就算项目里有多色图标也会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持多色图标，这些多色图标在 Unicode 模式下将不能使用，如果有需求建议使用symbol 的引用方式</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.eot');
  src: url('iconfont.eot?#iefix') format('embedded-opentype'),
      url('iconfont.woff2') format('woff2'),
      url('iconfont.woff') format('woff'),
      url('iconfont.ttf') format('truetype'),
      url('iconfont.svg#iconfont') format('svg');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont iconbaoyu"></span>
            <div class="name">
              暴雨
            </div>
            <div class="code-name">.iconbaoyu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icondabaoyu"></span>
            <div class="name">
              大暴雨
            </div>
            <div class="code-name">.icondabaoyu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icondayu"></span>
            <div class="name">
              大雨
            </div>
            <div class="code-name">.icondayu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconduoyun"></span>
            <div class="name">
              多云
            </div>
            <div class="code-name">.iconduoyun
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconduoyunye"></span>
            <div class="name">
              多云夜
            </div>
            <div class="code-name">.iconduoyunye
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icondaxue"></span>
            <div class="name">
              大雪2
            </div>
            <div class="code-name">.icondaxue
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconleizhenyu"></span>
            <div class="name">
              雷阵雨
            </div>
            <div class="code-name">.iconleizhenyu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconleizhenyubanbingbao"></span>
            <div class="name">
              雷阵雨伴冰雹
            </div>
            <div class="code-name">.iconleizhenyubanbingbao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconbaoxue"></span>
            <div class="name">
              暴雪2
            </div>
            <div class="code-name">.iconbaoxue
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconmai"></span>
            <div class="name">
              霾
            </div>
            <div class="code-name">.iconmai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconqiangshachenbao"></span>
            <div class="name">
              强沙尘暴
            </div>
            <div class="code-name">.iconqiangshachenbao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconfuchen1"></span>
            <div class="name">
              浮尘
            </div>
            <div class="code-name">.iconfuchen1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconqing-ye1"></span>
            <div class="name">
              晴-夜
            </div>
            <div class="code-name">.iconqing-ye1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconqing1"></span>
            <div class="name">
              晴
            </div>
            <div class="code-name">.iconqing1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconshachenbao1"></span>
            <div class="name">
              沙尘暴
            </div>
            <div class="code-name">.iconshachenbao1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconwu"></span>
            <div class="name">
              雾
            </div>
            <div class="code-name">.iconwu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconxiaoyu"></span>
            <div class="name">
              小雨
            </div>
            <div class="code-name">.iconxiaoyu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icontedabaoyu"></span>
            <div class="name">
              特大暴雨
            </div>
            <div class="code-name">.icontedabaoyu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconxiaoxue"></span>
            <div class="name">
              小雪2
            </div>
            <div class="code-name">.iconxiaoxue
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconyanzhongmai"></span>
            <div class="name">
              严重霾
            </div>
            <div class="code-name">.iconyanzhongmai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconyangsha"></span>
            <div class="name">
              扬沙
            </div>
            <div class="code-name">.iconyangsha
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconyujiaxue"></span>
            <div class="name">
              雨夹雪2
            </div>
            <div class="code-name">.iconyujiaxue
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconzhenxue-ye"></span>
            <div class="name">
              阵雪-夜2
            </div>
            <div class="code-name">.iconzhenxue-ye
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconzhenxue"></span>
            <div class="name">
              阵雪2
            </div>
            <div class="code-name">.iconzhenxue
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconyin"></span>
            <div class="name">
              阴
            </div>
            <div class="code-name">.iconyin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconzhenyu"></span>
            <div class="name">
              阵雨2
            </div>
            <div class="code-name">.iconzhenyu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconzhongdumai"></span>
            <div class="name">
              中度霾
            </div>
            <div class="code-name">.iconzhongdumai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconzhongyu"></span>
            <div class="name">
              中雨
            </div>
            <div class="code-name">.iconzhongyu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconzhongdumai1"></span>
            <div class="name">
              重度霾
            </div>
            <div class="code-name">.iconzhongdumai1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont iconzhongxue"></span>
            <div class="name">
              中雪2
            </div>
            <div class="code-name">.iconzhongxue
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>兼容性良好，支持 IE8+，及所有现代浏览器。</li>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
          <li>不过因为本质上还是使用的字体，所以多色图标还是不支持的。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont iconxxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconbaoyu"></use>
                </svg>
                <div class="name">暴雨</div>
                <div class="code-name">#iconbaoyu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icondabaoyu"></use>
                </svg>
                <div class="name">大暴雨</div>
                <div class="code-name">#icondabaoyu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icondayu"></use>
                </svg>
                <div class="name">大雨</div>
                <div class="code-name">#icondayu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconduoyun"></use>
                </svg>
                <div class="name">多云</div>
                <div class="code-name">#iconduoyun</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconduoyunye"></use>
                </svg>
                <div class="name">多云夜</div>
                <div class="code-name">#iconduoyunye</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icondaxue"></use>
                </svg>
                <div class="name">大雪2</div>
                <div class="code-name">#icondaxue</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconleizhenyu"></use>
                </svg>
                <div class="name">雷阵雨</div>
                <div class="code-name">#iconleizhenyu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconleizhenyubanbingbao"></use>
                </svg>
                <div class="name">雷阵雨伴冰雹</div>
                <div class="code-name">#iconleizhenyubanbingbao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconbaoxue"></use>
                </svg>
                <div class="name">暴雪2</div>
                <div class="code-name">#iconbaoxue</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconmai"></use>
                </svg>
                <div class="name">霾</div>
                <div class="code-name">#iconmai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconqiangshachenbao"></use>
                </svg>
                <div class="name">强沙尘暴</div>
                <div class="code-name">#iconqiangshachenbao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconfuchen1"></use>
                </svg>
                <div class="name">浮尘</div>
                <div class="code-name">#iconfuchen1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconqing-ye1"></use>
                </svg>
                <div class="name">晴-夜</div>
                <div class="code-name">#iconqing-ye1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconqing1"></use>
                </svg>
                <div class="name">晴</div>
                <div class="code-name">#iconqing1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconshachenbao1"></use>
                </svg>
                <div class="name">沙尘暴</div>
                <div class="code-name">#iconshachenbao1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconwu"></use>
                </svg>
                <div class="name">雾</div>
                <div class="code-name">#iconwu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconxiaoyu"></use>
                </svg>
                <div class="name">小雨</div>
                <div class="code-name">#iconxiaoyu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icontedabaoyu"></use>
                </svg>
                <div class="name">特大暴雨</div>
                <div class="code-name">#icontedabaoyu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconxiaoxue"></use>
                </svg>
                <div class="name">小雪2</div>
                <div class="code-name">#iconxiaoxue</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconyanzhongmai"></use>
                </svg>
                <div class="name">严重霾</div>
                <div class="code-name">#iconyanzhongmai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconyangsha"></use>
                </svg>
                <div class="name">扬沙</div>
                <div class="code-name">#iconyangsha</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconyujiaxue"></use>
                </svg>
                <div class="name">雨夹雪2</div>
                <div class="code-name">#iconyujiaxue</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconzhenxue-ye"></use>
                </svg>
                <div class="name">阵雪-夜2</div>
                <div class="code-name">#iconzhenxue-ye</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconzhenxue"></use>
                </svg>
                <div class="name">阵雪2</div>
                <div class="code-name">#iconzhenxue</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconyin"></use>
                </svg>
                <div class="name">阴</div>
                <div class="code-name">#iconyin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconzhenyu"></use>
                </svg>
                <div class="name">阵雨2</div>
                <div class="code-name">#iconzhenyu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconzhongdumai"></use>
                </svg>
                <div class="name">中度霾</div>
                <div class="code-name">#iconzhongdumai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconzhongyu"></use>
                </svg>
                <div class="name">中雨</div>
                <div class="code-name">#iconzhongyu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconzhongdumai1"></use>
                </svg>
                <div class="name">重度霾</div>
                <div class="code-name">#iconzhongdumai1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#iconzhongxue"></use>
                </svg>
                <div class="name">中雪2</div>
                <div class="code-name">#iconzhongxue</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
