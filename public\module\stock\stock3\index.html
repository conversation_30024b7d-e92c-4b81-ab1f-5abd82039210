<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
    <link rel="stylesheet" href="40e93b11cbd4174c60b22cba0d630300.css" />
    <style>
      ul {
        margin: 0;
        padding: 0;
      }

      ul li {
        list-style: none;
      }

      body,
      html {
        height: 100%;
        margin: 0;
        user-select: none;
      }

      .wrap {
        width: 100%;
        height: 100%;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
      }

      .content-wrap {
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        padding: 4vw;
      }

      .stock {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        font-size: 7vw;
      }

      .stock .top,
      .stock .bottom {
        display: flex;
        justify-content: space-between;
      }

      .stock .top {
        margin-bottom: 10vh;
      }

      .stock .name {
        margin-right: 2vw;
        color: rgb(0, 0, 0);
        font-weight: bold;
        font-size: 10vw;
        margin-bottom: 1vh;
        flex: 1;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }

      .stock .key {
        color: rgb(165, 155, 153);
      }

      .p {
        font-size: 10vw;
      }

      .empty-wrap {
        width: 100vw;
        height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 8vmin;
        color: #409eff;
      }

      .green {
        color: rgb(35, 145, 111);
      }

      [v-cloak] {
        display: none;
      }

      .stock {
        flex: 1;
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
      }

      .first-stock {
        height: 50%;
        width: 95%;
      }

      .second-stock {
        height: 50%;
        width: 95%;
      }

      .current {
        height: 10vw;
        display: flex;
        align-items: center;
        justify-content: center;
        color:#ffbf00;
        text-shadow:  #ffbf00 1px 1px 6px;
        /* 浏览器暂不支持显示 */
        /* background: linear-gradient(to right, #ff0000, #ffff00); */
        /*设置渐变的方向从左到右 颜色从ff0000到ffff00*/
        /* -webkit-background-clip: text; */
        /*将设置的背景颜色限制在文字中*/
        /* -webkit-text-fill-color: transparent; */
        /*给文字设置成透明*/
      }

      .current .p {
        font-size: 9vw;
      }

      .current .rate {
        display: flex;
      }

      .current .ud {
        margin: 0 3vw;
        font-size: 9vw;
      }

      .current .pc {
        font-size: 9vw;
      }

      .current .right {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }
    </style>
    <!-- vue -->
    <script src="174365807c06490c848d7b1d45fdc348.js"></script>
    <!-- jquery.js -->
    <script src="13c0a5055cca7b2463b2f73701960b9e.js"></script>
    <!-- mqtt -->
    <script src="d4bfcfa9de50875589eaab322a015441.js"></script>
    <!-- echarts -->
    <script src="eda4ac44a8b468c6867659a705c15707.js"></script>
    <script src="page.js"></script>
  </head>

  <body>
      <div id="app" class="wrap" v-cloak :style="style">
        <div class="current">
          <!-- 当前价格（元） -->
          <div class="p">{{listInfo.p || '-'}}元</div>
          <div class="rate">
            <!-- 当前涨跌额（元） -->
            <div class="ud"> {{listInfo.ud}} </div>
            <!-- 涨跌幅（%） -->
            <div class="pc">{{listInfo.pc+'%'}}</div>
          </div>
        </div>
        <div class="stock">
          <div id="first-stock" class="first-stock"> </div>
          <div id="second-stock" class="second-stock"></div>
        </div>
      </div>
      <!-- <div v-else class="empty-wrap">当前无股票信息</div> -->
  </body>

  <script>
    window.$page = page;
    const STYLE = ['backgroundColor'];
    const PROP = ['key'];
    const TYPE = 'REAL_TIME_SPECIFIED_STOCK';
    const app = new Vue({
      el: '#app',
      data: {
        //k线图数据
        candlestickBrushData: [
          ['2004-01-02', 10452.74, 10409.85, 10367.41, 10554.96, 168890000],
          ['2004-01-05', 10411.85, 10544.07, 10411.85, 10575.92, 221290000],
          ['2004-01-06', 10543.85, 10538.66, 10454.37, 10584.07, 191460000],
        ],
        //k线图处理后的数据
        processedData: {},
        //分时图数据
        timeChartData: [
          { volume: 1231.0, datetime: '2024-04-02 11:00', price: 14.28, increase: 0.0, ratio: 0.0 },
          { volume: 1208.0, datetime: '2024-04-02 11:05', price: 14.3, increase: -0.14, ratio: -0.02 },
          { volume: 1570.0, datetime: '2024-04-02 11:10', price: 14.26, increase: -0.07, ratio: -0.01 },
        ],
        //分时图处理后的数据
        xData: [],
        hourData: [],
        listInfo: {
          fm: '-0.06',
          h: '16.53',
          hs: '0.58',
          lb: '1.18',
          l: '16.11',
          lt: '10855932471.00',
          ltText: '108.56亿',
          o: '16.20',
          pe: '45.35',
          pc: '0.98',
          p: '16.43',
          sz: '20403724311.00',
          szText: '204.04亿',
          cje: '62366319.00',
          cjeText: '6236.63万',
          ud: '0.16',
          v: '38143',
          vText: '3.81万',
          yc: '16.27',
          zf: '2.58',
          zs: '0.00',
          sjl: '3.91',
          zdf60: '-2.78',
          zdfnc: '-3.07',
          t: '2023-05-19 11:22:06',
          name: '三六一',
          market: 'SH',
        },
        key: '601352',
        props: page.props,
        mqttClient: null,
        mqttTopic: '',
        mqtt: {},
        firstChart: null,
        secondChart: null,
      },
      computed: {
        style() {
          let style = {};
          for (let item of this.props) {
            if (STYLE.includes(item.field)) {
              style[item.field] = item.value;
            }
          }
          return style;
        },
      },
      created() {
        this.log('created');

        if (this.isWindows()) {
          return;
        }
        this.listInfo = {};
        this.candlestickBrushData = [];
        this.timeChartData = [];
        if (window.DSS20AndroidJS && window.DSS20AndroidJS.getWebMqttWs) {
          var data = window.DSS20AndroidJS.getWebMqttWs();
          this.log('mqtt地址：' + data);
          try {
            var info = JSON.parse(JSON.parse(data));
            this.getMqttServerAndConnect(info);
          } catch (err) {
            this.log('mqtt地址解析失败');
          }
        } else {
          this.log('获取mqtt地址失败');
        }
      },
      mounted() {
        window['update'] = (val, mqtt = null) => {
          let styles = [],
            props = [];
          for (let i = 0; i < val.length; i++) {
            let item = val[i];
            if (STYLE.includes(item.field)) {
              styles.push(item);
            } else if (PROP.includes(item.field)) {
              props.push(item);
              let index = this.getPropIndex(item.field);
              this.props[index].value = item.value;
            }
          }

          styles.length && this.updateStyles(styles);
          if (mqtt) {
            this.getMqttServerAndConnect(mqtt);
          } else {
            this.updateProps();
          }
        };

        window['setMqttParam'] = (param) => {
          this.getMqttServerAndConnect(param);
        };

        window['updateMqtt'] = (param) => {
          this.updateMqtt(param);
        };
        this.firstChart = echarts.init(document.getElementById('first-stock'));
        this.secondChart = echarts.init(document.getElementById('second-stock'));

        this.drawK();
        this.drawTime();
        window.addEventListener('resize', this.resize);
      },

      beforeDestory() {
        this.mqttClose();
        window.removeEventListener('resize', this.resize);
        this.firstChart && this.firstChart.dispose();
        this.secondChart && this.secondChart.dispose();
      },
      methods: {
        resize() {
          this.firstChart.resize();
          this.secondChart.resize();
        },
        /*
         * 终端打印
         */
        log(msg) {
          console.log(msg);
          window.DSS20AndroidJS && window.DSS20AndroidJS.htmlLogcat('股票节目：' + msg);
        },
        /*
         * 判断当前环境是否是pc
         */
        isWindows() {
          return window.DSS20AndroidJS === undefined;
        },
        getMac() {
          if (this.isWindows()) {
            return ''
          }
          var data = window.DSS20AndroidJS.terminalInfo();
          var info = JSON.parse(data);
          return info.mac;
        },
        updateMqtt(mqtt) {
          if (!mqtt) {
            return;
          }
          mqtt = JSON.parse(mqtt);

          if ((this.mqttClient && this.mqtt.ws !== mqtt.ws) || (!this.mqttClient && !Object.keys(this.mqtt).length)) {
            this.log('mqtt重连');
            this.mqttClose();
            this.mqtt = mqtt;
            this.getMqttServerAndConnect(mqtt);
          }
        },
        updateProps() {
          for (let prop of this.props) {
            if (prop.field === 'key' && prop.value && prop.value.length === 6) {
              if (this.mqttClient) {
                this.mqttPublish(prop.value);
              }
            }
          }
        },
        updateStyles(styles) {
          for (let style of styles) {
            let index = this.getPropIndex(style.field);
            if (index !== -1) {
              let data = this.props[index];
              data.value = style.value;
              this.$set(this.props, index, data);
            }
          }
        },
        getPropIndex(name) {
          for (let i = 0; i < this.props.length; i++) {
            if (this.props[i].field === name) {
              return i;
            }
          }
          return -1;
        },
        /**
         * 格式化价格
         */
        numberFormat(value) {
          if (isNaN(value)) {
            return '-';
          }
          let param = {};
          let k = 10000;
          let sizes = ['', '万', '亿', '万亿'];
          let i;
          if (value < k) {
            param.value = value;
            param.unit = '';
          } else {
            i = Math.floor(Math.log(value) / Math.log(k));
            param.value = (value / Math.pow(k, i)).toFixed(2);
            param.unit = sizes[i];
          }

          return param.value + param.unit;
        },
        /**
         * 随机ID
         * @param {*} len
         * @param {*} radix
         * @returns
         */
        randomId(len, radix) {
          var chars =
            '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('')
          var uuid = [],
            i
          radix = radix || chars.length
          if (len) {
            // Compact form
            for (i = 0; i < len; i++) uuid[i] = chars[0 | (Math.random() * radix)]
          } else {
            // rfc4122, version 4 form
            var r
            // rfc4122 requires these characters
            uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-'
            uuid[14] = '4'
            // Fill in random data.  At i==19 set the high bits of clock sequence as
            // per rfc4122, sec. 4.1.5
            for (i = 0; i < 36; i++) {
              if (!uuid[i]) {
                r = 0 | (Math.random() * 16)
                uuid[i] = chars[i == 19 ? (r & 0x3) | 0x8 : r]
              }
            }
          }
          return uuid.join('')
        },
        /**
         * 连接到MQTT服务器并订阅
         */
        getMqttServerAndConnect(param) {
          this.log('mqtt参数：' + JSON.stringify(param));

          const that = this;
          let { ws, username, password } = param;
          let mac = this.getMac() || ''
          // 客户端ID
          let clientId = `js_stock3_${mac}_${this.randomId(16, 16)}`;
          that.mqttClient = mqtt.connect(ws, {
            clientId,
            username,
            password,
            clean: true,
            connectTimeout: 5 * 1000,
            keepalive: 30
          });
          that.mqttClient.on('connect', () => {
            this.log('mqtt连接成功');

            this.updateProps();
          });
          that.mqttClient.on('error', (error) => {
            this.log('mqtt连接失败:' + error);
            this.listInfo = {};
            this.candlestickBrushData = [];
            this.timeChartData = [];
          });
          //监听接收消息事件
          that.mqttClient.on('message', (topic, message) => {
            try {
              this.log('获取mqtt消息:' + topic + message);
              message = JSON.parse(message.toString());
              if (topic.indexOf('REAL_TIME_SPECIFIED_STOCK') !== -1) {
                this.key = message.key;
                // 格式化成交量、成交额、流通市值、总市值
                message.value.cjeText = this.numberFormat(message.value.cje);
                message.value.vText = this.numberFormat(message.value.v);
                message.value.ltText = this.numberFormat(message.value.lt);
                message.value.szText = this.numberFormat(message.value.sz);
                message.value.market = message.value.market.toUpperCase();
                this.listInfo = message.value;
              } else if (topic.indexOf('CURRENT_SPECIFIED_STOCK') !== -1) {
                this.key = message.key;
                this.timeChartData = message.value;
                this.drawTime();
              } else if (topic.indexOf('HISTORY_SPECIFIED_STOCK') !== -1) {
                this.key = message.key;
                this.candlestickBrushData = message.value;
                this.drawK();
              }
            } catch (err) {
              this.log('mqtt消息解析失败');
            }
          });
        },

        /**
         * 发布MQTT主题
         */
        mqttPublish(key) {
          let topic = `dss2/server/datum/watch`;
          let message = `{"type":"${TYPE}","key":"${key}"}`;
          this.log('mqtt发布主题:' + message);

          this.mqttClient.publish(topic, message, { qos: 1, retain: true }, (err, res) => {
            if (err) {
              this.log('mqtt发布主题失败', err);
              return;
            }
          });
          message = `{"type":"HISTORY_SPECIFIED_STOCK","key":"${key}"}`;
          this.log('mqtt发布主题:' + message);
          this.mqttClient.publish(topic, message, { qos: 1, retain: true }, (err, res) => {
            if (err) {
              this.log('mqtt发布主题失败', err);
              return;
            }
          });
          message = `{"type":"CURRENT_SPECIFIED_STOCK","key":"${key}"}`;
          this.log('mqtt发布主题:' + message);
          this.mqttClient.publish(topic, message, { qos: 1, retain: true }, (err, res) => {
            if (err) {
              this.log('mqtt发布主题失败', err);
              return;
            }
            this.mqttRefreshSubscribe(key);
          });
        },
        /**
         * 订阅MQTT主题
         */
        mqttRefreshSubscribe(key) {
          // 取消之前的订阅
          if (this.mqttTopic) {
            this.mqttClient.unsubscribe(this.mqttTopic);
          }
          this.mqttTopic = `dss2/server/datum/notify/+/${key}`;
          this.log('mqtt订阅主题:' + this.mqttTopic);

          this.mqttClient.subscribe(this.mqttTopic, { qos: 1 }, function (err, res) {
            if (err) {
              this.log('mqtt订阅主题失败:', err);
              return;
            }
          });
        },
        /**
         * 释放MQTT客户端
         */
        mqttClose() {
          if (this.mqttClient) {
            this.mqttClient.unsubscribe(this.mqttTopic);
            this.mqttClient.end();
          }
        },
        calculateMA(dayCount, data) {
          var result = [];
          for (var i = 0, len = data.values.length; i < len; i++) {
            if (i < dayCount) {
              result.push('-');
              continue;
            }
            var sum = 0;
            for (var j = 0; j < dayCount; j++) {
              sum += data.values[i - j][1];
            }
            result.push(+(sum / dayCount).toFixed(3));
          }
          return result;
        },
        //每次数据更新后调用即可
        drawTime() {
          if (!this.timeChartData) {
            return;
          }
          this.splitData(this.timeChartData);
          let option = {
            grid: {
              top: 70,
              bottom: 10,
              left: 10,
              right: 10,
              containLabel: true,
            },
            xAxis: [
              {
                type: 'category',
                data: this.xData,
                axisLine: {
                  show: false,
                },
                axisTick: {
                  show: false,
                },
                axisLabel: {
                  fontSize: '8px',
                  color: 'rgba(255,255,255,0.8)',
                  showMinLabel: true, // 强制显示最小值
                  showMaxLabel: true, // 强制显示最大值
                },
              },
            ],
            yAxis: [
              {
                type: 'value',
                scale:true,
                splitArea: {
                  show: false,
                },
                splitLine: {
                  show: false,
                },
                axisLabel: {
                  show: false,
                },
              },
            ],
            series: [
              {
                type: 'line',
                data: this.hourData,
                symbol: 'none',
                lineStyle: {
                  width: 1,
                },
                symbol: 'emptyCircle',
                symbolSize: 6,
                areaStyle: {
                  color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [
                      {
                        offset: 0,
                        color: 'rgba(1, 141, 227, 0.84)', // 0% 处的颜色
                      },
                      {
                        offset: 0.5,
                        color: 'rgba(102, 200, 235, 0.41)', 
                      },
                      {
                        offset: 1,
                        color: 'rgba(102, 200, 235, 0)', // 100% 处的颜色
                      },
                    ],
                    global: false,
                  },
                },
              },
            ],
          };
          this.secondChart.setOption(option, true);
        },
        splitData(data) {
          //只取最后一天的数据
          let lastDate = data[data.length - 1].datetime.split(' ')[0];
          let lastDayData = data.filter((d) => d.datetime.startsWith(lastDate));
          this.xData = lastDayData.map((d) => d.datetime.split(' ')[1]);
          this.hourData = lastDayData.map((d) => d.price);
        },
        //每次数据更新后调用即可
        drawK() {
          if (!this.candlestickBrushData) {
            return;
          }
          this.processedData = this.processData(this.candlestickBrushData);

          let option = {
            animation: true,
            legend: {
              show: false,
            },
            visualMap: {
              //控制成交量的柱状图与上面蜡烛图的颜色一致
              show: false,
              seriesIndex: 2,
              pieces: [
                {
                  value: 1,
                  color: '#00da3c',
                },
                {
                  value: -1,
                  color: '#ec0000',
                },
              ],
            },
            grid: [
              {
                top: '20%',
                left: '5%',
                right: '5%',
                height: '50%',
              },
              {
                left: '5%',
                right: '5%',
                bottom: '0%',
                height: '20%',
              },
            ],
            xAxis: [
              {
                type: 'category',
                data: this.processedData.categoryData,
                boundaryGap: false,
                axisLine: { onZero: false, show: false },
                axisTick: { show: false },
                splitLine: { show: false },
                min: 'dataMin',
                max: 'dataMax',
                axisPointer: {
                  z: 100,
                },
                splitLine: {
                  show: false,
                },
                axisLabel: {
                  show: true,
                  color: 'rgba(255,255,255,0.8)',
                  formatter: function (value, index) {
                    return value.slice(5).replace('-', '/');
                  },
                  fontSize: '8px',
                  showMinLabel: true, // 强制显示最小值
                  showMaxLabel: true, // 强制显示最大值
                },
              },
              {
                type: 'category',
                gridIndex: 1,
                data: this.processedData.categoryData,
                boundaryGap: false,
                axisLine: { onZero: false, show: false },
                axisTick: { show: false },
                splitLine: { show: false },
                axisLabel: { show: false },
                min: 'dataMin',
                max: 'dataMax',
                splitLine: {
                  show: false,
                },
              },
            ],
            yAxis: [
              {
                show: false,
                scale: true,
                splitArea: {
                  show: false,
                },
                splitLine: {
                  show: false,
                },
              },
              {
                scale: true,
                gridIndex: 1,
                splitNumber: 2,
                axisLabel: { show: false },
                axisLine: { show: false },
                axisTick: { show: false },
                splitLine: { show: false },
              },
            ],
            series: [
              {
                name: '',
                type: 'candlestick',
                data: this.processedData.values,
                barWidth: 5,
                itemStyle: {
                  color: '#ec0000',
                  color0: '#00da3c',
                  borderColor: undefined,
                  borderColor0: undefined,
                },
              },
              {
                name: 'MA5',
                type: 'line',
                data: this.calculateMA(5, this.processedData),
                smooth: true,
                lineStyle: {
                  opacity: 0.5,
                },
              },
              {
                name: 'Volume',
                type: 'bar',
                xAxisIndex: 1,
                yAxisIndex: 1,
                barWidth: 3,
                data: this.processedData.volumes,
              },
            ],
          };
          this.firstChart.setOption(option, true);
        },
        //处理k线图数据
        processData(data) {
          let categoryData = [];
          let values = [];
          let volumes = [];
          for (let i = 0; i < data.length; i++) {
            categoryData.push(data[i].splice(0, 1)[0]);
            values.push(data[i]);
            volumes.push([i, data[i][4], data[i][0] < data[i][1] ? -1 : 1]);
          }
          return {
            categoryData: categoryData,
            values: values,
            volumes: volumes,
          };
        },
      },
    });
  </script>
</html>
