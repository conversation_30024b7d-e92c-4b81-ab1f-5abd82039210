<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Document</title>
  <link rel="stylesheet" href="40e93b11cbd4174c60b22cba0d630300.css" />
  <style>
    ul {
      margin: 0;
      padding: 0;
    }

    ul li {
      list-style: none;
    }

    body,
    html {
      height: 100%;
      margin: 0;
    }

    .wrap {
      width: 100%;
      height: 100%;
      position: relative;
      display: flex;
      flex-direction: column;
      color: white;
    }

    .wrap>img {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
      z-index: -1
    }

    .header-wrap {
      width: 100%;
      padding: 5vh 3.6vw 3vh;
      font-weight: bold;
      box-sizing: border-box;
    }

    .header-wrap .current-date{
      font-size: 2vw;
      font-family: SimSun, 宋体, serif;
    }

    .header-wrap .current-time{
      font-size: 3.6vw;
      font-family: "Microsoft YaHei";
      margin-top: 2vh;
    }

    .content-wrap {
      box-sizing: border-box;
      width: 100%;
      flex:1;
      padding: 0 8vw 0vh 8vw;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }

    .content-wrap .title-wrap {
      display: grid;
      grid-template-columns: 22% 56% 22%;
      font-size: 2.9vw;
      font-weight: bold;
      padding: 4vh 0;
      border-bottom: 0.4vh solid rgb(131,185,246);
    }

    .content-wrap .title-wrap div {
      text-align: center;
    }

    .content {
      margin-top: 2vh;
    }

    .content li {
      display: grid;
      grid-template-columns: 22% 56% 22%;
      font-size: 2.4vw;
      line-height: 2.5;
      box-sizing: border-box;
    }

    .content li+li {}

    .content li div {
      text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .content li .time{
      font-family: 宋体, serif;
      font-weight: bold;
    }

    [v-cloak] {
      display: none;
    }
  </style>
  <!-- vue -->
  <script src="174365807c06490c848d7b1d45fdc348.js"></script>
  <!-- jquery.js -->
  <script src="13c0a5055cca7b2463b2f73701960b9e.js"></script>
  <!-- mqtt -->
  <script src="d4bfcfa9de50875589eaab322a015441.js"></script>
  <script src="page.js"></script>
</head>

<body>
  <div id="app" class="wrap" v-cloak>
    <div :style="style" class="wrap">
      <img src="./fd4f437fe153eb5e673a4f8f55af4c3e.jpg" alt="" v-if="style.bShowBg" />
      <div class="header-wrap">
        <div class="time-wrap">
          <div class="current-date">{{currentDate}}</div>
          <div class="current-time">{{currentTime}}</div>
        </div>
      </div>
      <div class="content-wrap">
        <div class="title-wrap">
          <div class="title">会议时间</div>
          <div class="title">会议议题</div>
          <div class="title">会议室</div>
        </div>
        <div class="content" id="scrollWrap">
          <ul>
            <li v-for="item in currentPageConfs" :key="item.confId">
              <div class="time">{{item.period}}</div>
              <div class="subject">{{item.subject}}</div>
              <div>{{item.roomName}}</div>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</body>

<script>
  window.$page = page;
  const cssProperty = ["height", "width", "fontSize"];
  const STYLE = ["color", "backgroundColor"];
  const PROP = ["conferenceRoom"];
  const topicPrefix = "dss2/web/conference/room/";
  const DATES = ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十', '十一', '十二', '十三', '十四', '十五', '十六', '十七', '十八', '十九', '二十', '二十一', '二十二', '二十三', '二十四', '二十五', '二十六', '二十七', '二十八', '二十九', '三十', '三十一'];
  const WEEKS = ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"];

  const ItemsPerPage = 4
  // 10秒
  const AutoPlayInterval = 10 * 1000

  const app = new Vue({
    el: "#app",
    data: {
      props: page.props,
      mqttClient: null,
      timer: null,
      roomIdList: new Set(),
      conferenceList: [],
      mqtt: {},
      currentDate: '',
      currentTime: '',
      currentTimer: null,
      currentPage: 1,
      switchPageTimer:null
    },
    computed: {
      style() {
        let style = {};
        for (let prop of this.props) {
          if (cssProperty.includes(prop.field)) {
            style[prop.field] = prop.value + "%"
          } else {
            style[prop.field] = prop.value
          }
        }
        return style;
      },
      confList(){
        let list = []
        this.conferenceList.forEach(room=>{
          room.data.forEach(conf=>{
            conf.roomId = room.roomId
            conf.roomName = room.roomName
          })
          list = list.concat(room.data)
        })
        return list
      },
      currentPageConfs() {
        const start = (this.currentPage - 1) * ItemsPerPage
        const end = start + ItemsPerPage
        return this.confList.slice(start, end)
      },
      totalPages() {
        return Math.ceil(this.confList.length / ItemsPerPage)
      },
    },
    created() {
      this.updateCurrentTime()

      for (let i = 1; i <= 5; i++) {
        this.roomIdList.add(-i);
        this.conferenceList.push({
          roomId: -i,
          roomName: "会议室" + i,
          data: [
            {
              id: -i,
              subject: "研究会议" + i,
              period: "10:00-12:00",
              dept: "机构" + i
            },
            // {
            //   id: -i,
            //   subject: "研究会议" + i,
            //   time: "14:00-16:00",
            //   dept: "机构" + i
            // },
            // {
            //   id: -i,
            //   subject: "研究会议" + i,
            //   time: "14:00-16:00",
            //   dept: "机构" + i
            // }
          ]
        });
      }
      // 调试
      if (this.isWindows()) {
        // this.roomIdList.clear();
        // this.conferenceList = [];
        // let ws = "ws://192.168.91.86:8080/mqtt";
        // let username = "admin";
        // let password = "nst@aliyun";
        // this.getMqttServerAndConnect({ ws, username, password });
        return;
      }

      this.roomIdList.clear();
      this.conferenceList = [];
      var data = window.DSS20AndroidJS.getWebMqttWs();
      var info = JSON.parse(JSON.parse(data));
      this.getMqttServerAndConnect(info);
    },
    mounted() {
      window["update"] = (val, mqtt = null) => {
        this.updateProps(val);
      };

      window["setMqttParam"] = param => {
        this.getMqttServerAndConnect(param);
      };

      window["updateMqtt"] = param => {
        this.updateMqtt(param);
      };

      this.startAutoPlay()
    },

    beforeDestory() {
      this.timer && clearInterval(this.timer);
      this.currentTimer && clearInterval(this.currentTimer);
      this.mqttClose();
      this.stopAutoPlay()
    },
    watch: {
      'style.autoPlayInterval'() {
        this.stopAutoPlay()
        this.startAutoPlay()
      }
    },
    methods: {
      startAutoPlay() {
        this.switchPageTimer = setInterval(() => {
          this.nextPage()
        }, this.style.autoPlayInterval * 1000 || AutoPlayInterval)
      },
      stopAutoPlay() {
        if (this.switchPageTimer) {
          clearInterval(this.switchPageTimer)
          this.switchPageTimer = null
        }
      },
      nextPage() {
        this.currentPage = this.currentPage >= this.totalPages ? 1 : this.currentPage + 1
      },
      updateCurrentTime() {
        this.initCurrentTime()
        this.currentTimer = setInterval(this.initCurrentTime, 60 * 1000)
      },
      initCurrentTime() {
        const date = new Date();
        const monthIndex = date.getMonth()+1;
        const dateIndex = date.getDate();
        const weekIndex = date.getDay();
        const hours = ('0' + date.getHours()).slice(-2);
        const minutes = ('0' + date.getMinutes()).slice(-2);
        this.currentTime = hours + ':' + minutes
        this.currentDate = monthIndex + '月' + dateIndex + '日' + ' '+ WEEKS[weekIndex]
      },
      /*
       * 终端打印
       */
      log(msg) {
        if (this.isWindows()) {
          console.log(msg)
          return;
        }
        window.DSS20AndroidJS &&
          window.DSS20AndroidJS.htmlLogcat("汇总屏：" + msg);
      },
      updateProps(props) {
        for (let prop of props) {
          let index = this.getPropIndex(prop.field);
          if (index !== -1) {
            let data = this.props[index];
            data.value = prop.value;
            this.$set(this.props, index, data);
          }
        }
      },
      getPropIndex(name) {
        for (let i = 0; i < this.props.length; i++) {
          if (this.props[i].field === name) {
            return i;
          }
        }
        return -1;
      },
      updateMqtt(mqtt) {
        if (!mqtt) {
          return;
        }
        mqtt = JSON.parse(mqtt);

        if (
          (this.mqttClient && this.mqtt.ws !== mqtt.ws) ||
          (!this.mqttClient && !Object.keys(this.mqtt).length)
        ) {
          this.log("mqtt重连");
          this.mqttClose();
          this.roomIdList.clear();
          this.conferenceList = [];
          this.mqtt = mqtt;
          this.getMqttServerAndConnect(mqtt);
        }
      },
      isWindows() {
        // var userAgent = navigator.userAgent;
        // return userAgent.indexOf("Windows") > -1;
        return window.DSS20AndroidJS === undefined;
      },
      getMac() {
        if(this.isWindows()){
          return ''
        }
        var data = window.DSS20AndroidJS.terminalInfo();
        var info = JSON.parse(data);
        return info.mac;
      },
      scroll() {
        let wrapper = $("#scrollWrap");
        let ulWrapper = $("#scrollWrap ul");
        function Marquee() {
          if (ulWrapper.height() <= wrapper.height()) {
            return;
          }
          let scrollHeight = ulWrapper.find("li").height();
          ulWrapper.stop().animate(
            {
              marginTop: -scrollHeight
            },
            1600,
            () => {
              ulWrapper
                .css({ marginTop: 0 })
                .find("li:first")
                .appendTo(ulWrapper);
            }
          );
        }
        this.timer && clearInterval(this.timer);
        this.timer = setInterval(Marquee, 2000);
      },
      conferenceRoomSubscribe() {
        for (let prop of this.props) {
          if (prop.field === "conferenceRoom") {
            if (!prop.value.length && this.isWindows()) {
              return;
            }

            //删除之前的订阅列表及会议信息
            this.roomIdList.forEach(id => {
              let topic = this.getTopic(id);
              this.mqttClient.unsubscribe(topic);
            });
            this.conferenceList = [];
            this.roomIdList.clear();

            //订阅当前的会议室
            this.roomIdList = new Set(
              prop.value.map(item => {
                this.mqttRefreshSubscribe(item.roomId);
                return item.roomId;
              })
            );
          }
        }
      },
      formateData(dstList, roomId, roomName) {
        let index = this.conferenceList.findIndex(
          item => item.roomId === roomId
        );
        if (index !== -1) {
          this.conferenceList[index].data = dstList;
        } else {
          this.conferenceList.push({ data: dstList, roomId, roomName });
        }
      },
      deleteRoomById(roomId) {
        let srcList = JSON.parse(JSON.stringify(this.conferenceList));
        return srcList.filter(item => item.roomId !== roomId);
      },
      getPeriod(startTimeStamp, endTimeStamp, periodArr) {
        if (periodArr[1].includes('永久')) {
          return '永久会议'
        }
        let startTimeArr = periodArr[0].split(' ')
        let endTimeArr = periodArr[1].split(' ')
        let curEndTime = new Date(new Date(startTimeStamp).setHours(23, 59, 59, 999))
          .getTime()
        // 跨天会议,超过一天显示+1
        if (endTimeStamp > curEndTime) {
          endTimeArr[1] += '+1'
        }
        return startTimeArr[1] + '-' + endTimeArr[1]
      },
      /**
       * 随机ID
       * @param {*} len
       * @param {*} radix
       * @returns
       */
      randomId(len, radix) {
        var chars =
          '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('')
        var uuid = [],
          i
        radix = radix || chars.length
        if (len) {
          // Compact form
          for (i = 0; i < len; i++) uuid[i] = chars[0 | (Math.random() * radix)]
        } else {
          // rfc4122, version 4 form
          var r
          // rfc4122 requires these characters
          uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-'
          uuid[14] = '4'
          // Fill in random data.  At i==19 set the high bits of clock sequence as
          // per rfc4122, sec. 4.1.5
          for (i = 0; i < 36; i++) {
            if (!uuid[i]) {
              r = 0 | (Math.random() * 16)
              uuid[i] = chars[i == 19 ? (r & 0x3) | 0x8 : r]
            }
          }
        }
        return uuid.join('')
      },
      /**
       * 连接到MQTT服务器并订阅
       */
      getMqttServerAndConnect(param) {
        this.log("mqtt参数：" + JSON.stringify(param));

        const that = this;
        let { ws, username, password } = param;
        let mac = this.getMac() || ''
        // 客户端ID
        let clientId = `js_summaryScreen3_${mac}_${this.randomId(16, 16)}`;
        that.mqttClient = mqtt.connect(ws, {
          clientId,
          username,
          password,
          clean: true,
          connectTimeout: 5 * 1000,
          keepalive: 30
        });
        that.mqttClient.on("connect", () => {
          this.log("mqtt连接成功");
          this.conferenceRoomSubscribe();
        });
        that.mqttClient.on("error", error => {
          this.log("mqtt连接失败:" + error);
        });
        //监听接收消息事件
        that.mqttClient.on("message", (topic, message) => {
          message = JSON.parse(message.toString());
          this.log("获取mqtt消息：" + JSON.stringify(message));
          const { meetingroom, confDetailDTOList } = message;

          // 会议室被删除后，返回的roomName为空，删除该会议室的所有会议
          if (!meetingroom.roomName) {
            this.log("会议室被删除");
            this.conferenceList = this.deleteRoomById(meetingroom.id);
            return;
          }

          const { id, roomName } = meetingroom;
          let res = [];
          if (confDetailDTOList.length) {
            res = confDetailDTOList.filter(item => {
              if (item.approved !== 4) {
                return false;
              }

              let periodArr = item.period.split("-");
              item.period = this.getPeriod(item.start, item.end, periodArr)

              // 已经结束的会议不显示
              if (item.end < +new Date()) {
                return false
              }

              return true;
            });
          }

          // if (!res.length) {
          //   res = [
          //     {
          //       subject: "",
          //       period: "",
          //       confId: Math.random(),
          //       roomId: id
          //     }
          //   ];
          // }
          this.log("格式化后数据：" + JSON.stringify(res));
          this.formateData(res, id, roomName);
        });
      },

      /**
       * 发布MQTT主题
       */
      mqttPublish(id) {
        let topic = `dss2/web/conference/room/${id}`;
        let message = `{"command":"WEATHER","parameters":{${id}:"${id}"}}`;
        this.log("mqtt发布主题:" + message);
        this.mqttClient.publish(
          topic,
          message,
          { qos: 1, retain: true },
          (err, res) => {
            if (err) {
              this.log("mqtt发布主题失败", err);
              return;
            }
            this.mqttRefreshSubscribe(id);
          }
        );
      },
      /**
       * 订阅MQTT主题
       */
      mqttRefreshSubscribe(id) {
        const that = this;
        let topic = topicPrefix + id;
        this.log("mqtt订阅主题:" + topic);
        that.mqttClient.subscribe(topic, { qos: 1 }, (err, res) => {
          if (err) {
            console.log(err)
            this.log("mqtt订阅主题失败:", err);
            return;
          }
        });
      },
      /**
       * 释放MQTT客户端
       */
      mqttClose() {
        this.mqtt = {};
        if (this.mqttClient) {
          this.roomIdList.forEach(id => {
            let topic = this.getTopic(id);
            this.mqttClient.unsubscribe(topic);
          });
          this.mqttClient.end();
        }
      },
      getTopic(id) {
        return topicPrefix + id;
      }
    }
  });
</script>

</html>