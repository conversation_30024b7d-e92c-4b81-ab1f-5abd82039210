import request from "@/utils/request";

//获取服务端域名列表
export function getServerDns() {
  return request({
    url: "/sysManagement/domainServer",
    method: "get"
  });
}

//更新服务端域名列表
export function updateServerDns(data) {
  return request({
    url: "/sysManagement/domainServer",
    method: "put",
    headers: {
      "Content-Type": "application/json;"
    },
    data
  });
}

//获取终端域名列表
export function getTerminalDns() {
  return request({
    url: "/sysManagement/domainTerminal",
    method: "get"
  });
}

//新增终端域名
export function addTerminalDns(data) {
  return request({
    url: "/sysManagement/domainTerminal",
    method: "post",
    data,
    headers: {
      "Content-Type": "application/json;"
    }
  });
}

//删除终端域名
export function deleteTerminalDns(id) {
  return request({
    url: "/sysManagement/domainTerminal/" + id,
    method: "delete"
  });
}

//更新终端域名
export function updateTerminalDns(data) {
  return request({
    url: "/sysManagement/domainTerminal",
    method: "put",
    headers: {
      "Content-Type": "application/json;"
    },
    data
  });
}

//将终端域名设置为默认
export function setDefaultTerminalDns(id) {
  return request({
    url: "/sysManagement/domainTerminal/default/" + id,
    method: "get"
  });
}

//启动终端域名设置
export function enableTerminalDns() {
  return request({
    url: "/sysManagement/domainTerminal/reset/enable",
    method: "get"
  });
}

//关闭终端域名设置
export function disableTerminalDns() {
  return request({
    url: "/sysManagement/domainTerminal/reset/disable",
    method: "get"
  });
}

//启动服务端域名设置
export function enableServerDns() {
  return request({
    url: "/sysManagement/domainServer/reset/enable",
    method: "get"
  });
}

//关闭服务端域名设置
export function disableServerDns() {
  return request({
    url: "/sysManagement/domainServer/reset/disable",
    method: "get"
  });
}

//获取服务端/终端默认ip列表
export function getDefaultServer() {
  return request({
    url: "/sysManagement/domainServer/ip",
    method: "get"
  });
}

//上传升级包并升级
export function uploadUpgradePatch(data, onUploadProgress) {
  let file = new FormData();
  file.append('file', data)

  return request({
    url: "/sys/upgrade",
    method: "post",
    data: file,
    onUploadProgress,
    headers: {
      "Content-Type": "multipart/form-data"
    },
  });
}

/**
 * 查询第三方账号列表
 * @param {*} data-page
 * @param {*} data-size
 * @returns
 */
export function getThirdPartyAccountList(data) {
  return request({
    url: "/oauth/thirdparty/account",
    method: "get",
    params: data
  });
}

/**
 * 创建第三方账号
 * @param {*} data-appName    应用名称
 * @param {*} data-appId      应用ID
 * @param {*} data-appSecret  应用Secret
 * @param {*} data-enable     是否启用
 * @returns
 */
export function saveThirdPartyAccount(data) {
  return request({
    url: "/oauth/thirdparty/account",
    method: "post",
    data,
    headers: {
      "Content-Type": "application/json;"
    }
  });
}

/**
 * 删除第三方账号
 * @param {*} ids 账号id
 * @returns
 */
export function deleteThirdPartyAccount(ids) {
  return request({
    url: "/oauth/thirdparty/account",
    method: "delete",
    data: ids,
    headers: {
      "Content-Type": "application/json;"
    }
  });
}

/**
 * 更新第三方账号
 * @param {*} data-id
 * @param {*} data-appName    应用名称
 * @param {*} data-appId      应用ID
 * @param {*} data-appSecret  应用Secret
 * @param {*} data-enable     是否启用
 * @returns
 */
export function updateThirdPartyAccount(data) {
  return request({
    url: "/oauth/thirdparty/account",
    method: "put",
    headers: {
      "Content-Type": "application/json;"
    },
    data
  });
}

/**
 * 第三方账号登录
 * @param {*} data-appId      应用ID
 * @param {*} data-appSecret  应用Secret
 * @returns
 */
export function loginThirdParty(data) {
  return request({
    url: "/oauth/thirdparty/login",
    method: "post",
    headers: {
      "Content-Type": "application/json;"
    },
    data
  });
}

/**
 * 查询会议指引模板
 * @returns
 */
export function getConfModule() {
  return request({
    url: "/oauth/module",
    method: "get",
  });
}

/**
 * 导入会议指引模板
 * @returns
 */
export function uploadConfModule(data,onUploadProgress) {
  let file = new FormData();
  Object.keys(data).forEach((key) => {
    data[key] && file.append(key, data[key]);
  });

  return request({
    url: `/oauth/module/upload`,
    method: "post",
    data: file,
    onUploadProgress,
    headers: {
      "Content-Type": "multipart/form-data"
    },
  });
}  

/**
 * 删除会议指引模板
 * @returns
 */
export function deleteConfModule(id) {
  return request({
    url: `/oauth/module?id=${id}`,
    method: "delete",
  });
}


