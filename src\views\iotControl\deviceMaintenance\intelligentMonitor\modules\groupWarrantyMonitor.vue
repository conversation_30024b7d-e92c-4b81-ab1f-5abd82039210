<template>
  <el-dialog :title="groupName" :visible.sync="visible" width="900px" :close-on-click-modal="false" @close="close">
    <div class="life-dialog-container">
      <el-table :data="list" height="100%" @row-click="handleRowClick">
        <el-table-column type="index" label="序号" width="50" show-overflow-tooltip fixed="left"></el-table-column>
        <el-table-column prop="warrantyStatus" label="保修状态" min-width="180" :show-overflow-tooltip="true" fixed="left">
          <template slot-scope="{ row }">
            <el-tag :class="[row.warrantyStatus, 'tag']" v-if="row.warrantyStatus">{{ getWarrantyStatusType(row).label }}</el-tag>
            <span class="warranty-day" v-if="row.warrantyStatus === 'overdue'">已过保{{ row.warrantyDay }}天</span>
            <span class="warranty-day" v-else>剩余{{ row.warrantyDay }}天</span>
          </template>
        </el-table-column>
        <el-table-column prop="deviceName" label="设备名称" min-width="180" :show-overflow-tooltip="true" fixed="left"> </el-table-column>
        <el-table-column prop="commissioningDate" label="投入使用时间" min-width="160" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            {{ scope.row.commissioningDate ? new Date(scope.row.commissioningDate).Format('yyyy-MM-dd hh:mm') : '--' }}</template
          >
        </el-table-column>
        <el-table-column prop="warranty" label="保修期(年)" min-width="100" :show-overflow-tooltip="true" align="center"> </el-table-column>
        <el-table-column prop="spaceName" label="所属空间" min-width="140" :show-overflow-tooltip="true"> </el-table-column>
        <el-table-column prop="gatewayName" label="所属智慧中控" min-width="120" :show-overflow-tooltip="true"> </el-table-column>
        <el-table-column prop="typeName" label="设备类型" min-width="160" :show-overflow-tooltip="true"> </el-table-column>
        <el-table-column prop="model" label="设备型号" min-width="140" :show-overflow-tooltip="true"> </el-table-column>
        <el-table-column prop="brand" label="设备品牌" min-width="120" :show-overflow-tooltip="true"> </el-table-column>
        <el-table-column label="操作" fixed="right" width="100" align="center">
          <template slot-scope="{ row }">
            <el-button size="mini" class="edit-btn" icon="el-icon-edit" @click.stop="editDevice(row)"> 编辑 </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </el-dialog>
</template>

<script>
  import dlg from '@/mixins/dlg';
  import { WARRANTY_STATUS } from '@/views/iotControl/enum.js';
  import deviceEditDialog from './deviceEditDialog.vue';
  import { getWarrantyStatusInGroup } from '@/api/iotControl';
  import transform from '@/utils/transform';
  export default {
    mixins: [dlg],
    components: {
      deviceEditDialog,
    },
    props: {
      groupId: {
        type: String,
        default: '',
      },
      groupName: {
        type: String,
        default: '',
      },
    },
    data() {
      return {
        WARRANTY_STATUS,
        list: [],
      };
    },
    created() {
      this.getWarrantyStatusInGroup();
    },

    methods: {
      getWarrantyStatusInGroup() {
        let params = {
          groupId: this.groupId,
        };
        getWarrantyStatusInGroup(params)
          .then(({ data }) => {
            this.list = data;
          })
          .catch((e) => {});
      },
      getWarrantyStatusType(row) {
        return this.WARRANTY_STATUS.find((item) => item.value === row.warrantyStatus);
      },
      deviceEditDlg: transform(deviceEditDialog),
      editDevice(row) {
        let that = this;
        this.deviceEditDlg({
          propsData: {
            deviceObj: row,
          },
          methods: {
            refresh() {
              that.getWarrantyStatusInGroup();
            },
          },
        });
      },
      handleRowClick(row) {
        this.$emit('rowClick', row);
      },
    },
  };
</script>

<style lang="scss" scoped>
  .life-dialog-container {
    height: 380px;
  }
  .tag {
    &.fresh {
      color: #808080;
      background-color: rgba(61, 204, 166, 0.3);
      border: 1px solid #3dcca6;
    }
    &.good,
    &.under {
      color: #808080;
      background-color: rgba(135, 206, 250, 0.3);
      border: 1px solid #87cefa;
    }
    &.renew,
    &.expiring {
      color: #808080;
      background-color: rgba(250, 190, 70, 0.3);
      border: 1px solid #fabe46;
    }
    &.overdue {
      color: #808080;
      background-color: rgba(245, 108, 108, 0.3);
      border: 1px solid #f56c6c;
    }
  }
  .warranty-day {
    margin-left: 10px;
  }
</style>
