<template>
  <div id="transfer">
    <div class="wrap left">
      <div class="header">
        <slot name="titleLeft"></slot>
      </div>
      <div class="input-wrap">
        <slot name="inputWrap"></slot>
      </div>
      <el-table
        :data="leftList"
        ref="leftTable"
        @selection-change="handleLeftSelectionChange"
        @row-click="clickLeftRow"
        @sort-change="sortChange"
        :default-sort="defaultSort"
        size="mini"
        height="100%"
      >
        <el-table-column
          type="selection"
          width="45"
          :selectable="isDisabledSelected"
        ></el-table-column>
        <slot name="leftTable"></slot>
      </el-table>
      <div class="pagination">
        <slot name="pagination"></slot>
      </div>
    </div>
    <div class="btn-wrap">
      <el-button
        type="primary"
        icon="el-icon-d-arrow-right"
        @click="rightMove"
        :disabled="!leftSelection.length || (!isMulti && rightList.length >= 1)"
        size="small"
      ></el-button>
      <el-button
        type="primary"
        icon="el-icon-d-arrow-left"
        @click="leftMove"
        :disabled="!rightSelection.length"
        size="small"
      ></el-button>
    </div>
    <div class="wrap right">
      <div class="header">
        <slot name="titleRight"></slot>
      </div>
      <el-table
        :data="rightList"
        ref="rightTable"
        @selection-change="handleRightSelectionChange"
        @row-click="clickRightRow"
        size="mini"
        height="100%"
        key="rightTable"
      >
        <el-table-column type="selection" width="45"> </el-table-column>
        <slot name="rightTable"></slot>
      </el-table>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    list: {
      type: Array,
      required: true
    },
    keyword: {
      type: String,
      required: true
    },
    chooseList: {
      type: Array,
      default() {
        return [];
      }
    },
    disabedList: {
      type: Array,
      default() {
        return [];
      }
    },
    defaultSort: {
      type: Object,
      default() {
        return {};
      }
    },
    // 是否支持多选
    isMulti: {
      type: Boolean,
      default: true
    }
  },
  watch: {
    chooseList(newVal, oldVal) {
      this.rightList = [...newVal];
    },
    list(newVal) {
      this.leftList = newVal.map(item => {
        if (
          this.rightIdList.includes(item[this.keyword]) ||
          this.disableIdList.includes(item[this.keyword])
        ) {
          let newItem = { ...item, disabled: true };
          return newItem;
        }
        return item;
      });
    },
    keyword(newVal, oldVal) {
      this.rightList = [];
    },
    isMulti: {
      immediate: true,
      handler(val){
        // console.log('isMulti', val);
      }
    }
  },
  data() {
    return {
      leftList: this.list,
      leftSelection: [],
      rightList: this.chooseList,
      rightSelection: []
    };
  },
  computed: {
    leftSelIdList() {
      return this.leftSelection.map(item => item[this.keyword]);
    },
    rightSelIdList() {
      return this.rightSelection.map(item => item[this.keyword]);
    },
    rightIdList() {
      return this.rightList.map(item => item[this.keyword]);
    },
    disableIdList() {
      return this.disabedList.map(item => item[this.keyword]);
    }
  },
  methods: {
    clickLeftRow(row) {
      if (row.disabled) {
        return;
      }
      this.$refs.leftTable.toggleRowSelection(row);
    },
    clickRightRow(row, column, event) {
      if (event.target.nodeName !== "DIV") {
        return false;
      }
      this.$refs.rightTable.toggleRowSelection(row);
    },
    handleLeftSelectionChange(val) {
      // console.log('SelectionChange', val);
      if(!this.isMulti && val.length > 1){
        for(let i = 0; i < val.length; i++){
          let row = val[i];
          if(i == val.length - 1){
            this.$refs.leftTable.toggleRowSelection(row, true);
            this.leftSelection = [row];
          }else{
            this.$refs.leftTable.toggleRowSelection(row, false);
          }
        }
      }else{
        this.leftSelection = val;
      }
      // console.log('leftSelection', this.leftSelection);

      // this.leftSelection = val;
    },
    handleRightSelectionChange(val) {
      this.rightSelection = val;
    },
    leftMove() {
      //将左侧相同的数据置为可用
      this.setSelectionStatus(false);
      //删除右侧选中数据
      let list = this.rightList.filter(
        item => !this.rightSelIdList.includes(item[this.keyword])
      );
      this.rightList = list;
      this.clearSelection();
    },
    rightMove() {
      //将左侧相同的数据置为不可用
      this.setSelectionStatus(true);
      //必须生成新的数组，否则将改变props中chooseList的值
      let newArr = this.rightList.concat(this.leftSelection);
      this.rightList = newArr;
      this.clearSelection();
      this.$emit("rightMove", this.rightList);
    },
    setSelectionStatus(status) {
      let selIdList = status ? this.leftSelIdList : this.rightSelIdList;
      let list = this.leftList.map(item => {
        if (selIdList.includes(item[this.keyword])) {
          return { ...item, disabled: status };
        }
        return item;
      });
      this.leftList = list;
    },
    // 清空选中项
    clearSelection() {
      this.$refs.leftTable.clearSelection();
      this.$refs.rightTable.clearSelection();
    },
    //设置该行数据是否可用
    isDisabledSelected(row) {
      return !row.disabled;
    },
    sortChange(col) {
      this.$emit("sortChange", col);
    }
  }
};
</script>

<style scoped lang="scss">
#transfer {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
  height: 440px;
  .wrap {
    width: calc((100% - 80px) / 2);
    height: 100%;
    border: 1px solid #ebeef5;
    border-radius: 4px;
    display: flex;
    flex-direction: column;

    .header {
      height: 35px;
      background-color: #f5f7fa !important;
      border-bottom: 1px solid #ebeef5;
      color: #000;
      font-size: 16px;
      line-height: 35px;
      padding-left: 20px;
    }
    .input-wrap {
      margin: 5px 0 5px 10px;
    }
    .table {
      margin-top: 5px;
      .el-table {
        height: 100%;
      }
    }
    .pagination-container {
      margin: 5px;
      overflow: auto;
    }
    &.left .table {
      height: calc(100% - 77px);
      .input-wrap {
        margin-left: 10px;
        margin-top: 5px;
      }
    }
    &.right .table {
      height: calc(100% - 35px);
      ::v-deep .el-table--scrollable-x .el-table__body-wrapper {
        height: calc(100% - 38px);
      }
    }
  }
  .btn-wrap {
    width: 40px;
    margin: 0 20px;
    flex-shrink: 0;
    align-self: center;
    text-align: center;
    display: flex;
    flex-direction: column;
    .el-button + .el-button {
      margin-top: 10px;
      margin-left: 0;
    }
  }
}
</style>
