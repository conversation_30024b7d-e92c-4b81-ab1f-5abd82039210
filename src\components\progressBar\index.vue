<template>
  <el-tooltip :content="tooltip" placement="top" effect="light">
    <div class="progress-bar">
      <div class="progress-bar-content">
        <div v-for="(item, index) in processArr" :key="index"
          :style="{ width: (total == 0 ? 0 : (item.value / total * 100)) + '%', backgroundColor: item.color }"
          :class="['bar', index == processArr.length - 1 ? 'last-bar' : '']"></div>
      </div>
      <el-tooltip :content="`预留: ${total - unReservedNum}, 总计数量: ${total}`" placement="top" effect="light" v-if="total != 0 && unReservedNum != 0">
        <div class="reverse-line" :style="`left: ${unReservedNum / total * 100}%`"></div>
      </el-tooltip>
      <div class="progress-bar-text ellipsis" v-if="showTooltipText">{{ tooltip }}</div>
    </div>
  </el-tooltip>
</template>

<script>
export default {
  props: {
    process: {
      type: Array,
      default: () => []
    },
    showTooltipText: {
      type: Boolean,
      default: false
    },
    // 总数
    total: {
      type: Number,
      default: 0
    }
  },
  computed: {
    tooltip() {
      let res = "";
      this.process.forEach((item, index) => {
        let suffix = index < this.process.length - 1 ? ", " : "";
        res += item.name + ":" + item.value + suffix;
      });
      return res;
    },
    processArr() {
      return this.process.filter(item => {
        return item.value !== 0;
      })
    }
  },
  data() {
    return {
      // 非预留数量
      unReservedNum: 0
    };
  },
  watch: {
    process: {
      handler(val) {
        for (let item of this.process) {
          if (item.key == 'reserved') {
            this.unReservedNum = this.total - item.value;
            break;
          }
        }
      },
      immediate: true
    }
  }
};
</script>
<style lang="scss" scoped>
$radius: 10px;

.progress-bar {
  display: flex;
  flex-direction: row;
  position: relative;
  width: 100%;
  height: 16px;
  border-radius: $radius;
  background: #ddd;
  overflow: hidden;

  .progress-bar-content {
    position: absolute;
    width: 100%;
    height: 100%;
  }

  .reverse-line {
    height: 100%;
    position: absolute;
    width: 0px;
    border-left: 0.5px solid #fff;
  }

  .bar {
    height: 16px;
    cursor: pointer;
    display: inline-block;
    float: left;

    &:first-child {
      min-width: 5px;
      border-top-left-radius: $radius;
      border-bottom-left-radius: $radius;
    }

    &:last-child {
      min-width: 5px;
      border-top-right-radius: $radius;
      border-bottom-right-radius: $radius;
    }

    &.last-bar,
    &.empty-bar {
      border-top-right-radius: $radius;
      border-bottom-right-radius: $radius;
    }
  }

  .progress-bar-text {
    position: absolute;
    font-size: 12px;
    height: 16px;
    line-height: 16px;
    top: 50%;
    left: 0;
    padding: 0 5px;
    transform: translateY(-50%);
    max-width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    box-sizing: border-box;
  }
}
</style>
