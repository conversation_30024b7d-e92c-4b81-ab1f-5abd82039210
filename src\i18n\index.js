// src/i18n.js
import Vue from 'vue';
import VueI18n from 'vue-i18n';
Vue.use(VueI18n);

// 从缓存中读取
const serviceArea = localStorage.getItem('serviceAreaDisplayName') || '服务区';
const deptLabel = sessionStorage.getItem('deptLabel') || '机构';

const messages = {
  en: {
    serviceArea: 'Service Area',
    deptLabel: 'department',
    cascadePointNickname: 'cascadePoint'
  },
  zh: {
    serviceArea,
    deptLabel,
    cascadePointNickname: '级联点'
  },
};

const i18n = new VueI18n({
  // 设置默认语言
  locale: 'zh',
  messages,
});

/**
 * 修改字段的值
 * @param {Object} obj {key:value}
 */
i18n.setKeyValue = (obj) => {
  i18n.setLocaleMessage(i18n.locale, {
    ...i18n.messages[i18n.locale],
    ...obj
  });
  for(let key in obj){
    console.log(i18n.t(key))
  }
}

export default i18n;
