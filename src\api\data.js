import request from "@/utils/request";
import $qs from "qs";

// 获得数据源
export function getDataResource(data) {
  return request({
    url: "/iot/datasource/" + data,
    method: "get"
  });
}

// 获得数据源配置
export function getDataResourceConfig(data) {
  return request({
    url: "/iot/datasource/config/" + data,
    method: "get"
  });
}

//添加数据源
export function addDataResource(data) {
  return request({
    url: "/iot/datasource",
    method: "post",
    data,
    headers: {
      "Content-Type": "application/json;"
    }
  });
}

//删除数据源
export function deleteDataResource(data) {
  return request({
    url: "/iot/datasource/" + data,
    method: "delete"
  });
}

//更新数据源
export function updateDataResource(data) {
  return request({
    url: "/iot/datasource",
    method: "put",
    data,
    headers: {
      "Content-Type": "application/json;"
    }
  });
}

// 获得驱动
export function getDrivers() {
  return request({
    url: "/iot/driver",
    method: "get"
  });
}

// 获得指定驱动参数
export function getDriverParam(data) {
  return request({
    url: "/iot/driver/form/" + data,
    method: "get"
  });
}

// 获得节点
export function getTagNode(data) {
  let val = data ? "/" + data : "";
  return request({
    url: "/iot/tagNode" + val,
    method: "get"
  });
}

// 添加节点
export function addTagNode(data) {
  return request({
    url: "/iot/tagNode",
    method: "post",
    data,
    headers: {
      "Content-Type": "application/json;"
    }
  });
}

// 删除节点
export function deleteTagNode(data) {
  return request({
    url: "/iot/tagNode/" + data,
    method: "delete"
  });
}

// 更新节点
export function updateTagNode(data) {
  return request({
    url: "/iot/tagNode",
    method: "put",
    data,
    headers: {
      "Content-Type": "application/json;"
    }
  });
}

//查询数据源拥有的测点
export function getDataResourceTag(data) {
  return request({
    url: "/iot/datasourceTag/" + data,
    method: "get"
  });
}

//创建测点
export function addTag(data) {
  return request({
    url: "/iot/tag",
    method: "post",
    data,
    headers: {
      "Content-Type": "application/json;"
    }
  });
}

//删除测点
export function deleteTag(data) {
  return request({
    url: "/iot/tag/" + data,
    method: "delete"
  });
}

//更新测点
export function updateTag(data) {
  return request({
    url: "/iot/tag",
    method: "put",
    data,
    headers: {
      "Content-Type": "application/json;"
    }
  });
}

//查询测点
export function getTag(data) {
  return request({
    url: "/iot/tag/" + data,
    method: "get"
  });
}

export function getMqttServer() {
  return request({
    url: "/iot/mqtt/",
    method: "get"
  });
}

//查询系统中所有天气数据源
export function getWeatherDataSource() {
  return request({
    url: "/webapp/weather/getDataSource",
    method: "get"
  });
}

//设置天气数据源
export function setWeatherDataSource(data) {
  return request({
    url: "/webapp/weather/setDataSource",
    method: "get",
    params: data
  });
}

//获取机器码
export function getMachineCode() {
  return request({
    url: "/license/machineCode",
    method: "get"
  });
}

//获取授权信息
export function getLicense() {
  return request({
    url: "/license/",
    method: "get"
  });
}

//上传授权证书
export function uploadLicense(data) {
  let file = new FormData();
  file.append("file", data);
  return request({
    url: "/license/",
    method: "post",
    data: file,
    headers: {
      "Content-Type": "multipart/form-data;"
    }
  });
}

/********************************集成控制 */
//获取网关列表
export function getTerminal(data) {
  return request({
    url: "/demo/terminal/" + data,
    method: "get"
  });
}

//控制宝利通终端
export function commandPoly(data) {
  return request({
    url: "/demo/poly",
    method: "post",
    data,
    headers: {
      "Content-Type": "application/json;"
    }
  });
}

//控制TCL电视机
export function commandTCL(data) {
  return request({
    url: "/demo/tcl",
    method: "post",
    data,
    headers: {
      "Content-Type": "application/json;"
    }
  });
}
