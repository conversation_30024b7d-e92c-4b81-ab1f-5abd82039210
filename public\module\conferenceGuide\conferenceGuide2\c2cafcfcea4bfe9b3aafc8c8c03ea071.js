const NUMBER = 5;
const VERTICAL_SCROLL_INTERVAL = 10000;
const HORIZONTAL_SCROLL_INTERVAL = 20;
Vue.component("scroll-list", {
  template: `<div class="scroll-wrap" ref="scroll-wrap">
      <ul>      
        <slot name="content" :curPage="curPage"></slot>
      </ul>
    </div>`,
  data: function() {
    return {
      timer: null,
      curPage: 0
    };
  },
  mounted() {
    this.scroll();
  },
  beforeDestory() {
    clearTimeout(this.timer);
  },
  methods: {
    getScrollInterval() {
      let wrapper = this.$refs["scroll-wrap"];
      let ele = wrapper.getElementsByClassName("getWidth");
      let maxScrollInterval = VERTICAL_SCROLL_INTERVAL;
      let start = NUMBER * this.curPage;
      let end = Math.min(ele.length, start + NUMBER);
      for (let i = start; i < end; i++) {
        let interval = ele[i].scrollWidth * HORIZONTAL_SCROLL_INTERVAL;
        maxScrollInterval = Math.max(interval, maxScrollInterval);
      }
      return maxScrollInterval + 1000;
    },
    scroll() {
      let $wrapper = $(this.$refs["scroll-wrap"]);
      let wrapperHeight = $wrapper.height();
      let ulWrapper = $wrapper.find("ul");
      let curHeight = wrapperHeight;
      let that = this;

      function startTimer() {
        that.timer && clearInterval(that.timer);
        that.timer = setInterval(Marquee, that.getScrollInterval());
      }

      function Marquee() {
        let ulWrapperHeight = ulWrapper.height();
        wrapperHeight = $wrapper.height();
        if (ulWrapperHeight < wrapperHeight) {
          if(that.curPage !== 0){
            that.curPage = 0;
            ulWrapper.css("margin-top", 0);
          }
          return;
        }
        if (curHeight >= ulWrapperHeight) {
          that.curPage = 0;
          startTimer();

          ulWrapper.css("margin-top", 0);
          curHeight = wrapperHeight;
          return;
        } else {
          ulWrapper.stop().animate(
            {
              marginTop: -curHeight
            },
            1000,
            "linear",
            () => {
              curHeight += wrapperHeight;
              that.curPage++;

              startTimer();
            }
          );
        }
      }
      startTimer();
    }
  }
});
