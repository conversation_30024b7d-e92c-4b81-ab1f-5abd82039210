// import parseTime, formatTime and set to filter
export { parseTime, formatTime } from "@/utils";
import { IsStrEmpty } from "@/utils/utils";
import {
  material,
  week,
  ANIMATION,
  PLAY_TYPE,
  PERIOD,
  AUDIT,
  APPROVED_STATUS
} from "@/utils/enum";

/**
 * Show plural label if time is plural number
 * @param {number} time
 * @param {string} label
 * @return {string}
 */
function pluralize(time, label) {
  if (time === 1) {
    return time + label;
  }
  return time + label + "s";
}

/**
 * @param {number} time
 */
export function timeAgo(time) {
  const between = Date.now() / 1000 - Number(time);
  if (between < 3600) {
    return pluralize(~~(between / 60), " minute");
  } else if (between < 86400) {
    return pluralize(~~(between / 3600), " hour");
  } else {
    return pluralize(~~(between / 86400), " day");
  }
}

/**
 * Number formatting
 * like 10000 => 10k
 * @param {number} num
 * @param {number} digits
 */
export function numberFormatter(num, digits) {
  const si = [
    { value: 1e18, symbol: "E" },
    { value: 1e15, symbol: "P" },
    { value: 1e12, symbol: "T" },
    { value: 1e9, symbol: "G" },
    { value: 1e6, symbol: "M" },
    { value: 1e3, symbol: "k" }
  ];
  for (let i = 0; i < si.length; i++) {
    if (num >= si[i].value) {
      return (
        (num / si[i].value)
          .toFixed(digits)
          .replace(/\.0+$|(\.[0-9]*[1-9])0+$/, "$1") + si[i].symbol
      );
    }
  }
  return num.toString();
}

/**
 * 10000 => "10,000"
 * @param {number} num
 */
export function toThousandFilter(num) {
  return (+num || 0)
    .toString()
    .replace(/^-?\d+/g, m => m.replace(/(?=(?!\b)(\d{3})+$)/g, ","));
}

/**
 * Upper case first char
 * @param {String} string
 */
export function uppercaseFirst(string) {
  return string.charAt(0).toUpperCase() + string.slice(1);
}

export function materialName(type, syncId) {
  let obj = material.filter(item => item.type === type);
  if (obj.length > 1) {
    let bSyncID = !IsStrEmpty(syncId);
    obj = obj.filter(item => item.bSyncId === bSyncID);
  }
  return (obj.length && obj[0].name) || "";
}

export function weekName(en) {
  let res = week.find(item => item.en === en);
  return (res && res.name) || "";
}

export function animationName(en) {
  let res = week.find(item => item.en === en);
  return (res && res.name) || "";
}

export function animationNameFilter(effect) {
  console.log(effect);
  let res = ANIMATION.find(item => item.value === effect);
  return (res && res.animationName) || "";
}

export function playTypeNameFilter(label) {
  let res = PLAY_TYPE.find(item => item.label === label);
  return (res && res.value) || "";
}

export function periodNameFilter(value) {
  let res = PERIOD.find(item => item.value === value);
  return (res && res.label) || "";
}

export function auditNameFilter(value) {
  let res = AUDIT.find(item => item.value === value);
  return (res && res.label) || "";
}

export function approvedFilter(approved, key) {
  let res = APPROVED_STATUS.find(item => item.value === approved);
  return (res && res[key]) || "";
}
