<template>
  <div class="wrap">
    <div class="header-wrap">
      <div class="title">本月告警设备分类占比</div>
      <el-link @click="showDetail">查看详情 ></el-link>
    </div>
    <div class="chart-wrap">
      <div ref="chart" class="pie-chart" v-show="data.length > 0"> </div>
      <div class="empty-data" v-show="data.length === 0"> 暂无统计数据 </div>
    </div>
  </div>
</template>

<script>
  import { getDeviceTypeAlarm } from '@/api/iotControl';
  export default {
    data() {
      return {
        data: [],
        chart: null,
        timer: null,
      };
    },
    mounted() {
      this.getData();
      this.timer = setInterval(() => this.getData(), 10 * 1000);
      window.addEventListener('resize', this.resize);
    },
    methods: {
      getData() {
        getDeviceTypeAlarm({ timeType: 1 }).then(({ data }) => {
          this.data = data;
          this.$nextTick(() => {
            if (this.data.length > 0) {
              if (!this.chart) {
                this.chart = this.$echarts.init(this.$refs.chart);
              }
              this.draw();
            }
          });
        });
      },
      resize() {
        this.$nextTick(() => {
          this.chart && this.chart.resize();
        });
      },
      draw() {
        let option = {
          legend: {
            type: 'scroll',
            orient: 'vertical',
            right: '0px',
            top: '0px',
            data: this.data.map((item) => item.devSubTypeName),
            formatter: (name) => {
              const item = this.data.find((d) => d.devSubTypeName === name);
              const total = this.data.reduce((sum, d) => sum + d.count, 0);
              const percent = ((item.count / total) * 100).toFixed(2);
              return `{name|${name}}\n ${item.count}次 {percent|(${percent}%)}`;
            },
            textStyle: {
              rich: {
                name: {
                  color: '#000',
                  fontSize: 12,
                  lineHeight: 16,
                },
                count: {
                  color: '#000',
                  fontSize: 12,
                  lineHeight: 16,
                },
                percent: {
                  color: '#999',
                  fontSize: 12,
                  lineHeight: 16,
                },
              },
            },
          },
          series: [
            {
              type: 'pie',
              radius: '60%',
              center: ['32%', '50%'],
              label: {
                show: false,
              },
              data: this.data.map((item) => ({
                name: item.devSubTypeName,
                value: item.count,
              })),
            },
          ],
          tooltip: {
            trigger: 'item',
            confine: true,
            formatter: '{b} : {c}次',
          },
        };

        this.chart.setOption(option);
      },
      showDetail() {
        this.$router.push({
          name: 'IotAlarmCenter',
        });
      },
      refresh() {
        this.getData();
      },
    },
    beforeDestroy() {
      this.chart && this.chart.dispose();
      this.chart = null;
      clearInterval(this.timer);
      window.removeEventListener('resize', this.resize);
    },
  };
</script>

<style lang="scss" scoped>
  .wrap {
    width: 100%;
    height: 100%;
  }
  .chart-wrap {
    height: calc(100% - 40px);
    width: 100%;
    .pie-chart {
      width: 100%;
      height: 100%;
    }
    .empty-data {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      font-size: 14px;
    }
  }
</style>
