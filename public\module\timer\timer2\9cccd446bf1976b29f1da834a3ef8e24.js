/**
 * 农历与阳历的转换，目前只能支持1900至2100的转换
 * User: conis<<EMAIL>>
 * Github: https://github.com/conis/chinese-lunar
 * Date: 1/29/13
 * Time: 9:58 上午
 *
 */
(function() {
	/*
	 农历每一年，对应公历的数据
	 此数据来源于互联网，原作者不详细，在此感谢
	 MAPPING[0][0]：每年闰月的月份，0表示不闰
	 MAPPING[0][1, 13]：表示每月初一对应的阳历时间，前两个字符表示月，后两个字符表示月
	 */
	var MAPPING = [
		[8, "0131", "0301", "0331", "0429", "0528", "0627", "0726", "0825", "0924", "1023", "1122", "1222", "1320"], 	//1900
		[0, "0219", "0320", "0419", "0518", "0616", "0716", "0814", "0913", "1012", "1111", "1211", "1310"], 	//1901
		[0, "0208", "0310", "0408", "0508", "0606", "0705", "0804", "0902", "1002", "1031", "1130", "1230"], 	//1902
		[5, "0129", "0227", "0329", "0427", "0527", "0625", "0724", "0823", "0921", "1020", "1119", "1219", "1317"], 	//1903
		[0, "0216", "0317", "0416", "0515", "0614", "0713", "0811", "0910", "1009", "1107", "1207", "1306"], 	//1904
		[0, "0204", "0306", "0405", "0504", "0603", "0703", "0801", "0830", "0929", "1028", "1127", "1226"], 	//1905
		[4, "0125", "0223", "0325", "0424", "0523", "0622", "0721", "0820", "0918", "1018", "1116", "1216", "1314"], 	//1906
		[0, "0213", "0314", "0413", "0512", "0611", "0710", "0809", "0908", "1007", "1106", "1205", "1304"], 	//1907
		[0, "0202", "0303", "0401", "0430", "0530", "0629", "0728", "0827", "0925", "1025", "1124", "1223"], 	//1908
		[2, "0122", "0220", "0322", "0420", "0519", "0618", "0717", "0816", "0914", "1014", "1113", "1213", "1311"], 	//1909
		[0, "0210", "0311", "0410", "0509", "0607", "0707", "0805", "0904", "1003", "1102", "1202", "1301"], 	//1910
		[6, "0130", "0301", "0330", "0429", "0528", "0626", "0726", "0824", "0922", "1022", "1121", "1220", "1319"], 	//1911
		[0, "0218", "0319", "0417", "0517", "0615", "0714", "0813", "0911", "1010", "1109", "1209", "1307"], 	//1912
		[0, "0206", "0308", "0407", "0506", "0605", "0704", "0802", "0901", "0930", "1029", "1128", "1227"], 	//1913
		[5, "0126", "0225", "0327", "0425", "0525", "0623", "0723", "0821", "0920", "1019", "1117", "1217", "1315"], 	//1914
		[0, "0214", "0316", "0414", "0514", "0613", "0712", "0811", "0909", "1009", "1107", "1207", "1305"], 	//1915
		[0, "0203", "0304", "0403", "0502", "0601", "0630", "0730", "0829", "0927", "1027", "1125", "1225"], 	//1916
		[2, "0123", "0222", "0323", "0421", "0521", "0619", "0719", "0818", "0916", "1016", "1115", "1214", "1313"], 	//1917
		[0, "0211", "0313", "0411", "0510", "0609", "0708", "0807", "0905", "1005", "1104", "1203", "1302"], 	//1918
		[7, "0201", "0302", "0401", "0430", "0529", "0628", "0727", "0825", "0924", "1024", "1122", "1222", "1321"], 	//1919
		[0, "0220", "0320", "0419", "0518", "0616", "0716", "0814", "0912", "1012", "1110", "1210", "1309"], 	//1920
		[0, "0208", "0310", "0408", "0508", "0606", "0705", "0804", "0902", "1001", "1031", "1129", "1229"], 	//1921
		[5, "0128", "0227", "0328", "0427", "0527", "0625", "0724", "0823", "0921", "1020", "1119", "1218", "1317"], 	//1922
		[0, "0216", "0317", "0416", "0516", "0614", "0714", "0812", "0911", "1010", "1108", "1208", "1306"], 	//1923
		[0, "0205", "0305", "0404", "0504", "0602", "0702", "0801", "0830", "0929", "1028", "1127", "1226"], 	//1924
		[4, "0124", "0223", "0324", "0423", "0522", "0621", "0721", "0819", "0918", "1018", "1116", "1216", "1314"], 	//1925
		[0, "0213", "0314", "0412", "0512", "0610", "0710", "0808", "0907", "1007", "1105", "1205", "1304"], 	//1926
		[0, "0202", "0304", "0402", "0501", "0531", "0629", "0729", "0827", "0926", "1025", "1124", "1224"], 	//1927
		[2, "0123", "0221", "0322", "0420", "0519", "0618", "0717", "0815", "0914", "1013", "1112", "1212", "1311"], 	//1928
		[0, "0210", "0311", "0410", "0509", "0607", "0707", "0805", "0903", "1003", "1101", "1201", "1231"], 	//1929
		[6, "0130", "0228", "0330", "0429", "0528", "0626", "0726", "0824", "0922", "1022", "1120", "1220", "1319"], 	//1930
		[0, "0217", "0319", "0418", "0517", "0616", "0715", "0814", "0912", "1011", "1110", "1209", "1308"], 	//1931
		[0, "0206", "0307", "0406", "0506", "0604", "0704", "0802", "0901", "0930", "1029", "1128", "1227"], 	//1932
		[5, "0126", "0224", "0326", "0425", "0524", "0623", "0722", "0821", "0920", "1019", "1118", "1217", "1315"], 	//1933
		[0, "0214", "0315", "0414", "0513", "0612", "0712", "0810", "0909", "1008", "1107", "1207", "1305"], 	//1934
		[0, "0204", "0305", "0403", "0503", "0601", "0701", "0730", "0829", "0928", "1027", "1126", "1226"], 	//1935
		[3, "0124", "0223", "0323", "0421", "0521", "0619", "0718", "0817", "0916", "1015", "1114", "1214", "1313"], 	//1936
		[0, "0211", "0313", "0411", "0510", "0609", "0708", "0806", "0905", "1004", "1103", "1203", "1302"], 	//1937
		[7, "0131", "0302", "0401", "0430", "0529", "0628", "0727", "0825", "0924", "1023", "1122", "1222", "1320"], 	//1938
		[0, "0219", "0321", "0420", "0519", "0617", "0717", "0815", "0913", "1013", "1111", "1211", "1309"], 	//1939
		[0, "0208", "0309", "0408", "0507", "0606", "0705", "0804", "0902", "1001", "1031", "1129", "1229"], 	//1940
		[6, "0127", "0226", "0328", "0426", "0526", "0625", "0724", "0823", "0921", "1020", "1119", "1218", "1317"], 	//1941
		[0, "0215", "0317", "0415", "0515", "0614", "0713", "0812", "0910", "1010", "1108", "1208", "1306"], 	//1942
		[0, "0205", "0306", "0405", "0504", "0603", "0702", "0801", "0831", "0929", "1029", "1127", "1227"], 	//1943
		[4, "0125", "0224", "0324", "0423", "0522", "0621", "0720", "0819", "0917", "1017", "1116", "1215", "1314"], 	//1944
		[0, "0213", "0314", "0412", "0512", "0610", "0709", "0808", "0906", "1006", "1105", "1205", "1303"], 	//1945
		[0, "0202", "0304", "0402", "0501", "0531", "0629", "0728", "0827", "0925", "1025", "1124", "1223"], 	//1946
		[2, "0122", "0221", "0323", "0421", "0520", "0619", "0718", "0816", "0915", "1014", "1113", "1212", "1311"], 	//1947
		[0, "0210", "0311", "0409", "0509", "0607", "0707", "0805", "0903", "1003", "1101", "1201", "1230"], 	//1948
		[7, "0129", "0228", "0329", "0428", "0528", "0626", "0726", "0824", "0922", "1022", "1120", "1220", "1318"], 	//1949
		[0, "0217", "0318", "0417", "0517", "0615", "0715", "0814", "0912", "1011", "1110", "1209", "1308"], 	//1950
		[0, "0206", "0308", "0406", "0506", "0605", "0704", "0803", "0901", "1001", "1030", "1129", "1228"], 	//1951
		[5, "0127", "0225", "0326", "0424", "0524", "0622", "0722", "0820", "0919", "1019", "1117", "1217", "1315"], 	//1952
		[0, "0214", "0315", "0414", "0513", "0611", "0711", "0810", "0908", "1008", "1107", "1206", "1305"], 	//1953
		[0, "0203", "0305", "0403", "0503", "0601", "0630", "0730", "0828", "0927", "1027", "1126", "1225"], 	//1954
		[3, "0124", "0222", "0324", "0422", "0522", "0620", "0719", "0818", "0916", "1016", "1114", "1214", "1313"], 	//1955
		[0, "0212", "0312", "0411", "0510", "0609", "0708", "0806", "0905", "1004", "1103", "1203", "1301"], 	//1956
		[8, "0131", "0302", "0331", "0430", "0529", "0628", "0727", "0825", "0924", "1023", "1122", "1221", "1320"], 	//1957
		[0, "0218", "0320", "0419", "0519", "0617", "0717", "0815", "0913", "1013", "1111", "1211", "1309"], 	//1958
		[0, "0208", "0309", "0408", "0508", "0606", "0706", "0804", "0903", "1002", "1101", "1130", "1230"], 	//1959
		[6, "0128", "0227", "0327", "0426", "0525", "0624", "0724", "0822", "0921", "1020", "1119", "1218", "1317"], 	//1960
		[0, "0215", "0317", "0415", "0515", "0613", "0713", "0811", "0910", "1010", "1108", "1208", "1306"], 	//1961
		[0, "0205", "0306", "0405", "0504", "0602", "0702", "0731", "0830", "0929", "1028", "1127", "1227"], 	//1962
		[4, "0125", "0224", "0325", "0424", "0523", "0621", "0721", "0819", "0918", "1017", "1116", "1216", "1315"], 	//1963
		[0, "0213", "0314", "0412", "0512", "0610", "0709", "0808", "0906", "1006", "1104", "1204", "1303"], 	//1964
		[0, "0202", "0303", "0402", "0501", "0531", "0629", "0728", "0827", "0925", "1024", "1123", "1223"], 	//1965
		[3, "0121", "0220", "0322", "0421", "0520", "0619", "0718", "0816", "0915", "1014", "1112", "1212", "1311"], 	//1966
		[0, "0209", "0311", "0410", "0509", "0608", "0708", "0806", "0904", "1004", "1102", "1202", "1231"], 	//1967
		[7, "0130", "0228", "0329", "0427", "0527", "0626", "0725", "0824", "0922", "1022", "1120", "1220", "1318"], 	//1968
		[0, "0217", "0318", "0417", "0516", "0615", "0714", "0813", "0912", "1011", "1110", "1209", "1308"], 	//1969
		[0, "0206", "0308", "0406", "0505", "0604", "0703", "0802", "0901", "0930", "1030", "1129", "1228"], 	//1970
		[5, "0127", "0225", "0327", "0425", "0524", "0623", "0722", "0821", "0919", "1019", "1118", "1218", "1316"], 	//1971
		[0, "0215", "0315", "0414", "0513", "0611", "0711", "0809", "0908", "1007", "1106", "1206", "1304"], 	//1972
		[0, "0203", "0305", "0403", "0503", "0601", "0630", "0730", "0828", "0926", "1026", "1125", "1224"], 	//1973
		[4, "0123", "0222", "0324", "0422", "0522", "0620", "0719", "0818", "0916", "1015", "1114", "1214", "1312"], 	//1974
		[0, "0211", "0313", "0412", "0511", "0610", "0709", "0807", "0906", "1005", "1103", "1203", "1301"], 	//1975
		[8, "0131", "0301", "0331", "0429", "0529", "0627", "0727", "0825", "0924", "1023", "1121", "1221", "1319"], 	//1976
		[0, "0218", "0320", "0418", "0518", "0617", "0716", "0815", "0913", "1013", "1111", "1211", "1309"], 	//1977
		[0, "0207", "0309", "0407", "0507", "0606", "0705", "0804", "0902", "1002", "1101", "1130", "1230"], 	//1978
		[6, "0128", "0227", "0328", "0426", "0526", "0624", "0724", "0823", "0921", "1021", "1120", "1219", "1318"], 	//1979
		[0, "0216", "0317", "0415", "0514", "0613", "0712", "0811", "0909", "1009", "1108", "1207", "1306"], 	//1980
		[0, "0205", "0306", "0405", "0504", "0602", "0702", "0731", "0829", "0928", "1028", "1126", "1226"], 	//1981
		[4, "0125", "0224", "0325", "0424", "0523", "0621", "0721", "0819", "0917", "1017", "1115", "1215", "1314"], 	//1982
		[0, "0213", "0315", "0413", "0513", "0611", "0710", "0809", "0907", "1006", "1105", "1204", "1303"], 	//1983
		[10, "0202", "0303", "0401", "0501", "0531", "0629", "0728", "0827", "0925", "1024", "1123", "1222", "1321"], 	//1984
		[0, "0220", "0321", "0420", "0520", "0618", "0718", "0816", "0915", "1014", "1112", "1212", "1310"], 	//1985
		[0, "0209", "0310", "0409", "0509", "0607", "0707", "0806", "0904", "1004", "1102", "1202", "1231"], 	//1986
		[6, "0129", "0228", "0329", "0428", "0527", "0626", "0726", "0824", "0923", "1023", "1121", "1221", "1319"], 	//1987
		[0, "0217", "0318", "0416", "0516", "0614", "0714", "0812", "0911", "1011", "1109", "1209", "1308"], 	//1988
		[0, "0206", "0308", "0406", "0505", "0604", "0703", "0802", "0831", "0930", "1029", "1128", "1228"], 	//1989
		[5, "0127", "0225", "0327", "0425", "0524", "0623", "0722", "0820", "0919", "1018", "1117", "1217", "1316"], 	//1990
		[0, "0215", "0316", "0415", "0514", "0612", "0712", "0810", "0908", "1008", "1106", "1206", "1305"], 	//1991
		[0, "0204", "0304", "0403", "0503", "0601", "0630", "0730", "0828", "0926", "1026", "1124", "1224"], 	//1992
		[3, "0123", "0221", "0323", "0422", "0521", "0620", "0719", "0818", "0916", "1015", "1114", "1213", "1312"], 	//1993
		[0, "0210", "0312", "0411", "0511", "0609", "0709", "0807", "0906", "1005", "1103", "1203", "1301"], 	//1994
		[8, "0131", "0301", "0331", "0430", "0529", "0628", "0727", "0826", "0925", "1024", "1122", "1222", "1320"], 	//1995
		[0, "0219", "0319", "0418", "0517", "0616", "0715", "0814", "0912", "1012", "1111", "1211", "1309"], 	//1996
		[0, "0207", "0309", "0407", "0507", "0605", "0705", "0803", "0902", "1002", "1031", "1130", "1230"], 	//1997
		[5, "0128", "0227", "0328", "0426", "0526", "0624", "0723", "0822", "0921", "1020", "1119", "1219", "1317"], 	//1998
		[0, "0216", "0318", "0416", "0515", "0614", "0713", "0811", "0910", "1009", "1108", "1208", "1307"], 	//1999
		[0, "0205", "0306", "0405", "0504", "0602", "0702", "0731", "0829", "0928", "1027", "1126", "1226"], 	//2000
		[4, "0124", "0223", "0325", "0423", "0523", "0621", "0721", "0819", "0917", "1017", "1115", "1215", "1313"], 	//2001
		[0, "0212", "0314", "0413", "0512", "0611", "0710", "0809", "0907", "1006", "1105", "1204", "1303"], 	//2002
		[0, "0201", "0303", "0402", "0501", "0531", "0630", "0729", "0828", "0926", "1025", "1124", "1223"], 	//2003
		[2, "0122", "0220", "0321", "0419", "0519", "0618", "0717", "0816", "0914", "1014", "1112", "1212", "1310"], 	//2004
		[0, "0209", "0310", "0409", "0508", "0607", "0706", "0805", "0904", "1003", "1102", "1201", "1231"], 	//2005
		[7, "0129", "0228", "0329", "0428", "0527", "0626", "0725", "0824", "0922", "1022", "1121", "1220", "1319"], 	//2006
		[0, "0218", "0319", "0417", "0517", "0615", "0714", "0813", "0911", "1011", "1110", "1210", "1308"], 	//2007
		[0, "0207", "0308", "0406", "0505", "0604", "0703", "0801", "0831", "0929", "1029", "1128", "1227"], 	//2008
		[5, "0126", "0225", "0327", "0425", "0524", "0623", "0722", "0820", "0919", "1018", "1117", "1216", "1315"], 	//2009
		[0, "0214", "0316", "0414", "0514", "0612", "0712", "0810", "0908", "1008", "1106", "1206", "1304"], 	//2010
		[0, "0203", "0305", "0403", "0503", "0602", "0701", "0731", "0829", "0927", "1027", "1125", "1225"], 	//2011
		[4, "0123", "0222", "0322", "0421", "0521", "0619", "0719", "0817", "0916", "1015", "1114", "1213", "1312"], 	//2012
		[0, "0210", "0312", "0410", "0510", "0608", "0708", "0807", "0905", "1005", "1103", "1203", "1301"], 	//2013
		[9, "0131", "0301", "0331", "0429", "0529", "0627", "0727", "0825", "0924", "1024", "1122", "1222", "1320"], 	//2014
		[0, "0219", "0320", "0419", "0518", "0616", "0716", "0814", "0913", "1013", "1112", "1211", "1310"], 	//2015
		[0, "0208", "0309", "0407", "0507", "0605", "0704", "0803", "0901", "1001", "1031", "1129", "1229"], 	//2016
		[6, "0128", "0226", "0328", "0426", "0526", "0624", "0723", "0822", "0920", "1020", "1118", "1218", "1317"], 	//2017
		[0, "0216", "0317", "0416", "0515", "0614", "0713", "0811", "0910", "1009", "1108", "1207", "1306"], 	//2018
		[0, "0205", "0307", "0405", "0505", "0603", "0703", "0801", "0830", "0929", "1028", "1126", "1226"], 	//2019
		[4, "0125", "0223", "0324", "0423", "0523", "0621", "0721", "0819", "0917", "1017", "1115", "1215", "1313"], 	//2020
		[0, "0212", "0313", "0412", "0512", "0610", "0710", "0808", "0907", "1006", "1105", "1204", "1303"], 	//2021
		[0, "0201", "0303", "0401", "0501", "0530", "0629", "0729", "0827", "0926", "1025", "1124", "1223"], 	//2022
		[2, "0122", "0220", "0322", "0420", "0519", "0618", "0718", "0816", "0915", "1015", "1113", "1213", "1311"], 	//2023
		[0, "0210", "0310", "0409", "0508", "0606", "0706", "0804", "0903", "1003", "1101", "1201", "1231"], 	//2024
		[6, "0129", "0228", "0329", "0428", "0527", "0625", "0725", "0823", "0922", "1021", "1120", "1220", "1319"], 	//2025
		[0, "0217", "0319", "0417", "0517", "0615", "0714", "0813", "0911", "1010", "1109", "1209", "1308"], 	//2026
		[0, "0206", "0308", "0407", "0506", "0605", "0704", "0802", "0901", "0930", "1029", "1128", "1228"], 	//2027
		[5, "0126", "0225", "0326", "0425", "0524", "0623", "0722", "0820", "0919", "1018", "1116", "1216", "1315"], 	//2028
		[0, "0213", "0315", "0414", "0513", "0612", "0711", "0810", "0908", "1008", "1106", "1205", "1304"], 	//2029
		[0, "0203", "0304", "0403", "0502", "0601", "0701", "0730", "0829", "0927", "1027", "1125", "1225"], 	//2030
		[3, "0123", "0221", "0323", "0422", "0521", "0620", "0719", "0818", "0917", "1016", "1115", "1214", "1313"], 	//2031
		[0, "0211", "0312", "0410", "0509", "0608", "0707", "0806", "0905", "1004", "1103", "1203", "1301"], 	//2032
		[7, "0131", "0301", "0331", "0429", "0528", "0627", "0726", "0825", "0923", "1023", "1122", "1222", "1320"], 	//2033
		[0, "0219", "0320", "0419", "0518", "0616", "0716", "0814", "0913", "1012", "1111", "1211", "1309"], 	//2034
		[0, "0208", "0310", "0408", "0508", "0606", "0705", "0804", "0902", "1001", "1031", "1130", "1229"], 	//2035
		[6, "0128", "0227", "0328", "0426", "0526", "0624", "0723", "0822", "0920", "1019", "1118", "1217", "1316"], 	//2036
		[0, "0215", "0317", "0416", "0515", "0614", "0713", "0811", "0910", "1009", "1107", "1207", "1305"], 	//2037
		[0, "0204", "0306", "0405", "0504", "0603", "0702", "0801", "0830", "0929", "1028", "1126", "1226"], 	//2038
		[5, "0124", "0223", "0325", "0423", "0523", "0622", "0721", "0820", "0918", "1018", "1116", "1216", "1314"], 	//2039
		[0, "0212", "0313", "0411", "0511", "0610", "0709", "0808", "0906", "1006", "1105", "1204", "1303"], 	//2040
		[0, "0201", "0302", "0401", "0430", "0530", "0628", "0728", "0827", "0925", "1025", "1124", "1223"], 	//2041
		[2, "0122", "0220", "0322", "0420", "0519", "0618", "0717", "0816", "0914", "1014", "1113", "1212", "1311"], 	//2042
		[0, "0210", "0311", "0410", "0509", "0607", "0707", "0805", "0903", "1003", "1102", "1201", "1231"], 	//2043
		[7, "0130", "0229", "0329", "0428", "0527", "0625", "0725", "0823", "0921", "1021", "1119", "1219", "1318"], 	//2044
		[0, "0217", "0319", "0417", "0517", "0615", "0714", "0813", "0911", "1010", "1109", "1208", "1307"], 	//2045
		[0, "0206", "0308", "0406", "0506", "0604", "0704", "0802", "0901", "0930", "1029", "1128", "1227"], 	//2046
		[5, "0126", "0225", "0326", "0425", "0525", "0623", "0723", "0821", "0920", "1019", "1117", "1217", "1315"], 	//2047
		[0, "0214", "0314", "0413", "0513", "0611", "0711", "0810", "0908", "1008", "1106", "1205", "1304"], 	//2048
		[0, "0202", "0304", "0402", "0502", "0531", "0630", "0730", "0828", "0927", "1027", "1125", "1225"], 	//2049
		[3, "0123", "0221", "0323", "0421", "0521", "0619", "0719", "0817", "0916", "1016", "1114", "1214", "1313"], 	//2050
		[0, "0211", "0313", "0411", "0510", "0609", "0708", "0806", "0905", "1005", "1103", "1203", "1302"], 	//2051
		[8, "0201", "0301", "0331", "0429", "0528", "0627", "0726", "0824", "0923", "1022", "1121", "1221", "1320"], 	//2052
		[0, "0219", "0320", "0419", "0518", "0616", "0716", "0814", "0912", "1012", "1110", "1210", "1309"], 	//2053
		[0, "0208", "0309", "0408", "0508", "0606", "0705", "0804", "0902", "1001", "1031", "1129", "1229"], 	//2054
		[6, "0128", "0226", "0328", "0427", "0526", "0625", "0724", "0823", "0921", "1020", "1119", "1218", "1317"], 	//2055
		[0, "0215", "0316", "0415", "0515", "0613", "0713", "0811", "0910", "1009", "1107", "1207", "1305"], 	//2056
		[0, "0204", "0305", "0404", "0504", "0602", "0702", "0731", "0830", "0929", "1028", "1126", "1226"], 	//2057
		[4, "0124", "0223", "0324", "0423", "0522", "0621", "0720", "0819", "0918", "1017", "1116", "1216", "1314"], 	//2058
		[0, "0212", "0314", "0412", "0512", "0610", "0710", "0808", "0907", "1006", "1105", "1205", "1304"], 	//2059
		[0, "0202", "0303", "0401", "0501", "0530", "0628", "0727", "0826", "0924", "1024", "1123", "1223"], 	//2060
		[3, "0121", "0220", "0322", "0420", "0519", "0618", "0717", "0815", "0914", "1013", "1112", "1212", "1311"], 	//2061
		[0, "0209", "0311", "0410", "0509", "0607", "0707", "0805", "0903", "1003", "1101", "1201", "1231"], 	//2062
		[7, "0129", "0228", "0330", "0428", "0528", "0626", "0726", "0824", "0922", "1022", "1120", "1220", "1318"], 	//2063
		[0, "0217", "0318", "0417", "0516", "0615", "0714", "0813", "0911", "1010", "1109", "1208", "1307"], 	//2064
		[0, "0205", "0307", "0406", "0505", "0604", "0704", "0802", "0901", "0930", "1029", "1128", "1227"], 	//2065
		[5, "0126", "0224", "0326", "0424", "0524", "0623", "0722", "0821", "0919", "1019", "1117", "1217", "1315"], 	//2066
		[0, "0214", "0315", "0414", "0513", "0612", "0711", "0810", "0909", "1008", "1107", "1206", "1305"], 	//2067
		[0, "0203", "0304", "0402", "0502", "0531", "0629", "0729", "0828", "0926", "1026", "1125", "1224"], 	//2068
		[4, "0123", "0221", "0323", "0421", "0521", "0619", "0718", "0817", "0915", "1015", "1114", "1214", "1312"], 	//2069
		[0, "0211", "0312", "0411", "0510", "0609", "0708", "0806", "0905", "1004", "1103", "1203", "1301" ], // 2070
		[8, "0131", "0302", "0331", "0430", "0529", "0628", "0727", "0825", "0924", "1023", "1122", "1221", "1320" ], // 2071
		[0, "0219", "0320", "0418", "0518", "0616", "0716", "0814", "0912", "1012", "1110", "1210", "1308" ], // 2072
		[0, "0207", "0309", "0407", "0507", "0606", "0705", "0804", "0902", "1001", "1031", "1129", "1229" ], // 2073
		[6, "0127", "0226", "0327", "0426", "0526", "0624", "0724", "0822", "0921", "1020", "1119", "1218", "1317" ], // 2074
		[0, "0215", "0317", "0415", "0515", "0613", "0713", "0812", "0910", "1010", "1108", "1208", "1306" ], // 2075
		[0, "0205", "0305", "0404", "0503", "0602", "0701", "0731", "0829", "0928", "1028", "1126", "1226" ], // 2076
		[4, "0124", "0223", "0324", "0423", "0522", "0620", "0720", "0818", "0917", "1017", "1116", "1215", "1314" ], // 2077
		[0, "0212", "0314", "0412", "0512", "0610", "0709", "0808", "0906", "1006", "1105", "1204", "1303" ], // 2078
		[0, "0202", "0303", "0402", "0501", "0531", "0629", "0728", "0827", "0925", "1025", "1123", "1223" ], // 2079
		[3, "0122", "0221", "0321", "0420", "0519", "0618", "0717", "0815", "0914", "1013", "1111", "1211", "1310" ], // 2080
		[0, "0209", "0310", "0409", "0509", "0607", "0707", "0805", "0903", "1003", "1101", "1130", "1230" ], // 2081
		[7, "0129", "0227", "0329", "0428", "0528", "0626", "0725", "0824", "0922", "1022", "1120", "1219", "1318" ], // 2082
		[0, "0217", "0318", "0417", "0517", "0615", "0715", "0813", "0912", "1011", "1110", "1209", "1308" ], // 2083
		[0, "0206", "0307", "0405", "0505", "0603", "0703", "0802", "0831", "0930", "1029", "1128", "1227" ], // 2084
		[5, "0126", "0224", "0326", "0424", "0523", "0622", "0722", "0820", "0919", "1019", "1117", "1217", "1315" ], // 2085
		[0, "0214", "0315", "0414", "0513", "0611", "0711", "0809", "0908", "1008", "1106", "1206", "1305" ], // 2086
		[0, "0203", "0305", "0403", "0503", "0601", "0630", "0730", "0828", "0927", "1026", "1125", "1225" ], // 2087
		[4, "0124", "0222", "0323", "0421", "0521", "0619", "0718", "0817", "0915", "1014", "1113", "1213", "1312" ], // 2088
		[0, "0210", "0312", "0411", "0510", "0609", "0708", "0806", "0904", "1004", "1102", "1202", "1301" ], // 2089
		[8, "0130", "0301", "0331", "0430", "0529", "0628", "0727", "0825", "0924", "1023", "1121", "1221", "1320" ], // 2090
		[0, "0218", "0320", "0419", "0518", "0617", "0716", "0815", "0913", "1013", "1111", "1210", "1309" ], // 2091
		[0, "0207", "0308", "0407", "0506", "0605", "0705", "0803", "0902", "1001", "1031", "1129", "1229" ], // 2092
		[6, "0127", "0225", "0327", "0426", "0525", "0624", "0723", "0822", "0921", "1020", "1119", "1218", "1317" ], // 2093
		[0, "0215", "0316", "0415", "0514", "0613", "0712", "0811", "0910", "1009", "1108", "1208", "1306" ], // 2094
		[0, "0205", "0306", "0405", "0504", "0602", "0702", "0731", "0830", "0928", "1028", "1127", "1227" ], // 2095
		[4, "0125", "0224", "0324", "0423", "0522", "0620", "0720", "0818", "0916", "1016", "1115", "1215", "1313" ], // 2096
		[0, "0212", "0314", "0412", "0512", "0610", "0709", "0808", "0906", "1005", "1104", "1204", "1302" ], // 2097
		[0, "0201", "0303", "0402", "0501", "0531", "0629", "0728", "0826", "0925", "1024", "1123", "1222" ], // 2098
		[2, "0121", "0220", "0322", "0420", "0520", "0619", "0718", "0816", "0915", "1014", "1112", "1212", "1310" ] // 2099
	];
	var MINYEAR = 1900;
	var _chineseLunar = {};

	/*
	 * 分析日期表达式，并提取其中的单位和数值
	 */
	var _expression = function(expr) {
		var list = expr.match(/[+-]?\d+((ms)|[yMdhmsw])/g);
		var result = [];
		for(var i = 0; i < list.length; i++){
			//提取单位和数值
			if (/([+-])(\d+)(.+)/.test(list[i])) {
				var val = parseInt(RegExp.$2);
				if(RegExp.$1 === "-") val = -val;

				result.push({
					value: val,
					unit: RegExp.$3
				});
			};
			return result;
		};
	};

	//计算公历两个日期之差
	var _solarDiff = function(left, right, interval) {
		var span = left.getTime() - right.getTime(); //相差毫秒
		switch (interval) {
			case "y": return parseInt(left.getFullYear() - right.getFullYear());
			case "M": return parseInt((left.getFullYear() - right.getFullYear()) * 12 + (left.getMonth() - right.getMonth()));
			case "d": return Math.ceil(span / 1000 / 60 / 60 / 24);
			case "w": return Math.floor(span / 1000 / 60 / 60 / 24 / 7);
			case "h": return Math.floor(span / 1000 / 60 / 60);
			case "m": return Math.floor(span / 1000 / 60);
			case "s": return Math.floor(span / 1000);
			case "ms": return parseInt(span);
		}
	}

	//_solarAdd(date, '5d-6m');
	var _solarAdd = function(date, expr){

	};

	/*
	 找到农历
	 isPerYear，是否为农历前一年的对应数据
	 */
	var _findLunar = function(solar, index, minMonth, maxMonth, isPreYear) {
		//取得映射的数据
		var mapping = MAPPING[index];
		if (!mapping) return false;

		var year = solar.getFullYear()
			, month = solar.getMonth() + 1
			, date = solar.getDate();
		var lunarYear = year;
		var lunarMonth, find, solarMonth;

		//查找农历
		for (var i = mapping.length - 1; i > 0; i--) {
			lunarMonth = i;
			//取对应的农历月与天
			var segMonth = Number(mapping[i].substring(0, 2));
			var segDay = Number(mapping[i].substring(2, 4));

			solarMonth = isPreYear && segMonth > 12 ? segMonth - 12 : segMonth;
			find = solarMonth < month || (solarMonth == month && segDay <= date) ||
				((segMonth <= minMonth || segMonth >= maxMonth) && isPreYear);
			if ((solarMonth == 12 && solarMonth > month && i == 1)) {
				find = true;
				year--;
			};
			if (find) break;
		}

		//如果找到，则赋值
		if(!find) return false;
		//取前一年
		if (isPreYear && segMonth == 12) year = year - 1;
		lunarYear = isPreYear ? lunarYear - 1 : lunarYear;
		return {
			year: year,
			month: solarMonth,
			day: segDay,
			lunarYear: lunarYear,
			lunarMonth: lunarMonth,
			leapMonth: mapping[0]			//闰月
		};
	};

	//日期累加
	var _dateAdd = function(lunar, value, unit){
		if(unit == 'M'){
			return _chineseLunar.monthAdd(lunar, value);
		}else{
			//转换为阳历，计算完再转为农历
			var solar = _chineseLunar.lunarToSolar(lunar);
			solar = _solarAdd(solar, value, unit);
			return _chineseLunar.solarToLunar(solar);
		};
	};

	/*
	 农历相加
	 */
	_chineseLunar.dateAdd = function(lunar, expr) {
		//分析表达式
		var list = _expression(expr);

		for(var i = 0; i < list.length; i ++){
			lunar = _dateAdd(lunar, list[i]);
		};
		return lunar;
	};

	/*
	 计算两个农历时间的差值，主要计算月份之间的差，其它和公历是一样的
	 */
	_chineseLunar.dateDiff = function(lunar1, lunar2, expr) {
		//计算农历月份差值
		if(expr == "M"){
			return _chineseLunar.monthDiff(lunar1, lunar2);
		};

		//先转成公历，除了月份，其它的都可以按公历计算
		var solar1 = _chineseLunar.lunarToSolar(lunar1);
		var solar2 = _chineseLunar.lunarToSolar(lunar2);
		//再把农历转到公历
		return _solarDiff(solar2, solar1, expr);
	};

	/*
	 农历月份相加
	 */
	_chineseLunar.monthAdd = function(lunar, inc) {
		//如果是Date，则转换为农历
		if (lunar instanceof Date) lunar = _chineseLunar.solarToLunar(lunar);
		if (inc == 0) return lunar;

		var year = lunar.year, count;
		var month = lunar.month;
		if(lunar.leap ||
			(lunar.leapMonth > 0 && lunar.month > lunar.leapMonth)) month++;

		var run = true;
		do {
			//计算当前年有多少个月
			count = _chineseLunar.monthsOfYear(year);
			inc = inc + month - count;
			if (inc <= 0) {
				run = false;
				month = year == lunar.year ? count + inc : count + inc - month;
			}else {
				year++;
				month = 1;
			}
		} while (run);

		//获取最后的结果年的闰月是哪一个月
		var leapMonth = _chineseLunar.leapMonthOfYear(year);
		var leap = false;
		//如果闰月大于农历月，则月份减1
		if (leapMonth > 0 && month > leapMonth) {
			month--;
			//如果减完后月份和闰月相等，表示是闰月
			leap = month == leapMonth;
		}

		return {
			year: year,
			month: month,
			leap: leap,
			leapMonth: leapMonth
		};
	};

	/*
	 * 返回两段日期的农历差了多少个月，因为有闰月，所以和公历不一样
	 * date1和date2允许为公历
	 */
	_chineseLunar.monthDiff = function(lunar1, lunar2) {
		//如果是公历的日期格式，则转换为农历
		var count = 0;

		//如果数据类型是日期，则转换为农历
		if (lunar1 instanceof Date) lunar1 = _chineseLunar.solarToLunar(lunar1);
		if (lunar2 instanceof Date) lunar2 = _chineseLunar.solarToLunar(lunar2);

		//两个日期是同一年
		if (lunar1.year == lunar2.year) {
			count = lunar2.month - lunar1.month;
			//中间有闰月的存在，计数器加一
			if (lunar1.leapMonth >= lunar1.month && lunar1.leapMonth <= lunar2.month) count++;
		} else {
			//计算首年，如果当前的闰月大于当前月，或者当前年有闰月且当前月等于闰月，但当前月又不是闰月，则要多添加一个月
			count = 12;
			if(lunar1.leapMonth > lunar1.month ||
				(lunar1.leapMonth == lunar1.month && !lunar1.isLeaMonth)) count += 1;
			count  -= lunar1.month;

			//计算两年之间中间的年月份
			var year = lunar1.year + 1;
			for (var i = year; i < lunar2.year; i++) {
				count += _chineseLunar.monthsOfYear(year++);
			}
			//计算最后一年
			count += lunar2.month;
			if (lunar2.isLeapMonth || lunar2.month < lunar2.leapMonth) count++;
		};
		return count;
	};

	/*
	 * 计算某年某月一个有多少天
	 * daysOfMonth({}) 或者 daysOfMonth(year, month, leap);
	 */
	_chineseLunar.daysOfMonth = function(year, month, leap) {
		if (typeof (year) == "object") {
			month = year.month;
			leap = year.leap;
			year = year.year;
		};

		var date1 = _chineseLunar.lunarToSolar(year, month, 1, leap);
		var leapMonth = _chineseLunar.leapMonthOfYear(year);
		if (leapMonth == month && !leap) {
			//如果是闰月和当前一月一至，且当前月不是闰月，说明下一个月是闰月，例如2009年5月，这一年闰5月，如果传过来的不是闰月，那么下一个月就是闰月
			leap = true;
		} else if (month == 12) {
			//农历的最后一个月
			year++;
			month = 1;
		} else {
			leap = false;
			month++;
		};

		var date2 = _chineseLunar.lunarToSolar(year, month, 1, leap);
		return _chineseLunar.dateDiff(date2, date1, "d");

	};

	//获取农历某一年有多少个月
	_chineseLunar.monthsOfYear = function(year) {
		return MAPPING[year - MINYEAR].length - 1;
	};

	//获取农历某年的闰月是几月，
	_chineseLunar.leapMonthOfYear = function(year) {
		var info = MAPPING[year - MINYEAR];
		return info ? info[0] : 0;
	};

	/*
	 农历转阳历
	 lunarToSolar({})，或者lunarToSolar(year, month, day, leap)
	 */
	_chineseLunar.lunarToSolar = function(year, month, day, leap) {
		var arg0 = arguments[0];

		//第一个参数是对象
		if (typeof (arg0) == "object" && arguments.length == 1) {
			year = arg0.year;
			month = arg0.month;
			day = arg0.day;
			leap = arg0.leap;
		};

		//根据偏移量取得映射数据
		var offset = year - MINYEAR;
		//所查询的日期超出范围
		if (offset < 0 || offset > MAPPING.length){
			throw new Error('Specified date range is invalid.');
		};

		//取得润月是哪一个月
		var leapMonth = MAPPING[offset][0];
		//如果isLeap=true，并且当前月份就是闰月，或者本月有闰月，且当前月份大于闰月，则月份需要加1
		if ((leap && month == leapMonth) ||
			(leapMonth > 0 && month > leapMonth)){
			month += 1;
		};

		//取出对应到某个月的片断
		var segment = MAPPING[offset][month]; 		//农历第一天对应公历的具体天
		var mapMonth = Number(segment.substring(0, 2))
		var mapDate = Number(segment.substring(2, 4));

		if (mapMonth > 12) {
			year += 1;
			mapMonth -= 12;
		};

		var solar = new Date(year, mapMonth - 1, mapDate);
		var time = solar.getTime() + ((day - 1) * 24 * 60 * 60 * 1000);
		return new Date(time);
	};

	/*
	 公历转农历
	 1.查找对应农历初一是哪一天
	 2.将农历初一转换为公历
	 3.计入偏移量
	 */
	_chineseLunar.solarToLunar = function(solar, format) {
		var offset = solar.getFullYear() - MINYEAR;
		//超出范围
		if(offset <= 0 || offset >= MAPPING.length){
			throw new Error('Specified date range is invalid.');
		};

		//查找范围内的农历数据
		var data = _findLunar(solar, offset, 0, 13, false);
		//如果没有找到，则找前一年的，因为农历在公历之前，并且不会超过一年，查一年就可以了
		data = data || _findLunar(solar, offset - 1, 12, 99, true);

		//还是没有找到，表示超出范围
		if (!data) return false;

		//农历初一对应公历的哪一天
		var firstDay = new Date(data.year, data.month - 1, data.day);
		var day = _solarDiff(solar, firstDay, "d") + 1;

		//返回的农历结果
		var result = {
			leap: data.leapMonth > 0 && data.leapMonth + 1 == data.lunarMonth,
			year: data.lunarYear,
			month: data.leapMonth > 0 && data.lunarMonth > data.leapMonth ? data.lunarMonth - 1 : data.lunarMonth,
			day: day,
			leapMonth: data.leapMonth
		};

		//判断是否要格式化结果
		return (format && result) ? _chineseLunar.format(result, format) : result;
	};

	//获取中国传统干支的名称
	_chineseLunar.traditionalYearName = function(year) {
		var Gan = "甲乙丙丁戊己庚辛壬癸".split("");
		var Zhi = "子丑寅卯辰巳午未申酉戌亥".split("");
		year = year - MINYEAR + 36;
		return (Gan[year % 10] + Zhi[year % 12] + "年");
	};

	//获取中文的年
	_chineseLunar.yearName = function(year) {
		var cnStr = '〇,一,二,三,四,五,六,七,八,九'.split(",");
		var cYear = year.toString();
		var result = '';
		for (var i = 0; i < cYear.length; i++) {
			result += cnStr[parseInt(cYear.charAt(i))];
		}
		return result + '年';
	};

	//获取中国的生肖
	_chineseLunar.animalName = function(year) {
		return "鼠牛虎兔龙蛇马羊猴鸡狗猪".split("")[(year - 4) % 12];
	};

	//获取农历月的名称
	_chineseLunar.monthName = function(month, traditional, leap) {
		var monthName = "正,二,三,四,五,六,七,八,九,十,十一,十二".split(",");
		if (traditional) { monthName[11] = "腊" }
		return (leap ? "闰" : "") + monthName[month - 1] + "月";
	};

	//获取农历传统天的名称
	_chineseLunar.dayName = function(lunar) {
		switch (lunar) {
			case 10: return '初十';
			case 20: return '二十';
			case 30: return '三十';
			default: return ("初十廿卅".split("")[Math.floor(lunar / 10)] +
				"一二三四五六七八九十".split("")[(lunar - 1) % 10]) || lunar;
		}
	};

	//格式化农历日期，date是农历的日期
	_chineseLunar.format = function(lunar, expr) {
		return expr.replace(/[TAYyMmdD]/g, function(m, i) {
			switch (m) {
				//获取传统的年
				case "T": return _chineseLunar.traditionalYearName(lunar.year);
				//获取生肖
				case "A": return _chineseLunar.animalName(lunar.year);
				//获取中文的年
				case "Y": return _chineseLunar.yearName(lunar.year);
				//获取数字年
				case "y": return lunar.year;
				//获取月份
				case "m": return _chineseLunar.monthName(lunar.month, false, lunar.leap);
				//获取传统的月
				case "M": return _chineseLunar.monthName(lunar.month, true, lunar.leap);
				//获取天
				case "d": return _chineseLunar.dayName(lunar.day);
				//如果是初一，则显示月，而不是显示
				case "D":
					if(lunar.day == 1){
						return _chineseLunar.monthName(lunar.month, false, lunar.leap);
					}else{
						return _chineseLunar.dayName(lunar.day);
					};
			}
		});
	};


	if (typeof define === 'function'){
		define (function (){
			return _chineseLunar;
		});
	}else if(typeof exports === 'object'){
		module.exports = _chineseLunar;
	}else{
		window.chineseLunar = _chineseLunar;
	};
})();
