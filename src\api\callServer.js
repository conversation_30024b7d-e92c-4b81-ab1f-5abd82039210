import request from "@/utils/request";
import $qs from "qs";

//查询mcu列表
export function getMcuList(
  page,
  size,
  direction = "DESC",
  property = "name",
  nameToSearch = "",
  ipToSearch = "",
  systemStatus = "",
  runStatus=""
) {
  return request({
    url: `/webapp/mcu/server?page=${page}&size=${size}&direction=${direction}&property=${property}&nameToSearch=${nameToSearch}&ipToSearch=${ipToSearch}&systemStatus=${systemStatus}&runStatus=${runStatus}`,
    method: "get"
  });
}

//检查MCU是否被使用
export function checkMcuStatue(id) {
  return request({
    url: `/webapp/mcu/server/testCase?id=${id}`,
    method: "get"
  });
}

//删除mcu
export function deleteMcu(data) {
  return request({
    url: "/webapp/mcu/server",
    method: "post",
    headers: { "Content-Type": "application/json" },
    data
  });
}

//更新mcu
export function updateMcu(data) {
  return request({
    url: "/webapp/mcu/server",
    method: "put",
    headers: { "Content-Type": "application/json" },
    data
  });
}

//阀值查询
export function getMcuThreshold() {
  return request({
    url: "/webapp/mcu/threshold",
    method: "get"
  });
}

//阀值设置
export function setMcuThreshold(data) {
  return request({
    url: "/webapp/mcu/threshold",
    method: "put",
    headers: { "Content-Type": "application/json" },
    data
  });
}

// 分页查询mcu终端
export function getMcuTerminal({
  page,
  size,
  mac,
  terminalIp,
  terminalName,
  systemStatus,
  runStatus,
  direction,
  properties
}) {
  return request({
    url: `/webapp/diagnoseterminal?page=${page}&size=${size}&mac=${mac}&terminalIp=${terminalIp}&terminalName=${terminalName}&systemStatus=${systemStatus}&runStatus=${runStatus}&direction=${direction}&properties=${properties}`,
    method: "get"
  });
}

//分页查询测试用例
export function getTestTemplate(
  page,
  size,
  direction = "DESC",
  property = "name",
  name = "",
  status = ""
) {
  return request({
    url: `/webapp/mcu/test/case/all?page=${page}&size=${size}&direction=${direction}&property=${property}&nameToSearch=${name}&status=${status}`,
    method: "get"
  });
}

//删除测试用例
export function deleteTestTemplate(data) {
  return request({
    url: `/webapp/mcu/test/case/delete`,
    method: "post",
    headers: { "Content-Type": "application/json" },
    data
  });
}

//更新测试用例
export function updateTestTemplate(data) {
  return request({
    url: `/webapp/mcu/test/case`,
    method: "put",
    headers: { "Content-Type": "application/json" },
    data
  });
}

//检查测试用例状态
export function checkTestTemplateStatus(id) {
  return request({
    url: `/webapp/mcu/test/case/checkStatus?id=${id}`,
    method: "get"
  });
}

//检查测试用例中终端是否被使用
export function checkTestTerminalStatus(id) {
  return request({
    url: `/webapp/mcu/test/case/checkTerminal?id=${id}`,
    method: "get"
  });
}

export function getTestTemplateDetail(id) {
  return request({
    url: `/webapp/mcu/test/case?id=${id}`,
    method: "get"
  });
}

//添加测试用例
export function addTestTemplate(data) {
  return request({
    url: `/webapp/mcu/test/case`,
    method: "post",
    headers: { "Content-Type": "application/json" },
    data
  });
}

//查询测试用例的执行历史
export function getTestHistory(testCaseId, page, size) {
  return request({
    url: `/webapp/mcu/test/history?testCaseId=${testCaseId}&page=${page}&size=${size}`,
    method: "get"
  });
}

//查询测试用例的某次执行历史
export function getTestHistoryDetail(testHistoryId) {
  return request({
    url: `/webapp/mcu/test/history/findOne?testHistoryId=${testHistoryId}`,
    method: "get"
  });
}

//删除测试用例执行历史
export function deleteTestHistory(data) {
  return request({
    url: `/webapp/mcu/test/history/delete`,
    method: "post",
    headers: { "Content-Type": "application/json" },
    data
  });
}

//查询某一次测试最新的数据
export function getTestHistoryLatestData(testHistoryId) {
  return request({
    url: `/webapp/mcu/test/telemetry/overall/latest?testHistoryId=${testHistoryId}`,
    method: "get"
  });
}

//创建呼叫参数模板
export function addCallParamTemplate(data) {
  return request({
    url: `/webapp/mcu/template`,
    method: "post",
    headers: { "Content-Type": "application/json" },
    data
  });
}

//删除呼叫参数模板
export function deleteCallParamTemplate(data) {
  return request({
    url: `/webapp/mcu/template/delete`,
    method: "post",
    headers: { "Content-Type": "application/json" },
    data
  });
}

//更新呼叫参数模板
export function updateCallParamTemplate(data) {
  return request({
    url: `/webapp/mcu/template`,
    method: "put",
    headers: { "Content-Type": "application/json" },
    data
  });
}

//查询呼叫参数模板
export function getCallParamTemplate(
  page,
  size,
  direction = "DESC",
  property = "name",
  nameToSearch = ""
) {
  return request({
    url: `/webapp/mcu/template?page=${page}&size=${size}&direction=${direction}&property=${property}&nameToSearch=${nameToSearch}`,
    method: "get"
  });
}

//查询某一测试用例最新一次测试的最新一次数据
export function getTestHistoryLatestAnalyze(testCaseId) {
  return request({
    url: `/webapp/mcu/test/telemetry/overall/recentLatest?testCaseId=${testCaseId}`,
    method: "get"
  });
}

//查询某次测试中某一条有向弧的测试数据
export function getTestHistoryEdgeData(
  testHistoryId,
  testCaseEdgeId,
  startTime
) {
  return request({
    url: `/webapp/mcu/test/telemetry/edge?testHistoryId=${testHistoryId}&testCaseEdgeId=${testCaseEdgeId}&startTime=${startTime}`,
    method: "get"
  });
}

//查询对某次测试中某一条有向弧的测试数据的分析
export function getTestHistoryEdgeStatistic(
  testHistoryId,
  testCaseEdgeId,
  startTime
) {
  return request({
    url: `/webapp/mcu/test/telemetry/edge/statistic?testHistoryId=${testHistoryId}&testCaseEdgeId=${testCaseEdgeId}&startTime=${startTime}`,
    method: "get"
  });
}

/**
 * 查询某次测试中某一条有向弧的测试数据
 * @param testHistoryId 测试id
 * @param testCaseEdgeId 链路id
 * @param interval 采样间隔
 * @param startTime 开始时间
 * @param endTime 结束时间
 */
export function getTestHistoryEdgePeriodData(
  testHistoryId,
  testCaseEdgeId,
  interval,
  startTime,
  endTime
) {
  return request({
    url: `/webapp/mcu/test/telemetry/edge/period?testHistoryId=${testHistoryId}&testCaseEdgeId=${testCaseEdgeId}&interval=${interval}&startTime=${startTime}&endTime=${endTime}`,
    method: "get"
  });
}

//查询对某次测试分析
export function getTestHistoryStatistic(testHistoryId,direction,property) {
  return request({
    url: `/webapp/mcu/test/telemetry/overall/statistic?testHistoryId=${testHistoryId}&direction=${direction}&property=${property}`,
    method: "get"
  });
}

export function getTestGraph(testId, testHistoryId) {
  return request({
    url: `/webapp/mcu/test/case/graph?id=${testId}&testHistoryId=${testHistoryId}`,
    method: "get"
  });
}

/* 
* 某条链路异常记录查询
* testHistoryId 测试历史id
* edgeId 链路id
*/
export function getExceptEdge(testHistoryId,edgeId) {
  return request({
    url: `/webapp/mcu/test/telemetry/edge/exception?testHistoryId=${testHistoryId}&edgeId=${edgeId}`,
    method: "get"
  });
}

/* 
* 异常链路最近一条记录查询
* testHistoryId 测试历史id
*/
export function getLastExceptEdge(testHistoryId) {
  return request({
    url: `/webapp/mcu/test/telemetry/edge/exception/last?testHistoryId=${testHistoryId}`,
    method: "get"
  });
}

//测试用例阀值查询
export function getTestThreshold() {
  return request({
    url: "/webapp/mcu/telemetry/threshold",
    method: "get"
  });
}

//测试用例阀值设置
export function setTestThreshold(data) {
  return request({
    url: "/webapp/mcu/telemetry/threshold",
    method: "put",
    headers: { "Content-Type": "application/json" },
    data
  });
}

//获取某一次测试历史的调试信息
export function getHistoryDebug(historyId, page, size, levelToSearch = "") {
  return request({
    url: `/webapp/mcu/test/debug?historyId=${historyId}&page=${page}&size=${size}&levelToSearch=${levelToSearch}`,
    method: "get"
  });
}

//获取某一次测试历史的评分排名
export function getHistoryRank(historyId,direction) {
  return request({
    url: `/webapp/mcu/test/telemetry/edge/rank?testHistoryId=${historyId}&direction=${direction}`,
    method: "get"
  });
}

//获取某一次测试历史的平均抖动
export function getHistoryJitter(historyId) {
  return request({
    url: `/webapp/mcu/test/telemetry/edge/jitter?testHistoryId=${historyId}`,
    method: "get"
  });
}

//获取某一次测试历史的延迟
export function getHistoryDelay(historyId) {
  return request({
    url: `/webapp/mcu/test/telemetry/edge/delay?testHistoryId=${historyId}`,
    method: "get"
  });
}

//获取某一次测试历史的平均丢包率
export function getHistoryLossRate(historyId) {
  return request({
    url: `/webapp/mcu/test/telemetry/edge/loss?testHistoryId=${historyId}`,
    method: "get"
  });
}

//获取某一次测试历史的丢包
export function getHistoryLoss(historyId, interval) {
  return request({
    url: `/webapp/mcu/test/telemetry/edge/loss/line?testHistoryId=${historyId}&period=${interval}`,
    method: "get"
  });
}

//获取某一次测试历史的乱序
export function getHistoryDisorderRate(historyId, interval) {
  return request({
    url: `/webapp/mcu/test/telemetry/edge/disorder/line?testHistoryId=${historyId}&period=${interval}`,
    method: "get"
  });
}

//获取上一次测试历史
export function getPrevHistory(historyId) {
  return request({
    url: `webapp/mcu/test/history/previous?testHistoryId=${historyId}`,
    method: "get"
  });
}

//获取下一次测试历史
export function getNextHistory(historyId) {
  return request({
    url: `webapp/mcu/test/history/next?testHistoryId=${historyId}`,
    method: "get"
  });
}
