import i18n from '@/i18n';

export const SPACE_STATUS = [
  { label: '空闲', value: 'idle', icon: 'status@online' },
  { label: '占用', value: 'busy', icon: 'spaceCtrl@occupied' },
];

export const GATEWAY_STATUS = [
  { label: '在线', value: 'online', icon: 'status@online' },
  { label: '离线', value: 'offline', icon: 'status@offline' },
];

// 审核状态
export const APPROVE_STATUS = [
  { label: '已审核', value: true },
  { label: '未审核', value: false },
];

export const DEVICE_ONLINE_STATUS = [
  { label: '在线', value: 'online', icon: 'status@online' },
  { label: '离线', value: 'offline', icon: 'status@offline' },
];

export const DEVICE_ALARM_STATUS = [
  { label: '正常', value: 'normal', icon: 'status@online' },
  { label: '告警', value: 'alarm', icon: 'status@notInstalled' },
];

export const DEVICE_STATUS_ALL = [
  { label: '正常', value: 'normal', icon: 'status@online' },
  { label: '告警', value: 'alarm', icon: 'status@notInstalled' },
  { label: '离线', value: 'offline', icon: 'status@offline' },
];

//寿命检测 根据理论寿命时长,定义状态枚举 【全新】【良好 20%】【建议更换 80%】【超期运行 >100%】;
export const LIFE_STATUS = [
  { label: '全新', value: 'fresh' },
  { label: '良好', value: 'good' },
  { label: '建议更换', value: 'renew' },
  { label: '超期运行', value: 'overdue' },
];

//保修状态 【保修中】【即将到期】【过保】
export const WARRANTY_STATUS = [
  { label: '保修中', value: 'under' },
  { label: '即将到期', value: 'expiring' },
  { label: '过保', value: 'overdue' },
];

export const DEVICE_VALID_STATUS = [
  { label: '正常', value: 'valid', icon: 'status@online' },
  { label: '失效', value: 'invalid', icon: 'status@offline' },
];

export const DEVICE_TYPE = [
  { label: '温湿度传感器', value: '温湿度传感器' },
  { label: '窗帘电机', value: '窗帘电机' },
];
/**
 * 应用状态
 */
export const APK_STATUS = [
  { label: '未安装', value: 'notInstalled', icon: 'status@notInstalled' },
  { label: '未运行', value: 'notRunning', icon: 'status@stoped' },
  { label: '后台运行', value: 'backRunning', icon: 'status@backRunning' },
  { label: '前台运行', value: 'foreRunning', icon: 'status@foreRunning' },
];

export const PERMISSION_TYPE = [
  { label: '可读', value: 'R' },
  { label: '可写', value: 'W' },
  { label: '可读写', value: 'RW' },
];

export const ALARM_LEVEL = [
  {
    label: '普通',
    value: 'ERROR',
    color: 'rgba(255, 199, 58, 0.55)',
    type: 'info',
  },
  {
    label: '警告',
    value: 'WARN',
    color: 'rgb(230,162,60)',
    type: 'warning',
  },
  {
    label: '严重',
    value: 'FATAL',
    color: 'rgb(194, 31, 48)',
    type: 'danger',
  },
];

// 权限
export const AUTH = [
  {
    label: `${i18n.t('deptLabel')}空间`,
    value: 1,
  },
  {
    label: '公共空间',
    value: 2,
  },
];

// 设备操作历史的操作类型
export const OPERATION_TYPE = [
  {
    label: '设置属性',
    value: 0,
    type: '',
  },
  {
    label: '执行方法',
    value: 1,
    type: 'success',
  },
  {
    label: '执行场景模式',
    value: 2,
    type: 'info',
  },
  {
    label: '执行联动规则',
    value: 3,
    type: 'warning',
  },
  {
    label: '事件监测',
    value: 4,
    type: 'danger',
  },
];

//工单进度
export const WORKORDER_STATUS = [
  {
    label: '待受理',
    value: 0,
    color: 'rgb(255, 31, 0)',
  },
  {
    label: '受理中',
    value: 1,
    color: 'rgb(24, 144, 255)',
  },
  {
    label: '已完结',
    value: 2,
    color: '#41b349',
  },
];

export const VALUE_TYPE_ENUM = [
  { label: '整数', value: 'INTEGER' },
  { label: '小数', value: 'DECIMAL' },
  { label: '布尔值', value: 'BOOLEAN' },
  { label: '字符串', value: 'STRING' },
  { label: '文本枚举', value: 'ENUM' },
  { label: '数字枚举', value: 'NUMBER_ENUM' },
  { label: '对象', value: 'OBJECT' },
  { label: '数组', value: 'ARRAY' },
];

export const VALUE_TYPE_ENUM_MAP = {
  INTEGER: '整数',
  DECIMAL: '小数',
  BOOLEAN: '布尔值',
  STRING: '字符串',
  ENUM: '文本枚举',
  NUMBER_ENUM: '数字枚举',
  OBJECT: '对象',
  ARRAY: '数组',
};

export const OBJECT_MODEL = [
  { label: '属性', value: 'PROPERTY' },
  { label: '方法', value: 'METHOD' },
  { label: '事件', value: 'EVENT' },
];

export const PARAM_TYPE_ENUM = [
  { label: '整数', value: 'INTEGER' },
  { label: '小数', value: 'DECIMAL' },
  { label: '布尔值', value: 'BOOLEAN' },
  { label: '字符串', value: 'STRING' },
  { label: '文本枚举', value: 'ENUM' },
  { label: '数字枚举', value: 'NUMBER_ENUM' },
];

export const ARRAY_TYPE_ENUM = [
  { label: '整数', value: 'INTEGER' },
  { label: '小数', value: 'DECIMAL' },
  { label: '布尔值', value: 'BOOLEAN' },
  { label: '字符串', value: 'STRING' },
  { label: '对象', value: 'OBJECT' },
  { label: '数组', value: 'ARRAY' },
];

export const IOT_EVENT_ENUM = [
  { label: '设备上线', value: 'ONLINE', type: 'success' },
  { label: '设备下线', value: 'OFFLINE', type: 'info' },
  { label: '设备告警', value: 'ALARM', type: 'warning' },
  { label: '其他', value: 'OTHER', type: '' },
];

export const IOT_CONNECT_TYPE_ENUM = [
  { label: 'modbus485', value: 0 },
  { label: 'RS485', value: 1 },
  { label: 'RS232', value: 2 },
  { label: 'wifi', value: 3 },
  { label: 'Bluetooth', value: 4 },
  { label: 'Layer', value: 5 },
  { label: 'Zigbee', value: 6 },
  { label: 'TCP', value: 7 },
  { label: 'UDP', value: 8 },
  { label: 'PjLink', value: 9 },
  { label: 'infrared', value: 10 },
  { label: 'http', value: 11 },
  { label: 'sdk', value: 12 },
  { label: 'MQTT', value: 13 },
  { label: 'WS', value: 14 },
  { label: 'unknown', value: -1 },
];

//传输协议
export const IOT_TRANSFER_PROTOCOL = [
  { label: 'MQTT', value: 'MQTT' },
  { label: 'HTTP', value: 'HTTP' },
  { label: 'TCP', value: 'TCP' },
  { label: 'UDP', value: 'UDP' },
];

export const REPEAT_TYPE_ENUM = [
  // {
  //   label: '只执行一次',
  //   value: 'once',
  // },
  {
    label: '每日',
    value: 'everyDay',
  },
  {
    label: '每周',
    value: 'everyWeekDay',
  },
  {
    label: '每月',
    value: 'everyMonth',
  },
];

export const PERIOD_REPEAT_TYPE_ENUM = [
  {
    label: '每日',
    value: 'everyDay',
  },
  {
    label: '每周',
    value: 'everyWeekDay',
  },
  {
    label: '每月',
    value: 'everyMonth',
  },
];

export const VALID_PERIOD_TYPE_ENUM = [
  {
    label: '指定时间段',
    value: 'specificTimeRange',
  },
  {
    label: '全天',
    value: 'wholeDay',
  },
];

export const REPEAT_DAY_ENUM = [
  {
    label: '周一',
    value: 1,
  },
  {
    label: '周二',
    value: 2,
  },
  {
    label: '周三',
    value: 3,
  },
  {
    label: '周四',
    value: 4,
  },
  {
    label: '周五',
    value: 5,
  },
  {
    label: '周六',
    value: 6,
  },
  {
    label: '周日',
    value: 7,
  },
];

export const PROPERTY_OPT_ENUM = [
  {
    label: '等于',
    value: 'EQ',
    symbol: '=',
  },
  {
    label: '不等于',
    value: 'NE',
    symbol: '≠',
  },
  {
    label: '大于',
    value: 'GT',
    symbol: '>',
  },
  {
    label: '小于',
    value: 'LT',
    symbol: '≤',
  },
  {
    label: '大于等于',
    value: 'GE',
    symbol: '≥',
  },
  {
    label: '小于等于',
    value: 'LE',
    symbol: '<=',
  },
];
//非数字比较符
export const PROPERTY_OPT_ENUM_NOT_NUMBER = [
  {
    label: '等于',
    value: 'EQ',
    symbol: '=',
  },
  {
    label: '不等于',
    value: 'NE',
    symbol: '≠',
  },
];

export const SATISFY_CONDITION_ENUM = [
  {
    label: '满足任一',
    value: 'any',
  },
  {
    label: '满足全部',
    value: 'all',
  },
];

export const SEQUENCE_TYPE_ENUM = [
  {
    label: '并行',
    value: 'parallel',
  },
  {
    label: '串行',
    value: 'serial',
  },
];

export const ENABLED_ENUM = [
  {
    label: '已启用',
    value: true,
  },
  {
    label: '未启用',
    value: false,
  },
];

export const CONFIRMED_ENUM = [
  {
    label: '已确认',
    value: true,
  },
  {
    label: '未确认',
    value: false,
  },
];

export const ALARM_ENUM = [
  {
    label: '平台规则告警',
    value: 'platform_automation_alarm',
  },
  {
    label: '设备事件告警',
    value: 'device_alarm_event',
  }
  //{
  //  label: '中控离线告警',
  //  value: 'gateway_offline_alarm',
  //},
];

export const AIR_QUALITY_LEVEL = [
  {
    label: '优',
    value: 1,
    color: 'rgb(0, 255, 0)',
    backgroundColor: 'rgb(0, 255, 0,0.1)',
  },
  {
    label: '良',
    value: 2,
    color: 'rgb(255, 255, 0)',
    backgroundColor: 'rgb(255, 255, 0,0.1)',
  },
  {
    label: '轻度污染',
    value: 3,
    color: 'rgb(255, 165, 0)',
    backgroundColor: 'rgb(255, 165, 0,0.1)',
  },
  {
    label: '中度污染',
    value: 4,
    color: 'rgb(255, 0, 0)',
    backgroundColor: 'rgb(255, 0, 0,0.1)',
  },
  {
    label: '重度污染',
    value: 5,
    color: 'rgb(255, 0, 255)',
    backgroundColor: 'rgb(255, 0, 255,0.1)',
  },
  {
    label: '严重污染',
    value: 6,
    color: 'rgb(165, 42, 42)',
    backgroundColor: 'rgb(165, 42, 42,0.1)',
  },
];

export function getAirQualityLevel(pm1_0, pm2_5, pm10, tvoc, co2, hcho) {
  let pm1_0Quality = formatQuality('PM1_0', pm1_0);
  let pm2_5Quality = formatQuality('PM2_5', pm2_5);
  let pm10Quality = formatQuality('PM10', pm10);
  let tvocQuality = formatQuality('TVOC', tvoc);
  let co2Quality = formatQuality('CO2', co2);
  let hchoQuality = formatQuality('HCHO', hcho);
  let values = [pm1_0Quality, pm2_5Quality, pm10Quality, tvocQuality, co2Quality, hchoQuality];

  // 找到最大值
  let overallQuality = values.reduce((a, b) => (a > b ? a : b));

  return overallQuality;
}

export function formatQuality(name, val) {
  let quality = 1;

  // PM 1.0
  if (name == 'PM1_0') {
    if (val <= 17) {
      quality = 1;
    } else if (val > 17 && val <= 37) {
      quality = 2;
    } else if (val > 37 && val <= 57) {
      quality = 3;
    } else if (val > 57 && val <= 75) {
      quality = 4;
    } else if (val > 75 && val <= 150) {
      quality = 5;
    } else if (val > 150) {
      quality = 6;
    }
  }
  // PM 2.5
  else if (name == 'PM2_5') {
    if (val <= 35) {
      quality = 1;
    } else if (val > 35 && val <= 75) {
      quality = 2;
    } else if (val > 75 && val <= 115) {
      quality = 3;
    } else if (val > 115 && val <= 150) {
      quality = 4;
    } else if (val > 150 && val <= 250) {
      quality = 5;
    } else if (val > 250) {
      quality = 6;
    }
  }
  // PM 10
  else if (name == 'PM10') {
    if (val <= 50) {
      quality = 1;
    } else if (val > 50 && val <= 150) {
      quality = 2;
    } else if (val > 150 && val <= 250) {
      quality = 3;
    } else if (val > 250 && val <= 350) {
      quality = 4;
    } else if (val > 350 && val <= 420) {
      quality = 5;
    } else if (val > 420) {
      quality = 6;
    }
  }
  // TVOC 总挥发性有机物
  else if (name == 'TVOC') {
    if (val <= 130) {
      quality = 1;
    } else if (val > 130 && val <= 250) {
      quality = 2;
    } else if (val > 250 && val <= 500) {
      quality = 3;
    } else if (val > 500 && val <= 2500) {
      quality = 4;
    } else if (val > 2500 && val <= 20300) {
      quality = 5;
    } else if (val > 20300) {
      quality = 6;
    }
  }
  // 二氧化碳
  else if (name == 'CO2') {
    if (val <= 450) {
      quality = 1;
    } else if (val > 450 && val <= 1000) {
      quality = 2;
    } else if (val > 1000 && val <= 1500) {
      quality = 3;
    } else if (val > 1500 && val <= 2000) {
      quality = 4;
    } else if (val > 2000 && val <= 3000) {
      quality = 5;
    } else if (val > 3000) {
      quality = 6;
    }
  }
  // 甲醛
  else if (name == 'HCHO') {
    if (val <= 40) {
      quality = 1;
    } else if (val > 40 && val <= 65) {
      quality = 2;
    } else if (val > 65 && val <= 122) {
      quality = 3;
    } else if (val > 122 && val <= 325) {
      quality = 4;
    } else if (val > 325 && val <= 488) {
      quality = 5;
    } else if (val > 488) {
      quality = 6;
    }
  }

  return quality;
}

export const INSPECT_RECORD_STATUS_ENUM = [
  {
    label: '未开始',
    value: 'ready',
    type: '',
  },
  {
    label: '执行中',
    value: 'underway',
    type: 'warning',
  },
  {
    label: '已完成',
    value: 'finished',
    type: 'success',
  },
  {
    label: '超时',
    value: 'timeout',
    type: 'danger',
  },
];
