<template>
  <el-dialog :title="title" :visible.sync="visible" width="900px" :close-on-click-modal="false" @close="close">
    <div class="date-picker-wrap">
      <el-button
        type="primary"
        :size="size"
        icon="el-icon-plus"
        @click.native="viewEditInspectTemplate('add')"
        v-action:inspectTask|addInspectTaskTemplate
      >
        添加
      </el-button>
      <el-button
        :size="size"
        icon="el-icon-delete"
        @click.native="deleteInspectTemplate()"
        type="danger"
        v-action:inspectTask|deleteInspectTaskTemplate
      >
        删除
      </el-button>
      <el-input
        class="ml-10"
        placeholder="巡检模板名称"
        suffix-icon="el-icon-search"
        :size="size"
        style="width: 150px"
        v-model="filter.name"
        v-debounce="[
          (e) => {
            getInspectTemplate(e);
          },
        ]"
      />
      <el-input
        class="ml-10"
        placeholder="巡检模板描述"
        suffix-icon="el-icon-search"
        :size="size"
        style="width: 150px"
        v-model="filter.remark"
        v-debounce="[
          (e) => {
            getInspectTemplate(e);
          },
        ]"
      />
    </div>
    <el-table :data="inspectTemplateList" highlight-current-row height="500px" v-loading="loading" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="35" align="center"> </el-table-column>
      <el-table-column label="序号" width="70" align="center" type="index"> </el-table-column>
      <el-table-column prop="name" label="巡检模板名称" min-width="140" show-overflow-tooltip> </el-table-column>
      <el-table-column prop="remark" label="巡检模板描述" min-width="120" show-overflow-tooltip> </el-table-column>
      <el-table-column prop="creator" label="编辑人" min-width="100" show-overflow-tooltip> </el-table-column>
      <el-table-column prop="gmtModified" label="编辑时间" min-width="140" align="center" show-overflow-tooltip>
        <template slot-scope="{ row }">
          {{ row.gmtModified ? formatDate(row.gmtModified) : '--' }}
        </template>
      </el-table-column>
      <el-table-column prop="taskStatus" label="操作" min-width="100" show-overflow-tooltip>
        <template slot-scope="{ row }">
          <el-button @click="viewEditInspectTemplate('view', row.id)" type="primary" size="mini" plain>查看详情</el-button>
        </template>
      </el-table-column>
    </el-table>
  </el-dialog>
</template>

<script>
  import dlg from '@/mixins/dlg';
  import { getInspectTemplate, deleteInspectTemplate } from '@/api/iotControl';
  export default {
    props: {},
    mixins: [dlg],
    data() {
      return {
        size: 'small',
        filter: {
          name: '',
          remark: '',
        },
        inspectTemplateList: [],
        loading: false,
        multipleSelection: [],
      };
    },
    created() {
      this.getInspectTemplate();
    },
    methods: {
      getInspectTemplate() {
        this.loading = true;
        let params = {
          name: this.filter.name,
          remark: this.filter.remark,
        };
        getInspectTemplate(params).then(({ data }) => {
          this.inspectTemplateList = data;
          this.loading = false;
        });
      },
      handleSelectionChange(selection) {
        this.multipleSelection = selection;
      },
      viewEditInspectTemplate(type, id) {
        this.$emit('handleViewTemplate', type, id);
        this.close();
      },
      deleteInspectTemplate() {
        deleteInspectTemplate(this.multipleSelection.map((item) => item.id)).then(() => {
          this.getInspectTemplate();
        });
      },
      formatDate(inputDate) {
        const date = new Date(inputDate);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        // 格式化为 MM/dd hh:mm
        return `${year}-${month}-${day} ${hours}:${minutes}`;
      },
    },
    computed: {
      title() {
        return '巡检模板';
      },
    },
  };
</script>

<style scoped lang="scss"></style>
