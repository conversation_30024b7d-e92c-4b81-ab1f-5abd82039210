<!-- 机构级联选择器 -->
<!-- 懒加载 -->
<!-- 回显 -->
<!-- 搜索 -->

<template>
  <!--      -->
  <el-cascader
    :placeholder="`请选择或搜索${$t('deptLabel')}`"
    ref="cascader"
    :show-all-levels="false"
    :clearable="bShowClearable"
    @change="handleChangeDivision"
    v-model="dept"
    :size="size"
    :props="props"
    filterable
    :options="deptList"
    :before-filter="filterDept"
    @input.native="doSearchDept"
    @visible-change="handleCascaderVisibleChange"
  >
    <div slot="empty" v-loading="loading"></div>
  </el-cascader>
  <!-- :filter-method="doSearchDept" -->
  <!-- <el-cascader :props="props"></el-cascader> -->
</template>

<script>
import {
  getDepartementList,
  getOwnDepartementList,
  getOwnCompanyDeptList,
  getCompanyDept,
  getOwnCompanyDept,
  getMyDept,
  getChildrenDept,
  getDeptByName,
  getMyCompany,
  getDepartementInfo,
} from "@/api/system";
import debounce from "lodash/debounce";

export default {
  props: {
    deptValue: {
      type: String | Number,
    },
    bInit: {
      type: Boolean,
      default: false,
    },
    bShowClearable: {
      type: Boolean,
      default: true,
    },
    //查询机构时根机构是否包含所有子级机构,true为不包含
    bOwn: {
      type: Boolean,
      default: false,
    },
    //查询机构时是否从公司开始查询，true为从公司开始查询
    bCompany: {
      type: Boolean,
      default: false,
    },
    size: {
      type: String,
      default: "small",
    },
    // 当前查询的根id
    rootId: {
      type: String | Number,
    },
  },
  data() {
    let that = this;
    return {
      deptList: [],
      loading: false,
      rootDeptId: null,
      props: {
        value: "id",
        label: "deptName",
        checkStrictly: true,
        expandTrigger: "hover",
        lazy: true,
        lazyLoad(node, resolve) {
          const { level } = node;
          if (level === 0) {
            that.initDept((data) => {
              resolve(data);
            });
          }
          if (level > 0) {
            if (!node.data || !node.data.id) {
              resolve([]);
            }
            getChildrenDept(node.data.id).then(({ data }) => {
              let list = data.map((item) => ({
                id: item.id,
                deptName: item.name,
                leaf: item.leaf,
              }));
              resolve(list);
            });
          }
        },
      },
    };
  },
  computed: {
    dept: {
      get() {
        return this.deptValue;
      },
      set(newValue) {
        this.$emit("update:deptValue", newValue);
        // 通知父组件，让父组件在数据改变时进行一些其他操作
        this.$emit('change',newValue)
      },
    },
    bRoot() {
      return sessionStorage.getItem("bRoot") === "true";
    },
  },
  created() {
    let that = this;
    this.debounceGetDeptByName = debounce(function (value) {
      that.getDeptByName(value);
    }, 1 * 1000);
  },
  methods: {
    /**
     * 初始化机构树
     */
    initDept(cb) {
      this.loading = true;
      let fn = null,
        param = null;
      // 有指定的根id，从根id开始显示
      if (this.rootId) {
        fn = getDepartementInfo;
        param = this.rootId;
      } else if (this.bCompany) {
        fn = getMyCompany;
      } else {
        fn = getMyDept;
      }
      fn(param)
        .then(({ data }) => {
          // 保存当前树的根机构id
          this.rootDeptId = data.id;
          let res = { deptName: data.name || data.deptName, id: data.id };
          //根机构仅显示根机构，不显示子级
          if (this.bRoot && this.bOwn) {
            console.log("不显示子级");
            cb([{ ...res, leaf: true }]);
          } else {
            cb([{ ...res, leaf: false }]);
          }
          if(this.bInit){
            this.dept = [data.id]
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    /**
     * 根据机构名称搜索机构
     */
    doSearchDept(el) {
      this.debounceGetDeptByName(el.target.value);
    },
    /**
     * 筛选之前的钩子，参数为输入的值，若返回 false 或者返回 Promise 且被 reject，则停止筛选。
     * 因要搜索出来要展示的是：可供展开和多选的下拉框，所以只能将其返回false,才可隐藏起它本来的下拉框。
     */
    filterDept() {
      return false;
    },
    getDeptByName(name) {
      if (!name) {
        this.props.lazy = true;
        this.initDept((data) => {
          this.$refs.cascader.$refs.panel.activePath = [];
          this.deptList = data;
        });
        return;
      }
      getDeptByName(this.rootDeptId, name).then(({ data }) => {
        //关闭el-cascader的懒加载
        this.props.lazy = false;
        this.$refs.cascader.$refs.panel.activePath = [];
        this.deptList = data;
      });
    },
    handleCascaderVisibleChange(visible) {
      // true下拉框展开 false下拉框收起  展开时清掉上次搜索结果 恢复如初
      if (visible && !this.props.lazy) {
        this.props.lazy = true;
        this.$refs.cascader.$refs.panel.activePath = [];
        this.initDept((data) => {
          this.deptList = data;
        });
      }
    },
    // 获取该角色下的所有机构列表
    // getDept() {
    //   this.loading = true;
    //   //bCompany为true，bOwn为true,获取当前用户所在公司的所有机构,根机构只返回根机构
    //   //bOwn为true,获取当前用户所在部门及子部门树，根机构只返回根机构
    //   //bOwn为false,获取当前用户所在部门及子部门树，根机构获取所有机构列表
    //   let fn = null,
    //     params = null;
    //   if (this.bCompany && this.bOwn) {
    //     fn = getOwnCompanyDeptList;
    //   } else if (this.bCompany && !this.bOwn) {
    //     // 根机构获取所有机构列表
    //     // 非根机构获取公司的所有机构
    //     fn = getCompanyDept;
    //   } else if (!this.bCompany && this.bOwn) {
    //     fn = getOwnDepartementList;
    //   } else {
    //     fn = getDepartementList;
    //   }

    //   //查询从指定机构开始的下级机构（包括子级）
    //   if (!this.bInit && this.deptValue) {
    //     fn = getOwnCompanyDept;
    //     params = this.deptValue;

    //     // if(sessionStorage.getItem('bRoot')=='true'){
    //     //   根机构用查询公司的接口
    //     //   fn = getOwnCompanyDept;
    //     //   params = this.deptValue;
    //     // }else{
    //     //   非根机构用查询当前所属机构的接口
    //     //   fn = getDepartementList
    //     // }
    //   }

    //   fn(params)
    //     .then((response) => {
    //       this.deptList = response.data;
    //       this.bInit &&
    //         !this.deptValue &&
    //         response.data.length &&
    //         this.$emit("update:deptValue", response.data[0].id);
    //     })
    //     .finally(() => {
    //       this.loading = false;
    //     });
    // },
    /**
     *
     */
    handleChangeDivision() {
      this.$refs.cascader.dropDownVisible = false;
    },
  },
};
</script>
<style lang="scss" scoped>
</style>
