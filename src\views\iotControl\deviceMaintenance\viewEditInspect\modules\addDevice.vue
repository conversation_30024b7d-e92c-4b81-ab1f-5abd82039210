<template>
  <el-dialog :title="title" :visible.sync="visible" width="1200px" :close-on-click-modal="false" @close="close">
    <el-cascader
      v-model="search.classification"
      :options="deviceTypeOption"
      :props="{ checkStrictly: true }"
      @change="getDeviceList"
      size="small"
      class="mr-10"
      style="width: 150px"
      clearable
      filterable
      placeholder="设备类型"
    >
    </el-cascader>
    <el-select
      v-model="search.gatewayId"
      class="mr-10"
      size="small"
      placeholder="所属智慧中控"
      @change="
        {
          getDeviceList();
        }
      "
      clearable
      style="width: 150px"
    >
      <el-option v-for="item in gatewayList" :key="item.mac" :label="item.name" :value="item.mac"> </el-option>
    </el-select>
    <el-select
      v-model="search.spaceId"
      class="mr-10"
      size="small"
      placeholder="所属空间"
      @change="
        {
          getDeviceList();
        }
      "
      clearable
      style="width: 150px"
    >
      <el-option v-for="item in spaceList" :key="item.id" :label="item.spaceName" :value="item.id"> </el-option>
    </el-select>
    <el-input
      placeholder="设备名称"
      suffix-icon="el-icon-search"
      v-model="search.name"
      v-debounce="[
        (e) => {
          getDeviceList();
        },
      ]"
      size="small"
      style="width: 150px"
    ></el-input>
    <el-table :data="deviceList" ref="table" @selection-change="handleSelectionChange" height="450px" v-loading="loading">
      <el-table-column type="selection" width="35" align="center" :selectable="checkSelectable"> </el-table-column>
      <el-table-column prop="deviceName" label="设备名称" :show-overflow-tooltip="true" min-width="150"> </el-table-column>
      <el-table-column prop="spaceName" label="所属空间" :show-overflow-tooltip="true" min-width="150"> </el-table-column>
      <el-table-column prop="deviceTypeName" label="设备类型" min-width="120" :show-overflow-tooltip="true">
        <template slot-scope="{ row }"> {{ row.deviceTypeName }}/{{ row.devSubTypeName }} </template>
      </el-table-column>
      <el-table-column prop="model" label="设备型号" min-width="140" :show-overflow-tooltip="true"> </el-table-column>
      <el-table-column prop="deviceId" label="设备ID" min-width="140" :show-overflow-tooltip="true"> </el-table-column>
    </el-table>

    <div slot="footer" class="dialog-footer">
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="handleOk">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import dlg from '@/mixins/dlg';
  import { deviceGetAllList, getNewDeviceTypes, getAllGatewayList, getAllGatewaySpace } from '@/api/iotControl';
  export default {
    props: {
      selectedDevices: {
        type: Array,
        default: () => [],
      },
    },
    mixins: [dlg],
    data() {
      return {
        title: '选择设备',
        deviceList: [],
        //加载状态
        loading: false,
        //选中设备列表
        multipleSelection: [],
        deviceTypeOption: [],
        search: {
          classification: [],
          name: '',
          gatewayId: '',
          spaceId: '',
        },
        gatewayList: [],
        spaceList: [],
      };
    },
    created() {
      this.getSpaceList();
      this.getGatewayList();
      this.getDeviceList();
      getNewDeviceTypes().then(({ data }) => {
        this.deviceTypeOption = data;
      });
    },
    methods: {
      getGatewayList() {
        getAllGatewayList({
          approved: true,
        }).then((response) => {
          this.gatewayList = response.data;
        });
      },
      getSpaceList() {
        getAllGatewaySpace().then((response) => {
          this.spaceList = response.data;
        });
      },
      /**
       * 获取设备列表
       */
      getDeviceList() {
        this.loading = true;
        deviceGetAllList({
          spaceId: this.search.spaceId,
          gatewayId: this.search.gatewayId,
          deviceName: this.search.name,
          deviceType: this.search.classification.length > 0 ? this.search.classification[0] : '',
          devSubType: this.search.classification.length > 1 ? this.search.classification[1] : '',
        })
          .then((res) => {
            this.deviceList = res.data;
            this.setSelectedRows();
          })
          .finally(() => {
            this.loading = false;
          });
      },
      // 设置已选行
      setSelectedRows() {
        //注意：这里需要使用nextTick，否则无法选中
        this.$nextTick(() => {
          this.deviceList.forEach((row) => {
            if (this.selectedDevices.some((device) => device.deviceId === row.deviceId)) {
              this.$refs.table.toggleRowSelection(row, true);
            }
          });
        });
      },
      /**
       * table选中改变
       */
      handleSelectionChange(val) {
        this.multipleSelection = val;
      },
      /**
       * 确定
       */
      handleOk() {
        this.$emit('handleSelect', this.multipleSelection);
        this.close();
      },
      // 检查是否可选
      checkSelectable(row) {
        // 如果已经在已选设备列表中，则不可选
        return !this.selectedDevices.some((device) => device.deviceId === row.deviceId);
      },
    },
  };
</script>

<style scoped lang="scss"></style>
