let page = {
  width: 350,
  height: 180,
  props: [
    {
      type: "el-color-picker",
      title: "文字颜色",
      field: "color",
      value: "#FFFFFF",
      props: {
        predefine: [
          "#FFFFFF",
          "#000000",
          "#C00000",
          "#FF0000",
          "#FFC000",
          "#FFFF00",
          "#92D050",
          "#00B050",
          "#00B0F0",
          "#0070C0",
          "#002060",
          "#7030A0",
        ],
      },
    },
    {
      type: "el-color-picker",
      title: "背景颜色",
      field: "backgroundColor",
      value: "#0070C0",
      props: {
        showAlpha: true,
        predefine: [
          "#FFFFFF",
          "#000000",
          "#C00000",
          "#FF0000",
          "#FFC000",
          "#FFFF00",
          "#92D050",
          "#00B050",
          "#00B0F0",
          "#0070C0",
          "#002060",
          "#7030A0",
        ],
      },
    },
    {
      type: "el-color-picker",
      title: "有会背景",
      field: "backgroundActiveColor",
      value: "#D71418",
      props: {
        showAlpha: true,
        predefine: [
          "#FFFFFF",
          "#000000",
          "#C00000",
          "#FF0000",
          "#FFC000",
          "#FFFF00",
          "#92D050",
          "#00B050",
          "#00B0F0",
          "#0070C0",
          "#002060",
          "#7030A0",
        ],
      },
    },
    {
      type: "el-slider",
      title: "内容占比",
      field: "width",
      value: 100,
      props: {
        min: 50,
        max: 100,
        step: 10,
      },
    },
    {
      type: "el-switch",
      title: "签到",
      field: "bShowSign",
      value: true,
    },
    {
      type: "el-switch",
      title: "预订人",
      field: "bShowReserver",
      value: true,
    },
    {
      type: "el-switch",
      title: "状态灯",
      field: "statusLight",
      value: true,
      control: [
        {
          value: true,
          rule: ['statusLightBrightness', 'freeLightColor', 'meetingLightColor', 'signLightColor', 'statusLightEffect'],
        },
      ],
    },
    // {
    //   type: "el-slider",
    //   title: "灯光亮度",
    //   field: "statusLightBrightness",
    //   value: 100,
    //   props: {
    //     min: 10,
    //     max: 100,
    //     step: 10,
    //   },
    // },
    {
      type: "colorSelect",
      title: "空闲灯光",
      field: "freeLightColor",
      value: "3",
    },
    {
      type: "colorSelect",
      title: "会中灯光",
      field: "meetingLightColor",
      value: "5",
    },
    {
      type: "colorSelect",
      title: "签到灯光",
      field: "signLightColor",
      value: "7",
    },
    // {
    //   type: "select",
    //   title: "灯光效果",
    //   field: "statusLightEffect",
    //   value: "1",
    //   props: {
    //     clearable: false,
    //   },
    //   options: [
    //     { value: "1", label: "无" },
    //     { value: "2", label: "呼吸" },
    //     { value: "3", label: "闪烁" },
    //   ],
    // },
  ],
};
