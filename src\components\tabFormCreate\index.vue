<template>
  <div class="tab-form-create">
    <el-tabs v-model="activeName" :before-leave="beforeChangeTab">
      <el-tab-pane :label="customForm.length > 1 ? item.group : ''" :name="index + ''" v-for="(item, index) in customForm">
        <form-create
          v-model="fApis[index]"
          :rule="item.params"
          :option="{
            submitBtn: false,
            form: { labelWidth: labelWidth + 'px', labelPosition: 'left' },
          }"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
  //用于生成自定义表单并加载自定义组件
  import formCreate from '@form-create/element-ui';
  import _ from 'lodash';
  import { getFormValue } from './utils';
  import { isObject } from '@/utils/validate';
  import customUpload from './customUpload';
  formCreate.component('customUpload', customUpload);
  import customScreen from './screen/index';
  formCreate.component('customScreen', customScreen);
  import customScreenColor from './screenColor/index';
  formCreate.component('customScreenColor', customScreenColor);
  import customColorSelect from './colorSelect/index';
  formCreate.component('customColorSelect', customColorSelect);
  import customSlider from './slider/index';
  formCreate.component('customSlider', customSlider);
  import customAutoComplete from './customAutoComplete';
  formCreate.component('customAutoComplete', customAutoComplete);
  import checkboxAll from './checkboxAll';
  formCreate.component('checkboxAll', checkboxAll);
  import deptCascader from './deptCascader'
  formCreate.component('departmentCascader', deptCascader);
  import selectOptionsProperty from './effect/selectOptionsProperty';
  formCreate.register(selectOptionsProperty);
  import clear from './effect/clear';
  formCreate.register(clear);
  import specifiedClear from './effect/specifiedClear';
  formCreate.register(specifiedClear);
  

  export default {
    components: {
      customUpload,
      customScreen,
    },
    props: {
      form: {
        default: () => [],
        type: Array,
      },
      labelWidth: {
        default: 163,
        type: Number,
      },
    },
    components: {
      formCreate: formCreate.$form(),
    },
    data() {
      return {
        activeName: '0',
        fApis: [],
        customForm: [],
      };
    },
    watch: {
      form: {
        immediate: true,
        handler(newVal) {
          let json = _.cloneDeep(newVal);
          this.getFormatJson(json);
        },
      },
    },
    methods: {
      //切换标签页前进行表单验证，验证成功才切换tab页
      beforeChangeTab(active, old) {
        let bValid = true;
        // this.validateForm(old, (valid, fail) => {
        //   bValid = valid;
        // });
        return bValid;
      },
      // 切换tab页
      switchTab(index) {
        if (index < 0 || index > this.customForm.length - 1) {
          return;
        }
        this.activeName = index + '';
      },
      reloadForms(length) {
        if (!this.fApis.length) {
          return;
        }
        this.fApis = this.fApis.filter((item, index) => {
          if (index + 1 > length) {
            // console.log("destroy");
            item.destroy && item.destroy();
            return false;
          }
          item.reload && item.reload();
          return true;
        });
      },
      async validateForms() {
        let bValid = true;
        let index = 0;
        for (let i = 0; i < this.fApis.length; i++, index++) {
          bValid = await this.validateForm(i);
          if (!bValid) {
            break;
          }
        }
        return { valid: bValid, index };
      },
      validateForm(index) {
        if (!this.fApis.length || !this.fApis[index] || !this.fApis[index].validate) {
          return new Promise((resolve, reject) => {
            resolve(true);
          });
        }
        return new Promise((resolve, reject) => {
          this.fApis[index].validate((valid, fail) => {
            if (valid) {
              resolve(true);
            } else {
              resolve(false);
            }
          });
        });
      },
      // 格式化后端返回的json
      getFormatJson(json) {
        this.reloadForms(json.length);
        this.customForm = json.map((item, index) => {
          let params = item.params.map((param) => {
            return this.formatParam(param);
          });
          let newParams = this.handleControl(params);
          return { group: item.group, params: newParams };
        });
        this.activeName = '0';
      },
      // 组件数据转换为符合form-create的格式
      formatParam(param, index) {
        let obj = {
          ...param,
          type: param.widget,
        };
        if (param.validate && isObject(param.validate)) {
          // 如果正则为空，删除正则字段，否则验证时会判断值是否为字符串
          if (!param.validate.pattern) {
            delete param.validate.pattern;
          } else {
            param.validate.pattern = new RegExp(param.validate.pattern);
          }
          obj.validate = [param.validate];
        } else {
          obj.validate = [];
        }
        if (param.tips) {
          obj.suffix = {
            type: 'el-tooltip',
            children: [{ type: 'i', class: 'el-icon-question' }],
            props: { content: param.tips, effect: 'light', placement: 'top' },
          };
        }
        if (!obj.props) {
          obj.props = {};
        } else if (obj.props.type === 'password') {
          obj.props.showPassword = true;
          if (obj.widget === 'input') {
            obj.props.autocomplete = 'new-password';
          }
        }

        obj.props.options = param.options;
        return obj;
      },
      /**
       * 数组转树形结构
       * @param list 源数组
       * @param tree 树
       * @param field 组件的唯一值
       */
      listToTree(list, tree, field) {
        list.forEach((item) => {
          // 已经被处理过的数据不再处理
          // 否则控件显隐的控制会出现问题
          if (item.control) {
            tree.push(item);
            return;
          }
          if (!item.field) {
            return;
          }
          // 判断是否为父级菜单
          if (((!item.enable || !Object.keys(item.enable).length) && !field) || (item.enable && item.enable.field === field)) {
            const child = {
              ...item,
              control: [],
            };
            // 迭代 list， 找到当前符合条件的子组件
            this.listToTree(list, child.control, child.field);
            // 删掉不存在 children 值的属性
            // if (child.control.length <= 0) {
            //   delete child.control;
            // }
            // 加入到树中
            if (child.enable && child.enable.field === field) {
              let handler = tree.find((_) => _.value === child.enable.value);
              if (handler) {
                handler.rule.push(child);
              } else {
                tree.push({ value: child.enable.value, rule: [child] });
              }
            } else {
              tree.push(child);
            }
          }
        });
      },
      // 处理组件联动的数据
      handleControl(form) {
        let newForm = [];
        this.listToTree(form, newForm);
        return newForm;
      },
      // 获取表单值
      getFormValue() {
        return getFormValue(this.customForm);
      },
    },
  };
</script>
<style lang="scss" scoped>
  ::v-deep .el-tabs__header {
    width: 100%;
    max-width: 800px;
  }
  .el-form {
    width: 100%;
    max-width: 800px;
    ::v-deep .el-form-item__content {
      .el-cascader,
      .el-select,
      .el-input-number,
      .el-date-editor {
        width: 100%;
      }
      .el-slider {
        width: calc(100% - 10px);
      }
      .el-icon-info,
      .el-icon-question {
        font-size: 17px;
        position: absolute;
        right: -20px;
        top: 50%;
        transform: translate(0, -50%);
      }
      .el-switch {
        + .el-icon-question {
          left: 50px;
          right: auto;
        }
      }
      .el-icon-question {
        color: #f56c6c;
      }
      .el-icon-info {
        color: #409eff;
      }

      .el-input.el-input--mini {
        height: 28px;
      }
    }
  }
</style>
