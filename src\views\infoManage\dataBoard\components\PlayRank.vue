<template>
  <div class="play-rank-wrap">
    <div class="title-wrap"> 节目单播放排名 </div>
    <div class="play-rank-chart-wrap">
      <div
        class="chart-wrap"
        ref="charts"
        v-loading="loading"
        element-loading-spinner="el-icon-loading"
        element-loading-background="transparent"
      ></div>
    </div>
  </div>
</template>

<script>
  const colorList = ['#8072FC', '#71B0FC', '#00C8FC', '#88CDFE'];

  export default {
    data() {
      return {
        chart: null,
        loading: false,
      };
    },
    created() {
      this.getData();
    },
    beforeDestroy() {
      this.chart && this.chart.destroy();
    },
    methods: {
      updateChart(option) {
        if (!this.chart) {
          this.chart = this.$echarts.init(this.$refs.charts);
        }
        this.chart.setOption(option, true);
      },
      getData() {
        this.loading = true;
        setTimeout(() => {
          this.loading = false;
          let data = [
            {
              name: '节目单一',
              value: 50,
            },
            {
              name: '节目单二',
              value: 45,
            },
            {
              name: '节目单三',
              value: 40,
            },
            {
              name: '节目单四',
              value: 32,
            },
            {
              name: '节目单五',
              value: 30,
            },
            {
              name: '节目单六',
              value: 22,
            },
            {
              name: '节目单七',
              value: 20,
            },
            {
              name: '节目单八',
              value: 10,
            },
            {
              name: '节目单九',
              value: 9,
            },
            {
              name: '节目单十',
              value: 5,
            },
          ];
          this.initChart(data);
        }, 2 * 1000);
      },
      initChart(data) {
        let option = {
          tooltip: {
            trigger: 'axis',
          },
          legend: {
            show: false,
          },
          grid: {
            bottom: 30,
            left: 40,
            right: 30,
            top: 30,
          },
          xAxis: {
            data: data.map((item) => item.name),
            axisLine: {
              lineStyle: {
                color: 'rgba(255, 255,255, .5)',
              },
            },
            axisLabel: {
              color: '#fff',
              // interval: 0,
              // rotate: -45,
              formatter: function (value, index) {
                if (value.length > 4) {
                  return value.slice(0, 4) + '..';
                }
                return value;
              },
            },
          },
          yAxis: {
            minInterval: 1,
            splitLine: {
              show: false,
            },
            axisLabel: {
              color: '#fff',
            },
          },
          series: [
            {
              name: '播放次数',
              type: 'bar',
              barMaxWidth: 32,
              data: data.map((item) => item.value),
              itemStyle: {
                normal: {
                  color: (params) => {
                    let index = params.dataIndex;
                    if (index < 2) {
                      return colorList[0];
                    } else if (index >= 2 && index < 4) {
                      return colorList[1];
                    } else if (index >= 4 && index < 7) {
                      return colorList[2];
                    } else {
                      return colorList[3];
                    }

                    // return colorList[params.dataIndex % colorList.length]
                  },
                  label: {
                    show: true, //开启显示
                    position: 'top', //在上方显示
                    textStyle: {
                      //数值样式
                      color: '#fff',
                      fontSize: 12,
                    },
                  },
                },
              },
            },
          ],
        };
        this.updateChart(option);
      },
    },
  };
</script>

<style lang="scss" scoped>
  .play-rank-wrap {
    background: url(../img/box-left-bg.png) no-repeat;
    box-sizing: border-box;
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    .title-wrap {
      font-family: 'YouSheBiaoTiHei';
      font-size: 18px;
      padding: 15px 0 10px 20px;
    }

    .play-rank-chart-wrap {
      height: calc(100% - 40px);
      width: 100%;
      .chart-wrap {
        width: 100%;
        height: 100%;
      }
    }
  }
</style>
