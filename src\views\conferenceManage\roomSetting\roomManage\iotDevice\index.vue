<template>
  <div class="room-iot-list full-wh" v-loading="loading">
    <div class="page-header">
      <el-button class="back-btn mr-10" size="small" @click="goBack">
        <i class="el-icon-back"></i>
      </el-button>
      <el-input
        placeholder="所属智慧中控"
        clearable
        suffix-icon="el-icon-search"
        class="filter-cpm-clz"
        v-model="search.gatewayName"
        size="small"
      ></el-input>
      <el-input placeholder="设备名称" clearable suffix-icon="el-icon-search" class="filter-cpm-clz" v-model="search.name" size="small"></el-input>
      <el-select v-model="search.groupType" size="small" placeholder="设备分组类型" class="filter-cpm-clz" clearable>
        <el-option label="未分组设备" :value="0"> </el-option>
        <el-option label="设备组" :value="1"> </el-option>
      </el-select>
    </div>
    <el-table class="page-body" row-key="id" :data="filterDevices" highlight-current-row ref="table" height="100%" v-adaptive="{ bottomOffset: 5 }">
      <el-table-column label="设备名称" min-width="200" show-overflow-tooltip>
        <template slot-scope="{ row }">
          {{ row.deviceName }}
        </template>
      </el-table-column>
      <el-table-column label="设备图片" min-width="100" show-overflow-tooltip>
        <template slot-scope="{ row }">
          <el-image
            :src="row.logo || './iot-web/img/thumbnail/unknown_device.png'"
            :preview-src-list="[row.logo || './iot-web/img/thumbnail/unknown_device.png']"
            style="width: 40px; height: 40px"
            fit="contain"
          >
            <div slot="error" class="image-slot">
              <el-image src="./iot-web/img/thumbnail/unknown_device.png"></el-image>
            </div>
          </el-image>
        </template>
      </el-table-column>
      <el-table-column label="设备状态" min-width="150" show-overflow-tooltip>
        <template slot-scope="{ row }">
          <div class="stauts-wrap">
            <svg-icon :icon-class="row.status && row.status.icon" />
            <el-link v-if="row.status.value == 'alarm'" type="primary" @click="openAlarmDlg(row.deviceId, row.deviceName)">
              {{ row.status.label }}
            </el-link>
            <span v-else> {{ row.status && row.status.label }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="一级分类" min-width="150" show-overflow-tooltip align="center">
        <template slot-scope="{ row }">
          {{ row.deviceTypeName }}
        </template>
      </el-table-column>
      <el-table-column label="二级分类" min-width="150" show-overflow-tooltip align="center">
        <template slot-scope="{ row }">
          {{ row.devSubTypeName }}
        </template>
      </el-table-column>
      <el-table-column label="所属智慧中控" min-width="150" show-overflow-tooltip align="center">
        <template slot-scope="{ row }">
          {{ row.gatewayName }}
        </template>
      </el-table-column>
      <el-table-column label="空间" min-width="150" show-overflow-tooltip align="center">
        <template slot-scope="{ row }">
          {{ row.spaceName }}
        </template>
      </el-table-column>
      <el-table-column label="品牌" min-width="150" show-overflow-tooltip align="center">
        <template slot-scope="{ row }">
          {{ row.brand }}
        </template>
      </el-table-column>
      <el-table-column label="型号" min-width="150" show-overflow-tooltip align="center">
        <template slot-scope="{ row }">
          {{ row.model }}
        </template>
      </el-table-column>
      <el-table-column label="设备分组类型" min-width="150" show-overflow-tooltip align="center">
        <template slot-scope="{ row }">
          <template v-if="row.groupType === 1">
            <el-link type="primary" @click="openGroupDeviceDlg(row)">
              {{ formatDeviceGroupType(row.groupType) }}
            </el-link>
          </template>
          <template v-else>
            {{ formatDeviceGroupType(row.groupType) }}
          </template>
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" width="120" align="center">
        <template slot-scope="{ row }">
          <el-button size="mini" class="add-btn" icon="el-icon-thumb" :disabled="!row.online" @click.stop="checkAction(row)"> 操作 </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 设备组弹窗 -->
    <el-dialog
      :visible.sync="groupDeviceDlgVisible"
      :title="groupDeviceDlgTitle"
      @close="closeGroupDeviceDlg"
      :close-on-click-modal="false"
      width="1000px"
    >
      <el-table row-key="id" :data="groupDeviceList" highlight-current-row ref="groupDeviceTable" height="600px">
        <el-table-column prop="deviceName" label="设备名称" min-width="200" :show-overflow-tooltip="true" sortable>
          <template slot-scope="{ row }">
            {{ row.deviceName }}
          </template>
        </el-table-column>
        <el-table-column prop="status" sortable label="设备状态" min-width="150" show-overflow-tooltip align="center">
          <template slot-scope="{ row }">
            <div class="stauts-wrap">
              <svg-icon class="svg-icon" :icon-class="row.status && row.status.icon" />
              <el-link v-if="row.status.value == 'alarm'" type="primary" @click="openAlarmDlg(row.deviceId, row.deviceName)">
                {{ row.status.label }}
              </el-link>
              <span v-else> {{ row.status && row.status.label }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="spaceName" label="空间" min-width="120" :show-overflow-tooltip="true" sortable align="center">
          <template slot-scope="scope">
            {{ scope.row.spaceName }}
          </template>
        </el-table-column>
        <el-table-column prop="brand" label="设备品牌" min-width="120" :show-overflow-tooltip="true" sortable align="center"> </el-table-column>
        <el-table-column prop="model" label="设备型号" min-width="120" :show-overflow-tooltip="true" sortable align="center"> </el-table-column>
        <el-table-column align="center" prop="gatewayName" label="所属智慧中控" min-width="120" :show-overflow-tooltip="true"> </el-table-column>
        <el-table-column align="center" prop="deptName" :label="`所属${$t('deptLabel')}`" min-width="120" :show-overflow-tooltip="true">
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="120" align="center">
          <template slot-scope="{ row }">
            <el-button size="mini" class="add-btn" icon="el-icon-thumb" :disabled="!row.online" @click.stop="checkAction(row)"> 操作 </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
  import {  DEVICE_STATUS_ALL } from '@/views/iotControl/enum';
  import { getRoomSpace } from '@/api/conferenceGuide';
  import transform from '@/utils/transform';
  import { getGroupDevices, getNewDeviceTypes, deviceGetAllList } from '@/api/iotControl';
  import alarm from '@/views/iotControl/deviceMaintenance/intelligentMonitor/modules/alarm.vue';
  export default {
    components: {
      alarm,
    },
    data() {
      return {
        devices: [],
        loading: false,
        search: {
          gatewayName: '',
          name: '',
          groupType: '',
        },
        groupDeviceId: '',
        // 设备组名称
        groupDeviceDlgTitle: '设备',
        // 设备组设备列表
        groupDeviceList: [],
        // 设备组弹框显示
        groupDeviceDlgVisible: false,
        DEVICE_STATUS_ALL,
      };
    },
    async created() {
      this.getData();
    },
    methods: {
      alarmDlg: transform(alarm),
      openAlarmDlg(deviceId, deviceName) {
        this.alarmDlg({
          propsData: {
            deviceId: deviceId,
            deviceName: deviceName,
          },
          methods: {
            refresh: () => {
              if (this.groupDeviceDlgVisible) {
                this.getGroupDeviceList();
                this.getData();
              } else {
                this.getData();
              }
            },
          },
        });
      },
      /**
       * 格式化设备组
       * @param {*} type
       */
      formatDeviceGroupType(type) {
        if (type === 0) {
          return '未分组设备';
        } else if (type === 1) {
          return '设备组';
        } else if (type === 2) {
          return '组内设备';
        } else {
          return '-';
        }
      },
      goBack() {
        this.$router.go(-1);
      },
      /**
       * 获取设备分类
       */
      getClassifications() {
        return getNewDeviceTypes().then((res) => {
          this.deviceClassificationOption = res?.data || [];
        });
      },
      /**
       * 过滤设备
       */
      getData() {
        this.getRoomSpace();
      },
      /**
       * 查询会议室绑定的空间
       */
      getRoomSpace() {
        this.loading = true;
        getRoomSpace(this.roomId)
          .then(async ({ data }) => {
            if (!data.spaceId) {
              this.loading = false;
              return;
            }
            let res = await deviceGetAllList({ spaceId: data.spaceId });
            if (res && res.data) {
              this.devices = res.data.map((item) => {
                let status = {};
                status = DEVICE_STATUS_ALL.find((val) => val.value == item.status);
                if (item.groupType == 1) {
                  item.hasChildren = true;
                  item.children = [];
                }

                return {
                  ...item,
                  status,
                };
              });
            }

            this.loading = false;
          })
          .catch(() => {
            this.loading = false;
          });
      },
      closeGroupDeviceDlg() {
        this.groupDeviceId = '';
        this.groupDeviceDlgTitle = '';
        this.groupDeviceList = [];
        this.groupDeviceDlgVisible = false;
      },
      /**
       * 查看设备组弹窗 组内设备
       * @param {*} row
       */
      openGroupDeviceDlg(row) {
        this.groupDeviceDlgTitle = row.deviceName;
        this.groupDeviceId = row.deviceId;
        this.getGroupDeviceList();
        this.groupDeviceDlgVisible = true;
      },
      getGroupDeviceList() {
        let params = {
          groupId: this.groupDeviceId,
        };

        getGroupDevices(params).then((res) => {
          let data = res.data.map((item) => {
            let status = {};
            status = DEVICE_STATUS_ALL.find((val) => val.value == item.status);

            if (item.groupType == 1) {
              item.hasChildren = true;
              item.children = [];
            }
            return {
              ...item,
              status,
            };
          });
          this.groupDeviceList = data;
        });
      },
      /**
       * 查看设备组件详情
       * @param {*} row
       */
      checkAction(row) {
        this.$router.push({
          name: 'IotDeviceAction',
          params: {
            id: row.deviceId,
            deviceType: row.deviceType,
            devSubType: row.devSubType,
            model: row.model,
          },
        });
      },
    },
    computed: {
      roomId() {
        return this.$route.params.id;
      },
      filterDevices() {
        let devices = this.devices;
        // 设备网关名称
        if (this.search.gatewayName) {
          devices = devices.filter((item) => item.gatewayName.toUpperCase().includes(this.search.gatewayName.toUpperCase()));
        }
        // 设备名称
        if (this.search.name) {
          devices = devices.filter((item) => item.deviceName.toUpperCase().includes(this.search.name.toUpperCase()));
        }

        // 设备分组类型
        if (this.search.groupType === 0 || this.search.groupType === 1) {
          devices = devices.filter((item) => item.groupType === this.search.groupType);
        }

        return devices;
      },
    },
  };
</script>
<style lang="scss" scoped>
  .stauts-wrap {
    display: flex;
    align-items: center;
    .svg-icon {
      margin-right: 5px;
    }
  }
  .room-iot-list {
    display: flex;
    flex-direction: column;

    .back-btn {
      font-size: 18px;
      display: flex;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      padding: 0px 10px;
    }

    .filter-cpm-clz {
      width: 160px;
      margin-right: 10px;
    }

    .page-header {
      display: flex;
      box-sizing: border-box;
      padding: 12px 0px;
    }

    .page-body {
      flex: 1;
      overflow: hidden;
    }
  }
</style>
