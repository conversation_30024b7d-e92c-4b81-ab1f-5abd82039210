<!-- iot模块，用户穿梭框 -->
<template>
  <el-dialog title="选择用户" :visible.sync="visible" width="1200px" custom-class="user-dialog" :close-on-click-modal="false" @close="close">
    <div class="margin-bottom10 search-wrap">
      <el-input
        placeholder="用户名/昵称"
        suffix-icon="el-icon-search"
        size="small"
        v-model="szName"
        clearable
        style="width: 200px"
        v-debounce="[
          (e) => {
            pagination.curPage = 1;
            getUserList(e);
          },
        ]"
        @clear="
          () => {
            pagination.curPage = 1;
            getUserList();
          }
        "
      />
      <dept-cascader :deptValue.sync="search.deptId" :bInit="false" :bOwn="bOwn" :bCompany="bCompany" class="ml-10"></dept-cascader>
      <el-checkbox v-model="search.excludeChild" class="ml-10">仅当前层级</el-checkbox>
    </div>
    <transfer
      ref="transfer"
      keyword="username"
      :list="userList"
      :chooseList="chooseUserList"
      class="user-transfer"
      :defaultSort="{ prop: sort.prop, order: 'ascending' }"
      @sortChange="sortChange"
      :isMulti="isMulti"
      v-loading="loading"
    >
      <div slot="titleLeft">候选用户</div>
      <template slot="leftTable">
        <el-table-column label="序号" width="50" show-overflow-tooltip align="center">
          <template slot-scope="scope">
            {{ scope.$index + (pagination.curPage - 1) * pagination.size + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="username" sortable="custom" :show-overflow-tooltip="true" label="用户名" min-width="80"> </el-table-column>
        <el-table-column prop="nickname" sortable="custom" :show-overflow-tooltip="true" label="昵称" min-width="80"> </el-table-column>
        <el-table-column prop="departmentName" :show-overflow-tooltip="true" :label="`所属${$t('deptLabel')}`" min-width="80" v-if="showDept"> </el-table-column>
      </template>
      <pagination
        slot="pagination"
        :total="pagination.total"
        :page.sync="pagination.curPage"
        :limit.sync="pagination.size"
        :layout="TRANSFER_PAGINATION_LAYOUT"
        @pagination="getUserList"
        :autoScroll="false"
      />
      <div slot="titleRight">已选用户</div>
      <template slot="rightTable">
        <el-table-column type="index" label="序号" min-width="50" align="center"> </el-table-column>
        <el-table-column prop="username" sortable :show-overflow-tooltip="true" label="用户名" min-width="80" />
        <el-table-column prop="nickname" sortable :show-overflow-tooltip="true" label="昵称" min-width="80"> </el-table-column>
        <el-table-column prop="departmentName" :show-overflow-tooltip="true" :label="`所属${$t('deptLabel')}`" v-if="showDept"> </el-table-column>
      </template>
    </transfer>
    <div slot="footer" class="dialog-footer">
      <el-button @click="close">取消</el-button>
      <el-button class="okBtn" @click="handleOk">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import deptCascader from '@/components/deptCascader';
  import dlg from '@/mixins/dlg';
  import { excludeChildMixin } from '@/mixins/prefer';
  import transfer from '@/components/transfer/index.vue';
  import { getUsers2 } from '@/api/user';
  import { TRANSFER_PAGINATION_LAYOUT } from '@/utils/enum';
  export default {
    mixins: [dlg,excludeChildMixin],
    components: {
      transfer,
      deptCascader,
    },
    props: {
      users: {
        type: Array,
        default: () => [],
        require: false,
      },
      // 查询机构时根机构是否包含所有子级机构,true为不包含
      bOwn: {
        type: Boolean,
        default: false,
      },
      // 查询机构时是否从公司开始查询，true为从公司开始查询
      bCompany: {
        type: Boolean,
        default: true,
      },
      // 是否多选
      isMulti: {
        type: Boolean,
        default: true,
      },
      // 是否显示机构列
      showDept: {
        type: Boolean,
        default: true,
      }
    },
    data() {
      return {
        loading: false,
        // 用户名搜索
        szName: '',
        // 用户列表
        userList: [],
        chooseUserList: [],

        // 分页参数
        pagination: {
          curPage: 1,
          size: 20,
          total: 0,
        },
        sort: {
          prop: 'username',
          order: 'ASC',
        },
        TRANSFER_PAGINATION_LAYOUT,
        search: {
          deptId: '',
          excludeChild: false,
        },
      };
    },
    created() {
      if (this.users && this.users.length) {
        this.chooseUserList = this.users.map((item) => ({
          username: item.username,
          id: item.id,
          ...item,
        }));
      }
      this.getUserList();
    },
    watch: {
      'search.deptId': function () {
        this.pagination.curPage = 1;
        this.getUserList();
      },
      'search.excludeChild': function () {
        this.pagination.curPage = 1;
        this.getUserList();
      },
    },
    methods: {
      handleOk() {
        let list = this.$refs.transfer.rightList;
        if (!this.isMulti && list.length > 1) {
          this.$message.warning('只能选择单个用户!');
          return;
        }
        this.$emit('handleSelect', list);
        this.close();
      },
      /**
       * 获取用户列表
       */
      getUserList() {
        this.loading = true;
        let deptId = Array.isArray(this.search.deptId) ? this.search.deptId[this.search.deptId.length - 1] : this.search.deptId;
        getUsers2({
          page: this.pagination.curPage,
          size: this.pagination.size,
          direction: this.sort.order,
          property: this.sort.prop,
          nameToSearch: this.szName,
          deptId,
          includeChild: !this.search.excludeChild,
        })
          .then((res) => {
            this.userList = res.data.rows;
            this.pagination.total = res.data.total;
          })
          .finally(() => (this.loading = false));
      },
      sortChange(col) {
        this.sort.order = 'ascending' === col.order ? 'ASC' : 'DESC';
        this.sort.prop = col.prop;
        this.getUserList();
      },
    },
  };
</script>
<style lang="scss" scoped>
  .user-dialog {
  }

  .b-soft-join {
    margin: 0 20px;
  }
</style>
