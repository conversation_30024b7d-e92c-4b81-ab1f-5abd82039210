<template>
  <div class="summary-wrap">
    <div class="h1">信息<br />发布</div>
    <div class="info-list-wrap">
      <div class="info-wrap" v-for="item in list" :key="item.title">
        <div class="title">{{ item.title }}</div>
        <div class="info">{{ item.value }}</div>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    data() {
      return {
        list: [
          {
            key: '',
            value: 2000,
            title: '素材',
          },
          {
            key: '',
            value: 500,
            title: '节目',
          },
          {
            key: '',
            value: 200,
            title: '节目单',
          },
          {
            key: '',
            value: 120,
            title: '终端',
          },
          {
            key: '',
            value: '240000h',
            title: '播放时长',
          },
        ],
      };
    },
  };
</script>

<style lang="scss" scoped>
  .summary-wrap {
    width: 100%;
    height: 100%;
    background: url('../img/summary-bg.png') center center no-repeat;
    background-size: 70% 70%;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    &::before {
      content: '';
      position: absolute;
      width: 100%;
      height: 100%;
      // background: url('../img/summary-circle.png') center 34% no-repeat;
      background-size: 85% 60%;
    }

    .h1 {
      font-size: 30px;
      line-height: 40px;
      letter-spacing: 3px;
    }

    .info-wrap {
      position: absolute;
      background: url('../img/summary-box.png') center center no-repeat;
      background-size: contain;
      padding: 10px 40px;
      &:nth-child(1) {
        left: 20%;
        top: 14%;
      }
      &:nth-child(2) {
        right: 14%;
        top: 32%;
      }
      &:nth-child(3) {
        left: 12%;
        bottom: 40%;
      }
      &:nth-child(4) {
        left: 24%;
        bottom: 14%;
      }
      &:nth-child(5) {
        right: 18%;
        bottom: 20%;
      }
      .title {
        color: #50ffff;
        margin-bottom: 5px;
      }
    }
  }
</style>
