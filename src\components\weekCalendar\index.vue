<template>
  <div>
    <div class="header">
      <div class="title">{{ curDate }}</div>
      <el-button-group size="mini">
        <el-button size="mini" @click="getLastWeek">上周</el-button>
        <el-button size="mini" @click="getCurrentDay">今天</el-button>
        <el-button size="mini" @click="getNextWeek">下周</el-button>
      </el-button-group>
    </div>
    <el-calendar :range="rangeDate" v-model="curValue">
      <template slot="dateCell" slot-scope="{ date, data }">
        <div class="calendar-day">
          <p :class="data.isSelected ? 'is-selected' : ''">
            {{ data.day.split("-")[2] }}
          </p>
        </div>
      </template>
    </el-calendar>
  </div>
</template>

<script>
import moment from "moment";

export default {
  data() {
    return {
      //每周起止日期
      rangeDate: [],
      curValue: new Date()
    };
  },
  created() {
    this.getCurrentWeek();
  },
  computed: {
    curDate() {
      return moment(this.curValue).format("YYYY年MM月");
    }
  },
  watch: {
    curValue(newVal) {
      this.$emit("handleDateChange", newVal);
    }
  },
  methods: {
    getNextWeek() {
      let next_monday = moment(this.rangeDate[0])
        .subtract(-7, "days")
        .format("YYYY-MM-DD"); //周一日期

      let next_sunday = moment(this.rangeDate[0])
        .subtract(-13, "days")
        .format("YYYY-MM-DD"); //周日日期
      console.log(next_monday);
      console.log(next_sunday);
      this.rangeDate = [next_monday, next_sunday];
      this.curValue = new Date(next_monday);
    },
    getLastWeek() {
      let last_monday = moment(this.rangeDate[0])
        .subtract(7, "days")
        .format("YYYY-MM-DD"); //周一日期

      let last_sunday = moment(this.rangeDate[0])
        .subtract(1, "days")
        .format("YYYY-MM-DD"); //周日日期
      console.log(last_monday);
      console.log(last_sunday);
      this.rangeDate = [last_monday, last_sunday];
      this.curValue = new Date(last_monday);
    },
    addWeek(value) {
      const week = moment(this.rangeDate[0]).week();
      const newDate = moment().week(week + value);

      const monday = newDate.startOf("isoWeek").format("YYYY-MM-DD");
      const sunday = newDate.endOf("isoWeek").format("YYYY-MM-DD");

      this.rangeDate = [monday, sunday];
    },
    getCurrentWeek() {
      // 获取本周周一的日期
      const lastMonday = moment()
        .startOf("isoWeek")
        .format("YYYY-MM-DD");
      // 获取本周周末的日期
      const lastSunday = moment()
        .endOf("isoWeek")
        .format("YYYY-MM-DD");

      this.rangeDate = [lastMonday, lastSunday];
    },
    getCurrentDay() {
      this.getCurrentWeek();
      // 防止初始化时请求多次接口
      this.curValue = new Date();
    }
  }
};
</script>
<style lang="scss" scoped>
.header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 0 5px 5px;
  border-bottom: 1px solid #dfe6ec;
}
::v-deep .el-calendar__header {
  display: none;
}
::v-deep .el-calendar__body {
  padding: 5px 0 0 0;
}
::v-deep .el-calendar-table {
  thead th {
    padding: 0;
  }
  tr:first-child td {
    border-top: none;
  }
  tr td:first-child {
    border-left: none;
  }
  td {
    border-bottom: none;
    border-right: none;
    &.is-selected {
      background-color: transparent;
    }
  }
  .el-calendar-day {
    height: auto;
    padding: 3px 0 0 0;
  }
  .calendar-day {
    p {
      margin: 0 auto;
      width: 23px;
      height: 23px;
      border-radius: 50%;
      &.is-selected {
        background-color: #409eff;
        color: white;
      }
    }
  }
}
</style>
