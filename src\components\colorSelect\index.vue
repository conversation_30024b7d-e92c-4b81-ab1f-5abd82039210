<template>
  <div class="custom-color-select">
    <el-select ref="colorSelect" placeholder="" :clearable="false" v-model="myColor" style="width: 100%" @change="handleChange">
      <el-option v-for="item in colorList" :key="item[value]" label=" " :value="item[value]">
        <div v-if="item.bg == '00000000'" style="text-align: center;">关闭灯光</div>
        <div v-else :style="`background-color: #${item[bg]}; width: 100% ;height: 90%;`"></div>
      </el-option>
    </el-select>
  </div>
</template>

<script>
export default {
  name: "colorSelect",
  //允许一个自定义组件在使用 v-model 时定制 prop 和 event。默认情况下，
  // 一个组件上的 v-model 会把 value 用作 prop 且把 input 用作 event，
  // 但是一些输入类型比如单选框和复选框按钮可能想使用 value prop 来达到不同的目的。使用 model 选项可以回避这些情况产生的冲突。
  model: {
    prop: 'color',
    event: 'updateColor'
  },
  props: {
    //颜色数组
    colorList: {
      type: Array,
      default: () => {
        return [
          {
            value: 0,
            bg: '00000000'
          },
          {
            value: 1,
            bg: '1F7BC1'
          },
          {
            value: 2,
            bg: 'e0ffff'
          },
          {
            value: 3,
            bg: '90ee90'
          },
          {
            value: 4,
            bg: 'fa89dd'
          },
          {
            value: 5,
            bg: 'ef6767'
          },
          {
            value: 6,
            bg: 'f6f2f2'
          },
          {
            value: 7,
            bg: 'ffffe0'
          }
        ];
      }
    },
    //颜色值的属性  默认是colorList中的bg
    bg: {
      type: String,
      default: 'bg'
    },
    //颜色的value
    value: {
      type: String,
      default: 'value'
    },
    //父组件绑定的值
    color: {
      type: String | Number,
      default: undefined
    }
  },
  data() {
    return {
      myColor: undefined
    }
  },
  methods: {
    //设置颜色选择框中颜色
    setSelectColor(value) {
      let item = this.colorList.find(item => {
        return item[this.value] == value
      })
      //通过操作dom节点改变样式
      this.$nextTick(() => {
        let dom = this.$refs.colorSelect;
        if (dom) {
          dom = dom.$el.children[0];
          let inputDom = dom.querySelectorAll(".el-input__inner");
          let icon = dom.querySelectorAll(".el-input__icon");
          if (!!item) {
            inputDom[0].style.setProperty("background", `#${item[this.bg]}`, 'important');
            // inputDom[0].style["background"] = '#' + item[this.bg];
            icon[0].style["color"] = "black";
          } else {
            //重置时变成白色
            this.myColor = null
            inputDom[0].style.setProperty("background", 'rgba(0, 0, 0, 0)', 'important');
            // inputDom[0].style["background"] = '#FFFFFF';
          }
        }
      })
    },
    handleChange(val) {
      this.setSelectColor(val);
      //触发updateColor事件更新父组件绑定值
      this.$emit('updateColor', val);
    }
  },
  created() {
    if (this.color || (typeof(this.color) == 'string' && this.color.length > 0)) {
      this.myColor = this.color;
      this.setSelectColor(this.color)
    }
  },
  watch: {
    'color': function (val) {
      this.setSelectColor(val);
    }
  }
}
</script>

<style scoped lang="scss">
.custom-color-select{
  position: relative;
  user-select: none;

  ::v-deep .el-input__inner{
    color: transparent;
  }
}
</style>


