<template>
  <div>
    <div class="filter-wrap">
      <el-select v-model="period" placeholder="请选择" @change="changePeriod" style="width: 120px">
        <el-option v-for="item in STATISTIC_TYPE" :key="item.value" :label="item.label" :value="item.value"> </el-option>
      </el-select>
      <el-date-picker
        placeholder="选择日期"
        v-model="timeModel"
        :type="periodDetail.type"
        :value-format="periodDetail.valueFormat"
        :format="periodDetail.format"
        :picker-options="datePickerOptions"
        :clearable="false"
        :key="periodDetail.type"
        :unlink-panels="true"
        @change="changeTime"
        class="ml-10"
        style="width: 200px"
      >
      </el-date-picker>
      <!-- <el-select v-model="quarter" placeholder="请选择季度" v-if="bQuarter" style="width: 120px; margin-left: 10px" @change="changeQuarter">
        <el-option v-for="item in QUARTER" :key="item.value" :label="item.label" :value="item.value"> </el-option>
      </el-select> -->
      <paging-select
        :selectValue="searchUser"
        placeholder="请选择或输入用户"
        :options="userList"
        :loading="loading"
        :total="userTotal"
        :clearable="true"
        label="label"
        _key="username"
        @getList="getUserList"
        @selectChange="handleUserChange"
        class="ml-10"
      ></paging-select>
    </div>
    <el-table :data="tableData" ref="table" highlight-current-row height="300px" v-adaptive>
      <el-table-column label="序号" width="60" fixed>
        <template slot-scope="scope">
          {{ scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="username" label="用户" :show-overflow-tooltip="true" min-width="200"> </el-table-column>
      <el-table-column prop="video" label="视频" :show-overflow-tooltip="true" min-width="200"> </el-table-column>
      <el-table-column prop="staticPicture" label="静图" :show-overflow-tooltip="true" min-width="200"> </el-table-column>
      <el-table-column prop="dynamicPicture" label="动图" :show-overflow-tooltip="true" min-width="200"> </el-table-column>
      <el-table-column prop="txt" label="文本" :show-overflow-tooltip="true" min-width="200"> </el-table-column>
      <el-table-column prop="pdf" label="PDF" :show-overflow-tooltip="true" min-width="200"> </el-table-column>
      <el-table-column prop="audio" label="音频" :show-overflow-tooltip="true" min-width="200"> </el-table-column>
      <el-table-column prop="office" label="文档" :show-overflow-tooltip="true" min-width="200"> </el-table-column>
    </el-table>
    <pagination
      :total="total"
      :page.sync="curPage"
      :limit.sync="size"
      @pagination="getData"
    />
  </div>
</template>

<script>
  import moment from 'moment';
  import pagingSelect from '@/components/pagingSelect';
  import { getMaterialStatistics } from '@/api/info';
  import { getUsersInDept } from '@/api/system';
  import { MATERIAL_TYPES } from '@/utils/material';
  import { QUARTER, STATISTIC_TYPE } from './enum';
  export default {
    components: {
      pagingSelect,
    },
    data() {
      return {
        QUARTER,
        STATISTIC_TYPE,
        loading: false,
        time: +new Date(),
        period: STATISTIC_TYPE[2].value,
        quarter: QUARTER[0].value,
        searchUser: '',
        userList: [],
        userTotal: 0,
        tableData: [],
        total: 0,
        curPage:1,
        size: 60,
        // 日期限制条件
        datePickerOptions: {
          disabledDate(time) {
            return time.getTime() > Date.now() - 8.64e6;
          },
          firstDayOfWeek: 1,
        },
      };
    },
    computed: {
      periodDetail() {
        let res = STATISTIC_TYPE.find((item) => item.value === this.period);
        return res || {};
      },
      bQuarter() {
        return false
      },
      bWeek() {
        return this.period === STATISTIC_TYPE[1].value;
      },
      timeModel: {
        get() {
          if (this.bWeek) {
            return moment(this.time).format('yyyy-MM-DD');
          }
          return this.time;
        },
        set(val) {
          if (this.bWeek) {
            val = new Date(val).getTime();
          } else if (this.bQuarter) {
            let year = new Date(val).getFullYear();
            val = +new Date(year, this.quarter, 1);
          }
          this.time = val;
          
        },
      },
    },
    mounted() {
      this.getData();
    },
    methods: {
      changePeriod() {
        if (this.bQuarter) {
          let quarter = (moment().quarter() - 1) * 3;
          let year = new Date().getFullYear();
          this.time = +new Date(year, quarter, 1);
          this.quarter = quarter;
        } else {
          this.time = +new Date();
        }
        this.getData();
      },
      changeTime() {
        this.getData();
      },
      changeQuarter() {
        let year = new Date(this.time).getFullYear();
        this.time = +new Date(year, this.quarter, 1);
        this.getData();
      },
      getUserList(config = {}) {
        this.loading = true;
        config.page = config.page || 1;
        config.size = config.size || 10;
        config.name = config.name || '';
        let deptId = Array.isArray(this.deptId) ? this.deptId.at(-1) : this.deptId;
        getUsersInDept({
          deptId,
          page: config.page,
          size: config.size,
          username: config.name,
          includeChild: true,
        })
          .then((res) => {
            this.userList = res.data.rows.map((item) => ({
              ...item,
              label:`${item.nickname}(${item.username})`
            }));
            this.userTotal = res.data.total;
          })
          .finally(() => (this.loading = false));
      },
      handleUserChange(selectUserName) {
        this.searchUser = selectUserName;
        this.getData()
      },
      getData() {
        this.loading = true;
        getMaterialStatistics(this.period, this.time,this.searchUser).then(({ data }) => {
          this.total = data.total;
          this.tableData = data.rows.map((item) => {
            MATERIAL_TYPES.forEach((materialTypeItem) => {
              let res = item.statistics.find((material) => material.code === materialTypeItem.code);
              if (res) {
                item[materialTypeItem.type] = res.count;
              } else {
                item[materialTypeItem.type] = 0;
              }
            });
            return item;
          });
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .filter-wrap {
    margin-top: 10px;
  }
</style>
