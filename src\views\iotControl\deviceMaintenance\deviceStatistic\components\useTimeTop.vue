<template>
  <div class="wrap">
    <div class="header-wrap">
      <div class="title">本月使用时长Top10</div>

      <el-link @click="showDetail">查看详情 ></el-link>
    </div>
    <div class="chart-wrap">
      <div ref="chart" class="bar-chart" v-show="data.length > 0"></div>
      <div class="empty-data" v-show="data.length === 0"> 暂无统计数据 </div>
    </div>
  </div>
</template>

<script>
  import { getDeviceStatisticTop } from '@/api/iotControl';
  import { calculateDuration } from '@/utils/utils';
  export default {
    data() {
      return {
        data: [],
        chart: null,
        timer: null,
      };
    },
    mounted() {
      this.getData();
      this.timer = setInterval(() => this.getData(), 10 * 1000);
      window.addEventListener('resize', this.resize);
    },
    methods: {
      getData() {
        getDeviceStatisticTop({
          timeType: 1,
          itemType: 3,
        }).then((response) => {
          this.data = response.data;
          //仅显示大于0的,并且只显示前十个
          this.data = this.data.filter((item) => item.count > 0);
          this.data = this.data.sort((a, b) => b.count - a.count).slice(0, 10);
          this.$nextTick(() => {
            if (this.data.length > 0) {
              if (!this.chart) {
                this.chart = this.$echarts.init(this.$refs.chart);
              }
              this.draw();
            }
          });
        });
      },
      resize() {
        this.$nextTick(() => {
          this.chart && this.chart.resize();
        });
      },
      draw() {
        const option = {
          tooltip: {
            trigger: 'axis',
            formatter: (params) => {
              return params[0].name + ' : ' + this.formatWorkTime(params[0].value);
            },
            axisPointer: {
              type: 'shadow',
            },
          },
          xAxis: {
            type: 'value',
            show: false,
          },
          grid: {
            top: '10px',
            left: '30px',
            right: '30px',
            bottom: '0px',
            containLabel: true,
          },
          yAxis: {
            type: 'category',
            data: this.data.sort((a, b) => a.count - b.count).map((item) => item.deviceName),
            axisLine: { show: false },
            axisTick: { show: false },
            axisLabel: {
              interval: 0,
              formatter: function (value) {
                const maxLength = 20;
                if (value.length > maxLength) {
                  return value.substring(0, maxLength) + '...';
                }
                return value;
              },
            },
          },
          series: [
            {
              type: 'bar',
              data: this.data.map((item) => ({
                value: item.count,
                itemStyle: {
                  color: {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 1,
                    y2: 0,
                    colorStops: [
                      {
                        offset: 0,
                        color: 'rgba(89, 128, 255, 0.9)',
                      },
                      {
                        offset: 1,
                        color: 'rgba(89, 128, 255, 0.4)',
                      },
                    ],
                  },
                  borderRadius: [0, 4, 4, 0],
                },
              })),

              barMaxWidth: '12px',
              label: {
                show: false,
              },
            },
          ],
        };
        this.chart.setOption(option);
      },
      showDetail() {
        this.$router.push({
          name: 'IntelligentMonitor',
          params: {
            activeType: 'lifeMonitor',
          },
        });
      },
      formatWorkTime(workTime) {
        const { days, hours } = calculateDuration(workTime, 's');
        return `${days}天${hours}小时`;
      },
      refresh() {
        this.getData();
      },
    },
    beforeDestroy() {
      this.chart && this.chart.dispose();
      this.chart = null;
      clearInterval(this.timer);
      window.removeEventListener('resize', this.resize);
    },
  };
</script>

<style lang="scss" scoped>
  .wrap {
    width: 100%;
    height: 100%;
  }
  .chart-wrap {
    height: calc(100% - 40px);
    width: 100%;
    .bar-chart {
      width: 100%;
      height: 100%;
    }
    .empty-data {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      font-size: 14px;
    }
  }
</style>
