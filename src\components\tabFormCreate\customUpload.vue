<template>
  <el-upload
    :auto-upload="false"
    action=""
    :http-request="doSubmit"
    :on-change="handleChangeImg"
    :file-list="fileList"
    :on-remove="handleRemoveImg"
    ref="pictureUpload"
    list-type="picture"
  >
    <el-button size="small" type="primary">选择图片</el-button>
    <div slot="tip" class="el-upload__tip ml-10">
      只能上传jpg/png/bmp文件，且不超过20M
    </div>
  </el-upload>
</template>

<script>
export default {
  props: {
    //预定义
    value: String|Array
  },
  data() {
    return { fileList: [] };
  },
  watch: {
    value(newVal) {
      console.log(newVal);
    }
  },
  methods: {
    doSubmit() {
      console.log("doSubmit");
    },
    //   移除图片
    handleRemoveImg(file, fileList) {
      this.fileList = fileList;
    },
    // 图片上传后
    handleChangeImg(file, fileList) {
      const type = file.raw.type;
      const fileTypeEnum = ["image/png", "image/jpeg", "image/bmp"];
      const bCorrectType = fileTypeEnum.find(item => item === type);
      if (!bCorrectType) {
        this.$message.warning("图片仅支持jpg、png或bmp格式");
        fileList.pop();
        return;
      }
      const bLt5M = file.size / 1024 / 1024 < 20;
      if (!bLt5M) {
        this.$message.warning("图片大小超过20M");
        fileList.pop();
        return;
      }

      //slice使用负值从数组的尾部选取元素
      this.fileList = fileList.slice(-1);
      //当组件值发生变化后,通过 input 事件更新值
      this.$emit("input", this.fileList[0].raw);
    }
  }
};
</script>
<style lang="scss" scoped></style>
