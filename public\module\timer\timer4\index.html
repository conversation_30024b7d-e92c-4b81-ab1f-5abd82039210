<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
    <link rel="stylesheet" href="40e93b11cbd4174c60b22cba0d630300.css" />
    <style>
      body,
      html {
        height: 100%;
        margin: 0;
      }
      .wrap {
        width: 100%;
        height: 100%;
        /* font-size: 8vw; */
        text-align: center;
      }
      i {
        font-style: normal;
        font-weight: bold;
        font-size: 4vmin;
      }

      .date {
        font-size: 3vmin;
        background-color: #f6eee8;
        border-radius: 2px;
        text-align: center;
        position: absolute;
        left: 50%;
        bottom: 27%;
        transform: translateX(-50%);
      }

      section.border-clock {
        background-color: #ebeb83;
        width: 80vmin;
        height: 80vmin;
        border: 3vmin solid #f4f48e;
        border-radius: 100%;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        box-shadow: 1.5vmin 1.5vmin 3.5vmin -1vmin #488893;
      }

      section.clock {
        width: 78vmin;
        height: 78vmin;
        border: 1vmin solid #e5e375;
        border-radius: 100%;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }

      div.minutes,
      div.hours,
      div.seconds {
        width: 0.3vmin;
        height: 0.3vmin;
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        transform-origin: 50% 0;
      }

      div.minutes {
        transition: transform 1s linear;
      }

      div.seconds::before,
      div.minutes::before,
      div.hours::before {
        content: "";
        position: absolute;
        bottom: 50%;
        left: 50%;
        transform-origin: 50% 0;
        transform: translateX(-50%);
      }

      div.seconds::before {
        width: 0.2vmin;
        height: 34vmin;
        background-color: #eb6444;
      }

      div.minutes::before {
        width: 0.4vmin;
        height: 25vmin;
        background-color: #68c3d4;
        border-radius: 0.3vmin;
      }

      div.hours::before {
        width: 0.6vmin;
        height: 20vmin;
        background-color: #68c3d4;
        border-radius: 0.3vmin;
      }

      .cercle {
        width: 1.5vmin;
        height: 1.5vmin;
        background-color: #fdfaf7;
        border: 0.5vmin solid #eb6444;
        border-radius: 100%;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }

      span {
        display: block;
        width: 0.3vmin;
        height: 95%;
        position: absolute;
        top: 50%;
        left: 50%;
      }
      span::after {
        content: "";
        background-color: #a0a1a4;
        position: absolute;
        width: 100%;
        height: 2vmin;
        top: 0;
        left: 0;
      }
      span::before {
        content: "";
        background-color: #a0a1a4;
        position: absolute;
        width: 100%;
        height: 2vmin;
        bottom: 0;
        left: 0;
      }

      span.fives::after,
      span.fives::before {
        height: 3vmin;
      }

      ul {
        height: 41%;
        width: 0;
        position: absolute;
        bottom: 47%;
        left: 50%;
      }

      li {
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        transform-origin: 50% 100%;
        list-style: none;
      }

      li i {
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
      }

      li:nth-child(2) {
        transform: rotate(30deg);
      }
      li:nth-child(2) i {
        transform: translateX(-50%) rotate(-30deg);
      }
      li:nth-child(3) {
        transform: rotate(60deg);
      }
      li:nth-child(3) i {
        transform: translateX(-50%) rotate(-60deg);
      }
      li:nth-child(4) {
        transform: rotate(90deg);
      }
      li:nth-child(4) i {
        transform: translateX(-50%) rotate(-90deg);
      }

      li:nth-child(5) {
        transform: rotate(120deg);
      }
      li:nth-child(5) i {
        transform: translateX(-50%) rotate(-120deg);
      }

      li:nth-child(6) {
        transform: rotate(150deg);
      }
      li:nth-child(6) i {
        transform: translateX(-50%) rotate(-150deg);
      }

      li:nth-child(7) {
        transform: rotate(180deg);
      }
      li:nth-child(7) i {
        transform: translateX(-50%) rotate(-180deg);
      }

      li:nth-child(8) {
        transform: rotate(210deg);
      }
      li:nth-child(8) i {
        transform: translateX(-50%) rotate(-210deg);
      }

      li:nth-child(9) {
        transform: rotate(240deg);
      }
      li:nth-child(9) i {
        transform: translateX(-50%) rotate(-240deg);
      }

      li:nth-child(10) {
        transform: rotate(270deg);
      }
      li:nth-child(10) i {
        transform: translateX(-50%) rotate(-270deg);
      }

      li:nth-child(11) {
        transform: rotate(300deg);
      }
      li:nth-child(11) i {
        transform: translateX(-50%) rotate(-300deg);
      }

      li:last-child {
        transform: rotate(330deg);
      }
      li:last-child i {
        transform: translateX(-50%) rotate(-330deg);
      }
      [v-cloak] {
        display: none;
      }
    </style>
    <script src="174365807c06490c848d7b1d45fdc348.js"></script>
    <script src="page.js"></script>
  </head>

  <body>
    <div id="app" class="wrap">
      <section class="border-clock" :style="style"></section>
      <section class="clock" :style="style">
        <ul>
          <li><i>12</i></li>
          <li><i>1</i></li>
          <li><i>2</i></li>
          <li><i>3</i></li>
          <li><i>4</i></li>
          <li><i>5</i></li>
          <li><i>6</i></li>
          <li><i>7</i></li>
          <li><i>8</i></li>
          <li><i>9</i></li>
          <li><i>10</i></li>
          <li><i>11</i></li>
        </ul>

        <output class="date" v-cloak>{{date}}</output>

        <div class="minutes"></div>
        <div class="hours"></div>
        <div class="seconds"></div>
        <div class="cercle"></div>
      </section>
    </div>
  </body>

  <script>
    window.$page = page;
    const cssProperty = ["height", "width", "fontSize"];
    const weeks = [
      "星期日",
      "星期一",
      "星期二",
      "星期三",
      "星期四",
      "星期五",
      "星期六"
    ];

    const app = new Vue({
      el: "#app",
      data: {
        date: null,
        props: page.props,
        timer: null
      },
      computed: {
        style() {
          let style = {};
          for (let prop of this.props) {
            let unit = cssProperty.includes(prop.field) ? "px" : "";
            style[prop.field] = prop.value + unit;
          }
          return style;
        }
      },
      created() {
        this.runClock();
        this.setTime();
      },
      mounted() {
        this.createSecondLines();
        window["update"] = (val, mqtt = null) => {
          this.updateProps(val);
        };
      },
      beforeDestory() {
        clearInterval(this.timer);
      },
      methods: {
        updateProps(props) {
          for (let prop of props) {
            let index = this.getPropIndex(prop.field);
            if (index !== -1) {
              let data = this.props[index];
              data.value = prop.value;
              this.$set(this.props, index, data);
            }
          }
        },
        getPropIndex(name) {
          for (let i = 0; i < this.props.length; i++) {
            if (this.props[i].field === name) {
              return i;
            }
          }
          return -1;
        },
        setTime() {
          clearInterval(this.timer);
          this.timer = setInterval(() => {
            this.runClock();
          }, 1000);
        },
        createSecondLines() {
          var clock = document.querySelector(".clock");
          var rotate = 0;

          var byFive = function(n) {
            return n / 5 === parseInt(n / 5, 10) ? true : false;
          };

          for (i = 0; i < 30; i++) {
            var span = document.createElement("span");

            if (byFive(i)) {
              span.className = "fives";
            }

            span.style.transform =
              "translate(-50%,-50%) rotate(" + rotate + "deg)";
            clock.appendChild(span);
            rotate += 6;
          }
        },
        printDate(time) {
          var months = [
            "01",
            "02",
            "03",
            "04",
            "05",
            "06",
            "07",
            "08",
            "09",
            "10",
            "11",
            "12"
          ];
          this.date =
            months[time.getMonth()] +
            " / " +
            time.getDate() +
            " " +
            weeks[time.getDay()];
        },
        runClock() {
          var time = new Date();

          var hours = time.getHours();
          var minutes = time.getMinutes();
          var seconds = time.getSeconds();

          var clock = {
            hours: document.querySelector(".hours"),
            minutes: document.querySelector(".minutes"),
            seconds: document.querySelector(".seconds")
          };

          var deg = {
            hours: 30 * hours + 0.5 * minutes,
            minutes: 6 * minutes + 0.1 * seconds,
            seconds: 6 * seconds
          };

          deg.hours += 360 / 43200;
          deg.minutes += 360 / 3600;
          deg.seconds += 360 / 60;

          clock.hours.style.transform = "rotate(" + deg.hours + "deg)";
          clock.minutes.style.transform = "rotate(" + deg.minutes + "deg)";
          clock.seconds.style.transform = "rotate(" + deg.seconds + "deg)";
          this.printDate(time);
        }
      }
    });
  </script>
</html>
