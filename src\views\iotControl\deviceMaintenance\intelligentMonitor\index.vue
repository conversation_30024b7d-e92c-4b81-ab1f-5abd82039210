<template>
  <div class="wrap">
    <el-card class="card">
      <el-button class="upgrade-btn" type="primary" size="small" @click="doUpgrade" icon="el-icon-upload2" v-if="false">批量升级</el-button>
      <el-tabs v-model="activeName" @tab-click="handleTabClick">
        <el-tab-pane label="状态监测" name="statusMonitor">
          <div class="content-top">
            <div class="btn-area">
              <el-select
                v-model="statusFilter.status"
                class="mr-10"
                size="small"
                placeholder="在线状态"
                clearable
                @change="
                  {
                    statusPage.current = 1;
                    getStatusList();
                  }
                "
                style="width: 100px"
              >
                <el-option v-for="item in DEVICE_ONLINE_STATUS" :key="item.value" :label="item.label" :value="item.value"> </el-option>
              </el-select>
               <el-select
                v-model="statusFilter.status"
                class="mr-10"
                size="small"
                placeholder="告警状态"
                clearable
                @change="
                  {
                    statusPage.current = 1;
                    getStatusList();
                  }
                "
                style="width: 100px"
              >
                <el-option v-for="item in DEVICE_ALARM_STATUS" :key="item.value" :label="item.label" :value="item.value"> </el-option>
              </el-select>
              <el-select
                size="small"
                v-model="statusFilter.spaceId"
                placeholder="空间"
                @change="
                  {
                    statusPage.current = 1;
                    getStatusList();
                  }
                "
                clearable
                class="search-input mr-10"
                filterable
              >
                <el-option v-for="(item, index) in spaceList" :key="index" :label="item.spaceName" :value="item.id"> </el-option>
              </el-select>
              <el-select
                v-model="statusFilter.gatewayId"
                class="search-input"
                size="small"
                placeholder="智慧中控"
                @change="
                  {
                    statusPage.current = 1;
                    getStatusList();
                  }
                "
                clearable
                filterable
              >
                <el-option v-for="item in gatewayList" :key="item.mac" :label="item.name" :value="item.mac"> </el-option>
              </el-select>
            </div>
            <div class="btn-area">
              <el-cascader
                v-model="statusFilter.classification"
                :options="deviceTypeOption"
                :props="{ checkStrictly: true }"
                @change="
                  {
                    statusPage.current = 1;
                    getStatusList();
                  }
                "
                size="small"
                class="search-input mr-10"
                clearable
                filterable
                placeholder="设备类型"
              ></el-cascader>
              <el-select
                v-model="statusFilter.groupType"
                class="search-input mr-10"
                size="small"
                placeholder="设备分组类型"
                clearable
                @change="
                  {
                    statusPage.current = 1;
                    getStatusList();
                  }
                "
              >
                <el-option label="未分组设备" :value="0"> </el-option>
                <el-option label="设备组" :value="1"> </el-option>
              </el-select>
              <el-input
                placeholder="设备名称"
                suffix-icon="el-icon-search"
                v-model="statusFilter.name"
                v-debounce="[
                  (e) => {
                    statusPage.current = 1;
                    getStatusList();
                  },
                ]"
                size="small"
                class="search-input mr-10"
                clearable
                @clear="
                  () => {
                    statusPage.current = 1;
                    getStatusList();
                  }
                "
              ></el-input>
              <el-input
                placeholder="设备品牌"
                suffix-icon="el-icon-search"
                v-model="statusFilter.brand"
                v-debounce="[
                  (e) => {
                    statusPage.current = 1;
                    getStatusList();
                  },
                ]"
                size="small"
                class="search-input mr-10"
                clearable
                @clear="
                  () => {
                    statusPage.current = 1;
                    getStatusList();
                  }
                "
              ></el-input>
              <el-input
                placeholder="设备型号"
                suffix-icon="el-icon-search"
                v-model="statusFilter.model"
                v-debounce="[
                  (e) => {
                    statusPage.current = 1;
                    getStatusList();
                  },
                ]"
                size="small"
                class="search-input mr-10"
                clearable
                @clear="
                  () => {
                    statusPage.current = 1;
                    getStatusList();
                  }
                "
              ></el-input>
            </div>
          </div>
          <div class="content-body">
            <el-table
              :data="statusList"
              highlight-current-row
              height="100px"
              @sort-change="doStatusSortChange"
              @row-click="handleRowClick"
              :default-sort="{ prop: 'gmtModified', order: 'descending' }"
              v-loading="statusLoading"
              v-adaptive="{ bottomOffset: 70, topOffset: 220 }"
            >
              <el-table-column label="序号" width="50" fixed="left">
                <template slot-scope="scope">
                  {{ scope.$index + (statusPage.current - 1) * statusPage.size + 1 }}
                </template>
              </el-table-column>
              <el-table-column prop="status" label="状态" min-width="80" show-overflow-tooltip align="center" sortable="custom" fixed="left">
                <template slot-scope="{ row }">
                  <div class="status-wrap" v-if="row.status">
                    <svg-icon class="status" :icon-class="getStatusType(row).icon" />
                    <el-link v-if="getStatusType(row).value == 'alarm'" type="primary" @click.stop="openAlarmDlg(row.deviceId, row.deviceName)">
                      {{ getStatusType(row).label }}
                    </el-link>
                    <div v-else>
                      {{ getStatusType(row).label }}
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="deviceName" label="设备名称" min-width="180" :show-overflow-tooltip="true" fixed="left">
                <template slot-scope="{ row }">
                  <div class="name-img-wrap">
                    <el-image
                      @click.stop
                      :src="row.logo || './iot-web/img/thumbnail/unknown_device.png'"
                      :preview-src-list="[row.logo || './iot-web/img/thumbnail/unknown_device.png']"
                      style="width: 40px; height: 40px"
                      fit="contain"
                    >
                      <div slot="error" class="image-slot">
                        <el-image @click.stop src="./iot-web/img/thumbnail/unknown_device.png"></el-image>
                      </div>
                    </el-image>
                    <div class="device-name-wrap">
                      <template v-if="row.groupType === 1">
                        <el-link type="primary" @click.stop="openGroupDeviceDlg(row)" class="device-name">
                          {{ row.deviceName }}
                        </el-link>
                      </template>
                      <template v-else>
                        <span class="device-name">{{ row.deviceName }}</span>
                      </template>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="spaceName" label="所属空间" min-width="120" :show-overflow-tooltip="true"> </el-table-column>
              <el-table-column prop="gatewayName" label="所属智慧中控" min-width="120" :show-overflow-tooltip="true"> </el-table-column>
              <el-table-column prop="deviceTypeName" label="设备类型" min-width="160" :show-overflow-tooltip="true">
                <template slot-scope="{ row }"> {{ row.deviceTypeName }}/{{ row.devSubTypeName }} </template>
              </el-table-column>
              <el-table-column prop="model" label="设备型号" min-width="140" :show-overflow-tooltip="true"> </el-table-column>
              <el-table-column prop="brand" label="设备品牌" min-width="100" :show-overflow-tooltip="true"> </el-table-column>
              <el-table-column label="操作" fixed="right" width="280" align="center" v-if="hasAnyPermission">
                <template slot-scope="{ row }">
                  <el-button
                    size="mini"
                    :disabled="!row.online"
                    class="add-btn"
                    icon="el-icon-thumb"
                    @click.stop="checkAction(row)"
                    v-action:intelligentMonitor|operateMonitor
                  >
                    操作
                  </el-button>
                  <el-button
                    size="mini"
                    class="edit-btn"
                    icon="el-icon-edit"
                    @click.stop="editDevice(row, 'status')"
                    v-action:intelligentMonitor|editMonitor
                  >
                    编辑
                  </el-button>
                  <el-button
                    class="purple-btn ml-10"
                    size="mini"
                    icon="el-icon-notebook-2"
                    @click.native.stop="checkDetail(row)"
                    v-action:intelligentMonitor|editMonitor
                  >
                    详情
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <pagination :total="statusPage.total" :page.sync="statusPage.current" :limit.sync="statusPage.size" @pagination="getStatusList" />
          </div>

          <!-- 设备组弹窗 -->
          <el-dialog
            :visible.sync="groupDeviceDlgVisible"
            :title="GroupDeviceDlgTitle"
            @close="closeGroupDeviceDlg"
            :close-on-click-modal="false"
            width="1200px"
          >
            <el-table @row-click="handleRowClick" row-key="id" :data="groupDeviceList" highlight-current-row ref="groupDeviceTable" height="700px">
              <el-table-column label="序号" width="50" fixed="left" type="index"> </el-table-column>
              <el-table-column prop="status" label="状态" sortable min-width="80" show-overflow-tooltip align="center" fixed="left">
                <template slot-scope="{ row }">
                  <div class="status-wrap" v-if="row.status">
                    <svg-icon class="status" :icon-class="getStatusType(row).icon" />
                    <el-link v-if="getStatusType(row).value == 'alarm'" type="primary" @click.stop="openAlarmDlg(row.deviceId, row.deviceName)">
                      {{ getStatusType(row).label }}
                    </el-link>
                    <div v-else>
                      {{ getStatusType(row).label }}
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="deviceName" label="设备名称" min-width="120" :show-overflow-tooltip="true" fixed="left"> </el-table-column>
              <el-table-column prop="spaceName" label="所属空间" min-width="120" :show-overflow-tooltip="true"> </el-table-column>
              <el-table-column prop="gatewayName" label="所属智慧中控" min-width="120" :show-overflow-tooltip="true"> </el-table-column>
              <el-table-column prop="deviceTypeName" label="设备类型" min-width="160" :show-overflow-tooltip="true">
                <template slot-scope="{ row }"> {{ row.deviceTypeName }}/{{ row.devSubTypeName }} </template>
              </el-table-column>
              <el-table-column prop="model" label="设备型号" min-width="140" :show-overflow-tooltip="true"> </el-table-column>
              <el-table-column prop="brand" label="设备品牌" min-width="120" :show-overflow-tooltip="true"> </el-table-column>
              <el-table-column label="操作" fixed="right" width="280" align="center">
                <template slot-scope="{ row }">
                  <el-button size="mini" class="add-btn" icon="el-icon-thumb" :disabled="!row.online" @click.stop="checkAction(row)">
                    操作
                  </el-button>
                  <el-button size="mini" class="edit-btn" icon="el-icon-edit" @click.stop="editDevice(row, 'group')"> 编辑 </el-button>
                  <el-button class="purple-btn ml-10" size="mini" icon="el-icon-notebook-2" @click.native.stop="checkDetail(row)"> 详情 </el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-dialog>
        </el-tab-pane>
        <el-tab-pane label="寿命监测" name="lifeMonitor">
          <div class="content-top">
            <div class="btn-area">
              <el-select
                v-model="lifeFilter.lifeStatus"
                class="mr-10"
                size="small"
                placeholder="寿命状态"
                clearable
                @change="
                  {
                    lifePage.current = 1;
                    getLifeList();
                  }
                "
                style="width: 100px"
              >
                <el-option v-for="item in LIFE_STATUS" :key="item.value" :label="item.label" :value="item.value"> </el-option>
              </el-select>
              <el-select
                size="small"
                v-model="lifeFilter.spaceId"
                placeholder="空间"
                @change="
                  {
                    lifePage.current = 1;
                    getLifeList();
                  }
                "
                clearable
                class="search-input mr-10"
                filterable
              >
                <el-option v-for="(item, index) in spaceList" :key="index" :label="item.spaceName" :value="item.id"> </el-option>
              </el-select>
              <el-select
                v-model="lifeFilter.gatewayId"
                class="search-input"
                size="small"
                placeholder="智慧中控"
                @change="
                  {
                    lifePage.current = 1;
                    getLifeList();
                  }
                "
                clearable
                filterable
              >
                <el-option v-for="item in gatewayList" :key="item.mac" :label="item.name" :value="item.mac"> </el-option>
              </el-select>
            </div>
            <div class="btn-area">
              <el-cascader
                v-model="lifeFilter.classification"
                :options="deviceTypeOption"
                :props="{ checkStrictly: true }"
                @change="
                  {
                    lifePage.current = 1;
                    getLifeList();
                  }
                "
                size="small"
                class="search-input mr-10"
                clearable
                filterable
                placeholder="设备类型"
              ></el-cascader>
              <el-select
                v-model="lifeFilter.groupType"
                class="search-input mr-10"
                size="small"
                placeholder="设备分组类型"
                clearable
                @change="
                  {
                    lifePage.current = 1;
                    getLifeList();
                  }
                "
              >
                <el-option label="未分组设备" :value="0"> </el-option>
                <el-option label="设备组" :value="1"> </el-option>
              </el-select>
              <el-input
                placeholder="设备名称"
                suffix-icon="el-icon-search"
                v-model="lifeFilter.name"
                v-debounce="[
                  (e) => {
                    lifePage.current = 1;
                    getLifeList();
                  },
                ]"
                size="small"
                class="search-input mr-10"
                clearable
                @clear="
                  () => {
                    lifePage.current = 1;
                    getLifeList();
                  }
                "
              ></el-input>
              <el-input
                placeholder="设备品牌"
                suffix-icon="el-icon-search"
                v-model="lifeFilter.brand"
                v-debounce="[
                  (e) => {
                    lifePage.current = 1;
                    getLifeList();
                  },
                ]"
                @clear="
                  () => {
                    lifePage.current = 1;
                    getLifeList();
                  }
                "
                size="small"
                class="search-input mr-10"
                clearable
              ></el-input>
              <el-input
                placeholder="设备型号"
                suffix-icon="el-icon-search"
                v-model="lifeFilter.model"
                v-debounce="[
                  (e) => {
                    lifePage.current = 1;
                    getLifeList();
                  },
                ]"
                @clear="
                  () => {
                    lifePage.current = 1;
                    getLifeList();
                  }
                "
                size="small"
                class="search-input mr-10"
                clearable
              ></el-input>
            </div>
          </div>
          <div class="content-body">
            <el-table
              @row-click="handleRowClick"
              :data="lifeList"
              highlight-current-row
              height="100px"
              @sort-change="doLifeSortChange"
              :default-sort="{ prop: 'gmtModified', order: 'descending' }"
              v-loading="lifeLoading"
              v-adaptive="{ bottomOffset: 70, topOffset: 220 }"
            >
              <el-table-column label="序号" width="50" fixed="left">
                <template slot-scope="scope">
                  {{ scope.$index + (lifePage.current - 1) * lifePage.size + 1 }}
                </template>
              </el-table-column>
              <el-table-column prop="lifeStatus" label="寿命状态" min-width="220" :show-overflow-tooltip="true" fixed="left">
                <template slot-scope="{ row }">
                  <div class="life-status-wrap">
                    <div class="life-tag">
                      <el-tag :class="[row.lifeStatus, 'tag']" v-if="row.lifeStatus">{{ getLifeStatusType(row).label }}</el-tag>
                    </div>
                    <el-progress
                      :percentage="getLifeProgress(row)"
                      :text-inside="true"
                      :stroke-width="18"
                      class="life-progress"
                      :color="getLifeProgressColor(row)"
                    ></el-progress>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="deviceName" label="设备名称" min-width="180" :show-overflow-tooltip="true" fixed="left">
                <template slot-scope="{ row }">
                  <div class="name-img-wrap">
                    <el-image
                      @click.stop
                      :src="row.logo || './iot-web/img/thumbnail/unknown_device.png'"
                      :preview-src-list="[row.logo || './iot-web/img/thumbnail/unknown_device.png']"
                      style="width: 40px; height: 40px"
                      fit="contain"
                    >
                      <div slot="error" class="image-slot">
                        <el-image @click.stop src="./iot-web/img/thumbnail/unknown_device.png"></el-image>
                      </div>
                    </el-image>
                    <div class="device-name-wrap">
                      <template v-if="row.groupType === 1">
                        <el-link type="primary" @click.stop="openGroupLifeMonitorDlg(row)" class="device-name">
                          {{ row.deviceName }}
                        </el-link>
                      </template>
                      <template v-else>
                        <span class="device-name">{{ row.deviceName }}</span>
                      </template>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="commissioningDateTime" label="投入使用时间" min-width="170" :show-overflow-tooltip="true"> </el-table-column>
              <el-table-column label="投入使用时长" min-width="120" :show-overflow-tooltip="true">
                <template slot-scope="{ row }">
                  {{ row.commissioningDateDayHour || '-' }}
                </template>
              </el-table-column>
              <el-table-column label="实际使用时长" min-width="120" :show-overflow-tooltip="true">
                <template slot-scope="{ row }">
                  {{ row.workTimeDayHour || '-' }}
                </template>
              </el-table-column>
              <el-table-column label="在线时长" min-width="120" :show-overflow-tooltip="true">
                <template slot-scope="{ row }">
                  {{ row.onlineDayHour || '-' }}
                </template>
              </el-table-column>
              <el-table-column prop="offlineNum" label="离线次数" min-width="100" align="center" :show-overflow-tooltip="true"> </el-table-column>
              <el-table-column prop="theoreticalLifespan" label="理论寿命时长（小时）" align="center" min-width="160" :show-overflow-tooltip="true">
              </el-table-column>
              <el-table-column prop="spaceName" label="所属空间" min-width="140" :show-overflow-tooltip="true"> </el-table-column>
              <el-table-column prop="gatewayName" label="所属智慧中控" min-width="120" :show-overflow-tooltip="true"> </el-table-column>
              <el-table-column prop="typeName" label="设备类型" min-width="160" :show-overflow-tooltip="true"> </el-table-column>
              <el-table-column prop="model" label="设备型号" min-width="140" :show-overflow-tooltip="true"> </el-table-column>
              <el-table-column prop="brand" label="设备品牌" min-width="120" :show-overflow-tooltip="true"> </el-table-column>
              <el-table-column label="操作" fixed="right" width="100" align="center" v-if="hasEditPermission">
                <template slot-scope="{ row }">
                  <el-button size="mini" class="edit-btn" icon="el-icon-edit" @click.stop="editDevice(row, 'life')"> 编辑 </el-button>
                </template>
              </el-table-column>
            </el-table>
            <pagination :total="lifePage.total" :page.sync="lifePage.current" :limit.sync="lifePage.size" @pagination="getLifeList" />
          </div>
        </el-tab-pane>
        <el-tab-pane label="保修监测" name="warrantyMonitor">
          <div class="content-top">
            <div class="btn-area">
              <el-select
                v-model="warrantyFilter.warrantyStatus"
                class="mr-10"
                size="small"
                placeholder="保修状态"
                clearable
                @change="
                  {
                    warrantyPage.current = 1;
                    getWarrantyList();
                  }
                "
                style="width: 100px"
              >
                <el-option v-for="item in WARRANTY_STATUS" :key="item.value" :label="item.label" :value="item.value"> </el-option>
              </el-select>
              <el-select
                size="small"
                v-model="warrantyFilter.spaceId"
                placeholder="空间"
                @change="
                  {
                    warrantyPage.current = 1;
                    getWarrantyList();
                  }
                "
                clearable
                class="search-input mr-10"
                filterable
              >
                <el-option v-for="(item, index) in spaceList" :key="index" :label="item.spaceName" :value="item.id"> </el-option>
              </el-select>
              <el-select
                v-model="warrantyFilter.gatewayId"
                class="search-input"
                size="small"
                placeholder="智慧中控"
                @change="
                  {
                    warrantyPage.current = 1;
                    getWarrantyList();
                  }
                "
                clearable
                filterable
              >
                <el-option v-for="item in gatewayList" :key="item.mac" :label="item.name" :value="item.mac"> </el-option>
              </el-select>
            </div>
            <div class="btn-area">
              <el-cascader
                v-model="warrantyFilter.classification"
                :options="deviceTypeOption"
                :props="{ checkStrictly: true }"
                @change="
                  {
                    warrantyPage.current = 1;
                    getWarrantyList();
                  }
                "
                size="small"
                class="search-input mr-10"
                clearable
                filterable
                placeholder="设备类型"
              ></el-cascader>
              <el-select
                v-model="warrantyFilter.groupType"
                class="search-input mr-10"
                size="small"
                placeholder="设备分组类型"
                clearable
                @change="
                  {
                    warrantyPage.current = 1;
                    getWarrantyList();
                  }
                "
              >
                <el-option label="未分组设备" :value="0"> </el-option>
                <el-option label="设备组" :value="1"> </el-option>
              </el-select>
              <el-input
                placeholder="设备名称"
                suffix-icon="el-icon-search"
                v-model="warrantyFilter.name"
                v-debounce="[
                  (e) => {
                    warrantyPage.current = 1;
                    getWarrantyList();
                  },
                ]"
                size="small"
                class="search-input mr-10"
                clearable
                @clear="
                  () => {
                    warrantyPage.current = 1;
                    getWarrantyList();
                  }
                "
              ></el-input>
              <el-input
                placeholder="设备品牌"
                suffix-icon="el-icon-search"
                v-model="warrantyFilter.brand"
                v-debounce="[
                  (e) => {
                    warrantyPage.current = 1;
                    getWarrantyList();
                  },
                ]"
                size="small"
                class="search-input mr-10"
                clearable
                @clear="
                  () => {
                    warrantyPage.current = 1;
                    getWarrantyList();
                  }
                "
              ></el-input>
              <el-input
                placeholder="设备型号"
                suffix-icon="el-icon-search"
                v-model="warrantyFilter.model"
                v-debounce="[
                  (e) => {
                    warrantyPage.current = 1;
                    getWarrantyList();
                  },
                ]"
                size="small"
                class="search-input mr-10"
                clearable
                @clear="
                  () => {
                    warrantyPage.current = 1;
                    getWarrantyList();
                  }
                "
              ></el-input>
            </div>
          </div>
          <div class="content-body">
            <el-table
              @row-click="handleRowClick"
              :data="warrantyList"
              highlight-current-row
              height="100px"
              @sort-change="doWarrantySortChange"
              :default-sort="{ prop: 'gmtModified', order: 'descending' }"
              v-loading="warrantyLoading"
              v-adaptive="{ bottomOffset: 70, topOffset: 220 }"
            >
              <el-table-column label="序号" width="50" fixed="left">
                <template slot-scope="scope">
                  {{ scope.$index + (warrantyPage.current - 1) * warrantyPage.size + 1 }}
                </template>
              </el-table-column>
              <el-table-column prop="warrantyStatus" label="保修状态" min-width="180" :show-overflow-tooltip="true" fixed="left">
                <template slot-scope="{ row }">
                  <el-tag :class="[row.warrantyStatus, 'tag']" v-if="row.warrantyStatus">{{ getWarrantyStatusType(row).label }}</el-tag>
                  <span class="warranty-day" v-if="row.warrantyStatus === 'overdue'">已过保{{ row.warrantyDay }}天</span>
                  <span class="warranty-day" v-else>剩余{{ row.warrantyDay }}天</span>
                </template>
              </el-table-column>
              <el-table-column prop="deviceName" label="设备名称" min-width="180" :show-overflow-tooltip="true" fixed="left">
                <template slot-scope="{ row }">
                  <div class="name-img-wrap">
                    <el-image
                      @click.stop
                      :src="row.logo || './iot-web/img/thumbnail/unknown_device.png'"
                      :preview-src-list="[row.logo || './iot-web/img/thumbnail/unknown_device.png']"
                      style="width: 40px; height: 40px"
                      fit="contain"
                    >
                      <div slot="error" class="image-slot">
                        <el-image @click.stop src="./iot-web/img/thumbnail/unknown_device.png"></el-image>
                      </div>
                    </el-image>
                    <div class="device-name-wrap">
                      <template v-if="row.groupType === 1">
                        <el-link type="primary" @click.stop="openGroupWarrantyMonitorDlg(row)" class="device-name">
                          {{ row.deviceName }}
                        </el-link>
                      </template>
                      <template v-else>
                        <span class="device-name">{{ row.deviceName }}</span>
                      </template>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="commissioningDate" label="投入使用时间" min-width="160" :show-overflow-tooltip="true">
                <template slot-scope="scope"> {{ scope.row.commissioningDate ? formatTimestamp(scope.row.commissioningDate) : '--' }} </template>
              </el-table-column>
              <el-table-column prop="warranty" label="保修期(年)" min-width="100" :show-overflow-tooltip="true" align="center"> </el-table-column>
              <el-table-column prop="spaceName" label="所属空间" min-width="140" :show-overflow-tooltip="true"> </el-table-column>
              <el-table-column prop="gatewayName" label="所属智慧中控" min-width="120" :show-overflow-tooltip="true"> </el-table-column>
              <el-table-column prop="typeName" label="设备类型" min-width="160" :show-overflow-tooltip="true"> </el-table-column>
              <el-table-column prop="model" label="设备型号" min-width="140" :show-overflow-tooltip="true"> </el-table-column>
              <el-table-column prop="brand" label="设备品牌" min-width="120" :show-overflow-tooltip="true"> </el-table-column>
              <el-table-column label="操作" fixed="right" width="100" align="center" v-if="hasEditPermission">
                <template slot-scope="{ row }">
                  <el-button size="mini" class="edit-btn" icon="el-icon-edit" @click.stop="editDevice(row, 'warranty')"> 编辑 </el-button>
                </template>
              </el-table-column>
            </el-table>
            <pagination :total="warrantyPage.total" :page.sync="warrantyPage.current" :limit.sync="warrantyPage.size" @pagination="getWarrantyList" />
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <el-drawer title="设备详情" :visible.sync="drawer">
      <div class="divider"></div>
      <div class="device-detail">
        <el-descriptions title="基本信息" border :column="1">
          <el-descriptions-item label="设备名称">{{ drawerDeviceDetail.deviceName }}</el-descriptions-item>
          <el-descriptions-item label="设备ID">{{ drawerDeviceDetail.deviceId }}</el-descriptions-item>
          <el-descriptions-item label="理论寿命时长(小时)" v-if="activeName === 'lifeMonitor'">
            {{ `${drawerDeviceDetail.theoreticalLifespan}/约${Math.trunc(drawerDeviceDetail.theoreticalLifespan / 24)}天` || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="所属空间">{{ drawerDeviceDetail.spaceName }}</el-descriptions-item>
          <el-descriptions-item label="所属智慧中控">{{ drawerDeviceDetail.gatewayName }}</el-descriptions-item>
          <el-descriptions-item label="设备类型" v-if="activeName === 'statusMonitor'">
            {{ drawerDeviceDetail.deviceTypeName }}/{{ drawerDeviceDetail.devSubTypeName }}
          </el-descriptions-item>
          <el-descriptions-item label="设备类型" v-if="['lifeMonitor', 'warrantyMonitor'].includes(activeName)">
            {{ drawerDeviceDetail.typeName }}
          </el-descriptions-item>
          <el-descriptions-item label="设备型号">{{ drawerDeviceDetail.model }}</el-descriptions-item>
          <el-descriptions-item label="设备品牌">{{ drawerDeviceDetail.brand }}</el-descriptions-item>
          <el-descriptions-item label="版本信息" v-if="activeName === 'statusMonitor'">{{ drawerDeviceDetail.deviceVersion }}</el-descriptions-item>
          <el-descriptions-item label="产品编码" v-if="activeName === 'statusMonitor'">{{ drawerDeviceDetail.code }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-drawer>
  </div>
</template>

<script>
  import { hasPermission } from '@/directive/permission/index.js';
  import { DEVICE_STATUS_ALL, LIFE_STATUS, WARRANTY_STATUS ,DEVICE_ONLINE_STATUS,DEVICE_ALARM_STATUS} from '../../enum';
  import deviceEditDialog from './modules/deviceEditDialog.vue';
  import groupLifeMonitor from './modules/groupLifeMonitor.vue';
  import groupWarrantyMonitor from './modules/groupWarrantyMonitor.vue';
  import {
    getNewDeviceTypes,
    deviceGetDeviceList,
    getAssetLifeMonitor,
    getAllGatewaySpace,
    getGroupDevices,
    getAllGatewayList,
    getAssetWarrantyMonitor,
  } from '@/api/iotControl';
  import alarm from './modules/alarm.vue';
  import transform from '@/utils/transform';
  import { calculateDuration } from '@/utils/utils';

  export default {
    components: {
      deviceEditDialog,
      alarm,
      groupLifeMonitor,
      groupWarrantyMonitor,
    },
    data() {
      return {
        DEVICE_STATUS_ALL,
        DEVICE_ONLINE_STATUS,
        DEVICE_ALARM_STATUS,
        LIFE_STATUS,
        WARRANTY_STATUS,
        activeName: 'statusMonitor',
        gatewayList: [],
        spaceList: [],
        deviceTypeOption: [],
        statusLoading: false,
        statusList: [],
        statusPage: {
          total: 0,
          current: 1,
          size: 20,
        },
        statusFilter: {
          status: '',
          name: '',
          classification: [],
          gatewayId: '',
          spaceId: '',
          groupType: '',
          brand: '',
          model: '',
        },
        statusSort: {
          direction: 'DESC',
          properties: 'gmtModified',
        },
        // 组内设备列表弹窗
        groupDeviceDlgVisible: false,
        groupDeviceList: [],
        GroupDeviceDlgTitle: '',
        GroupDeviceId: '',
        lifeLoading: false,
        lifeList: [],
        lifePage: {
          total: 0,
          current: 1,
          size: 20,
        },
        lifeFilter: {
          lifeStatus: '',
          name: '',
          classification: [],
          gatewayId: '',
          spaceId: '',
          groupType: '',
          brand: '',
          model: '',
        },
        lifeSort: {
          direction: 'DESC',
          properties: 'gmtModified',
        },
        warrantyLoading: false,
        warrantyList: [],
        warrantyPage: {
          total: 0,
          current: 1,
          size: 20,
        },
        warrantyFilter: {
          warrantyStatus: '',
          name: '',
          classification: [],
          gatewayId: '',
          spaceId: '',
          groupType: '',
          brand: '',
          model: '',
        },
        warrantySort: {
          direction: 'DESC',
          properties: 'gmtModified',
        },
        drawer: false,
        //侧边抽屉查看的设备详情
        drawerDeviceDetail: {},
        timer: null,
      };
    },
    created() {
      this.activeName = this.$route.params.activeType || 'statusMonitor';
      this.getSpaceList();
      this.getGatewayList();
      this.getDeviceTypes();
      this.getStatusList();
      this.getLifeList();
      this.getWarrantyList();
      this.timer = setInterval(() => {
        this.getStatusList();
      }, 10 * 1000);
    },
    beforeDestroy() {
      clearInterval(this.timer);
    },
    methods: {
      doUpgrade() {},
      getSpaceList() {
        getAllGatewaySpace({ includeChild: true }).then((response) => {
          this.spaceList = response.data;
        });
      },
      getGatewayList() {
        getAllGatewayList({
          approved: true,
        }).then((response) => {
          this.gatewayList = response.data;
        });
      },
      getDeviceTypes() {
        getNewDeviceTypes().then(({ data }) => {
          this.deviceTypeOption = data;
        });
      },
      getStatusList() {
        if (this.statusList.length === 0) {
          this.statusLoading = true;
        }
        let params = {
          status: this.statusFilter.status,
          deviceName: this.statusFilter.name,
          deviceType: this.statusFilter.classification.length > 0 ? this.statusFilter.classification[0] : '',
          devSubType: this.statusFilter.classification.length > 1 ? this.statusFilter.classification[1] : '',
          gatewayId: this.statusFilter.gatewayId,
          spaceId: this.statusFilter.spaceId,
          page: this.statusPage.current,
          size: this.statusPage.size,
          direction: this.statusSort.direction,
          properties: this.statusSort.properties,
          groupType: this.statusFilter.groupType,
          brand: this.statusFilter.brand,
          model: this.statusFilter.model,
        };

        return deviceGetDeviceList(params)
          .then(({ data }) => {
            this.statusList = data.rows;
            this.statusPage.total = data.total;
          })
          .finally(() => {
            this.statusLoading = false;
          });
      },
      getStatusType(row) {
        return this.DEVICE_STATUS_ALL.find((item) => item.value === row.status);
      },
      getLifeStatusType(row) {
        return this.LIFE_STATUS.find((item) => item.value === row.lifeStatus);
      },
      alarmDlg: transform(alarm),
      openAlarmDlg(deviceId, deviceName) {
        this.alarmDlg({
          propsData: {
            deviceId: deviceId,
            deviceName: deviceName,
          },
          methods: {
            refresh: () => {
              if (this.groupDeviceDlgVisible) {
                getGroupDevices({
                  groupId: this.GroupDeviceId,
                }).then((res) => {
                  this.groupDeviceList = res.data;
                });
                this.getStatusList();
              } else {
                this.getStatusList();
              }
            },
          },
        });
      },
      deviceEditDlg: transform(deviceEditDialog),
      /**
       * 编辑 重命名
       * @param {*} row
       */
      editDevice(row, type) {
        let that = this;
        this.deviceEditDlg({
          propsData: {
            deviceObj: row,
          },
          methods: {
            refresh() {
              if (type === 'status') {
                that.getStatusList();
              } else if (type === 'life') {
                that.getLifeList();
              } else if (type === 'warranty') {
                that.getWarrantyList();
              } else if (type === 'group') {
                getGroupDevices({
                  groupId: that.GroupDeviceId,
                }).then((res) => {
                  that.groupDeviceList = res.data;
                });
              }
            },
          },
        });
      },
      doStatusSortChange(col) {
        this.statusSort.direction = 'ascending' === col.order ? 'ASC' : 'DESC';
        this.statusSort.properties = col.prop;
        this.getStatusList();
      },
      /**
       * 查看设备组弹窗 组内设备
       * @param {*} row
       */
      openGroupDeviceDlg(row) {
        this.GroupDeviceDlgTitle = row.deviceName;
        this.GroupDeviceId = row.deviceId;
        let params = {
          groupId: row.deviceId,
        };
        getGroupDevices(params).then((res) => {
          this.groupDeviceList = res.data;
        });
        this.groupDeviceDlgVisible = true;
      },
      closeGroupDeviceDlg() {
        this.GroupDeviceId = '';
        this.GroupDeviceDlgTitle = '';
        this.groupDeviceList.length = 0;
        this.groupDeviceDlgVisible = false;
      },
      handleTabClick(tab, event) {
        if (tab.index === '0') {
          this.getStatusList();
        } else if (tab.index === '1') {
          this.getLifeList();
        } else if (tab.index === '2') {
          this.getWarrantyList();
        }
      },
      checkDetail(row) {
        this.$router.push({
          name: 'IotDeviceInfo',
          params: {
            id: row.deviceId,
          },
        });
      },
      /**
       * 跳转设备操作页
       * @param {*} row
       */
      checkAction(row) {
        this.$router.push({
          name: 'IotDeviceAction',
          params: {
            id: row.deviceId,
            deviceType: row.deviceType,
            devSubType: row.devSubType,
            model: row.model,
          },
        });
      },
      formatTimestamp(timestamp) {
        const date = new Date(timestamp);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');

        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      },
      /**
       * 获取相隔天数
       * @param {Number} start 开始时间戳
       * @param {Number} end  结束时间戳
       */
      getDaysBetween(start, end) {
        if (!start || !end) {
          return 0;
        }
        return Math.ceil((new Date(end).getTime() - new Date(start).getTime()) / (1000 * 60 * 60 * 24));
      },
      /**
       * 返回天数
       * @param {秒时长} secondDuration
       */
      getDaysFromDuration(secondDuration) {
        return Math.ceil(secondDuration / (60 * 60 * 24));
      },
      getLifeList() {
        if (this.lifeList.length === 0) {
          this.lifeLoading = true;
        }
        let params = {
          page: this.lifePage.current,
          size: this.lifePage.size,
          spaceId: this.lifeFilter.spaceId,
          gatewayId: this.lifeFilter.gatewayId,
          direction: this.lifeSort.direction,
          properties: this.lifeSort.properties,
          deviceName: this.lifeFilter.name,
          deviceType: this.lifeFilter.classification.length > 0 ? this.lifeFilter.classification[0] : '',
          devSubType: this.lifeFilter.classification.length > 1 ? this.lifeFilter.classification[1] : '',
          groupType: this.lifeFilter.groupType,
          brand: this.lifeFilter.brand,
          model: this.lifeFilter.model,
          lifeStatus: this.lifeFilter.lifeStatus,
        };
        getAssetLifeMonitor(params)
          .then((res) => {
            const dataList = res.data.rows || [];
            dataList.forEach((item) => {
              item.commissioningDateTime = item.commissioningDate ? this.formatTimestamp(item.commissioningDate) : '-';
              // 投入使用时长
              item.commissioningDateDayHour = '';
              if (item.commissioningDate) {
                const { days, hours } = calculateDuration(new Date().getTime() - item.commissioningDate);
                item.commissioningDateDayHour = `${days}天${hours}小时`;
              }

              // 实际使用时长
              item.workTimeDayHour = '';
              if (item.workTime) {
                const { days, hours } = calculateDuration(item.workTime, 's');
                item.workTimeDayHour = `${days}天${hours}小时`;
              }

              // 在线时长
              item.onlineDayHour = '';
              if (item.onlineDuration) {
                const { days, hours } = calculateDuration(item.onlineDuration, 's');
                item.onlineDayHour = `${days}天${hours}小时`;
              }
            });
            this.lifeList = dataList;
            this.lifePage.total = res.data.total;
          })
          .finally(() => {
            this.lifeLoading = false;
          });
      },
      doLifeSortChange(col) {
        this.lifeSort.direction = 'ascending' === col.order ? 'ASC' : 'DESC';
        this.lifeSort.properties = col.prop;
        this.getLifeList();
      },
      getWarrantyList() {
        if (this.warrantyList.length === 0) {
          this.warrantyLoading = true;
        }
        let params = {
          page: this.warrantyPage.current,
          size: this.warrantyPage.size,
          spaceId: this.warrantyFilter.spaceId,
          gatewayId: this.warrantyFilter.gatewayId,
          direction: this.warrantySort.direction,
          properties: this.warrantySort.properties,
          deviceName: this.warrantyFilter.name,
          deviceType: this.warrantyFilter.classification.length > 0 ? this.warrantyFilter.classification[0] : '',
          devSubType: this.warrantyFilter.classification.length > 1 ? this.warrantyFilter.classification[1] : '',
          groupType: this.warrantyFilter.groupType,
          brand: this.warrantyFilter.brand,
          model: this.warrantyFilter.model,
          warrantyStatus: this.warrantyFilter.warrantyStatus,
        };
        getAssetWarrantyMonitor(params)
          .then((res) => {
            this.warrantyList = res.data.rows;
            this.warrantyPage.total = res.data.total;
          })
          .finally(() => {
            this.warrantyLoading = false;
          });
      },
      getWarrantyStatusType(row) {
        return this.WARRANTY_STATUS.find((item) => item.value === row.warrantyStatus);
      },
      doWarrantySortChange(col) {
        this.warrantySort.direction = 'ascending' === col.order ? 'ASC' : 'DESC';
        this.warrantySort.properties = col.prop;
        this.getWarrantyList();
      },
      groupLifeMonitorDlg: transform(groupLifeMonitor),
      openGroupLifeMonitorDlg(row) {
        let that = this;
        this.groupLifeMonitorDlg({
          propsData: {
            groupId: row.deviceId,
            groupName: row.deviceName,
          },
          methods: {
            rowClick(row) {
              that.drawerDeviceDetail = row;
              that.drawer = true;
            },
          },
        });
      },
      groupWarrantyMonitorDlg: transform(groupWarrantyMonitor),
      openGroupWarrantyMonitorDlg(row) {
        let that = this;
        this.groupWarrantyMonitorDlg({
          propsData: {
            groupId: row.deviceId,
            groupName: row.deviceName,
          },
          methods: {
            rowClick(row) {
              that.drawerDeviceDetail = row;
              that.drawer = true;
            },
          },
        });
      },
      handleRowClick(row) {
        this.drawerDeviceDetail = row;
        this.drawer = true;
      },
      getLifeProgress(row) {
        // workTime 单位秒 theoreticalLifespan 单位小时
        const progress = 100 - Math.trunc((row.workTime / 3600 / row.theoreticalLifespan) * 100);
        if (progress < 0) {
          return 0;
        }
        // console.log('progress', progress);
        return Number(progress.toFixed(2));
      },
      getLifeProgressColor(row) {
        if (row.lifeStatus === 'fresh') {
          return '#3dcca6';
        } else if (row.lifeStatus === 'good') {
          return '#87CEFA';
        } else if (row.lifeStatus === 'renew') {
          return '#fabe46';
        } else if (row.lifeStatus === 'overdue') {
          return '#f56c6c';
        }
      },
    },
    computed: {
      hasAnyPermission() {
        return hasPermission(undefined, 'intelligentMonitor', 'editMonitor') || hasPermission(undefined, 'intelligentMonitor', 'operateMonitor');
      },
      hasEditPermission() {
        return hasPermission(undefined, 'intelligentMonitor', 'editMonitor');
      },
    },
  };
</script>

<style lang="scss" scoped>
  .wrap {
    margin: 10px 0px 20px 0px;
    height: 100%;
    user-select: text;
  }
  .card {
    height: 100%;
    position: relative;
    .upgrade-btn {
      position: absolute;
      top: 16px;
      right: 24px;
      z-index: 2;
    }
    ::v-deep .el-card__body {
      height: 100%;
      padding: 20px 20px 0 20px;
      .el-tabs {
        height: 100%;
        display: flex;
        flex-direction: column;
        .el-tabs__content {
          flex: 1;
          height: 0;
          .el-tab-pane {
            height: 100%;
            display: flex;
            overflow: hidden;
            flex-direction: column;
            .content-body {
              flex: 1;
            }
          }
        }
      }
    }
  }
  .content-top .btn-area {
    margin: 0 0 10px 0;
  }
  .search-input {
    width: 160px;
  }
  .status-wrap {
    display: flex;
    width: 100%;
    justify-content: center;
    align-items: center;
  }
  .status {
    margin-right: 0.5em;
  }
  .name-img-wrap {
    display: flex;
    align-items: center;
    width: 100%;

    .device-name-wrap {
      flex: 1;
      margin-left: 5px;
      min-width: 0;
      .device-name {
        display: inline-block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 100%;
      }
    }
  }
  .tag {
    &.fresh {
      color: #808080;
      background-color: rgba(61, 204, 166, 0.3);
      border: 1px solid #3dcca6;
    }
    &.good,
    &.under {
      color: #808080;
      background-color: rgba(135, 206, 250, 0.3);
      border: 1px solid #87cefa;
    }
    &.renew,
    &.expiring {
      color: #808080;
      background-color: rgba(250, 190, 70, 0.3);
      border: 1px solid #fabe46;
    }
    &.overdue {
      color: #808080;
      background-color: rgba(245, 108, 108, 0.3);
      border: 1px solid #f56c6c;
    }
  }
  .warranty-day {
    margin-left: 10px;
  }
  .device-detail {
    margin: 20px;
  }
  .divider {
    background: #dcdfe6;
    height: 1px;
    width: 100%;
  }
  .life-status-wrap {
    width: 220px;
    display: flex;
    align-items: center;
    .life-tag {
      width: 71px;
    }
    .life-progress {
      width: 120px;
      margin-left: 10px;
    }
  }
</style>
