import request from '@/utils/request';
import $qs from 'qs';

/**
 * 获取机构列表（根机构获取所有机构列表）
 */
export function getDepartementList() {
  return request({
    url: '/oauth/department',
    method: 'get',
  });
}

/**
 * 获取当前用户所在公司树（根机构返回所有机构列表）
 */
export function getCompanyDept() {
  return request({
    url: '/oauth/department/allInCompany',
    method: 'get',
  });
}

/**
 * 获取公司下的机构（根机构只返回根机构，不包括子机构）
 * 会管中使用
 * @param companyId 公司id
 */
export function getOwnCompanyDept(companyId) {
  return request({
    url: '/oauth/department/company/' + companyId,
    method: 'get',
  });
}

/**
 * 查询当前用户所在公司树（根机构只返回根机构，不包括子机构）
 * 会管中使用
 */
export function getOwnCompanyDeptList() {
  return request({
    url: '/weapp/prepare/company',
    method: 'get',
  });
}

/**
 * 查询当前用户所在部门及子部门树（根机构只返回根机构，不包括子机构）
 * 会管中使用
 */
export function getOwnDepartementList() {
  return request({
    url: '/weapp/prepare/department',
    method: 'get',
  });
}

/**
 * 查询当前用户所在机构
 */
export function getMyDept() {
  return request({
    url: '/oauth/v2/my/dept',
    method: 'get',
  });
}

/**
 * 查询当前用户所在公司
 */
export function getMyCompany() {
  return request({
    url: '/oauth/v2/my/company',
    method: 'get',
  });
}

/**
 * 查询指定机构的子机构（不包括后代机构)
 * @param deptId 指定机构id
 */
export function getChildrenDept(deptId) {
  return request({
    url: `/oauth/v2/department/${deptId}/children`,
    method: 'get',
  });
}

/**
 * 根据机构名称查询机构
 * @param rootDeptId 根机构id
 * @param nameToSearch 机构名称
 */
export function getDeptByName(rootDeptId, nameToSearch = '') {
  return request({
    url: `/oauth/v2/department/search/${rootDeptId}/${nameToSearch}`,
    method: 'get',
  });
}

/**
 * 根据机构id查询机构(根机构查询直到根机构，非根机构查询直到公司)
 * @param deptId 机构id
 */
export function getDeptById(deptId) {
  return request({
    url: `/oauth/v2/department/path/${deptId}`,
    method: 'get',
  });
}

/**
 * 获取部门详情
 * @param {*} id
 * @returns
 */
export function getDepartementInfo(id) {
  return request({
    url: `/oauth/department/info/${id}`,
    method: 'get',
  });
}

/**
 * 指定机构的存储空间信息
 * @param deptId 机构id
 * @returns
 */
export function getDeptStorage(deptId) {
  return request({
    url: `/oauth/v2/department/${deptId}/storage`,
    method: 'get',
  });
}

// 添加机构
export function addDepartment(data) {
  return request({
    url: '/oauth/department',
    method: 'post',
    headers: {'Content-Type': 'application/json'},
    data: data,
  });
}

// 编辑机构
export function updateDepartment(data) {
  return request({
    url: '/oauth/department',
    method: 'put',
    headers: {'Content-Type': 'application/json'},
    data: data,
  });
}

//删除机构
export function deleteDepartement(departmentId) {
  return request({
    url: '/oauth/department/' + departmentId,
    method: 'delete',
  });
}

//查看终端数据
export function getTermListInDept(data) {
  return request({
    url: '/webapp/terminal/getTermListInDept',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//获取角色列表
export function getRoleList(data) {
  return request({
    url: '/role/getAll',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//添加角色
export function addRoleList(data) {
  return request({
    url: '/role/add',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//更新角色
export function updateRoleList(data) {
  return request({
    url: '/role/update',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//删除角色
export function deleteRoleList(data) {
  return request({
    url: '/role/delete',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//获取角色菜单
export function getRoleAuth(data) {
  return request({
    url: '/role/getMenuAuth',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//获取属于角色的用户
export function getUserInRole(data) {
  return request({
    url: '/role/getUsersInRole',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//获取用户角色
export function getUserRole(data) {
  return request({
    url: '/user/getUserRoles',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//获取用户列表
export function getUsersInDepartment(departmentId, page, size, direction = 'ASC', properties = 'username') {
  return request({
    url: '/oauth/user/department/' + departmentId + '/' + page + '/' + size + '/' + direction + '/' + properties,
    method: 'get',
  });
}

/**
 * 获取机构中用户列表
 * @param deptId 机构id
 * @param includeChild 包含下级
 * @param username 名称搜索
 * @param page
 * @param size
 * @param direction
 * @param property
 */
export function getUsersInDept(data) {
  data.direction = data.direction || 'ASC';
  data.properties = data.properties || 'username';
  return request({
    url: '/weapp/prepare/userInDept',
    method: 'get',
    params: data,
  });
}

//获取机构下的用户列表
export function getUsersInDepartmentWithNoPagination(departmentId) {
  return request({
    url: '/oauth/user/department/' + departmentId,
    method: 'get',
  });
}

//添加用户
export function addRole(data) {
  return request({
    url: '/user/add',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//编辑用户
export function updateRole(data) {
  return request({
    url: '/user/update',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//删除用户
export function deleteRole(data) {
  return request({
    url: '/user/delete',
    method: 'post',
    data: $qs.stringify(data),
  });
}

/**
 * 用户解锁
 * @param {*} userid
 * @returns
 */
export function unLockUser(userid) {
  return request({
    url: `/oauth/user/unlock/${userid}`,
    method: 'get',
  });
}

/**
 * 用户密码重置
 * @param {*} userid
 * @returns
 */
export function resetUser(userid) {
  return request({
    url: `/oauth/user/pwd/reset/${userid}`,
    method: 'get',
  });
}

//批量重置用户密码
export function batchReset(ids) {
  return request({
    url: `/oauth/user/pwd/batchReset`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
    data: ids,
  });
}

//批量解锁用户
export function batchUnLock(ids) {
  return request({
    url: `/oauth/user/batchUnlock`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
    data: ids,
  });
}

//批量启用/禁用用户
export function batchEnable(data) {
  return request({
    url: `/oauth/user/batchEnable`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

// 获取系统日志列表
// login true 登录日志  false 操作日志
export function getLogList(data) {
  return request({
    url: '/oplog',
    method: 'get',
    params: data,
  });
}

/**
 * 登陆统计
 * @returns
 */
export function getLoginStatistic(data) {
  return request({
    url: '/oplog/statistic/login',
    method: 'get',
  });
}

/**
 * 获取运行日志目录
 * @param {*} data
 * @returns
 */
export function getOplogDir(data) {
  return request({
    url: '/oplog/dir',
    method: 'get',
    params: data,
  });
}

/**
 * 预览运行日志
 * @param {*} data
 * @returns
 */
export function previewOplogDir(data) {
  return request({
    url: '/oplog/preview',
    method: 'post',
    params: data,
  });
}

//获取审核链列表
export function getChainList(data) {
  return request({
    url: '/webapp/approveChain/getChainList',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//获取审核人员
export function getApproveUser(data) {
  return request({
    url: '/user/getApproveUser',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//更新审核人员
export function updateApproveUser(data) {
  return request({
    url: '/approveChain/update',
    method: 'post',
    data,
  });
}

//删除审核人员
export function deleteApproveUser(data) {
  return request({
    url: '/approveChain/deleteChain',
    method: 'post',
    data,
  });
}

//添加审核链列表
export function addChainList(data) {
  return request({
    url: '/webapp/approveChain/addChain',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

//编辑审核链列表
export function updateChainList(data) {
  return request({
    url: '/webapp/approveChain/update',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

//删除审核链列表
export function deleteChainList(data) {
  return request({
    url: '/webapp/approveChain/deleteChain',
    method: 'post',
    data: $qs.stringify(data),
  });
}

/**
 * 获取公司列表(不包含虚拟总公司)
 */

export function getCompanyList() {
  return request({
    url: '/oauth/department/company',
    method: 'get',
  });
}

//获取公司下的角色
export function getCompanyRole(deptId) {
  return request({
    url: '/oauth/role/inferiors/' + deptId,
    method: 'get',
  });
}

//获取机构下的角色(不包括下级)
export function getDeptRole(deptId) {
  return request({
    url: '/oauth/role/inferiors/dept/' + deptId,
    method: 'get',
  });
}

/**
 * 获取公司下的用户
 */
export function getUsersInCompany(departmentId, page, size, username, direction = 'DESC', property = 'username') {
  return request({
    url: `/oauth/user/company/${departmentId}/${page}/${size}/${direction}/${property}/${username}`,
    method: 'get',
  });
}

//启用用户
export function enableUser(username) {
  return request({
    url: '/oauth/user/enable/' + username,
    method: 'get',
  });
}

//禁用用户
export function disableUser(username) {
  return request({
    url: '/oauth/user/disable/' + username,
    method: 'get',
  });
}

//导出用户信息
export function importUser(data) {
  let file = new FormData();
  file.append('file', data);
  return request({
    url: 'oauth/user/import',
    method: 'post',
    data: file,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

/**
 * 导入机构
 * @param {*} data
 * @returns
 */
export function importDept(data) {
  let file = new FormData();
  file.append('file', data);
  return request({
    url: '/oauth/department/import',
    method: 'post',
    data: file,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

/**
 * 查询ldap同步服务列表
 */
export function getLdapList(data) {
  return request({
    url: '/oauth/ldap',
    method: 'get',
    params: data,
  });
}

/**
 * 增加ldap同步服务
 */
export function addLdap(data) {
  return request({
    url: '/oauth/ldap',
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

/**
 * 编辑ldap同步服务
 */
export function editLdap(data) {
  return request({
    url: '/oauth/ldap',
    method: 'put',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

/**
 * 删除ldap同步服务
 */
export function deleteLdap(data) {
  return request({
    url: '/oauth/ldap',
    method: 'delete',
    data: $qs.stringify(data),
  });
}

/**
 * 开启ldap同步服务
 */
export function startLdap(data) {
  return request({
    url: '/oauth/ldap/call',
    method: 'get',
    params: data,
  });
}

/**
 * 关闭ldap同步服务
 */
export function closeLdap(data) {
  return request({
    url: '/oauth/ldap/shutdown',
    method: 'get',
    params: data,
  });
}

/**
 * ldap同步
 */
export function syncLdap(data) {
  return request({
    url: '/oauth/ldap/sync',
    method: 'get',
    params: data,
  });
}

/**
 * 获取组织同步列表
 */
export function getSyncList(data) {
  return request({
    url: '/oauth/synchronizer',
    method: 'get',
    params: data,
  });
}

/**
 * 查询组织同步类型
 */
export function getSyncType() {
  return request({
    url: '/oauth/synchronizer/type',
    method: 'get',
  });
}

/**
 * 获取组织同步设置参数
 */
export function getSyncParam(data) {
  return request({
    url: '/oauth/synchronizer/parameters',
    method: 'get',
    params: data,
  });
}

/**
 * 创建组织同步
 */
export function addSync(data) {
  return request({
    url: '/oauth/synchronizer',
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

/**
 * 编辑组织同步
 */
export function editSync(data) {
  return request({
    url: '/oauth/synchronizer',
    method: 'put',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

/**
 * 删除组织同步
 */
export function deleteSync(data) {
  return request({
    url: '/oauth/synchronizer',
    method: 'delete',
    params: data,
  });
}

/**
 * 执行组织同步
 */
export function startSync(data) {
  return request({
    url: '/oauth/synchronizer/sync',
    method: 'get',
    params: data,
  });
}

/**
 * 获取组织同步历史
 */
export function getSyncHistory(data) {
  return request({
    url: '/oauth/synchronizer/history',
    method: 'get',
    params: data,
  });
}

/**
 * 查询服务器列表
 * @param companyToSearch 公司名称
 * @param nameToSearch 名称
 */
export function getServerList(data) {
  return request({
    url: '/dashboard/server',
    method: 'get',
    params: data,
  });
}

/**
 * 查询服务器列表(分页)
 * @param params-page
 * @param params-size
 * @param params-direction
 * @param params-property
 * @param params-nameToSearch 公司名称
 * @param params-ipToSearch 名称
 */
export function getServerListByPage(params) {
  return request({
    url: '/dashboard/server/page',
    method: 'get',
    params,
  });
}

/**
 * 增加服务器
 */
export function addServer(data) {
  return request({
    url: '/dashboard/server',
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

/**
 * 编辑服务器
 */
export function editServer(data) {
  return request({
    url: '/dashboard/server',
    method: 'put',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

/**
 * 删除服务器
 */
export function deleteServer(data) {
  return request({
    url: '/dashboard/server',
    method: 'delete',
    data: $qs.stringify(data),
  });
}

/**
 * 查看异常报告
 * @param {*} page size
 * @returns
 */
export function getReport(data) {
  return request({
    url: '/weapp/report',
    method: 'get',
    params: data,
  });
}

/**
 * 删除异常报告
 * @param {*} data
 * @returns
 */
export function delReport(data) {
  return request({
    url: '/weapp/report',
    method: 'delete',
    params: data,
  });
}

/**
 * 获取SMC上的组织机构
 * @param {*} mcuId
 * @returns
 */
export function getSmcDept(mcuId) {
  return request({
    url: `/weapp/sync/dept?mcuId=${mcuId}`,
    method: 'get',
  });
}

/**
 * 查询组织架构
 * @param {*} mcuId
 * @param {*} uuid
 * @returns
 */
export function searchSmcDept(mcuId, uuid) {
  return request({
    url: `/weapp/sync/searchDept?mcuId=${mcuId}&uuid=${uuid}`,
    method: 'get',
  });
}

/**
 * 保存组织架构
 * @param {*} mcuId
 * @param {*} uuid
 * @param {*} deptId
 * @returns
 */
// mcuId, uuid, deptId
export function syncSmcDept(data) {
  return request({
    // url: `/weapp/sync/saveDept?mcuId=${mcuId}&uuid=${uuid}&deptId=${deptId}`,
    url: `/weapp/sync/saveDept`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

/**
 * 分页查询终端
 * mcuId
 * name
 * page
 * row
 * label 筛选条件类型
 * value 筛选条件的值
 * @returns
 */
export function getSmcTerminalByPage(data) {
  return request({
    url: `/weapp/sync/searchTerminal`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

/**
 * 保存视频终端数据
 * @param {*} mcuId
 * @param {*} deptId
 * @param {*} coverageId
 * @returns
 */
export function syncSmcTerminal(data) {
  return request({
    url: `/weapp/sync/saveTerminal`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

/**
 * 查看公告列表
 * @returns []
 */
export function getBulletin() {
  return request({
    url: `/oauth/bulletin`,
    method: 'get',
  });
}

/**
 * 删除公告
 * @param {*} data
 * @returns
 */
export function delBulletin(ids) {
  return request({
    url: `/oauth/bulletin?ids=${ids}`,
    method: 'delete',
  });
}

/**
 * 新建公告
 * @param {*} data
 "msg":"假期期间禁止出市，全体市民就地过节",
 "deadline":1664349000000
 * @returns
 */
export function createBulletin(data) {
  return request({
    url: `/oauth/bulletin`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
    data,
  });
}

/**
 * 获取外部系统链接
 * @returns
 */
export function getExternalSystemLink() {
  return request({
    url: `/sysManagement/externalLink`,
    method: 'get',
  });
}

/**
 * 发送deepseek聊天
 * @returns
 */
export function deepseekChat(data) {
  return request({
    url: `/oauth/deepseek/chat`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
    data
  });
}

// MQTT发布与接收公告 mqtt topic
// dss2/wx/bulletin/path  path是当前部门到根目录的路径

// 接收公告的mqtt topic
// dss2/wx/bulletin/deptId/#   deptId是用户所在部门Id
