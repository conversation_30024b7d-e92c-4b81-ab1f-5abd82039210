import html2Canvas from 'html2canvas';
import { jsPDF } from 'jspdf';

// pdfDom 页面dom , fileName 文件名
export function htmlToPdf(pdfDom, fileName = '巡检报表') {
  // A4 纸宽高
  const A4_WIDTH = 592.28,
    A4_HEIGHT = 841.89;
  html2Canvas(pdfDom, {
    useCORS: true, //是否尝试使用 CORS 从服务器加载图片
    allowTaint: true, //是否允许不同源的图片污染画布
    scale: 3, // 用于渲染的比例
  })
    .then((canvas) => {
      const pdf = new jsPDF('', 'pt', 'a4', true);
      // 计算 pdf 总高度
      const pdfHeight = (A4_WIDTH / canvas.width) * canvas.height;
      // A4_WIDTH + 4, pdfHeight + 4避免图片左右上下出现白边
      if (pdfHeight <= A4_HEIGHT) {
        // 单页情况
        pdf.addImage(canvas.toDataURL('image/jpeg', 1.0), 'JPEG', 0, 0, A4_WIDTH + 4, pdfHeight + 4);
      } else {
        // 多页情况
        let remainingHeight = canvas.height;
        let position = 0;
        const pageHeight = (canvas.width / A4_WIDTH) * A4_HEIGHT;

        while (remainingHeight > 0) {
          pdf.addImage(canvas.toDataURL('image/jpeg', 1.0), 'JPEG', 0, position, A4_WIDTH + 4, pdfHeight + 4);
          remainingHeight -= pageHeight;
          position -= A4_HEIGHT;

          if (remainingHeight > 0) {
            pdf.addPage();
          }
        }
      }
      pdf.save(`${fileName}.pdf`);
    })
    .catch((err) => {
      console.log(err);
    });
}
