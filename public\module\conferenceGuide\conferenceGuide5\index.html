<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Document</title>
  <link rel="stylesheet" href="40e93b11cbd4174c60b22cba0d630300.css" />
  <link rel="stylesheet" href="133e6f6888709a53e833618d0cd0b864.css" />
  <style>
    .svg-icon {
      width: 1em;
      height: 1em;
      vertical-align: -0.15em;
      fill: currentColor;
      overflow: hidden;
    }

    ul {
      margin: 0;
      padding: 0;
    }

    ul li {
      list-style: none;
    }

    body,
    html {
      height: 100%;
      margin: 0;
    }

    .wrap {
      width: 100%;
      height: 100%;
      box-sizing: border-box;
      overflow: hidden;
      color: white;
      /* background: rgb(215, 20, 24); */
      position: relative;

      display: flex;
      flex-direction: column;
    }

    .wrap>img {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
    }


    .header-wrap {
      width: 100%;
      height: 12vh;
    }

    .content-wrap {
      flex: 1;
      padding: 6vh 2vw;
      box-sizing: border-box;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      position: relative;
      z-index: 2;
      overflow: hidden;
    }



    .content-wrap .left-wrap,
    .right-wrap {
      /* background-color: rgba(0, 0, 0, 0.2); */
      background-color: rgba(255, 255, 255, 0.1);
      box-shadow: 0 2px 4px rgba(255, 255, 255, .15), 0 0 6px rgba(255, 255, 255, .04);
    }

    .content-wrap .left-wrap {
      width: 65%;
      box-sizing: border-box;
      padding: 0 4vw;
      display: flex;
      flex-direction: column;
    }

    .content-wrap .right-wrap {
      width: 34%;
      padding: 0 2vw;
      box-sizing: border-box;
    }

    .content-wrap .left-wrap .title {
      font-size: 4vw;
      border-bottom: 3px solid #fff;
      padding: var(--padding-height) 0;
    }

    .content-wrap .left-wrap .cur-wrap {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-evenly;
    }

    .content-wrap .left-wrap .cur {
      font-size: 2vw;
      padding-left: 1vw;
    }

    .content-wrap .right-wrap .title {
      text-align: center;
      font-size: 2vw;
      border-bottom: 3px solid #fff;
      padding: 3vh 0;
      margin-bottom: 1vh;
    }

    .content-wrap .right-wrap .scroll-wrap {
      height: calc(10vh * var(--num-meetings));
      overflow: hidden;
    }

    .content-wrap .right-wrap li {
      display: flex;
      font-size: 2.5vh;
      height: 10vh;
      line-height: 10vh;
    }

    .content-wrap .right-wrap li .theme {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .content-wrap .empty-wrap{
      font-size: 4vh;
      margin-top: 4vh;
    }

    .footer-wrap {
      height: 7vh;
      padding: 0 2vw;
      margin-bottom: 3vh;
      box-sizing: border-box;
      align-items: center;
    }

    .footer-wrap .time-line {
      display: flex;
      flex-direction: row;
      margin-bottom: 0.5vh;
      font-size: 2.5vh;
      position: relative;
    }

    .footer-wrap .time-line .blocks {
      display: inline-block;
      text-align: left;
      width: 24%;
    }

    .footer-wrap .usage-wrap {
      display: flex;
      flex-direction: row;
      border-radius: 1vmin;
      overflow: hidden;
      width: 100%;
      position: relative;
      overflow: hidden;
      height: 3.5vmin;
    }

    .absolute-time-axis {
      position: absolute;
      height: 3.5vmin
    }

    [v-cloak] {
      display: none;
    }
  </style>
  <!-- vue -->
  <script src="174365807c06490c848d7b1d45fdc348.js"></script>
  <!-- jquery.js -->
  <script src="13c0a5055cca7b2463b2f73701960b9e.js"></script>
  <!-- mqtt -->
  <script src="d4bfcfa9de50875589eaab322a015441.js"></script>
  <!-- 文字横向滚动组件 -->
  <script src="59e4fb71c5c2fa1dba4969d59dcad854.js"></script>
  <!-- 列表向上滚动组件 -->
  <script src="c2cafcfcea4bfe9b3aafc8c8c03ea071.js"></script>
  <script src="page.js"></script>
  <script>
    // 将时间字符串转换为分钟数
    function convertToMinutes(time) {
      const [hours, minutes] = time.split(':');
      return parseInt(hours) * 60 + parseInt(minutes);
    }

    // 将分钟数转换为时间字符串
    function convertToTimeString(minutes) {
      const hours = Math.floor(minutes / 60);
      const mins = minutes % 60;
      return `${String(hours).padStart(2, '0')}:${String(mins).padStart(2, '0')}`;
    }

    /**
     * 根据一天内占用时间段，求解一天内未占用的时间段
     * @param {*} occupiedIntervals const occupiedIntervals = [['10:20', '11:00'],['16:20', '16:40'],['10:30', '10:45'],['10:30', '11:30']];
     * @returns [['00:00', '10:20'],['11:30', '16:20'],['16:40', '24:00']]
     */

    function findAvailableTimeIntervals(occupiedIntervals) {
      // 1、遍历 occupiedIntervals 可能会出现 ['10:00': '08:00'] 的情况，表示当天10:00 - 次日 08:00 这时需要将 ['10:00': '08:00'] 转化为 ['10:00', '24:00']

      let formateIntervals = [];
      occupiedIntervals.forEach(intervals => {
        const start = intervals[0];
        const end = intervals[1];
        const startNum = convertToMinutes(start);
        const endNum = convertToMinutes(end);
        // 表示有跨天，
        if (startNum > endNum) {
          intervals[1] = '24:00';
        }
        formateIntervals.push(intervals);
      });

      // 将时间区间转换为分钟数表示的区间
      const occupiedMinutes = formateIntervals.map((interval) => [convertToMinutes(interval[0]), convertToMinutes(
        interval[1])]);

      // 按照开始时间排序
      occupiedMinutes.sort((a, b) => a[0] - b[0]);
      // 寻找未占用的时间区间
      const availableIntervals = [];
      let lastEnd = 0;
      for (const interval of occupiedMinutes) {
        const start = interval[0];
        if (start > lastEnd) {
          availableIntervals.push([lastEnd, start]);
        }
        const end = interval[1];
        lastEnd = Math.max(lastEnd, end);
      }
      if (lastEnd < 24 * 60) {
        // 当天结束时间按照 23:59计算 不然会出现 当天0:00 到24:00 都是 00:00 - 00:00
        availableIntervals.push([lastEnd, 23 * 60 + 59]);
      }

      // 将分钟数表示的区间转换为时间字符串表示的区间
      const result = availableIntervals.map((interval) => [convertToTimeString(interval[0]), convertToTimeString(
        interval[1])]);
      return result;
    }

  </script>
</head>

<body>
  <div id="app" class="wrap" v-cloak :style="{
      backgroundColor:style.backgroundColor,
      color:style.color}">
    <img src="./401bbad740e8542986b7df0cf6c4c2cf.jpg" alt="" v-if="style.bShowBg" />
    <div class="header-wrap" v-if="style.bShowHeader"></div>

    <div class="content-wrap">
      <div class="left-wrap">
        <div class="title" :style="{'--padding-height':leftTitlePaddingHeight,borderColor:style.color}">
          {{conferenceRoom.roomName||terminalName}}
        </div>
        <div class="cur-wrap" v-if="Object.keys(curConference).length">
          <div class="cur subject">会议主题：{{curConference.subject}}</div>
          <div class="cur time">会议时间：{{curConference.period}}</div>
          <div class="cur dept">预定部门：{{curConference.dept}}</div>
        </div>
        <div v-else class="empty-wrap">当前无会议</div>
      </div>
      <div class="right-wrap">
        <div class="title" :style="{borderColor:style.color}">会议列表</div>
        <div v-if="!conferenceInfo.length" class="empty-wrap">无后续会议</div>
        <scroll-list v-else :style="{'--num-meetings':rightNumMeetingsPerPage}">
          <template v-slot:content="{curPage}">
            <li v-for="(item,index) in conferenceInfo" :key="item.confId" @click="clickConference(item)">
              <div class="time">
                {{item.period}}
              </div>
              <div class="theme">
                <marquee :val="item.subject" :scroll="parseInt(index / rightNumMeetingsPerPage) === curPage"></marquee>
              </div>
            </li>
          </template>
        </scroll-list>
      </div>
    </div>

    <div class="footer-wrap" v-if="style.bShowTimeLine">
      <div class="time-line" :style="{color:style.progressTextColor}">
        <div class="blocks">0</div>
        <div class="blocks">03</div>
        <div class="blocks">06</div>
        <div class="blocks">09</div>
        <div class="blocks">12</div>
        <div class="blocks">15</div>
        <div class="blocks">18</div>
        <div class="blocks">21</div>
        <div class="midnight">24</div>
      </div>
      <div class="usage-wrap">
        <div v-for="item in timeAxis" class="absolute-time-axis" :key="item.key" :style="{
              width: `${item.width}%`,
              left: `${item.left}%`,
              'background-color':
                item.status === 0 ? style.progressBgColor : style.progressActiveBgColor
            }"></div>
      </div>
    </div>
  </div>
  </div>
</body>

<script>
  window.$page = page;
  const cssProperty = ["height", "width", "fontSize"];
  const app = new Vue({
    el: "#app",
    data: {
      props: page.props,
      mqttClient: null,
      mqttTopic: "",
      mac: null,
      curConference: {
        status: "正在会议中",
        subject: "第三季度经营情况分析大会",
        contactPerson: "刘勇",
        period: "16:57 - 17:27",
        periodArr: ["2021-03-04", "09:00-10:30"],
        qrCode: "./b947d49f014c663d02583f0f6cfc141f.png",
        dept:"研发部",
        participant: [{
          username: "张三丰",
          signIn: true
        },
        {
          username: "李四水",
          signIn: false
        },
        {
          username: "王五",
          signIn: false
        },
        {
          username: "刘勇",
          signIn: true
        },
        {
          username: "莹莹",
          signIn: false
        },
        {
          username: "于光",
          signIn: false
        }
        ]
      },
      conferenceList: [],
      conferenceInfo: [{
        confId: 1,
        contactPerson: "张三丰",
        participant: [{
          username: "张三丰",
          signIn: false
        }],
        period: "11:00-12:00",
        periodArr: ["2021-03-04", "11:00-12:00"],
        qrCode: "./7a8606304f019293184038e341f24d85.jpg",
        subject: "全国密码学与自主系统智能控制专题研讨会"
      },
      {
        confId: 2,
        contactPerson: "李四水",
        participant: [{
          username: "李四水",
          signIn: false
        }],
        period: "12:30-13:30",
        periodArr: ["2021-03-04", "12:30-13:30"],
        qrCode: "./7a8606304f019293184038e341f24d85.jpg",
        subject: "智能计算及应用国际会议"
      },
      {
        confId: 4,
        contactPerson: "刘小勇",
        participant: [{
          username: "刘小勇",
          signIn: false
        }],
        period: "14:00-15:00",
        periodArr: ["2021-03-04", "14:00-15:00"],
        qrCode: "./7a8606304f019293184038e341f24d85.jpg",
        subject: "干旱气候变化与可持续性发展学术研讨会"
      },
      {
        confId: 5,
        contactPerson: "方莹莹",
        participant: [{
          username: "方莹莹",
          signIn: false
        }],
        period: "15:20-16:00",
        periodArr: ["2021-03-04", "15:20-16:00"],
        qrCode: "./7a8606304f019293184038e341f24d85.jpg",
        subject: "中国IT、网络、信息技术、电子、仪器仪表创新学术会议"
      },
      {
        confId: 3,
        contactPerson: "王五",
        participant: [{
          username: "王五",
          signIn: false
        }],
        period: "17:30-19:00",
        periodArr: ["2021-03-04", "17:30-19:00"],
        qrCode: "./7a8606304f019293184038e341f24d85.jpg",
        subject: "国际气体净化技术研讨会"
      },
      {
        confId: 6,
        contactPerson: "余秋水",
        participant: [{
          username: "余秋水",
          signIn: false
        }],
        period: "20:00-22:00",
        periodArr: ["2021-03-04", "20:00-22:00"],
        qrCode: "./7a8606304f019293184038e341f24d85.jpg",
        subject: "图像处理与模式识别在工业工程中的应用国际学术研讨会"
      },
      {
        confId: 7,
        contactPerson: "余秋水",
        participant: [{
          username: "余秋水",
          signIn: false
        }],
        period: "23:00-24:00",
        periodArr: ["2021-03-04", "23:00-24:00"],
        qrCode: "./7a8606304f019293184038e341f24d85.jpg",
        subject: "图像处理与模式识别在工业工程中的应用国际学术研讨会"
      }
      ],
      conferenceRoom: {
        roomName: "A217陆家嘴会议室"
      },
      terminalName: "",
      // 会议室状态，0为空闲，1为占用
      status: [{
        label: "空闲中",
      },
      {
        label: "已预约",
      }
      ],
      timeAxis: [{
        key: Math.random(),
        width: "100%",
        status: 0
      }],
      timer: null,
      statusTimer: null,
      mqtt: {}
    },
    computed: {
      style() {
        let style = {};
        for (let prop of this.props) {
          if (cssProperty.includes(prop.field)) {
            style[prop.field] = prop.value + "%"
          } else {
            style[prop.field] = prop.value
          }
        }
        return style;
      },
      bConfs() {
        if (Object.keys(this.curConference).length) {
          return true
        } else {
          return false
        }
      },
      leftTitlePaddingHeight() {
        if (this.style.bShowHeader && this.style.bShowTimeLine) {
          return '8vh'
        } else if (this.style.bShowHeader || this.style.bShowTimeLine) {
          return '9vh'
        } else {
          return '10vh'
        }
      },
      rightNumMeetingsPerPage() {
        if (this.style.bShowHeader && this.style.bShowTimeLine) {
          return 5
        } else if (this.style.bShowHeader || this.style.bShowTimeLine) {
          return 6
        } else {
          return 7
        }
      }
    },
    created() {
      if (!this.isWindows()) {
        this.curConference = {};
        this.conferenceInfo = [];
        this.conferenceRoom = {
          roomName: ""
        };
        const {
          mac,
          name
        } = this.getTerminalInfo();
        if (mac) {
          this.mac = mac;
          this.terminalName = name
          this.conferenceRoom = {
            roomName: ""
          }
          //初始化终端灯状态
          this.setTerminalStatus(0);
          //刷新终端灯的状态
          this.statusTimer && clearInterval(this.statusTimer);
          this.statusTimer = setInterval(this.refreshTerminalStatus, 5000);
        } else {
          this.log("终端信息获取失败");
        }
        let mqtt = this.getMqtt();
        if (mqtt) {
          this.mqtt = mqtt;
          this.getMqttServerAndConnect(mqtt);
        } else {
          this.log("mqtt地址获取失败");
        }
      } else {
        this.initTimeline()
      }
      // let ws = "wss://rjms.putian-nst.com:443/mqtt/";
      // let username = "admin";
      // let password = "mqttServer";
      // let ws = "ws://218.94.61.65:8083/mqtt";
      // let username = "admin";
      // let password = "mqttServer";
      // let ws = "ws://192.168.89.153:8083/mqtt";
      // let username = "admin";
      // let password = "admin";
      // let ws = "ws://192.168.88.121:8080/mqtt";
      // let username = "admin";
      // let password = "";
      // let ws = "ws://192.168.89.251:8080/mqtt";
      // let username = "admin";
      // let password = "nst@aliyun";
      // this.getMqttServerAndConnect({ ws, username, password });
    },
    beforeDestory() {
      //关闭会议室状态
      this.setTerminalStatus(-1);
      this.mqttClose();
      this.IntervalClose();
    },
    mounted() {
      window["update"] = (val, mqtt = null) => {
        this.updateProps(val);
      };

      window["updateMqtt"] = param => {
        this.updateMqtt(param);
      };
    },
    methods: {
      initTimeline() {
        let confs = [this.curConference, ...this.conferenceInfo]
        let timeArr = confs.map(item => {
          let time = item.periodArr[1].split("-");
          startTimeArr = time[0].split(":").map(Number);
          endTimeArr = time[1].split(":").map(Number);
          return {
            startTimeArr,
            endTimeArr
          };
        });
        this.timeAxis = this.convertTimes2Pulses(timeArr);
      },
      updateMqtt(mqtt) {
        if (!mqtt) {
          return;
        }
        mqtt = JSON.parse(mqtt);

        if (
          (this.mqttClient && this.mqtt.ws !== mqtt.ws) ||
          (!this.mqttClient && !Object.keys(this.mqtt).length)
        ) {
          this.log("mqtt重连");
          this.mqttClose();
          this.curConference = {};
          this.conferenceInfo = [];
          this.conferenceRoom = {
            roomName: ""
          };
          this.mqtt = mqtt;
          this.getMqttServerAndConnect(mqtt);
        }
      },
      /*
       * 当前运行环境是终端还是pc
       */
      isWindows() {
        return window.DSS20AndroidJS === undefined;
      },
      /*
       * state -1:关闭 0:空闲 1:会议中 2:签到
       */
      setTerminalStatus(state) {
        if (window.DSS20AndroidJS) {
          // -1 关闭
          if (state === -1) {
            window.DSS20AndroidJS.updateLedStatusByWeb(0);
          }

          // 状态灯设置
          let temp = this.getProp("statusLight");
          if (temp) {
            // 开启
            if (temp.value) {
              let fieldKey = "";
              if (state === 0) {
                // 空闲 0
                fieldKey = "freeLightColor";
              } else if (state === 1) {
                // 会中 1
                fieldKey = "meetingLightColor";
              } else if (state === 2) {
                // 签到 2
                fieldKey = "signLightColor";
              }
              if (fieldKey) {
                let tempObj = this.getProp(fieldKey);
                if (tempObj) {
                  let lightVal = tempObj.value;
                  lightVal = Number(lightVal);
                  window.DSS20AndroidJS.updateLedStatusByWeb(lightVal);
                }
              }
            } else {
              // 关闭状态灯
              window.DSS20AndroidJS.updateLedStatusByWeb(0);
            }
          }
        }
      },
      /*
       * 终端打印
       */
      log(msg) {
        if (this.isWindows()) {
          console.log(msg)
          return;
        }
        window.DSS20AndroidJS &&
          window.DSS20AndroidJS.htmlLogcat("会议指引：" + msg);
      },
      clickConference(item) {
        this.log('conferenceInfo')
        window.DSS20AndroidJS &&
          window.DSS20AndroidJS.conferenceInfo(JSON.stringify(item));
      },
      getTerminalInfo() {
        var data = window.DSS20AndroidJS.terminalInfo();
        var info = JSON.parse(data);
        return info;
      },
      getMqtt() {
        var data = window.DSS20AndroidJS.getWebMqttWs();
        var info = {};
        try {
          info = data && JSON.parse(JSON.parse(data));
        } catch (err) {
          this.log("mqtt地址解析失败！");
        }

        return info;
      },
      updateProps(props) {
        for (let prop of props) {
          let index = this.getPropIndex(prop.field);
          if (index !== -1) {
            let data = this.props[index];
            data.value = prop.value;
            this.$set(this.props, index, data);
          }
        }
      },
      getPropIndex(name) {
        for (let i = 0; i < this.props.length; i++) {
          if (this.props[i].field === name) {
            return i;
          }
        }
        return -1;
      },
      /**
       * 更新数据
       */
      updateConfByTime() {
        if (!this.conferenceList.length && this.isWindows()) {
          return;
        }
        const cur = +new Date();

        let bProcessing = false;
        let res = this.conferenceList.filter(item => {
          if (item.approved !== 4) {
            return false;
          }
          const {
            start,
            end
          } = item;
          if (end < cur) {
            return false;
          } else if (start <= cur && end >= cur) {
            bProcessing = true;
          }
          return true;
        });
        //更新会议信息
        if (bProcessing) {
          this.curConference = {
            ...res.shift(),
            status: "正在会议中"
          };
        } else {
          this.curConference = {};
        }

        this.conferenceInfo = res;

        // 这里需要过滤出时间是当天的会议 支持跨天
        const tempConfList = JSON.parse(JSON.stringify(this.conferenceList));
        // 需要针对跨天进行处理
        tempConfList.forEach(item => {
          // 创建一个新的日期对象
          const now = new Date();
          // 设置时间为凌晨 00:00:00
          now.setHours(0, 0, 0, 0);
          const todayStartTimestamp = now.getTime();
          const todayEndTimestamp = todayStartTimestamp + 1000 * 3600 * 24;
          // 开始时间在今天，结束时间不在今天 endTimeArr => [24, 0]
          if (item.start >= todayStartTimestamp && item.start <= todayEndTimestamp && item.end >=
            todayEndTimestamp) {
            item.endTimeArr = [24, 0];
            item.bInToday = true;
          }

          // 开始时间不在今天、结束时间在今天 startTimeArr => [0, 0]
          if (item.start <= todayStartTimestamp && item.end >= todayStartTimestamp && item.end <=
            todayEndTimestamp) {
            item.startTimeArr = [0, 0];
            item.bInToday = true;
          }

          // 时间包括当天 开始时间小于当天0：00 结束时间大于 当天23：59
          if (item.start <= todayStartTimestamp && item.end >= todayEndTimestamp) {
            item.startTimeArr = [0, 0];
            item.endTimeArr = [24, 0];
            item.bInToday = true;
          }

          // 开始时间在今天 结束时间在今天
          if (item.start >= todayStartTimestamp && item.end <= todayEndTimestamp) {
            item.bInToday = true;
          }
        });


        let timeArr = tempConfList.filter(item => item.bInToday).map(item => {
          return {
            startTimeArr: item.startTimeArr,
            endTimeArr: item.endTimeArr
          };
        });
        this.timeAxis = this.convertTimes2Pulses(timeArr);
      },
      /**
       * 转换后端返回的会议时间段
       * @param times
       */
      convertTimes2Pulses(times = []) {
        let pulses = [];

        // 放入已被占用的时间段
        for (let occupied of times) {
          const {
            startTimeArr,
            endTimeArr
          } = occupied;
          // 起止时间横坐标
          let x0 = this.getCoordinateX(startTimeArr);
          let x1 = this.getCoordinateX(endTimeArr);
          let width = this.calculateWidth(x0, x1);
          let pulse = {
            period: occupied.period,
            // 宽度百分比，24小时共1440分钟，作为总宽度
            width,
            status: 1,
            // 左边距百分比
            left: Number(((x0 / 1440) * 100).toFixed(2)),
            x0: x0,
            x1: x1,
            key: Math.random(),
            dateRange: [startTimeArr.join(':'), endTimeArr.join(':')]
          };
          pulses.push(pulse);
        }

        // 补全没有被占用的时间段
        let result = [];
        if (pulses.length === 0) {
          // 全天没有会议
          result.push({
            period: "0:00-23:59",
            width: 100,
            status: 0,
            x0: 0,
            left: 0,
            x1: 1440,
            key: Math.random()
          });
        } else {
          // 需要找出空余的时间段区间 @update by Gong
          let occupiedIntervals = [];
          pulses.forEach((item) => {
            occupiedIntervals.push(item.dateRange);
            result.push(item);
          });

          try {
            let avaliableIntervals = findAvailableTimeIntervals(occupiedIntervals);
            // exp : [['00:00', '10:20'],['11:30', '16:20'],['16:40', '24:00']]
            avaliableIntervals.forEach((item) => {
              result.push({
                period: item.join('-'),
                width: ((convertToMinutes(item[1]) - convertToMinutes(item[0])) / 1440) *
                  100, // 转换为分钟数 宽度百分比
                left: (convertToMinutes(item[0]) / 1440) * 100, // 左边界百分比
                status: 0,
                x0: convertToMinutes(item[0]),
                x1: convertToMinutes(item[1]),
                key: Math.random(),
              });
            });
          } catch (error) {
            // ignore error
          }

          // 放入原点
          // result.push({
          //   period: "0:00-0:00",
          //   width: 0,
          //   status: 0,
          //   x0: 0,
          //   x1: 0,
          //   key: Math.random()
          // });
          // pulses.forEach((pulse, index) => {
          //   let pre = result[result.length - 1];
          //   if (pulse.x0 !== pre.x1) {
          //     // 与前一段有间隔，补上空白时间
          //     result.push({
          //       width: this.calculateWidth(pre.x1, pulse.x0),
          //       status: 0,
          //       x0: pulse.x0,
          //       x1: pre.x1,
          //       key: Math.random()
          //     });
          //   }
          //   result.push(pulse);

          //   if (index === pulses.length - 1 && pulse.x1 !== 1440) {
          //     // 最后一个元素，检查是否要在结尾追加
          //     result.push({
          //       width: this.calculateWidth(pulse.x1, 1440),
          //       status: 0,
          //       x0: pulse.x1,
          //       x1: 1440,
          //       key: Math.random()
          //     });
          //   }
          // });
          // // 删除原点
          // result.shift();
        }
        return result;
      },
      /**
       * 根据坐标算出宽度
       * @param coordinateX0 开始时间坐标
       * @param coordinateX1 结束时间坐标
       * @return string 时间段宽度占比，e.g. 10%
       */
      calculateWidth(coordinateX0, coordinateX1) {
        return Number((((coordinateX1 - coordinateX0) / 1440) * 100).toFixed(2));
      },

      /**
       * 获取时间点在一天中的横坐标，刻度为分钟
       * @param time e.g. 8:00
       */
      getCoordinateX(timeSplits) {
        let hour = parseInt(timeSplits[0]);
        let minute = parseInt(timeSplits[1]);
        // 起点横坐标
        return hour * 60 + minute;
      },
      //刷新终端灯的状态
      refreshTerminalStatus() {
        // 空闲
        let state = 0;

        // 会中
        if (
          Object.keys(this.curConference).length &&
          this.curConference.status === "正在会议中"
        ) {
          state = 1;
        }

        // 签到
        if (Object.keys(this.curConference).length && this.curConference.status === '签到中') {
          state = 2;
        }
        this.setTerminalStatus(state);
      },
      /**
       * 从 prop 中获取 val
       */
      getProp(field) {
        for (let prop of this.props) {
          if (prop.field == field) {
            return prop;
          }
        }
        return null;
      },
      /**
       * 随机ID
       * @param {*} len
       * @param {*} radix
       * @returns
       */
      randomId(len, radix) {
        var chars =
          '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('')
        var uuid = [],
          i
        radix = radix || chars.length
        if (len) {
          // Compact form
          for (i = 0; i < len; i++) uuid[i] = chars[0 | (Math.random() * radix)]
        } else {
          // rfc4122, version 4 form
          var r
          // rfc4122 requires these characters
          uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-'
          uuid[14] = '4'
          // Fill in random data.  At i==19 set the high bits of clock sequence as
          // per rfc4122, sec. 4.1.5
          for (i = 0; i < 36; i++) {
            if (!uuid[i]) {
              r = 0 | (Math.random() * 16)
              uuid[i] = chars[i == 19 ? (r & 0x3) | 0x8 : r]
            }
          }
        }
        return uuid.join('')
      },
      /**
       * 连接到MQTT服务器并订阅
       */
      getMqttServerAndConnect(param) {
        this.log("mqtt参数：" + JSON.stringify(param));

        const that = this;
        let {
          ws,
          username,
          password
        } = param;
        let mac = this.mac || ''
        // 客户端ID
        let clientId = `js_conferenceGuide5_${mac}_${this.randomId(16, 16)}`;

        that.mqttClient = mqtt.connect(ws, {
          clientId,
          username,
          password,
          clean: true,
          connectTimeout: 5 * 1000,
          keepalive: 30
        });
        that.mqttClient.on("connect", () => {
          this.log("mqtt连接成功");
          this.mqttRefreshSubscribe();
        });
        that.mqttClient.on("error", error => {
          this.log("mqtt连接失败：" + JSON.stringify(error));
          this.conferenceRoom = {};
          this.curConference = {};
          this.conferenceInfo = [];
        });
        //监听接收消息事件
        that.mqttClient.on("message", (topic, message) => {
          message = JSON.parse(message.toString());
          console.log(message)
          this.log("获取mqtt消息" + JSON.stringify(message));
          const {
            meetingroom,
            confDetailDTOList
          } = message;
          this.conferenceRoom = meetingroom;
          this.conferenceList = confDetailDTOList.map(item => {
            return this.getFormatData(item)
          });
          this.updateConfByTime();
        });
      },
      getPeriod(startTimeStamp, endTimeStamp, periodArr) {
        if(periodArr[1].includes('永久')){
          return '永久会议'
        }
        let startTimeArr = periodArr[0].split(' ')
        let endTimeArr = periodArr[1].split(' ')
        let curEndTime = new Date(new Date(startTimeStamp).setHours(23, 59, 59, 999))
          .getTime()
        // 跨天会议,超过一天显示+1
        if (endTimeStamp > curEndTime) {
          endTimeArr[1] += '+1'
        }
        return startTimeArr[1] + '-' + endTimeArr[1]
      },
      getFormatData(item) {
        let periodArr = item.period.split("-");
        let start, end, startTimeArr, endTimeArr
        // 获取startTimeArr、endTimeArr （[hh,mm]）
        // 会议可能跨天
        if (item.start && item.end) {
          start = item.start
          end = item.end

          const startTime = new Date(start)
          startTimeArr = [startTime.getHours(), startTime.getMinutes()]

          let curEndTime = new Date(new Date().setHours(23, 59, 59, 999)).getTime()
          let endTime = end > curEndTime ? new Date(curEndTime) : new Date(end)
          endTimeArr = [endTime.getHours(), endTime.getMinutes()]
        } else {
          // 获取start、end (时间戳)
          // 兼容之前的写法（会议不可能跨天）
          let timeArr = periodArr[1].split("-");
          startTimeArr = timeArr[0].split(":").map(Number);
          endTimeArr = timeArr[1].split(":").map(Number);

          start = new Date(new Date().setHours(startTimeArr[0], startTimeArr[1])).getTime()
          end = new Date(new Date().setHours(endTimeArr[0], endTimeArr[1])).getTime()
        }
        return {
          period: this.getPeriod(start, end, periodArr),
          periodArr,
          start,
          end,
          startTimeArr,
          endTimeArr,
          confId: item.confId,
          subject: item.subject,
          nickname: item.nickname,
          contactPerson: item.contactPerson,
          participant: item.participant,
          approved: item.approved,
          qrCode: item.qrCode
        }
      },

      /**
       * 发布MQTT主题
       */
      mqttPublish(address) {
        let topic = "dss2/terminal/conference";
        let message = `{"command":"WEATHER","parameters":{${param}:"${address}"}}`;
        this.log("mqtt发布主题: " + message);
        this.mqttClient.publish(
          topic,
          message, {
          qos: 1,
          retain: true
        },
          (err, res) => {
            if (err) {
              this.log("mqtt发布主题失败：", err);
              return;
            }
            this.mqttRefreshSubscribe(address);
          }
        );
      },
      /**
       * 订阅MQTT主题
       */
      mqttRefreshSubscribe() {
        // this.mac = "00073D9001D7";
        // this.mac = "00073D9003C5";
        const that = this;
        if (that.mqttTopic) {
          that.mqttClient.unsubscribe(that.mqttTopic);
        }
        that.mqttTopic = `dss2/web/conference/mac/${this.mac}`;
        this.log("mqtt订阅主题：" + that.mqttTopic);
        that.mqttClient.subscribe(
          that.mqttTopic, {
          qos: 1,
          rap: true
        },
          (err, res) => {
            if (err) {
              this.log("mqtt订阅主题失败：", err);
              return;
            }

            //刷新数据
            that.timer && clearInterval(that.timer);
            that.timer = setInterval(() => {
              that.updateConfByTime();
            }, 60000);
          }
        );
      },
      /**
       * 释放MQTT客户端
       */
      mqttClose() {
        this.mqtt = {};
        if (this.mqttClient) {
          this.mqttClient.unsubscribe(this.mqttTopic);
          this.mqttClient.end();
        }
      },
      IntervalClose() {
        this.timer && clearInterval(this.timer);
        this.statusTimer && clearInterval(this.statusTimer);
      }
    }
  });

</script>

</html>