<template>
  <div class="wrap">
    <div class="topo-header"> <svg-icon class="icon" icon-class="iot@topo"></svg-icon>{{ spaceInfo.spaceName }}空间拓扑</div>
    <div class="topo-wrap"> <topo-graph :isFullScreen="true" :spaceId="spaceId"></topo-graph></div>
  </div>
</template>

<script>
  import topoGraph from './topoGraph.vue';
  import { getSpaceInfo } from '@/api/iotControl';
  export default {
    components: {
      topoGraph,
    },
    data() {
      return {
        spaceId: '',
        spaceInfo: {
          spaceName: '',
        },
      };
    },
    created() {
      this.spaceId = this.$route.params.spaceId || '';
      if (this.spaceId) {
        getSpaceInfo(this.spaceId).then(({ data }) => {
          this.spaceInfo = data;
        });
      }
    },
    methods: {},
    computed: {},
  };
</script>

<style lang="scss" scoped>
  .wrap {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    .topo-header {
      color: #333;
      font-size: 16px;
      font-weight: 700;
      margin-bottom: 5px;
      padding: 20px 20px 20px 35px;
      border-bottom: 1px solid #ededed;
      display: flex;
      align-items: center;
      .icon {
        margin-right: 16px;
        font-size: 40px;
      }
    }
    .topo-wrap {
      flex: 1;
    }
  }
</style>
