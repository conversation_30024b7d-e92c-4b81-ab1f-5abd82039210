<template>
  <el-dialog :title="title" :visible.sync="visible" width="700px" :close-on-click-modal="false" @close="close">
    <el-table :data="templateList" ref="table" @selection-change="handleSelectionChange" height="450px" v-loading="loading" class="table">
      <el-table-column type="selection" width="35" align="center"> </el-table-column>
      <el-table-column prop="name" label="巡检模板名称" min-width="120" :show-overflow-tooltip="true"> </el-table-column>
      <el-table-column prop="remark" label="巡检模板描述" min-width="120" :show-overflow-tooltip="true"> </el-table-column>
      <el-table-column prop="creator" label="编辑人" min-width="120" :show-overflow-tooltip="true"> </el-table-column>
      <el-table-column prop="gmtModified" label="编辑时间" min-width="120" :show-overflow-tooltip="true" align="center">
        <template slot-scope="{ row }">
          {{ row.gmtModified ? formatDate(row.gmtModified) : '--' }}
        </template>
      </el-table-column>
    </el-table>

    <div slot="footer" class="dialog-footer">
      <el-button @click="close">取消</el-button>
      <el-button type="primary" @click="handleOk">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import dlg from '@/mixins/dlg';
  import { getInspectTemplate } from '@/api/iotControl';
  export default {
    mixins: [dlg],
    data() {
      return {
        title: '选择巡检模板',
        templateList: [],
        //加载状态
        loading: false,
        selection: [],
      };
    },
    created() {
      this.getInspectTemplate();
    },
    methods: {
      getInspectTemplate() {
        this.loading = true;
        getInspectTemplate()
          .then((res) => {
            this.templateList = res.data;
          })
          .finally(() => {
            this.loading = false;
          });
      },
      /**
       * table选中改变,仅单选
       */
      handleSelectionChange(val) {
        this.selection = val;
        if (val.length > 1) {
          this.$refs.table.clearSelection();
          this.$refs.table.toggleRowSelection(val.pop());
        }
      },
      /**
       * 确定
       */
      handleOk() {
        if (this.selection.length === 0) {
          this.$message.warning('请选择巡检模板');
          return;
        }
        this.$emit('handleSelect', this.selection[0]);
        this.close();
      },
      formatDate(inputDate) {
        const date = new Date(inputDate);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        // 格式化为 MM/dd hh:mm
        return `${year}-${month}-${day} ${hours}:${minutes}`;
      },
    },
  };
</script>

<style scoped lang="scss">
  /*  将全选项隐藏 */
  ::v-deep.table thead .el-table-column--selection .cell {
    display: none;
  }
</style>
