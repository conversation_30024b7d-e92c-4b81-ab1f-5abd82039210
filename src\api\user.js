import request from '@/utils/request';
import $qs from 'qs';
import { CLIENT_ID, CLIENT_SECRET } from '@/utils/auth';

export function loginAsOauth2Client(data) {
  let details = {
    grant_type: 'password',
    client_id: CLIENT_ID,
    client_secret: CLIENT_SECRET,
    scope: 'all',
    ...data,
  };
  return request({
    url: '/oauth/token',
    method: 'post',
    data: $qs.stringify(details),
  });
}

/**
 * token续签
 */
export function refreshToken() {
  return request({
    url: '/oauth/renew',
    method: 'post',
  });
}

// 仅供第三方账号单点登陆使用
export function getLoginUserInfo() {
  return request({
    url: '/oauth/sso',
    method: 'get',
  });
}

export function login(data) {
  return request({
    url: '/webapp/login',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//获取所有通知
export function getNotification(data) {
  return request({
    url: '/oauth/notification/getAll',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//删除所选通知
export function deleteNotification(data) {
  return request({
    url: '/oauth/notification/delete',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//删除所有通知
export function deleteAllNotification(data) {
  return request({
    url: '/oauth/notification/deleteAll',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//将所选消息标记为已读
export function readNotification(data) {
  return request({
    url: '/oauth/notification/read',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//将所有消息标记为已读
export function readAllNotification() {
  return request({
    url: '/oauth/notification/readAll',
    method: 'post',
  });
}

//修改密码
export function modifyPassword(oldPass, userPass) {
  return request({
    url: `/oauth/user/changePwd`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json;',
    },
    data: {
      oldPass,
      userPass,
    },
  });
}

//获取首页内容
export function getAllSystemInfo() {
  return request({
    url: '/webapp/system/getAllSystemInfo',
    method: 'post',
  });
}

export function getInfo(token) {
  return request({
    url: '/vue-element-admin/user/info',
    method: 'get',
    params: {
      token,
    },
  });
}

export function logout() {
  return request({
    url: '/webapp/logout',
    method: 'post',
  });
}

/**
 * 查询绑定指定角色的用户
 * @param {*} data
 * page size direction property
 * roleId 角色id nameToSearch用户名 deptId 机构id
 * @returns
 */
export function getUsersByRoleId(data) {
  return request({
    url: '/oauth/user/role',
    method: 'get',
    params: data,
  });
}

//获取用户列表（根机构用户可以获取所有的用户）
export function getUsers(page, size, direction, property, nameToSearch = '') {
  return request({
    url: '/oauth/user/' + page + '/' + size + '/' + direction + '/' + property + (nameToSearch ? '/' + nameToSearch : ''),
    method: 'get',
  });
}

/**
 * 查询用户
 * @param {*} data
 * page size direction property
 * deptId机构id nameToSearch用户名 includeChild包括子级
 * @returns
 */
export function getUsers2(data) {
  return request({
    url: '/oauth/user',
    method: 'get',
    params: data,
  });
}

/**
 * 仅供会管模块使用
 * 根据机构id获取用户列表(返回包括用户的视频终端信息)
 * @param {*} data
 * deptId           机构id
 * includeChild     包含子级
 * nameToSearch     用户名
 * property
 * direction
 * page
 * size
 * vcSpecial        是否考虑特殊部门的用户,false不考虑，查所有的，只有预约会议才会使用该字段
 * @returns
 */
export function getDeptUsers(data) {
  data.property = data.property || 'nickname';
  data.direction = data.direction || 'ASC';
  if (data.vcSpecial == null || data.vcSpecial == undefined) {
    data.vcSpecial = false;
  }

  return request({
    url: '/weapp/prepare/user',
    method: 'get',
    params: data,
  });
}

//添加用户
export function addUser(data) {
  return request({
    url: '/oauth/user',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

//更新用户
export function updateUser(data) {
  return request({
    url: '/oauth/user',
    method: 'put',
    data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

//删除用户
export function deleteUser(userId) {
  return request({
    url: '/oauth/user/' + userId,
    method: 'delete',
  });
}

//批量删除用户
export function batchDelUser(ids) {
  return request({
    url: `/oauth/user/batchDel`,
    method: 'delete',
    headers: {
      'Content-Type': 'application/json',
    },
    data: ids,
  });
}

//根据用户名获取用户详细信息
export function getUserInfo(username) {
  return request({
    url: '/oauth/user/username/web/' + username,
    method: 'get',
  });
}

//获取用户权限
export function getUserPermissions() {
  return request({
    url: '/oauth/user/permission',
    method: 'get',
  });
}

// 判断是否是根机构下用户
export function isRootCompany() {
  return request({
    url: '/oauth/user/isRootCompany',
    method: 'get',
  });
}

//获取软件版本号
export function getVersion() {
  return request({
    url: '/sysManagement/version',
    method: 'get',
  });
}

/**
 * 获取登录页logo及系统名称
 */
export function getLoginSystemInfo() {
  return request({
    url: '/oauth/systemparam',
    method: 'get',
  });
}

/**
 * 保存系统参数
 * @param {*} data
 * @param {*} onUploadProgress
 * @returns
 */
export function saveSystemParams(data, onUploadProgress) {
  let file = new FormData();
  Object.keys(data).forEach((key) => {
    file.append(key, data[key]);
  });
  return request({
    url: '/oauth/systemparam/bg',
    method: 'post',
    data: file,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    onUploadProgress,
  });
}

/**
 * 保存用户偏好
 * @param {*} data
 */
export function saveUserPrefer(data) {
  return request({
    url: '/oauth/user/prefer',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

/**
 * 获取用户偏好
 */
export function getUserPrefer() {
  return request({
    url: '/oauth/user/prefer',
    method: 'get',
  });
}

/**
 * 校验密码强度
 * @param {*} pwd
 * @returns
 */
export function chkPassword(pwd) {
  return request({
    url: `/oauth/user/checkPassword`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json',
    },
    data: {
      userPass: pwd,
    },
  });
}

/**
 * 不分页查询部门下用户
 * @returns
 */
export function getAllUsersInDept(deptId) {
  return request({
    url: `/weapp/prepare/department/recursion/${deptId}`,
    method: 'get',
  });
}

//上传用户头像
export function uploadUserAvatar(data, onUploadProgress) {
  let file = new FormData();
  Object.keys(data).forEach((key) => {
    file.append(key, data[key]);
  });
  return request({
    url: '/webapp/faceManager/upload/headProfile',
    method: 'post',
    data: file,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    onUploadProgress,
  });
}

/**
 * 删除头像
 * @param {*} username 用户名
 * @returns
 */
export function deleteAvatar(username) {
  return request({
    url: `/webapp/faceManager/headProfile?username=${username}`,
    method: 'delete',
  });
}

/**
 * 多个用户绑定角色
 * @param {*} data
 * userIds 用户id列表
 * roleId  角色id
 * @returns
 */
export function UsersBindRole(data) {
  return request({
    url: `/oauth/role/user`,
    method: 'post',
    data: $qs.stringify(data),
  });
}

/**
 * 多个用户解绑角色
 * @param {*} data
 * userIds 用户id列表
 * roleId  角色id
 * @returns
 */
export function UsersUnBindRole(data) {
  return request({
    url: `/oauth/role/user`,
    method: 'delete',
    data: $qs.stringify(data),
  });
}

/**
 * 验证验证码
 * data 验证码数据
 */
export function verifyCaptcha(data) {
  let file = new FormData();
  Object.keys(data).forEach((key) => {
    file.append(key, data[key]);
  });
  return request({
    url: `/oauth/captcha`,
    method: 'post',
    data: file,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

/**
 * 模糊查询用户
 * 根据用户名或昵称模糊查询用户, 不分页
 * companyId 必填
 * @returns
 */
export function fuzzyQueryUser(companyId = '', username = '') {
  return request({
    url: `/oauth/user/company/${companyId}/${username}`,
    method: 'get',
    headers: {
      noAlert: true,
    },
  });
}

/**
 * 注销
 * @returns
 */
export function logoutSystem() {
  return request({
    url: `/oauth/logout`,
    method: 'post',
  });
}
