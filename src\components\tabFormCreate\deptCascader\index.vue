<template>
  <el-cascader
    ref="cascader"
    :options="deptList"
    :show-all-levels="false"
    clearable
    @change="handleChangeDivision"
    v-model="deptValue"
    :props="{
      value: 'id',
      label: 'deptName',
      checkStrictly: true,
      expandTrigger: 'hover',
    }"
  ></el-cascader>
</template>

<script>
  import { getDepartementList } from '@/api/system';
  export default {
    props: {
      //预定义
      value: '',
    },
    data() {
      return {
        deptList: [],
      };
    },
    computed: {
      deptValue: {
        get() {
          return this.value;
        },
        set(newValue) {
          let deptId = Array.isArray(newValue) ? newValue[newValue.length - 1] : newValue;
          //当组件值发生变化后,通过 input 事件更新值
          this.$emit('input', deptId);
        },
      },
    },
    created(){
      this.getDept()
    },
    methods: {
      // 获取该用户下的所有部门列表
      getDept() {
        getDepartementList().then((response) => {
          this.deptList = response.data;
        });
      },
      handleChangeDivision() {
        this.$refs.cascader.dropDownVisible = false;
      },
    },
  };
</script>
<style lang="scss" scoped></style>
