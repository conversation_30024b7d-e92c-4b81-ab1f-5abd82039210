export function getZhValue(item) {
  if (item.dataType === "BOOLEAN") {
    return item.value ? "是" : "否";
  } else if (item.options && item.options.length) {
    let res = item.options.find((option) => option.value === item.value);
    return res?.label;
  } else {
    return item.value;
  }
}
export function getFormItem(item) {
  if (!item.field) {
    return null;
  }
  // 去除空格
  if (item.trim && item.value && item.value.trim){
    item.value = item.value.trim()
  }
  let obj = {
    field: item.field,
    value: item.value,
    displayValue: getZhValue(item),
    dataType: item.dataType,
    title: item.title,
  };
  return obj;
}
/**
 * 获取表单值
 * @param form 表单
 * @param formValue 表单值
 */
export function getFormValue(form, formValue = []) {
  form.forEach((item) => {
    item.params.map((param) => {
      let obj = getFormItem(param);
      if (!obj) {
        return;
      }

      formValue.push(obj);

      if (param.control && param.control.length) {
        getControlValue(param, formValue);
      }
    });
  });
  return formValue;
}
/**
 * 递归获取联动组件值
 * @param form 表单
 * @param formValue 表单值
 */
function getControlValue(form, formValue = []) {
  const { control, value } = form;

  control.forEach((_c) => {
    _c.value === value &&
      _c.rule &&
      _c.rule.length &&
      _c.rule.forEach((_r) => {
        let obj = getFormItem(_r);
        if (!obj) {
          return;
        }
        formValue.push(obj);
        if (_r.control && _r.control.length) {
          getControlValue(_r, formValue);
        }
      });
  });
}
