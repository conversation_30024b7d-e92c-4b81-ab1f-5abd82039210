<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
    <link rel="stylesheet" href="40e93b11cbd4174c60b22cba0d630300.css" />
    <style>
      body,
      html {
        height: 100%;
        margin: 0;
      }
      p {
        margin: 0;
      }
      .wrap {
        width: 100%;
        height: 100%;
        /* font-size: 8vw; */
        text-align: center;
        position: relative;
      }
      body main.clock-display {
        width: 160vmin;
        height: 60vmin;
        display: inline-block;
        text-align: center;
        padding: 6vmin 8vmin;
        border: solid 2vmin #ffffff;
        border-radius: 5vmin;
        background-color: #000000;
        box-shadow: 0.4vmin 0.4vmin 0.7vmin 0.4vmin rgba(0, 0, 0, 0.3);
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }
      .light-on {
        color: #ffffff !important;
      }
      /* DATE */
      .date-field {
        margin: 0.5vmin 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .date-field div {
        position: relative;
      }
      .date-field div p {
        font-size: 10vmin;
        position: relative;
        z-index: 100;
      }
      .date-field div p.placeholder {
        color: #2b2828;
        position: absolute;
        top: 0;
        z-index: 50;
      }
      .date-field div.day-of-week-mobile {
        display: none;
      }
      .date-field div.day-of-week p {
        font-size: 15vmin;
      }
      .date-field div p.type {
        font-size: 4vmin;
        font-weight: bold;
        font-family: sans-serif;
        text-transform: uppercase;
      }
      /* CLOCK */
      .clock-field {
        margin: 6vmin 0;
        display: flex;
        justify-content: center;
      }
      .clock-field div {
        display: inline-block;
        position: relative;
      }
      .clock-field div p {
        font-size: 35vmin;
        position: relative;
        z-index: 100;
      }
      .clock-field .numbers .placeholder {
        color: #2b2828;
        position: absolute;
        top: 0;
        z-index: 50;
      }
      .clock-field .am-pm {
        font-family: sans-serif;
        text-transform: uppercase;
      }
      .clock-field .am-pm div p {
        font-size: 3vmin;
        font-weight: bold;
        width: 100%;
      }
      [v-cloak] {
        display: none;
      }
    </style>
    <script src="174365807c06490c848d7b1d45fdc348.js"></script>
    <script src="page.js"></script>
  </head>

  <body>
    <div id="app" class="wrap" v-cloak>
      <main class="clock-display" :style="style">
        <div class="date-field">
          <div class="day-of-week">
            <p class="day-alpha">
              {{dayOfWeek}}
            </p>
          </div>
          <div class="month">
            <p class="month-alpha">{{month}}</p>
            <p class="type">month</p>
          </div>
          <div class="date">
            <p class="date-number">{{day}}</p>
            <p class="type">day</p>
          </div>
          <div class="year">
            <p class="year-number">{{year}}</p>
            <p class="type">year</p>
          </div>
        </div>
        <div class="clock-field">
          <div class="numbers">
            <p class="hours">{{hour}}</p>
          </div>
          <div class="colon">
            <p>:</p>
          </div>
          <div class="numbers">
            <p class="minutes">{{minute}}</p>
          </div>
          <div class="colon">
            <p>:</p>
          </div>
          <div class="numbers">
            <p class="seconds">{{second}}</p>
          </div>
          <div class="am-pm">
            <div v-if="am">
              <p class="am">AM</p>
            </div>
            <div v-else>
              <p class="pm">PM</p>
            </div>
          </div>
        </div>
      </main>
    </div>
  </body>

  <script>
    window.$page = page;
    const cssProperty = ["height", "width", "fontSize"];
    const days = [
      "SUNDAY",
      "MONDAY",
      "TUESDAY",
      "WEDNESDAY",
      "THURSDAY",
      "FRIDAY",
      "SATURDAY"
    ];
    const months = [
      "JAN",
      "FEB",
      "MAR",
      "APR",
      "MAY",
      "JUN",
      "JUL",
      "AUG",
      "SEP",
      "OCT",
      "NOV",
      "DEC"
    ];

    const app = new Vue({
      el: "#app",
      data: {
        date: new Date(),
        props: page.props,
        timer: null
      },
      computed: {
        style() {
          let style = {};
          for (let prop of this.props) {
            let unit = cssProperty.includes(prop.field) ? "px" : "";
            style[prop.field] = prop.value + unit;
          }
          return style;
        },
        am() {
          return this.hour < 12;
        },
        second() {
          return this.formatValue(this.date.getSeconds());
        },
        minute() {
          return this.formatValue(this.date.getMinutes());
        },
        hour() {
          return this.formatValue(this.date.getHours());
        },
        year() {
          return this.formatValue(this.date.getFullYear());
        },
        day() {
          return this.formatValue(this.date.getDate());
        },
        month() {
          return months[this.date.getMonth()];
        },
        dayOfWeek() {
          return days[this.date.getDay()];
        }
      },
      created() {
        this.setTime();
      },
      mounted() {
        window["update"] = (val,mqtt=null) => {
          this.updateProps(val);
        };
      },
      beforeDestory() {
        clearInterval(this.timer);
      },
      methods: {
        updateProps(props) {
          for (let prop of props) {
            let index = this.getPropIndex(prop.field);
            if (index !== -1) {
              let data = this.props[index];
              data.value = prop.value;
              this.$set(this.props, index, data);
            }
          }
        },
        getPropIndex(name) {
          for (let i = 0; i < this.props.length; i++) {
            if (this.props[i].field === name) {
              return i;
            }
          }
          return -1;
        },
        formatValue(val) {
          if (val < 10) {
            return "0" + val;
          }
          return val;
        },
        setTime() {
          clearInterval(this.timer);
          this.timer = setInterval(() => {
            this.date = new Date();
          }, 1000);
        }
      }
    });
  </script>
</html>
