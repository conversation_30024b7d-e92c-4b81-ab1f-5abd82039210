<!-- 告警规则和场景规则共用 -->
<template>
  <div class="wrap">
    <el-form ref="formRef" :model="formValue" :disabled="disabled" :rules="rules" label-width="135px" label-position="left" class="top-form">
      <el-form-item label="规则名称" prop="name">
        <el-input v-model="formValue.name" placeholder="请输入规则名称" class="limited-width" :readonly="disabled" />
      </el-form-item>
      <el-form-item label="规则描述" prop="description">
        <el-input placeholder="请输入规则描述" type="textarea" v-model="formValue.description" class="limited-width" :readonly="disabled" />
      </el-form-item>
      <el-form-item label="告警级别" prop="level" v-if="type === 'alarm'">
        <el-select style="width: 200px" v-model="level" placeholder="告级别" :disabled="disabled">
          <el-option :label="item.label" :value="item.value" v-for="item in ALARM_LEVEL" :key="item.value"></el-option>
        </el-select>
        <div class="leve-tips" style="font-style: italic">
          告警级别为
          <span style="font-weight: bold; color: #6b6b6b">普通</span>
          时发送站内信信息,级别为
          <span style="font-weight: bold; color: #f3ba0f">警告</span>&nbsp;
          <span style="font-weight: bold; color: #ff0d0d">严重</span>
          时发送邮件给设备所属的空间管理员
        </div>
      </el-form-item>
      <el-form-item prop="whenCondition" v-if="['automation', 'alarm'].includes(formValue.type)">
        <template slot="label">
          <div>当以下情况发生</div>
          <div class="label-tips">(任一情况发生即执行后续操作)</div>
        </template>
        <div>
          <template v-if="!disabled">
            <el-button
              size="medium"
              type="primary"
              @click="addSpecificTime()"
              class="mr-6"
              plain
              v-if="['automation', 'manual'].includes(formValue.type)"
              >添加特定时间</el-button
            >
            <el-button size="medium" type="primary" @click="addDevStatusChange(formValue.whenCondition)" plain>添加设备状态</el-button>
            <el-button size="medium" type="primary" @click="addDevEventChange(formValue.whenCondition)" plain>添加设备事件</el-button>
          </template>
          <div class="list-wrap">
            <div v-for="(condition, index) in formValue.whenCondition" :key="index" class="single-rule-item-wrap">
              <template v-if="condition.type === 'specificTime'">
                <el-time-picker v-model="condition.specificTime" placeholder="选择特定时间" class="mr-6 mt-6" value-format="HH:mm"> </el-time-picker>
                <el-select
                  style="width: 180px"
                  v-model="condition.repeatType"
                  placeholder="重复类型"
                  class="mr-6 mt-6"
                  :filterable="true"
                  @change="
                    {
                      condition.specificDay = null;
                      condition.weekday = null;
                      condition.dayOfMonth = null;
                    }
                  "
                >
                  <el-option :label="item.label" :value="item.value" v-for="item in REPEAT_TYPE_ENUM" :key="item.value"></el-option>
                </el-select>
                <el-date-picker
                  class="mr-6 mt-6"
                  v-model="condition.specificDay"
                  value-format="yyyy-MM-dd"
                  v-if="condition.repeatType === 'once'"
                  placeholder="选择日期"
                  type="date"
                />
                <el-select
                  style="width: 200px"
                  v-if="condition.repeatType === 'everyWeekDay'"
                  v-model="condition.weekday"
                  placeholder="重复周几"
                  class="mr-6 mt-6"
                  multiple
                  collapse-tags
                >
                  <el-option :label="item.label" :value="item.value" v-for="item in REPEAT_DAY_ENUM" :key="item.value"></el-option>
                </el-select>
                <el-select
                  style="width: 200px"
                  v-if="condition.repeatType === 'everyMonth'"
                  v-model="condition.dayOfMonth"
                  placeholder="重复每月几号"
                  class="mr-6 mt-6"
                  multiple
                  collapse-tags
                >
                  <el-option :label="item.label" :value="item.value" v-for="item in dayOptions()" :key="item.value"></el-option>
                </el-select>
              </template>
              <template v-if="condition.type === 'deviceStatusChange'">
                <property-cond-act
                  :deviceDetailMap="deviceDetailMap"
                  :allDeviceList="allDeviceList"
                  :property="condition"
                  @getDeviceDetail="getDeviceDetail"
                  type="condition"
                ></property-cond-act>
              </template>
              <template v-if="condition.type === 'deviceEvent'">
                <event-cond
                  :event="condition"
                  :allDeviceList="allDeviceList"
                  :deviceDetailMap="deviceDetailMap"
                  @getDeviceDetail="getDeviceDetail"
                ></event-cond>
              </template>
              <el-button size="small" @click="deleteItem(index, formValue.whenCondition)" class="ml-6 mt-6">删除</el-button>
            </div>
            <div v-show="!formValue.whenCondition || formValue.whenCondition.length == 0" class="empty-when-conditions-block"> 请添加 </div>
          </div>
        </div>
      </el-form-item>
      <el-form-item label="状态限制">
        <div>
          <div class="opt-cond-wrap mb-2">
            <span class="ml-6">条件限制：</span>
            <el-select v-model="formValue.satisfyCondition" placeholder="触发类型" :filterable="true" style="width: 180px">
              <el-option :label="item.label" :value="item.value" v-for="item in SATISFY_CONDITION_ENUM" :key="item.value"></el-option>
            </el-select>
          </div>
          <el-button type="primary" class="mb-2" @click="addDevStatusChange(formValue.statusCondition)" plain v-if="!disabled">添加条件</el-button>
          <div class="list-wrap">
            <div v-for="(condition, index) in formValue.statusCondition" :key="index" class="single-rule-item-wrap">
              <property-cond-act
                :deviceDetailMap="deviceDetailMap"
                :allDeviceList="allDeviceList"
                :property="condition"
                type="condition"
                @getDeviceDetail="getDeviceDetail"
              >
              </property-cond-act>
              <el-button size="small" @click="deleteItem(index, formValue.statusCondition)" class="ml-6 mt-6" v-if="!disabled"> 删除 </el-button>
            </div>
          </div>
        </div>
      </el-form-item>
      <el-form-item label="执行操作" v-if="['automation', 'manual'].includes(formValue.type)">
        <div>
          <template v-if="!disabled">
            <el-button size="medium" type="primary" @click="addThenAction('changeDeviceProperty')" plain>修改设备属性</el-button>
            <el-button size="medium" type="primary" @click="addThenAction('executeDeviceMethod')" plain>执行设备方法</el-button>
            <el-button size="medium" type="primary" @click="addThenAction('delayExecute')" plain>延迟执行</el-button>
            <el-button size="medium" type="primary" @click="addThenAction('callScene')" plain>调用场景</el-button>
          </template>
          <div class="drag-wrap">
            <draggable
              v-model="formValue.thenAction"
              :force-fallback="true"
              chosen-class="chosen-rule-item"
              ghost-class="ghost-rule-item"
              animation="300"
              handle=".single-action-item-wrap"
              :delay="200"
            >
              <div class="single-action-item-wrap" :key="index" v-for="(element, index) in formValue.thenAction">
                <i class="el-icon-rank mt-6 mr-6 drag-icon"></i>
                <div class="action-item">
                  <template v-if="element.type === 'delayExecute'">
                    <el-tag :hit="false" type="info" class="mt-6 mr-6">延迟</el-tag>
                    <el-input-number
                      controls-position="right"
                      class="mt-6"
                      style="width: 180px"
                      v-model="element.delayTime"
                      :min="0"
                    ></el-input-number>
                    <span class="ml-4 mt-6">ms</span>
                    <el-tooltip placement="top" content="延迟执行该操作，单位ms">
                      <i class="el-icon-warning-outline ml-4 mt-6 cursor-pointer"></i>
                    </el-tooltip>
                  </template>
                  <template v-if="element.type === 'doAlarm'">
                    <el-tag :hit="false" type="warning" class="mt-6 mr-6">告警</el-tag>
                    <el-input class="mt-6 mr-6" style="width: 180px" v-model="element.alarmContent" type="text" placeholder="告警信息" clearable />
                  </template>
                  <template v-if="element.type === 'callScene'">
                    <el-tag :hit="false" color="#e8d3ff" class="mt-6 mr-6" style="color: #845ec2">场景</el-tag>
                    <template v-if="!isValidValue(element.sceneId) || allExistRule.find((item) => item.value === element.sceneId)">
                      <el-select v-model="element.sceneId" placeholder="选择场景" :filterable="true" style="width: 180px" class="mt-6 mr-6">
                        <el-option
                          :label="item.label"
                          :value="item.value"
                          v-for="item in allExistRule.filter((item) => item.value !== id)"
                          :key="item.value"
                        ></el-option>
                      </el-select>
                    </template>
                    <template v-else>
                      <div class="mt-6">该场景【{{ element.sceneId }}】可能已被删除或未添加</div>
                    </template>
                  </template>
                  <template v-if="element.type === 'changeDeviceProperty'">
                    <el-tag :hit="false" class="mt-6 mr-6">属性</el-tag>
                    <property-cond-act
                      :deviceDetailMap="deviceDetailMap"
                      :allDeviceList="allDeviceList"
                      :property="element"
                      type="action"
                      @getDeviceDetail="getDeviceDetail"
                    ></property-cond-act>
                  </template>
                  <template v-if="element.type === 'executeDeviceMethod'">
                    <el-tag :hit="false" type="success" class="mt-6 mr-6">方法</el-tag>
                    <method-act
                      :allDeviceList="allDeviceList"
                      :method="element"
                      :deviceDetailMap="deviceDetailMap"
                      @getDeviceDetail="getDeviceDetail"
                    ></method-act>
                  </template>
                  <template v-if="!disabled">
                    <el-button size="small" type="danger" plain @click="deleteItem(index, formValue.thenAction)" class="ml-6 mt-6"> 删除 </el-button>
                    <el-button
                      size="small"
                      type="primary"
                      @click="copyItem(index, formValue.thenAction)"
                      class="ml-6 mt-6"
                      plain
                      v-if="!isValidValue(element.deviceId) || allDeviceList.find((item) => item.value === element.deviceId)"
                    >
                      复制
                    </el-button>
                  </template>
                </div>
              </div>
            </draggable>
          </div>
        </div>
      </el-form-item>
      <el-form-item label="生效时间段" prop="validPeriod">
        <el-select
          style="width: 180px"
          v-model="formValue.validPeriod.type"
          placeholder="生效时间类型"
          class="mr-6 mt-6"
          :filterable="true"
          @change="
            {
              formValue.validPeriod.startTime = undefined;
              formValue.validPeriod.endTime = undefined;
            }
          "
        >
          <el-option :label="item.label" :value="item.value" v-for="item in VALID_PERIOD_TYPE_ENUM" :key="item.value"></el-option>
        </el-select>
        <el-time-picker
          v-if="formValue.validPeriod.type === 'specificTimeRange'"
          placeholder="开始时间"
          value-format="HH:mm"
          class="mr-6 mt-6"
          v-model="formValue.validPeriod.startTime"
        />
        <el-time-picker
          v-if="formValue.validPeriod.type === 'specificTimeRange'"
          placeholder="结束时间"
          value-format="HH:mm"
          class="mr-6 mt-6"
          v-model="formValue.validPeriod.endTime"
        />
        <el-select
          style="width: 180px"
          v-model="formValue.validPeriod.repeatType"
          placeholder="重复类型"
          class="mr-6 mt-6"
          :filterable="true"
          @change="
            {
              formValue.validPeriod.weekday = null;
              formValue.validPeriod.dayOfMonth = null;
            }
          "
        >
          <el-option :label="item.label" :value="item.value" v-for="item in PERIOD_REPEAT_TYPE_ENUM" :key="item.value"></el-option>
        </el-select>
        <el-select
          style="width: 200px"
          v-if="formValue.validPeriod.repeatType === 'everyWeekDay'"
          v-model="formValue.validPeriod.weekday"
          placeholder="重复周几"
          class="mr-6 mt-6"
          multiple
          collapse-tags
        >
          <el-option :label="item.label" :value="item.value" v-for="item in REPEAT_DAY_ENUM" :key="item.value"></el-option>
        </el-select>
        <el-select
          style="width: 200px"
          v-if="formValue.validPeriod.repeatType === 'everyMonth'"
          v-model="formValue.validPeriod.dayOfMonth"
          placeholder="重复每月几号"
          class="mr-6 mt-6"
          multiple
          collapse-tags
        >
          <el-option :label="item.label" :value="item.value" v-for="item in dayOptions()" :key="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否自动创建工单" prop="createWorkOrder" v-if="['alarm'].includes(formValue.type)">
        <el-switch v-model="createWorkOrder" />
      </el-form-item>
      <el-form-item label="工单受理人" prop="workOrderManager" v-if="createWorkOrder">
        <mul-input
          style="width: 220px"
          placeholder="选择工单受理人"
          :value="workOrderManagerName"
          @click="selectWorkOrderManager"
          @clear="clearWorkOrderManager"
        >
        </mul-input>
        <div style="font-style: italic"> 发生告警时会同步创建工单并根据告警级别发送告警信息给工单受理人 </div>
      </el-form-item>
      <el-form-item label="是否启用" prop="enabled">
        <el-switch v-model="formValue.enabled" />
      </el-form-item>
      <el-form-item label="高级选项" @click.native="clickAdvancedOptions" class="advanced-options"> </el-form-item>
      <el-form-item label="静默时长" prop="silenceDuration" v-if="showAdvancedOptions">
        <el-input-number controls-position="right" v-model="formValue.silenceDuration" :min="0" style="width: 180px"> </el-input-number>
        <span class="ml-4">ms</span>
        <el-tooltip placement="top" content="静默时长内将不再重复执行规则">
          <i class="el-icon-warning-outline ml-4 cursor-pointer"></i>
        </el-tooltip>
      </el-form-item>
      <el-form-item label="执行方式" prop="sequenceType" v-if="['automation', 'manual'].includes(formValue.type) && showAdvancedOptions">
        <el-select v-model="formValue.sequenceType" placeholder="执行方式" style="width: 180px" :filterable="true">
          <el-option :label="item.label" :value="item.value" v-for="item in SEQUENCE_TYPE_ENUM" :key="item.value"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <el-button type="primary" @click="formSubmit" :loading="submitLoading" class="confirm-btn" v-if="!disabled">保存</el-button>
    <el-button @click="goBack">{{ disabled ? '返回' : '取消' }}</el-button>
  </div>
</template>

<script>
  import { getAllRule, getAlarmRuleById, deviceGetAllList, getProductDetailByCode, saveRule, getRuleById, saveAlarmRule } from '@/api/iotControl';
  import {
    REPEAT_TYPE_ENUM,
    PERIOD_REPEAT_TYPE_ENUM,
    VALID_PERIOD_TYPE_ENUM,
    REPEAT_DAY_ENUM,
    PROPERTY_OPT_ENUM,
    SATISFY_CONDITION_ENUM,
    SEQUENCE_TYPE_ENUM,
    ALARM_LEVEL,
  } from '@/views/iotControl/enum';
  import propertyCondAct from './propertyCondAct.vue';
  import methodAct from './methodAct.vue';
  import eventCond from './eventCond.vue';
  import draggable from 'vuedraggable';
  import userTransfer from '@/views/iotControl/modules/userTransfer.vue';
  import transform from '@/utils/transform';
  import mulInput from '@/components/mulInput';
  export default {
    components: { propertyCondAct, methodAct, draggable, userTransfer, mulInput, eventCond },
    props: {
      id: {
        type: String | Number,
        default: '',
      },
      type: {
        type: String,
        default: '',
      },
      disabled: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        ALARM_LEVEL,
        REPEAT_TYPE_ENUM,
        PERIOD_REPEAT_TYPE_ENUM,
        VALID_PERIOD_TYPE_ENUM,
        REPEAT_DAY_ENUM,
        PROPERTY_OPT_ENUM,
        SATISFY_CONDITION_ENUM,
        SEQUENCE_TYPE_ENUM,
        allDeviceList: [],
        allExistRule: [],
        //设备属性方法事件详情，避免多次请求
        deviceDetailMap: {},
        formValue: {
          name: '',
          type: '',
          enabled: true,
          description: '',
          // 触发条件[以下情况发生时]
          whenCondition: [],
          //触发条件的设备id列表
          whenConditionDeviceIdList: [],
          // 状态限制类型
          satisfyCondition: 'any',
          // 状态限制
          statusCondition: [],
          thenAction: [],
          // 静默时长【防止短时间内重复触发】
          silenceDuration: 0,
          // 生效时间段
          validPeriod: {
            type: 'wholeDay',
            startTime: null,
            endTime: null,
            repeatType: 'everyDay',
            dayOfMonth: null,
            weekday: null,
          },
          sequenceType: 'serial',
        },

        //告警附加属性选项
        level: 'ERROR',
        createWorkOrder: false,
        workOrderManager: '',
        workOrderManagerName: '',
        rules: {
          name: [{ required: true, message: '请输入规则名称', trigger: 'blur' }],
          workOrderManager: [
            {
              required: true,
              message: '请选择工单处理人',
              trigger: 'blur',
              validator: (rule, value, callback) => {
                if (this.workOrderManager) {
                  callback();
                } else {
                  callback('请选择工单处理人');
                }
              },
            },
          ],
        },
        submitLoading: false,
        showAdvancedOptions: false,
      };
    },
    created() {
      this.formValue.type = this.type;
      this.init();
    },
    methods: {
      init() {
        // 获取全部设备（包括设备组）
        deviceGetAllList().then(({ data }) => {
          //只显示有产品code的
          let deviceList = data
            .filter((item) => this.isValidValue(item.code))
            .map((item) => ({
              value: item.deviceId,
              label: `${item.deviceName}${item.spaceName ? ` (${item.spaceName})` : ''}`,
              rawDevice: item,
            }));
          this.allDeviceList = deviceList || [];

          //初始化
          if (this.id) {
            let fn = null;
            let param = null;
            if (['automation', 'manual'].includes(this.type)) {
              fn = getRuleById;
              param = this.id;
            }
            if (this.type === 'alarm') {
              fn = getAlarmRuleById;
              param = { id: this.id };
            }
            //初始化设备产品模型和属性方法模型
            fn(param).then(async ({ data }) => {
              if (this.type === 'alarm') {
                this.level = data.level;
                this.createWorkOrder = data.createWorkOrder || false;
                this.workOrderManager = data.workOrderManager;
                this.workOrderManagerName = data.workOrderManagerName;
                Object.assign(this.formValue, data.rule);
              } else if (['automation', 'manual'].includes(this.type)) {
                Object.assign(this.formValue, data);
              }
              for (let rule of this.formValue.whenCondition) {
                if (rule.type === 'deviceStatusChange') {
                  let code = this.allDeviceList.find((item) => item.value === rule.deviceId)?.rawDevice?.code;
                  await this.initDeviceDetail(code, rule, 'property');
                }
                if (rule.type === 'deviceEvent') {
                  let code = this.allDeviceList.find((item) => item.value === rule.deviceId)?.rawDevice?.code;
                  await this.initDeviceDetail(code, rule, 'event');
                }
              }
              for (let rule of this.formValue.statusCondition) {
                let code = this.allDeviceList.find((item) => item.value === rule.deviceId)?.rawDevice?.code;
                await this.initDeviceDetail(code, rule, 'property');
              }
              for (let rule of this.formValue.thenAction) {
                if (rule.type === 'changeDeviceProperty') {
                  let code = this.allDeviceList.find((item) => item.value === rule.deviceId)?.rawDevice?.code;
                  await this.initDeviceDetail(code, rule, 'property');
                }
                if (rule.type === 'executeDeviceMethod') {
                  let code = this.allDeviceList.find((item) => item.value === rule.deviceId)?.rawDevice?.code;
                  await this.initDeviceDetail(code, rule, 'method');
                }
              }
            });
          }
        });

        getAllRule({
          type: 'manual',
        }).then(({ data }) => {
          let sceneList = data.map((item) => ({
            label: item.name,
            value: item.id,
          }));
          this.allExistRule = sceneList || [];
        });
      },
      dayOptions() {
        return Array.from({ length: 31 }, (item, index) => ({
          label: `${index + 1}号`,
          value: index + 1,
        }));
      },
      addSpecificTime() {
        let specificTime = {
          type: 'specificTime',
          specificTime: null,
          repeatType: null,
          dayOfMonth: null,
          weekday: null,
          specificDay: null,
        };
        this.formValue.whenCondition.push(specificTime);
      },
      addDevEventChange(conditionList) {
        let devEventChange = {
          type: 'deviceEvent',
          deviceId: null,
          deviceName: null,
          propertyKey: null,
          propertyValue: null,
          operator: null,
          device: null,
          propModel: null,
          // 事件标识
          eventAddress: null,
          // 事件参数条件列表
          eventParamConditions: null,
          eventModel: null,
        };
        conditionList.push(devEventChange);
      },
      addDevStatusChange(conditionList) {
        let devStatusChange = {
          type: 'deviceStatusChange',
          deviceId: null,
          deviceName: null,
          propertyKey: null,
          propertyValue: null,
          operator: null,
          device: null,
          propModel: null,
        };
        conditionList.push(devStatusChange);
      },
      addThenAction(type) {
        let thenAction = {
          type: type,
          deviceId: null,
          deviceName: null,
          propertyKey: null,
          propertyValue: null,
          methodName: null,
          methodParams: null,
          delayTime: null,
          alarmContent: null,
          sceneId: null,
          groupTag: null,
          order: 0,
          device: null,
          propModel: null,
          methodModel: null,
        };
        this.formValue.thenAction.push(thenAction);
      },
      getDeviceDetail(code) {
        return new Promise((resolve, reject) => {
          if (!code) {
            resolve();
          } else if (!this.deviceDetailMap[code]) {
            getProductDetailByCode(code).then(({ data }) => {
              data.methods = data.methods.map((method) => ({
                value: method.address,
                description: method.description,
                label: method.name,
                params: method.params || [],
              }));
              //过滤前端不可见
              data.properties = data.properties
                .filter((prop) => prop.widgetVisible)
                .map((prop) => ({
                  value: prop.address,
                  description: prop.description,
                  label: prop.name,
                  constraint: prop.constraint,
                  access: prop.access,
                }));
              // 固定的上线下线事件
              let fixedEvent = [
                { type: 'EVENT', dataType: 'INTEGER', address: 'onlineEvent', name: '设备上线', description: '设备上线', eventType: 'ONLINE' },
                { type: 'EVENT', dataType: 'INTEGER', address: 'offlineEvent', name: '设备下线', description: '设备离线', eventType: 'OFFLINE' },
              ];
              let eventAddressSet = new Set(data.events.map((item) => item.address));
              // 过滤并添加固定事件
              data.events = [...fixedEvent.filter((item) => !eventAddressSet.has(item.address)), ...data.events].map(
                ({ address, description, name, params = [] }) => ({
                  value: address,
                  description,
                  label: name,
                  params,
                })
              );
              this.$set(this.deviceDetailMap, code, data);
              resolve();
            });
          } else {
            resolve();
          }
        });
      },
      async initDeviceDetail(code, rule, type) {
        await this.getDeviceDetail(code);
        if (type === 'property') {
          let propModel = this.deviceDetailMap[code].properties.find((item) => item.value === rule.propertyKey);
          this.$set(rule, 'propModel', propModel);
        }
        if (type === 'method') {
          let methodModel = this.deviceDetailMap[code].methods.find((item) => item.value === rule.methodName);
          this.$set(rule, 'methodModel', methodModel);
        }
        if (type === 'event') {
          let eventModel = this.deviceDetailMap[code].events.find((item) => item.value === rule.eventAddress);
          this.$set(rule, 'eventModel', eventModel);
        }
      },
      deleteItem(index, list) {
        list.splice(index, 1);
      },
      copyItem(index, list) {
        const item = JSON.parse(JSON.stringify(list[index]));
        list.push(item);
      },
      isValidValue(value) {
        if (Array.isArray(value)) {
          return value.length > 0;
        }
        return value !== undefined && value !== null && value !== '';
      },
      clearWorkOrderManager() {
        this.workOrderManager = '';
        this.workOrderManagerName = '';
      },
      userTransferDlg: transform(userTransfer),
      selectWorkOrderManager() {
        this.userTransferDlg({
          propsData: {
            users: this.workOrderManager ? [{ id: this.workOrderManager, username: this.workOrderManagerName }] : [],
            isMulti: false,
          },
          methods: {
            handleSelect: (users) => {
              this.workOrderManager = users[0].id;
              this.workOrderManagerName = users[0].username;
            },
          },
        });
      },
      isValidEventParamConditions(list) {
        if (!Array.isArray(list)) {
          return true;
        }

        return list.every((item) => {
          if (item.value === undefined || item.value === null || item.value === '') {
            return false;
          }
          return true;
        });
      },
      checkWhenConditionRequired(list) {
        if (!Array.isArray(list)) {
          return true;
        }
        return list.every((condition) => {
          if (condition.type === 'specificTime') {
            if (!condition.specificTime || !condition.repeatType) {
              return false;
            }
            if (condition.repeatType === 'once') {
              return this.isValidValue(condition.specificDay);
            }
            if (condition.repeatType === 'everyWeekDay') {
              return this.isValidValue(condition.weekday);
            }
            if (condition.repeatType === 'everyMonth') {
              return this.isValidValue(condition.dayOfMonth);
            }
          }
          if (condition.type === 'deviceStatusChange') {
            return this.isValidValue(condition.propertyValue);
          }
          if (condition.type === 'deviceEvent') {
            return condition.deviceId && condition.eventAddress && this.isValidEventParamConditions(condition.eventParamConditions);
          }
          return true;
        });
      },
      checkStatusConditionRequired(list) {
        if (!Array.isArray(list)) {
          return true;
        }
        return list.every((condition) => this.isValidValue(condition.propertyValue));
      },
      checkThenActionRequired(list) {
        if (!Array.isArray(list)) {
          return true;
        }
        return list.every((action) => {
          switch (action.type) {
            case 'changeDeviceProperty':
              return this.isValidValue(action.propertyValue);
            case 'executeDeviceMethod':
              return this.isValidValue(action.methodName);
            case 'delayExecute':
              return this.isValidValue(action.delayTime);
            case 'doAlarm':
              return this.isValidValue(action.alarmContent);
            case 'callScene':
              return this.isValidValue(action.sceneId);
            default:
              return true;
          }
        });
      },
      checkValidPeriodRequired(period) {
        if (period.repeatType === 'everyWeekDay' && !this.isValidValue(period.weekday)) {
          return false;
        }
        if (period.repeatType === 'everyMonth' && !this.isValidValue(period.dayOfMonth)) {
          return false;
        }
        if (period.type === 'specificTimeRange') {
          if (!period.startTime || !period.endTime) {
            return false;
          }
        }
        return true;
      },
      formSubmit() {
        if (!this.checkWhenConditionRequired(this.formValue.whenCondition)) {
          this.$message.warning('请填写情况发生必填项！');
          return;
        }
        if (!this.checkStatusConditionRequired(this.formValue.statusCondition)) {
          this.$message.warning('请填写状态条件必填项！');
          return;
        }
        if (!this.checkThenActionRequired(this.formValue.thenAction)) {
          this.$message.warning('请填写执行操作必填项！');
          return;
        }
        if (!this.isValidValue(this.formValue.silenceDuration)) {
          this.$message.warning('请填写静默时长！');
          return;
        }
        if (!this.checkValidPeriodRequired(this.formValue.validPeriod)) {
          this.$message.warning('请填写生效时间段必填项！');
          return;
        }
        //报警规则：当以下情况发生必填
        if (this.type === 'alarm' && this.formValue.whenCondition.length === 0) {
          this.$message.warning('告警规则中【当以下情况发生】必填！');
          return;
        }
        if (this.type === 'alarm' && this.createWorkOrder && !this.isValidValue(this.workOrderManager)) {
          this.$message.warning('请选择工单受理人！');
          return;
        }
        this.$refs.formRef.validate((valid) => {
          if (valid) {
            this.submitLoading = true;
            this.formValue.thenAction.forEach((action, index) => {
              action.order = index;
            });
            let fn = null;
            let params = {};
            if (this.type === 'alarm') {
              fn = saveAlarmRule;
              params = {
                id: this.id,
                level: this.level,
                rule: this.formValue,
                createWorkOrder: this.createWorkOrder,
                workOrderManager: this.workOrderManager,
              };
            } else {
              fn = saveRule;
              params = this.formValue;
            }
            fn(params)
              .then(() => {
                this.$message.success('保存成功!');
                this.goBack();
              })
              .finally(() => {
                this.submitLoading = false;
              });
            return;
          }
        });
      },
      // 取消
      goBack() {
        this.$router.go(-1);
      },
      clickAdvancedOptions() {
        this.showAdvancedOptions = !this.showAdvancedOptions;
      },
    },
  };
</script>
<style lang="scss" scoped>
  .wrap {
    margin-left: 20px;
    display: block;
    ::v-deep .el-input.is-disabled .el-input__inner {
      background-color: rgba(251, 251, 251, 0.7);
      color: #5d6f93;
    }

    ::v-deep .el-textarea.is-disabled .el-textarea__inner {
      background-color: rgba(251, 251, 251, 0.7);
      color: #5d6f93;
    }
  }
  .top-form {
    padding: 20px 0 1px;
    .confirm-btn {
      margin-left: 163px;
    }
  }
  .single-rule-item-wrap {
    padding: 6px 8px 12px 8px;
    margin-top: 10px;
    border: 0.5px dashed rgba(197, 197, 197, 0.7);
    border-radius: 4px;
    display: flex;
    align-items: center;
    transition: border linear 200ms;
  }

  .single-rule-item-wrap:hover {
    border: 0.5px dashed rgb(45, 140, 240);
  }

  .chosen-rule-item {
    background-color: rgba(45, 140, 240, 0.16);
  }

  .ghost-rule-item {
    visibility: hidden;
  }
  .single-action-item-wrap {
    padding: 6px 8px 12px 8px;
    margin-top: 10px;
    border: 0.5px dashed rgba(197, 197, 197, 0.7);
    border-radius: 4px;
    display: flex;
    align-items: center;
    transition: border linear 200ms;
    user-select: none;
    .action-item {
      display: flex;
      align-items: center;
    }
  }
  .single-action-item-wrap:hover {
    border: 0.5px dashed rgb(45, 140, 240);
  }

  .drag-icon {
    width: 20px;
    opacity: 0.5;
    transition: all linear;
    cursor: move;
    &:hover {
      opacity: 0.8;
    }
  }

  .limited-width {
    max-width: 500px;
  }
  .opt-cond-wrap {
    align-items: center;
    display: flex;
    margin-bottom: 10px;
    background-color: rgb(238, 243, 247);
    padding: 10px;
    border-radius: 5px;
    width: 280px;
  }
  ::v-deep.advanced-options .el-form-item__label {
    cursor: pointer !important;
  }
  ::v-deep.advanced-options .el-form-item__label:hover {
    color: #00457a !important;
  }
  .list-wrap {
    width: fit-content;
  }
  .drag-wrap {
    display: flex;
  }

  .label-tips {
    font-size: 12px;
    font-weight: 500;
  }

  .empty-when-conditions-block {
    margin-top: 12px;
    width: 350px;
    border-radius: 4px;
    height: 62px;
    border: 0.5px dashed rgba(197, 197, 197, 0.7);
    transition: border linear 200ms;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #8e8e8f;
    &:hover {
      border: 0.5px dashed rgb(45, 140, 240);
    }
  }
</style>
