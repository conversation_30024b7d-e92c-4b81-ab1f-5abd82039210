<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Document</title>
  <link rel="stylesheet" href="40e93b11cbd4174c60b22cba0d630300.css" />
  <link rel="stylesheet" href="133e6f6888709a53e833618d0cd0b864.css" />
  <style>
    .svg-icon {
      width: 1em;
      height: 1em;
      vertical-align: -0.15em;
      fill: currentColor;
      overflow: hidden;
    }

    ul {
      margin: 0;
      padding: 0;
    }

    ul li {
      list-style: none;
    }

    body,
    html {
      height: 100%;
      margin: 0;
    }

    .wrap {
      width: 100%;
      height: 100%;
      padding: 0 2%;
      box-sizing: border-box;
      overflow: hidden;
      color: white;
      background: rgb(215, 20, 24);
      position: relative;
    }

    .wrap .middle-wrap {
      width: 100%;
      height: 99%;
    }

    .middle-wrap .header-wrap {
      width: 100%;
      height: 12vh;
      line-height: 12vh;
      font-size: 8vh;
      padding: 0 2%;
      box-sizing: border-box;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-weight: bold;
    }

    .middle-wrap .content-wrap {
      height: calc(100% - 12vh);
      padding-top: 4%;
      box-sizing: border-box;
    }

    .content-wrap .top-wrap {
      height: 35%;
      box-sizing: border-box;
      overflow: hidden;
      border: 4px solid #fff;
      padding: 0 6% 2% 2%;
      border-radius: 6px;
    }

    .content-wrap .top-wrap .title {
      margin: 4vh 0 3.5vh;
      font-size: 3vh;
      font-weight: bold;
    }

    .content-wrap .top-wrap .empty-wrap {
      font-size: 6vh;
      line-height: 4.5vh;
      text-align: center;
    }

    .content-wrap .top-wrap .scroll-wrap {
      height: 13vh;
      overflow: hidden;
    }

    .content-wrap .top-wrap ul {
      width: 100%;
    }

    .content-wrap .top-wrap li {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      line-height: 2;
      font-size: 3.25vh;
    }

    .content-wrap .top-wrap li .left {
      display: flex;
      flex-direction: row;
      flex-wrap: nowrap;
      overflow: hidden;
    }

    .content-wrap .top-wrap li .left .time {
      padding-right: 0.5vmin;
      font-weight: bold;
      position: relative;
      flex-shrink: 0;
      width: 62vmin;
    }

    .content-wrap .top-wrap li .left .theme {
      overflow: hidden;
      white-space: nowrap;
      display: flex;
      align-items: center;
    }

    .content-wrap .top-wrap li .right {
      flex-shrink: 0;
    }

    .content-wrap .bottom-wrap {
      margin: 8vh 0 2% 2%;
      height: 55%;
    }

    .content-wrap .bottom-wrap .cur-wrap {
      display: flex;
      flex-direction: column;
      height: 100%;
    }

    .content-wrap .bottom-wrap .status {
      position: relative;
      font-size: 4vh;
      padding-bottom: 3vh;
      margin-bottom: 2vh;
      letter-spacing: 0.3vmin;
      font-weight: bold;
    }

    .content-wrap .bottom-wrap .cur-wrap {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: flex-start;
    }

    .content-wrap .bottom-wrap .cur-wrap .cur {
      font-size: 10vh;
      font-weight: bold;
      margin-bottom: 6vh;
      letter-spacing: 0.3vmin;
      overflow: hidden;
      word-break: break-all;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .content-wrap .bottom-wrap .extra-wrap {
      flex: 1;
      overflow: hidden;
      margin-right: 2%;
    }

    .content-wrap .bottom-wrap .extra-wrap .reservation {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      font-size: 4vh;
      margin-bottom: 1.5vh;
    }

    .content-wrap .bottom-wrap .extra-wrap .reservation .title {
      display: inline-block;
      max-width: 17.5vh;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-left: 0.5vmin;
      margin-right: 1vmin;
      padding-right: 1vmin;
      border-right: 1px solid white;
    }

    .content-wrap .bottom-wrap .qrcode-wrap {
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 1vh;
    }

    .content-wrap .bottom-wrap .qrcode-wrap img {
      height: calc(100% - 25vh);
    }

    .content-wrap .bottom-wrap .qrcode-wrap .desc {
      display: inline-block;
      border-radius: 1vmin;
      margin-top: 0.5vh;
      padding: 0.5vh;
      letter-spacing: 0.5vmin;
      box-sizing: border-box;
      font-size: 2vh;
    }

    .content-wrap>.empty-wrap {
      font-size: 8vh;
      height: 68%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    [v-cloak] {
      display: none;
    }
  </style>
  <!-- vue -->
  <script src="174365807c06490c848d7b1d45fdc348.js"></script>
  <!-- jquery.js -->
  <script src="13c0a5055cca7b2463b2f73701960b9e.js"></script>
  <!-- mqtt -->
  <script src="d4bfcfa9de50875589eaab322a015441.js"></script>
  <!-- 文字横向滚动组件 -->
  <script src="59e4fb71c5c2fa1dba4969d59dcad854.js"></script>
  <!-- 列表向上滚动组件 -->
  <script src="c2cafcfcea4bfe9b3aafc8c8c03ea071.js"></script>
  <script src="page.js"></script>
</head>

<body>
  <div id="app" class="wrap" v-cloak :style="{
      backgroundColor:bConfs?style.backgroundActiveColor:style.backgroundColor,
      color:style.color}">
    <!-- <img src="./f64bbff3ab87e4d06fb387b50451ba61.png" alt="" /> -->
    <div class="middle-wrap" :style="{width:style.width}">
      <div class="header-wrap">
        {{conferenceRoom.roomName||terminalName}}
      </div>
      <div class="content-wrap">
        <div class="top-wrap">
          <div class="title">会议列表</div>
          <div v-if="!conferenceInfo.length" class="empty-wrap">无后续会议</div>
          <scroll-list v-else>
            <template v-slot:content="{curPage}">
              <li v-for="(item,index) in conferenceInfo" :key="item.confId" @click="clickConference(item)">
                <div class="left">
                  <div class="time">
                    {{item.period}}
                  </div>
                  <div class="theme">
                    <marquee :val="item.subject" :scroll="parseInt(index / 2) === curPage"></marquee>
                  </div>
                </div>
                <div class="right" v-if="style.bShowReserver">
                  {{item.nickname || item.contactPerson}}
                </div>
              </li>
            </template>
        </div>
        <div class="bottom-wrap" v-if="Object.keys(curConference).length">
          <div class="status">{{curConference.status}}</div>
          <div class="cur-wrap">
            <div class="extra-wrap">
              <div class="cur">{{curConference.subject}}</div>
              <div class="reservation">
                <div v-if="style.bShowReserver">预订人: </div>
                <div class="title" v-if="style.bShowReserver">{{curConference.nickname || curConference.contactPerson}}</div>
                <div class="time">{{curConference.period}}</div>
              </div>
            </div>
            <div class="qrcode-wrap" v-if="style.bShowSign">
              <img :src="curConference.qrCode" alt="" />
              <span v-if="curConference.qrCode" class="desc">扫码签到</span>
            </div>
          </div>
        </div>
        <div v-else class="empty-wrap">当前无会议</div>
      </div>
    </div>
  </div>
</body>

<script>
  window.$page = page;
  const cssProperty = ["height", "width", "fontSize"];
  const app = new Vue({
    el: "#app",
    data: {
      props: page.props,
      mqttClient: null,
      mqttTopic: "",
      mac: null,
      curConference: {
        status: "正在会议中",
        subject: "研究会议",
        contactPerson: "刘勇",
        period: "2023/09/18 16:57-2023/09/18 17:27",
        periodArr: ["2021-03-04", "20:33-21:33"],
        qrCode: "./b947d49f014c663d02583f0f6cfc141f.png",
        participant: [
          {
            username: "张三",
            signIn: true
          },
          {
            username: "李四",
            signIn: false
          },
          {
            username: "王五",
            signIn: false
          },
          {
            username: "刘勇",
            signIn: true
          },
          {
            username: "莹莹",
            signIn: false
          },
          {
            username: "于光",
            signIn: false
          }
        ]
      },
      conferenceList: [],
      conferenceInfo: [
        {
          confId: 1,
          contactPerson: "刘勇",
          participant: [
            {
              username: "刘勇",
              signIn: false
            }
          ],
          period: "2023/09/18 16:57-2023/09/18 17:27",
          periodArr: ["2021-03-04", "20:33-21:33"],
          qrCode: "./7a8606304f019293184038e341f24d85.jpg",
          subject: "研究会议之研究关于菌鲍菇如何在戈壁存活并带领大家发家致富的会议"
        },
        {
          confId: 2,
          contactPerson: "刘勇",
          participant: [
            {
              username: "刘勇",
              signIn: false
            }
          ],
          period: "2023/09/18 16:57-2023/09/18 17:27",
          periodArr: ["2021-03-04", "20:33-21:33"],
          qrCode: "./7a8606304f019293184038e341f24d85.jpg",
          subject: "研究会议之研究关于菌鲍菇如何在戈壁存活并带领大家发家致富的会议"
        },
        {
          confId: 3,
          contactPerson: "刘勇",
          participant: [
            {
              username: "刘勇",
              signIn: false
            }
          ],
          period: "2023/09/18 16:57-2023/09/18 17:27",
          periodArr: ["2021-03-04", "20:33-21:33"],
          qrCode: "./7a8606304f019293184038e341f24d85.jpg",
          subject: "研究会议之研究关于菌鲍菇如何在戈壁存活并带领大家发家致富的会议"
        }
      ],
      conferenceRoom: { roomName: "A217陆家嘴会议室" },
      terminalName: "",
      // 会议室状态，0为空闲，1为占用
      status: [
        {
          label: "空闲中",
          color: "#2c9678"
        },
        {
          label: "已预约",
          color: "#c21f30"
        }
      ],
      timer: null,
      statusTimer: null,
      mqtt: {},
    },
    computed: {
      style() {
        let style = {};
        for (let prop of this.props) {
          if (cssProperty.includes(prop.field)) {
            style[prop.field] = prop.value + "%"
          } else {
            style[prop.field] = prop.value
          }
        }
        return style;
      },
      bConfs() {
        if (Object.keys(this.curConference).length) {
          return true
        }
        else {
          return false
        }
      }
    },
    created() {
      if (!this.isWindows()) {
        this.curConference = {};
        this.conferenceInfo = [];
        this.conferenceRoom = { roomName: "" };
        const { mac, name } = this.getTerminalInfo();
        if (mac) {
          this.mac = mac;
          this.terminalName = name
          this.conferenceRoom = { roomName: "" }
          //初始化终端灯状态
          this.setTerminalStatus(0);
          //刷新终端灯的状态
          this.statusTimer && clearInterval(this.statusTimer);
          this.statusTimer = setInterval(this.refreshTerminalStatus, 5000);
        } else {
          this.log("终端信息获取失败");
        }
        let mqtt = this.getMqtt();
        if (mqtt) {
          this.mqtt = mqtt;
          this.getMqttServerAndConnect(mqtt);
        } else {
          this.log("mqtt地址获取失败");
        }
      }
      // let ws = "wss://rjms.putian-nst.com:443/mqtt/";
      // let username = "admin";
      // let password = "mqttServer";
      // let ws = "ws://218.94.61.65:8083/mqtt";
      // let username = "admin";
      // let password = "mqttServer";
      // let ws = "ws://192.168.89.153:8083/mqtt";
      // let username = "admin";
      // let password = "admin";
      // let ws = "ws://192.168.88.121:8080/mqtt";
      // let username = "admin";
      // let password = "";
      // let ws = "ws://192.168.90.1:8080/mqtt";
      // let username = "admin";
      // let password = "nst@aliyun";
      // this.getMqttServerAndConnect({ ws, username, password });
    },
    beforeDestory() {
      //关闭会议室状态
      this.setTerminalStatus(-1);
      this.mqttClose();
      this.IntervalClose();
    },
    mounted() {
      window["update"] = (val, mqtt = null) => {
        this.updateProps(val);
      };

      window["updateMqtt"] = param => {
        this.updateMqtt(param);
      };
    },
    methods: {
      updateMqtt(mqtt) {
        if (!mqtt) {
          return;
        }
        mqtt = JSON.parse(mqtt);

        if (
          (this.mqttClient && this.mqtt.ws !== mqtt.ws) ||
          (!this.mqttClient && !Object.keys(this.mqtt).length)
        ) {
          this.log("mqtt重连");
          this.mqttClose();
          this.curConference = {};
          this.conferenceInfo = [];
          this.conferenceRoom = { roomName: "" };
          this.mqtt = mqtt;
          this.getMqttServerAndConnect(mqtt);
        }
      },
      /*
       * 当前运行环境是终端还是pc
       */
      isWindows() {
        return window.DSS20AndroidJS === undefined;
      },
      /*
       * state -1:关闭 0:空闲 1:会议中 2:签到
       */
      setTerminalStatus(state) {
        if (window.DSS20AndroidJS) {
          // -1 关闭
          if (state === -1) {
            // this.log("关闭状态灯");
            window.DSS20AndroidJS.updateLedStatusByWeb(0);
          }

          // 状态灯设置
          let temp = this.getProp("statusLight");
          // this.log("状态灯设置", temp.value);
          if (temp) {
            // 开启
            if (temp.value) {
              let fieldKey = "";
              if (state === 0) {
                // 空闲 0
                fieldKey = "freeLightColor";
              } else if (state === 1) {
                // 会中 1
                fieldKey = "meetingLightColor";
              } else if (state === 2) {
                // 签到 2
                fieldKey = "signLightColor";
              }
              if (fieldKey) {
                let tempObj = this.getProp(fieldKey);
                // this.log(JSON.stringify(tempObj));
                if (tempObj) {
                  let lightVal = tempObj.value;
                  lightVal = Number(lightVal);
                  // this.log("更改灯光", lightVal);
                  window.DSS20AndroidJS.updateLedStatusByWeb(lightVal);
                }
              }
            } else {
              // 关闭状态灯
              window.DSS20AndroidJS.updateLedStatusByWeb(0);
            }
          }
        }
      },
      /*
       * 终端打印
       */
      log(msg) {
        console.log(msg);
        window.DSS20AndroidJS &&
          window.DSS20AndroidJS.htmlLogcat("会议指引：" + msg);
      },
      clickConference(item) {
        this.log('conferenceInfo')
        window.DSS20AndroidJS &&
          window.DSS20AndroidJS.conferenceInfo(JSON.stringify(item));
      },
      getTerminalInfo() {
        var data = window.DSS20AndroidJS.terminalInfo();
        var info = JSON.parse(data);
        return info;
      },
      getMqtt() {
        var data = window.DSS20AndroidJS.getWebMqttWs();
        var info = {};
        try {
          info = data && JSON.parse(JSON.parse(data));
        } catch (err) {
          this.log("mqtt地址解析失败！");
        }

        return info;
      },
      updateProps(props) {
        for (let prop of props) {
          let index = this.getPropIndex(prop.field);
          if (index !== -1) {
            let data = this.props[index];
            data.value = prop.value;
            this.$set(this.props, index, data);
          }
        }
      },
      getPropIndex(name) {
        for (let i = 0; i < this.props.length; i++) {
          if (this.props[i].field === name) {
            return i;
          }
        }
        return -1;
      },
      /**
       * 更新数据
       */
      updateConfByTime() {
        if (!this.conferenceList.length && this.isWindows()) {
          return;
        }
        const cur = +new Date();

        let bProcessing = false,
          siginConferenceList = [];
        let res = this.conferenceList.filter(item => {
          // if (item.approved !== 4) {
          //   return false;
          // }
          const { start, end } = item;
          if (end < cur) {
            return false;
          } else if (start <= cur && end >= cur) {
            bProcessing = true;
          } else if (start - cur <= 15 * 1000 * 60) {
            siginConferenceList.push(item);
          }
          return true;
        });
        //更新会议信息
        if (bProcessing) {
          this.curConference = { ...res.shift(), status: "正在会议中" };
        } else if (siginConferenceList.length) {
          this.curConference = {
            ...siginConferenceList[0],
            status: "签到中"
          };
        } else {
          this.curConference = {};
        }
        console.log(res);
        this.conferenceInfo = res;
        let timeArr = this.conferenceList.map(item => {
          return {
            startTimeArr: item.startTimeArr,
            endTimeArr: item.endTimeArr
          };
        });
      },
      //刷新终端灯的状态
      refreshTerminalStatus() {
        // 空闲
        let state = 0;

        // 会中
        if (
          Object.keys(this.curConference).length &&
          this.curConference.status === "正在会议中"
        ) {
          state = 1;
        }

        // 签到
        if (Object.keys(this.curConference).length && this.curConference.status === '签到中') {
          state = 2;
        }
        this.setTerminalStatus(state);
      },
      /**
       * 从 prop 中获取 val
       */
      getProp(field) {
        for (let prop of this.props) {
          if (prop.field == field) {
            return prop;
          }
        }
        return null;
      },
      /**
       * 随机ID
       * @param {*} len
       * @param {*} radix
       * @returns
       */
      randomId(len, radix) {
        var chars =
          '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('')
        var uuid = [],
          i
        radix = radix || chars.length
        if (len) {
          // Compact form
          for (i = 0; i < len; i++) uuid[i] = chars[0 | (Math.random() * radix)]
        } else {
          // rfc4122, version 4 form
          var r
          // rfc4122 requires these characters
          uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-'
          uuid[14] = '4'
          // Fill in random data.  At i==19 set the high bits of clock sequence as
          // per rfc4122, sec. 4.1.5
          for (i = 0; i < 36; i++) {
            if (!uuid[i]) {
              r = 0 | (Math.random() * 16)
              uuid[i] = chars[i == 19 ? (r & 0x3) | 0x8 : r]
            }
          }
        }
        return uuid.join('')
      },
      /**
       * 连接到MQTT服务器并订阅
       */
      getMqttServerAndConnect(param) {
        this.log("mqtt参数：" + JSON.stringify(param));

        const that = this;
        let { ws, username, password } = param;
        let mac = this.mac || ''
        // 客户端ID
        let clientId = `js_conferenceGuide3_${mac}_${this.randomId(16, 16)}`;

        that.mqttClient = mqtt.connect(ws, {
          clientId,
          username,
          password,
          clean: true,
          connectTimeout: 5 * 1000,
          keepalive: 30
        });
        that.mqttClient.on("connect", () => {
          this.log("mqtt连接成功");
          this.mqttRefreshSubscribe();
        });
        that.mqttClient.on("error", error => {
          this.log("mqtt连接失败：" + JSON.stringify(error));
          this.conferenceRoom = {};
          this.curConference = {};
          this.conferenceInfo = [];
        });
        //监听接收消息事件
        that.mqttClient.on("message", (topic, message) => {
          message = JSON.parse(message.toString());
          console.log(message)
          this.log("获取mqtt消息" + JSON.stringify(message));
          const { meetingroom, confDetailDTOList } = message;
          this.conferenceRoom = meetingroom;
          this.conferenceList = confDetailDTOList.map(item => {
            return this.getFormatData(item)
          });
          console.log(this.conferenceList);
          this.updateConfByTime();
        });
      },
      getFormatData(item) {
        let periodArr = item.period.split("-");
        let start, end, startTimeArr, endTimeArr
        // 获取startTimeArr、endTimeArr （[hh,mm]）
        // 会议可能跨天
        if (item.start && item.end) {
          start = item.start
          end = item.end

          const startTime = new Date(start)
          startTimeArr = [startTime.getHours(), startTime.getMinutes()]

          let curEndTime = new Date(new Date().setHours(23, 59, 59, 999)).getTime()
          let endTime = end > curEndTime ? new Date(curEndTime) : new Date(end)
          endTimeArr = [endTime.getHours(), endTime.getMinutes()]
        } else {
          // 获取start、end (时间戳)
          // 兼容之前的写法（会议不可能跨天）
          let timeArr = periodArr[1].split("-");
          startTimeArr = timeArr[0].split(":").map(Number);
          endTimeArr = timeArr[1].split(":").map(Number);

          start = new Date(new Date().setHours(startTimeArr[0], startTimeArr[1])).getTime()
          end = new Date(new Date().setHours(endTimeArr[0], endTimeArr[1])).getTime()
        }
        return {
          period: item.period,
          periodArr,
          start,
          end,
          startTimeArr,
          endTimeArr,
          confId: item.confId,
          subject: item.subject,
          nickname: item.nickname,
          contactPerson: item.contactPerson,
          participant: item.participant,
          approved: item.approved,
          qrCode: item.qrCode
        }
      },

      /**
       * 发布MQTT主题
       */
      mqttPublish(address) {
        let topic = "dss2/terminal/conference";
        let message = `{"command":"WEATHER","parameters":{${param}:"${address}"}}`;
        this.log("mqtt发布主题: " + message);
        this.mqttClient.publish(
          topic,
          message,
          { qos: 1, retain: true },
          (err, res) => {
            if (err) {
              this.log("mqtt发布主题失败：", err);
              return;
            }
            this.mqttRefreshSubscribe(address);
          }
        );
      },
      /**
       * 订阅MQTT主题
       */
      mqttRefreshSubscribe() {
        // this.mac = "00073D9001D7";
        // this.mac = "00073D90000F";
        const that = this;
        if (that.mqttTopic) {
          that.mqttClient.unsubscribe(that.mqttTopic);
        }
        that.mqttTopic = `dss2/web/conference/mac/${this.mac}`;
        this.log("mqtt订阅主题：" + that.mqttTopic);
        that.mqttClient.subscribe(
          that.mqttTopic,
          { qos: 1, rap: true },
          (err, res) => {
            if (err) {
              this.log("mqtt订阅主题失败：", err);
              return;
            }

            //刷新数据
            that.timer && clearInterval(that.timer);
            that.timer = setInterval(() => {
              that.updateConfByTime();
            }, 60000);
          }
        );
      },
      /**
       * 释放MQTT客户端
       */
      mqttClose() {
        this.mqtt = {};
        if (this.mqttClient) {
          this.mqttClient.unsubscribe(this.mqttTopic);
          this.mqttClient.end();
        }
      },
      IntervalClose() {
        this.timer && clearInterval(this.timer);
        this.statusTimer && clearInterval(this.statusTimer);
      }
    }
  });
</script>

</html>
