<template>
  <el-dialog title="编辑参数" :visible.sync="visible" width="640px" :close-on-click-modal="false" @close="close">
    <el-form ref="form" :model="paramForm" :rules="rules" label-width="120px">
      <el-form-item label="参数名称" prop="name">
        <el-input v-model="paramForm.name" size="small" placeholder="请输入参数名称"></el-input>
      </el-form-item>
      <el-form-item label="参数标识" prop="address">
        <el-input v-model="paramForm.address" size="small" placeholder="请输入参数标识"></el-input>
      </el-form-item>
      <el-form-item label="参数描述" prop="description">
        <el-input type="textarea" :rows="2" placeholder="请输入参数描述" v-model="paramForm.description"> </el-input>
      </el-form-item>
      <el-divider></el-divider>
      <el-form-item label="数据类型" prop="constraint.dataType">
        <el-select v-model="paramForm.constraint.dataType" placeholder="请选择数据类型" @change="changeDataType">
          <el-option v-for="(item, index) in PARAM_TYPE_ENUM" :key="index" :label="item.label" :value="item.value"> </el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        label="取值范围"
        prop="valueRange"
        v-if="paramForm.constraint.dataType === 'INTEGER' || paramForm.constraint.dataType == 'DECIMAL'"
      >
        <div class="value-range-wrap">
          <el-input-number v-model="paramForm.constraint.min" placeholder="请输入最小值" controls-position="right"></el-input-number>
          <div class="value-range-symbol">~</div>
          <el-input-number v-model="paramForm.constraint.max" placeholder="请输入最大值" controls-position="right"></el-input-number>
        </div>
      </el-form-item>
      <el-form-item label="步长" prop="step" v-if="paramForm.constraint.dataType === 'INTEGER' || paramForm.constraint.dataType == 'DECIMAL'">
        <el-input-number v-model="paramForm.constraint.step" placeholder="请输入步长" controls-position="right"></el-input-number>
      </el-form-item>
      <el-form-item label="单位" prop="unit" v-if="paramForm.constraint.dataType === 'INTEGER' || paramForm.constraint.dataType == 'DECIMAL'">
        <el-input placeholder="请输入单位" v-model="paramForm.constraint.unit"> </el-input>
      </el-form-item>
      <el-form-item label="布尔值" prop="constraint.trueText" v-if="paramForm.constraint.dataType == 'BOOLEAN'">
        <el-input placeholder="对应文本" v-model="paramForm.constraint.trueText">
          <template slot="prepend">true</template>
        </el-input>
      </el-form-item>
      <el-form-item prop="constraint.falseText" v-if="paramForm.constraint.dataType == 'BOOLEAN'">
        <el-input placeholder="对应文本" v-model="paramForm.constraint.falseText">
          <template slot="prepend">false</template>
        </el-input>
      </el-form-item>
      <el-form-item label="最大字符长度" v-if="paramForm.constraint.dataType == 'STRING'">
        <el-input-number v-model="paramForm.constraint.maxLength" :min="0" placeholder="请输入最大长度" controls-position="right"></el-input-number>
      </el-form-item>
      <el-form-item label="正则表达式" v-if="paramForm.constraint.dataType == 'STRING'">
        <el-input v-model="paramForm.constraint.regex" placeholder="请输入正则表达式"></el-input>
      </el-form-item>
      <!-- ENUM、NUMBER_ENUM -->
      <!-- 分别对应文本枚举、数字类型枚举、布尔值请直接使用Boolean类型 -->
      <el-form-item label="文本枚举项" v-if="paramForm.constraint.dataType == 'ENUM'">
        <div v-if="paramForm.constraint.range.length > 0">
          <el-row v-for="(item, index) in paramForm.constraint.range" :key="index" style="margin-bottom: 10px">
            <el-col :span="9">
              <el-input v-model="item.value" placeholder="显示名称" />
            </el-col>
            <el-col :span="11" :offset="1">
              <el-input v-model="item.label" placeholder="枚举值" />
            </el-col>
            <el-col :span="2" :offset="1"><el-button type="text" @click="removeEnumItem(index)">删除</el-button></el-col>
          </el-row>
        </div>
        <div>
          <el-button type="text" @click="addEnumItem()">添加枚举项</el-button>
        </div>
      </el-form-item>
      <el-form-item label="数字枚举项" v-if="paramForm.constraint.dataType == 'NUMBER_ENUM'">
        <div v-if="paramForm.constraint.range.length > 0">
          <el-row v-for="(item, index) in paramForm.constraint.range" :key="index" style="margin-bottom: 10px">
            <el-col :span="9">
              <el-input v-model="item.value" placeholder="显示名称" />
            </el-col>
            <el-col :span="11" :offset="1">
              <el-input-number v-model="item.label" placeholder="枚举值" />
            </el-col>
            <el-col :span="2" :offset="1"><el-button type="text" @click="removeEnumItem(index)">删除</el-button></el-col>
          </el-row>
        </div>
        <div>
          <el-button type="text" @click="addEnumItem()">添加枚举项</el-button>
        </div>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="close">取消</el-button>
      <el-button class="okBtn" @click="changeParams()">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import dlg from '@/mixins/dlg';
  import { PARAM_TYPE_ENUM } from '@/views/iotControl/enum';
  export default {
    mixins: [dlg],
    props: {
      paramConfig: {
        type: Object,
        default: () => ({
          name: '',
          address: '',
          description: '',
          constraint: {
            dataType: 'INTEGER',
            min: undefined,
            max: undefined,
            step: undefined,
            unit: '',
            maxLength: undefined,
            regex: '',
            range: [],
            trueText: '',
            falseText: '',
          },
        }),
      },
    },
    data() {
      return {
        paramForm: {},
        PARAM_TYPE_ENUM,
        rules: {
          name: [
            {
              required: true,
              message: '请输入参数名称',
              trigger: 'blur',
            },
          ],
          address: [
            {
              required: true,
              message: '请输入参数标识',
              trigger: 'blur',
            },
          ],
          'constraint.dataType': [{ required: true, message: '请选择数据类型', trigger: 'change' }],
          'constraint.trueText': [{ required: true, message: '请输入true对应文本', trigger: 'change' }],
          'constraint.falseText': [{ required: true, message: '请输入false对应文本', trigger: 'change' }],
        },
      };
    },
    created() {
      this.paramForm = JSON.parse(JSON.stringify(this.paramConfig));
      if (this.paramForm.constraint) {
        this.paramForm.constraint.range = this.formatRangeArray(this.paramForm.constraint.range, this.paramForm.constraint.dataType);
        ['min', 'max', 'step', 'maxLength'].forEach((key) => {
          this.paramForm.constraint[key] = this.handleInputNumber(this.paramForm.constraint[key]);
        });
      }
    },
    methods: {
      //el-input-number组件,null和空都会解析为0,需要手动设为undefined
      handleInputNumber(val) {
        if (val === null || val === '') {
          return undefined;
        } else {
          return val;
        }
      },
      changeDataType() {
        this.resetConstraint();
      },
      resetConstraint() {
        const defaultConstraint = {
          min: undefined,
          max: undefined,
          step: undefined,
          unit: '',
          maxLength: undefined,
          regex: '',
          range: [],
          trueText: '',
          falseText: '',
        };

        // 保留 dataType，重置其他属性
        Object.keys(defaultConstraint).forEach((key) => {
          if (key in this.paramForm.constraint) {
            this.paramForm.constraint[key] = defaultConstraint[key];
          }
        });
      },
      //range数据格式化
      formatRangeArray(range, dataType) {
        let option = [];
        if (range && typeof range === 'object') {
          let entries = Object.entries(range);
          if (['NUMBER_ENUM'].includes(dataType)) {
            entries.sort((a, b) => a[1] - b[1]);
          } else {
            entries.sort((a, b) => a[1].localeCompare(b[1], 'en'));
          }
          option = entries.map(([key, value]) => ({
            label: value,
            value: key,
          }));
        }
        return option;
      },
      formatRangeObject(option) {
        let range = {};
        if (option && Array.isArray(option)) {
          option.forEach((item) => {
            if (item.label !== undefined && item.value !== undefined) {
              range[item.value] = item.label;
            }
          });
        }
        return range;
      },
      async changeParams() {
        let bValid = await this.$refs.form.validate();
        if (!bValid) {
          return;
        }
        if (this.paramForm.constraint.range) {
          for (let item of this.paramForm.constraint.range) {
            if (!item.value) {
              this.$message.error('枚举的名称不能为空!');
              return;
            }
            // 这里label和value反了
            if (item.label === null || item.label === undefined) {
              this.$message.error(`枚举${item.value}的值不能为空!`);
              return;
            }
          }
          this.paramForm.constraint.range = this.formatRangeObject(this.paramForm.constraint.range);
        }
        this.$emit('changeParams', this.paramForm);
        this.close();
      },
      removeEnumItem(index) {
        this.paramForm.constraint.range.splice(index, 1);
      },
      addEnumItem() {
        this.paramForm.constraint.range.push({
          value: '',
          label: undefined,
        });
      },
    },
    watch: {},
  };
</script>

<style lang="scss" scoped>
  .value-range-wrap {
    display: flex;
    .value-range-symbol {
      margin: 0 5px;
    }
  }
  ::v-deep .el-input-group__prepend {
    width: 70px;
    text-align: center;
    padding: 0;
  }
</style>
