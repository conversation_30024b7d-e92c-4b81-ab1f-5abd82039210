// import 'es6-promise/auto';
// import 'babel-polyfill';
import axios from 'axios';
import $qs from 'qs';
import router from '../router/index';
import { MessageBox, Message } from 'element-ui'

axios.defaults.withCredentials = true;
axios.defaults.headers.post['Content-Type'] = 'application/x-www-form-urlencoded; charset=UTF-8';

//创建axios实例
let service = axios.create({});
export default {
    //get请求，其他类型请求复制粘贴，修改method
    get(url, param) {
        return new Promise((cback, reject) => {
            service({
                method: 'get',
                url,
                params: $qs.stringify(param),
            }).then(res => {
                let res_code = res.status.toString();
                if ("200" === res_code) {
                    cback(res.data);
                } else {
                    reject(res.data);
                }
            }).catch(err => {
                if (401 === err.response.status) {
                    router.push("/login");
                } else {
                    if (!err.response) {
                        console.log('请求错误');
                        //Message是element库的组件，可以去掉
                        Message({
                            showClose: true,
                            message: '请求错误',
                            type: 'error'
                        });
                    } else {
                        reject(err.response);
                        console.log(err.response, '异常2')
                    }
                }
            })

        })
    },
    post(url, param, isShowMsg = false) {
        param = $qs.stringify(param, { indices: false });
        return new Promise((resolve, reject) => {
            service({
                method: 'post',
                url,
                data: param
            }).then(res => {
                let res_code = res.status.toString();
                if ("200" === res_code) {
                    if (200 === res.data.code) {
                        resolve(res.data);
                        if(isShowMsg) {
                            Message({
                                showClose: true,
                                message: '操作成功',
                                type: 'success',
                                duration: 1500
                            });
                        }
                    } else {
                        if(isShowMsg) {
                            Message({
                                showClose: true,
                                message: '操作失败',
                                type: 'error'
                            });
                        }
                        reject(res.data);
                    }
                } else {
                    console.log(res, '请求失败');
                }
            }).catch(err => {
                if (401 === err.response.status || 302 === err.response.status) {
                    router.push("/login");
                } else {
                    if (!err.response) {
                        console.log('请求错误');
                        //Message是element库的组件，可以去掉
                        Message({
                            showClose: true,
                            message: '请求错误',
                            type: 'error'
                        });
                    } else {
                        reject(err.response);
                        console.log(err.response, '异常2')
                    }
                }
            })
        })
    },
    get1(url, param) {
        return new Promise((cback, reject) => {
            service({
                method: 'get',
                url,
                params: $qs.stringify(param)
            }).then(res => {
                let res_code = res.status.toString();
                if ("200" === res_code) {
                    cback(res);
                } else {
                    reject(res.data);
                }
            }).catch(err => {
                if (401 === err.response.status) {
                    router.push("/login");
                } else {
                    if (!err.response) {
                        console.log('请求错误');
                        //Message是element库的组件，可以去掉
                        Message({
                            showClose: true,
                            message: '请求错误',
                            type: 'error'
                        });
                    } else {
                        reject(err.response);
                        console.log(err.response, '异常2')
                    }
                }
            })

        })
    },
    post1(url, param) {
        //param = $qs.stringify(param, { indices: false });
        return new Promise((resolve, reject) => {
            service({
                headers: {
                    "Content-Type": "application/json; charset=UTF-8"
                },
                method: 'post',
                url,
                data: param
            }).then(res => {
                let res_code = res.status.toString();
                if ("200" === res_code) {
                    if (200 === res.data.code) {
                        resolve(res.data);
                    } else {
                        reject(res.data);
                    }
                } else {
                    console.log(res, '请求失败');
                }
            }).catch(err => {
                if (401 === err.response.status) {
                    router.push("/login");
                } else {
                    if (!err.response) {
                        console.log('请求错误');
                        //Message是element库的组件，可以去掉
                        Message({
                            showClose: true,
                            message: '请求错误',
                            type: 'error'
                        });
                    } else {
                        reject(err.response);
                        console.log(err.response, '异常2')
                    }
                }
            })
        })
    },
    upload(url, param) {
        return new Promise((resolve, reject) => {
            service({
                headers: {
                    "Content-Type": "multipart/form-data; charset=UTF-8"
                },
                method: 'post',
                url,
                data: param
            }).then(res => {
                let res_code = res.status.toString();
                if ("200" === res_code) {
                    if (200 === res.data.code) {
                        resolve(res.data);
                    } else {
                        reject(res.data);
                    }
                } else {
                    console.log(res, '请求失败');
                }
            }).catch(err => {
                if (401 === err.response.status) {
                    router.push("/login");
                } else {
                    if (!err.response) {
                        console.log('请求错误');
                        //Message是element库的组件，可以去掉
                        Message({
                            showClose: true,
                            message: '请求错误',
                            type: 'error'
                        });
                    } else {
                        reject(err.response);
                        console.log(err.response, '异常2')
                    }
                }
            })
        })
    },
    delete(url, param) {
        return new Promise((resolve, reject) => {
            service({
                method: 'delete',
                url,
                params: param,
            }).then(res => {
                let res_code = res.status.toString();
                if ("200" === res_code) {
                    resolve(res);
                } else {
                    console.log(res, '异常1');
                }
            }).catch(err => {
                if (!err.response) {
                    console.log('请求错误');
                    //Message是element库的组件，可以去掉
                    Message({
                        showClose: true,
                        message: '请求错误',
                        type: 'error'
                    });
                } else {
                    reject(err.response);
                    console.log(err.response, '异常2')
                }
            })
        })
    },
    get2(url, param) {
        return new Promise((cback, reject) => {
            service({
                method: 'get',
                url,
                params: $qs.stringify(param),
                withCredentials: false
            }).then(res => {
                let res_code = res.status.toString();
                if ("200" === res_code) {
                    cback(res.data);
                } else {
                    reject(res.data);
                }
            }).catch(err => {
                if (401 === err.response.status) {
                    router.push("/login");
                } else {
                    if (!err.response) {
                        console.log('请求错误');
                        //Message是element库的组件，可以去掉
                        Message({
                            showClose: true,
                            message: '请求错误',
                            type: 'error'
                        });
                    } else {
                        reject(err.response);
                        console.log(err.response, '异常2')
                    }
                }
            })

        })
    },
}
