<template>
  <div class="full-wh">
    <div class="content-top">
      <div class="btn-area">
        <el-button type="success" plain :size="size" @click="getInspectRecord">刷新</el-button>
        <el-date-picker
          :size="size"
          v-model="filter.timeRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="timestamp"
          class="ml-10"
          @change="
            {
              page.current = 1;
              getInspectRecord();
            }
          "
        >
        </el-date-picker>
        <el-select
          :size="size"
          v-model="filter.taskStatus"
          placeholder="请选择执行状态"
          clearable
          class="ml-10"
          @change="
            {
              page.current = 1;
              getInspectRecord();
            }
          "
        >
          <el-option v-for="item in INSPECT_RECORD_STATUS_ENUM" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </div>
    </div>
    <div class="content-body">
      <el-table
        :data="inspectRecordList"
        row-key="id"
        highlight-current-row
        @sort-change="doSortChange"
        ref="table"
        :default-sort="{ prop: 'id', order: 'descending' }"
        height="100px"
        v-loading="loading"
        v-adaptive
        class="border-table"
      >
        <el-table-column label="序号" width="55" align="center" fixed="left">
          <template slot-scope="scope">
            {{ scope.$index + (page.current - 1) * pageSize + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="taskTime" label="巡检时间" min-width="120" align="center" show-overflow-tooltip fixed="left">
          <template slot-scope="{ row }">
            {{ formatDate(row.taskTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="name" label="巡检计划名称" min-width="150" show-overflow-tooltip fixed="left"> </el-table-column>
        <el-table-column prop="remark" label="巡检结果描述" min-width="150" show-overflow-tooltip> </el-table-column>
        <el-table-column label="执行类型" min-width="100" align="center">
          <template slot-scope="{ row }">
            {{ row.inspectType === 'auto' ? '自动' : '手动' }}
          </template>
        </el-table-column>
        <el-table-column prop="inspector" label="巡检人" min-width="100" show-overflow-tooltip> </el-table-column>
        <el-table-column prop="taskStatus" label="巡检状态" min-width="120" align="center" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <el-tag :type="INSPECT_RECORD_STATUS_ENUM.find((obj) => obj.value === row.taskStatus).type">
              {{ INSPECT_RECORD_STATUS_ENUM.find((obj) => obj.value === row.taskStatus).label }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="100" show-overflow-tooltip fixed="right">
          <template slot-scope="{ row }">
            <el-button @click="openInspectRecordDetail(row.id)" type="primary" size="mini" plain>查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      <pagination :total="page.total" :page.sync="page.current" :limit.sync="pageSize" @pagination="getInspectRecord" />
    </div>
  </div>
</template>

<script>
  import { tablePageSize as pageSize } from '@/mixins/prefer.js';
  import { getInspectRecord } from '@/api/iotControl';
  import { INSPECT_RECORD_STATUS_ENUM } from '@/views/iotControl/enum';
  export default {
    mixins: [pageSize],
    data() {
      return {
        INSPECT_RECORD_STATUS_ENUM,
        size: 'small',
        filter: {
          inspectId: '',
          taskStatus: '',
          timeRange: [undefined, undefined],
        },
        page: {
          total: 0,
          current: 1,
        },
        inspectRecordList: [],
        sort: {
          direction: 'DESC',
          properties: 'id',
        },
        loading: false,
      };
    },
    created() {
      this.getInspectRecord();
    },
    methods: {
      /**
       * 获取巡检记录列表
       */
      getInspectRecord() {
        this.loading = true;
        let params = {
          page: this.page.current,
          size: this.pageSize,
          properties: this.sort.properties,
          direction: this.sort.direction,
          id: this.filter.inspectId,
          taskStatus: this.filter.taskStatus,
          start: this.filter.timeRange ? this.filter.timeRange[0] : '',
          end: this.filter.timeRange ? this.filter.timeRange[1] : '',
          unpaged: false,
        };
        getInspectRecord(params)
          .then(({ data }) => {
            this.inspectRecordList = data.rows;
            this.page.total = data.total;
          })
          .finally(() => {
            this.loading = false;
          });
      },
      doSortChange(col) {
        this.sort.direction = 'ascending' === col.order ? 'ASC' : 'DESC';
        this.sort.properties = col.prop;
        this.getInspectRecord();
      },
      formatDate(inputDate) {
        const date = new Date(inputDate);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        return `${year}-${month}-${day} ${hours}:${minutes}`;
      },
      openInspectRecordDetail(id) {
        this.$router.push({
          name: 'viewInspectRecordDetail',
          params: { id: id },
        });
      },
    },
  };
</script>

<style lang="scss" scoped></style>
