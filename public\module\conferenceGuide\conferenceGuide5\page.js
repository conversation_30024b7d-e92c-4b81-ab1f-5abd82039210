let page = {
  width: 350,
  height: 180,
  props: [
    {
      type: "el-color-picker",
      title: "文字颜色",
      field: "color",
      value: "#FFFFFF",
      props: {
        predefine: [
          "#FFFFFF",
          "#000000",
          "#C00000",
          "#FF0000",
          "#FFC000",
          "#FFFF00",
          "#92D050",
          "#00B050",
          "#00B0F0",
          "#0070C0",
          "#002060",
          "#7030A0",
        ],
      },
    },
    {
      type: "el-color-picker",
      title: "背景颜色",
      field: "backgroundColor",
      value: "rgba(50,98,183,0)",
      props: {
        showAlpha: true,
        predefine: [
          "#FFFFFF",
          "#000000",
          "#C00000",
          "#FF0000",
          "#FFC000",
          "#FFFF00",
          "#92D050",
          "#00B050",
          "#00B0F0",
          "#0070C0",
          "#002060",
          "#7030A0",
        ],
      },
    },
    {
      type: "el-color-picker",
      title: "进度条背景",
      field: "progressBgColor",
      value: "rgba(255,255,255,0.1)",
      props: {
        showAlpha: true,
        predefine: [
          "#FFFFFF",
          "#000000",
          "#C00000",
          "#FF0000",
          "#FFC000",
          "#FFFF00",
          "#92D050",
          "#00B050",
          "#00B0F0",
          "#0070C0",
          "#002060",
          "#7030A0",
        ],
      },
    },
    {
      type: "el-color-picker",
      title: "进度条颜色",
      field: "progressActiveBgColor",
      value: "rgb(1, 119, 217)",
      props: {
        showAlpha: true,
        predefine: [
          "#FFFFFF",
          "#000000",
          "#C00000",
          "#FF0000",
          "#FFC000",
          "#FFFF00",
          "#92D050",
          "#00B050",
          "#00B0F0",
          "#0070C0",
          "#002060",
          "#7030A0",
        ],
      },
    },
    {
      type: "el-color-picker",
      title: "进度条文字",
      field: "progressTextColor",
      value: "#FFFFFF",
      props: {
        showAlpha: true,
        predefine: [
          "#FFFFFF",
          "#000000",
          "#C00000",
          "#FF0000",
          "#FFC000",
          "#FFFF00",
          "#92D050",
          "#00B050",
          "#00B0F0",
          "#0070C0",
          "#002060",
          "#7030A0",
        ],
      },
    },
    {
      type: "el-switch",
      title: "背景图片",
      field: "bShowBg",
      value: true,
    },
    {
      type: "el-switch",
      title: "头部",
      field: "bShowHeader",
      value: true,
    },
    {
      type: "el-switch",
      title: "时间占用条",
      field: "bShowTimeLine",
      value: true,
    },
    {
      type: "el-switch",
      title: "状态灯",
      field: "statusLight",
      value: true,
      control: [
        {
          value: true,
          rule: [
            "statusLightBrightness",
            "freeLightColor",
            "meetingLightColor",
            "signLightColor",
            "statusLightEffect",
          ],
        },
      ],
    },
    {
      type: "colorSelect",
      title: "空闲灯光",
      field: "freeLightColor",
      value: "3",
    },
    {
      type: "colorSelect",
      title: "会中灯光",
      field: "meetingLightColor",
      value: "5",
    },
    {
      type: "colorSelect",
      title: "签到灯光",
      field: "signLightColor",
      value: "7",
    }
  ],
};
