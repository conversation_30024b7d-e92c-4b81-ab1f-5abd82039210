<template>
  <div class="layout">
    <!-- max-width 210px tree-width 210px -->
    <div :class="['left-wrap', isFold ? 'is-hidden' : '']">
      <slot name="tree">
        <tree
          @handleServiceChange="handleTreeChange"
          :options="treeOptions"
          :bOwn="treebOwn"
          :bCompanyDept="bCompanyDept"
          :init="treeOptions.length > 1 ? init : treeOptions[0]"
        ></tree>
      </slot>
    </div>
    <div :class="['middle-wrap', isFold ? 'is-hidden' : '']">
      <div :class="['fold-btn', isFold ? 'is-fold' : 'is-unfold']" @click="isFold = !isFold"></div>
    </div>
    <div class="right-wrap">
      <slot></slot>
    </div>
  </div>
</template>

<script>
  import tree from './components/tree.vue';
  export default {
    props: {
      treeOptions: {
        type: Array,
        default: () => ['server'],
      },
      init: {
        type: String,
        default: 'server',
      },
      treebOwn: {
        type: Boolean,
        default: true,
      },
      bCompanyDept: {
        type: Boolean,
        default: false,
      },
    },
    components: {
      tree,
    },
    data() {
      return {
        // 是否折叠左侧区域
        isFold: false,
      };
    },
    methods: {
      handleTreeChange(data) {
        this.$emit('handleTreeChange', data);
      },
    },
  };
</script>
<style lang="scss" scoped>
  .left-wrap {
    transition: all 0.5s;
    max-width: 210px;
    overflow: hidden;

    &.is-hidden {
      max-width: 0;
      opacity: 0;
    }
  }
  .layout {
    display: flex;
    width: 100%;
    height: calc(100% - 10px);
    margin-top: 10px;
    border: 1px solid rgba(0, 0, 0, 0.06);

    .middle-wrap {
      width: 12px;
      background: #f7f7f7;
      position: relative;
    }

    .fold-btn {
      width: 10px;
      height: 35px;
      background: #1f7bc1;
      position: absolute;
      top: calc(50% - 17.5px);
      right: 0.5px;
      border-radius: 3px;
      cursor: pointer;

      &.is-fold {
        &::after {
          content: '';
          width: 10px;
          height: 35px;
          background: url('../../assets/more_unfold.png') center no-repeat;
          display: block;
          border-radius: 3px;
          background-size: 19px;
          background-position-x: -5px;
          transform: rotate(180deg);
        }
      }

      &.is-unfold {
        &::after {
          content: '';
          width: 10px;
          height: 35px;
          background: url('../../assets/more_unfold.png') center no-repeat;
          display: block;
          border-radius: 3px;
          background-size: 19px;
          background-position-x: -5px;
        }
      }
    }

    .right-wrap {
      padding-left: 10px;
      flex: 1;
      overflow: auto;
      border-left: 1px solid rgba(0, 0, 0, 0.06);
      transition: all 1s;
    }
  }
</style>
