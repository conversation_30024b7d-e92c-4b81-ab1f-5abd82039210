// 打字机队列
export class Typewriter {
  queue = [];
  statusQueue = [];
  consuming = false;
  timer = null;
  doneTimer = null;
  onConsume = () => { };
  callBack = () => { };
  constructor(onConsume, callBack) {
    this.onConsume = onConsume; // 初始化消费字符的回调
    this.callBack = callBack; // 结束的回调
  }

  // 输出速度动态控制
  adjustSpeed() {
    const TIME_ELAPSED = 2000
    const MAX_SPEED = 200
    const speed = TIME_ELAPSED / this.queue.length
    if (speed > MAX_SPEED) {
      return MAX_SPEED
    }
    else {
      return speed
    }
  }

  // 清空已有的队列
  clearQueue(){
    this.queue = []
    this.statusQueue = []
  }

  // 添加字符串到队列
  add(str,status) {
    if (!str)
      return
    str = str.replaceAll('\\n', '\n')
    
    for (const char of str) {
      this.queue.push(char)
      this.statusQueue.push(status)
    }
  }

  // 消费
  consume() {
    if (this.queue.length > 0) {
      const str = this.queue.shift()
      const status = this.statusQueue.shift()
      str && this.onConsume(str, status)
    }
  }

  // 消费下一个
  next() {
    this.consume()
    // 根据队列中字符的数量来设置消耗每一帧的速度，用定时器消耗
    this.timer = setTimeout(() => {
      this.consume()
      if (this.consuming) {
        this.next()
      }
    }, this.adjustSpeed())
  }

  // 开始消费队列
  start() {
    console.log('start')
    // 清空已有的队列
    this.clearQueue()
    this.consuming = true
    this.next()
  }

  // 取消消费队列
  cancel() {
    // 清空已有的队列
    this.clearQueue()
    // 结束
    this.done()
  }

  // 自动等打印完再结束消费队列，且有传回调函数的话执行回调函数
  done() {
    if (this.queue.length === 0) {
      clearTimeout(this.doneTimer)
      clearTimeout(this.timer)
      this.consuming = false
      this.callBack?.()
    }else {
      this.doneTimer = setTimeout(() => {
        this.done()
      }, 1000)
    }
  }
}
