const debounce = (func, time = 0.6 * 1000, ctx) => {
  let timer;
  return (...params) => {
    clearTimeout(timer);
    timer = setTimeout(() => {
      func.apply(ctx, params);
    }, time);
  };
};

export default {
  bind: (el, { value }, vnode) => {
    const [target, event = "input", time] = value;
    const debounced = debounce(target, time, vnode);
    el.addEventListener(event, debounced);
    el._debounced = debounced;
    el._debouncedEvent = event;
  },
  destroy(el) {
    el.removeEventListener(el._debouncedEvent, el._debounced);
  },
};
