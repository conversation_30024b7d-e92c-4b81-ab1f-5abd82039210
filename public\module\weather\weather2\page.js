let page = {
  width: 654,
  height: 193,
  props: [
    {
      type: "cascader",
      title: "所在区域",
      field: "address",
      value: ['*'],
      props: {
        options: province_city_area,
        //选择后展示的函数，用于自定义显示格式
        renderFormat: label => label.join(" / "),
        //是否支持清除
        clearable: true,
        placeholder: "请选择",
        //是否支持搜索
        filterable: true
      }
    },
    {
      type: "el-color-picker",
      title: "文字颜色",
      field: "color",
      value: "#ffffff",
      props: {
        predefine: [
          "#FFFFFF",
          "#000000",
          "#C00000",
          "#FF0000",
          "#FFC000",
          "#FFFF00",
          "#92D050",
          "#00B050",
          "#00B0F0",
          "#0070C0",
          "#002060",
          "#7030A0",
        ]
      }
    },
    {
      type: "el-color-picker",
      title: "背景颜色",
      field: "backgroundColor",
      value: "rgb(20,136,204)",
      props: {
        showAlpha: true,
        predefine: [
          "#FFFFFF",
          "#000000",
          "#C00000",
          "#FF0000",
          "#FFC000",
          "#FFFF00",
          "#92D050",
          "#00B050",
          "#00B0F0",
          "#0070C0",
          "#002060",
          "#7030A0",
        ]
      }
    }
  ]
};
