import store from '@/store';

/**
 * Action 权限指令
 * 指令用法：
 *  - 在需要控制 action 级别权限的组件上使用 v-action:[method] , 如下：
 *    <i-button v-action:add >添加用户</a-button>
 *    <a-button v-action:delete>删除用户</a-button>
 *    <a v-action:edit @click="edit(record)">修改</a>
 *
 *  - 当前用户没有权限时，组件上使用了该指令则会被隐藏
 *  - 当后台权限跟 pro 提供的模式不同时，只需要针对这里的权限过滤进行修改即可
 *
 *  @see https://github.com/vueComponent/ant-design-vue-pro/pull/53
 */
export default {
  inserted: function (el, binding, vnode) {
    const actionName = binding.arg;
    const [permission, action] = actionName.split('|');
    const permissionList = store.getters && store.getters.permissions;
    if (!hasPermission(permissionList, permission, action)) {
      (el.parentNode && el.parentNode.removeChild(el)) || (el.style.display = 'none');
    }
  },
};

export function hasPermission(data = (store.getters && store.getters.permissions) || [], permission, action) {
  let res = false;

  const deepSearch = (funcData = data, funcPermission = permission, funcAction = action) => {
    for (let item of funcData) {
      if (item.permissionKey === funcPermission) {
        if (item.buttons.some((btn) => btn.permissionKey === funcAction) || funcAction === '' || funcAction === undefined) {
          res = true;
          return;
        }
      }

      if (item.children) {
        deepSearch(item.children, funcPermission, funcAction);
      }
    }
  };

  deepSearch(data, permission, action);

  return res;
}

export function hasPermissionWithoutStore(permission, action) {
  const data = (store.getters && store.getters.permissions) || [];
  let res = false;
  const deepSearch = (funcData = data, funcPermission = permission, funcAction = action) => {
    for (let item of funcData) {
      if (item.permissionKey === funcPermission) {
        if (item.buttons.some((btn) => btn.permissionKey === funcAction) || funcAction === '' || funcAction === undefined) {
          res = true;
          return;
        }
      }

      if (item.children) {
        deepSearch(item.children, funcPermission, funcAction);
      }
    }
  };

  deepSearch(data, permission, action);

  return res;
}
