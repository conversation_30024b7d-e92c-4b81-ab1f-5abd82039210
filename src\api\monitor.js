import request from "@/utils/request";

/**
 * 分页查询监控系统
 * @param page 当前页号
 * @param size 页内行数
 * @param sortBy 排序依据
 * @param directionBy 升序/降序 ASC/DESC
 * @param searchBy string,搜索关键字
 * @param statusBy boolean,状态筛选
 */
export function getServerList(data) {
  return request({
    url: `/weapp/ops/server`,
    method: 'get',
    params: data,
  });
}

/**
 * 删除监控系统
 * @param ids Array,频道id数组
 */
export function deleteServer(ids) {
  return request({
    url: `/stream/srs/channel`,
    method: 'delete',
    data: ids,
    headers: {
      "Content-Type": "application/json;"
    }
  });
}

/**
 * 单个/批量更新监控系统
 * @param id 系统ID
 * @param name 平台名称
 * @param departmentId 所属机构ID
 */
export function saveServer(data) {
  return request({
    url: `/weapp/ops/server`,
    method: 'post',
    data,
    headers: {
      "Content-Type": "application/json"
    }
  });
}

/**
 * 查询服务详情
 * @param opsServerId 服务id
 * @returns
 */
export function getServerDetail(opsServerId) {
  return request({
    url: `/weapp/ops/server/${opsServerId}/detail`,
    method: 'get',
  });
}

/**
 * 查询时序数据
 * @param opsServerId 服务id
 * @param opsTimeSeriesDataType 时序数据类型 CPU:CPU MEMORY:内存 DISK:磁盘 MEDIA:媒体
 * @param params Object {from:开始时间戳,to：结束时间戳}
 * @returns
 */
export function getServerTimeSeries(opsServerId, opsTimeSeriesDataType, params) {
  return request({
    url: `/weapp/ops/server/${opsServerId}/${opsTimeSeriesDataType}`,
    method: 'get',
    params
  });
}

/**
 * 查询线程数
 * @param opsServerId 服务id
 * @param params Object {from:开始时间戳,to：结束时间戳}
 * @returns
 */
export function getServerThread(opsServerId, params) {
  return request({
    url: `/weapp/ops/server/${opsServerId}/thread`,
    method: 'get',
    params
  });
}

/**
 * 查询进程数
 * @param opsServerId 服务id
 * @returns
 */
export function getServerApplication(opsServerId) {
  return request({
    url: `/weapp/ops/server/${opsServerId}/application`,
    method: 'get'
  });
}

/**
 * 查询告警详情
 * @param opsServerId 服务id
 * @returns
 */
export function getServerAlarm(opsServerId, params, data = {}) {
  console.log(data)
  return request({
    url: `/weapp/ops/server/${opsServerId}/alarm`,
    method: 'post',
    params,
    data,
    headers: {
      "Content-Type": "application/json"
    }
  });
}

/**
 * 查询系统摘要信息
 * @param opsServerId 服务id
 * @returns
 */
export function getServerSummary(opsServerId) {
  return request({
    url: `/weapp/ops/server/${opsServerId}/summary`,
    method: 'get',
  });
}

/**
 * 查询时序数据-网络接收
 * @param opsServerId 服务id
 * @param params Object {from:开始时间戳,to：结束时间戳}
 * @returns
 */
export function getServerNetworkRx(opsServerId, params) {
  return request({
    url: `/weapp/ops/server/${opsServerId}/networkRx`,
    method: 'get',
    params
  });
}

/**
 * 查询时序数据-网络发送
 * @param opsServerId 服务id
 * @param params Object {from:开始时间戳,to：结束时间戳}
 * @returns
 */
export function getServerNetworkTx(opsServerId, params) {
  return request({
    url: `/weapp/ops/server/${opsServerId}/networkTx`,
    method: 'get',
    params
  });
}

/**
 * 分页查询终端
 * @param page 当前页号
 * @param size 页内行数
 * @param sortBy 排序依据
 * @param directionBy 升序/降序 ASC/DESC
 * @param searchBy string,搜索关键字
 * @param statusBy boolean,状态筛选
 * @param approvedBy boolean,审核筛选
 */
export function getEdgeList(data) {
  return request({
    url: `/weapp/ops/edge`,
    method: 'get',
    params: data,
  });
}

/**
 * 编辑/审核终端
 * @param deviceId 设备ID
 * @param name 名称
 * @param departmentId 所属机构ID
 */
export function saveEdge(data) {
  return request({
    url: `/weapp/ops/edge`,
    method: 'post',
    data,
    headers: {
      "Content-Type": "application/json"
    }
  });
}

/**
 * 查询终端详情
 * @param deviceId 设备id
 * @returns
 */
export function getEdgeDetail(deviceId) {
  return request({
    url: `/weapp/ops/edge/${deviceId}/detail`,
    method: 'get',
  });
}

/**
 * 查询终端截图
 * @param deviceId 设备id
 * @param type 截图类型 SCREENSHOT_TYPE_OPTIONS
 * @returns
 */
export function getEdgeScreenshot(deviceId,type) {
  return request({
    url: `/weapp/ops/edge/${deviceId}/screenshot`,
    method: 'get',
    params:{type}
  });
}

/**
 * 查询终端时序数据
 * @param deviceId 设备id
 * @param params Object {from:开始时间戳,to：结束时间戳,type}
 * @returns
 */
export function getEdgeTimeSeries(deviceId, params) {
  return request({
    url: `/weapp/ops/edge/${deviceId}/timeSeries`,
    method: 'get',
    params
  });
}

/**
 * 查询终端日志
 * @param deviceId 设备id
 * @param params-page 当前页号
 * @param params-size 页内行数
 * @returns
 */
export function getEdgeLog(deviceId,params) {
  return request({
    url: `/weapp/ops/edge/${deviceId}/log`,
    method: 'get',
    params
  });
}

/**
 * 删除终端日志
 * @param deviceId 设备id
 * @param ids 日志id列表
 * @returns
 */
export function deleteEdgeLog(deviceId, ids) {
  return request({
    url: `/weapp/ops/edge/${deviceId}/log`,
    method: 'post',
    data:ids,
    headers: {
      "Content-Type": "application/json"
    }
  });
}

/**
 * 开启终端日志收集
 * @param deviceId 设备id
 * @param type TODAY :当天日志，LAST_7_DAYS :近7天日志
 * @returns
 */
export function collectionEdgeLog(deviceId, type) {
  return request({
    url: `/weapp/ops/edge/${deviceId}/collection`,
    method: 'post',
    params:{type}
  });
}

/**
 * 查询终端日志收集状态
 * @param deviceId 设备id
 * @returns
 */
export function getEdgeLogCollection(deviceId) {
  return request({
    url: `/weapp/ops/edge/${deviceId}/collection`,
    method: 'get',
  });
}

/**
 * 查询呼叫记录
 * @param params-page 当前页号
 * @param params-size 页内行数
 * @param params-searchBy string,搜索关键字
 * @param params-okBy boolean,状态筛选
 * @returns
 */
export function getCallList(params) {
  return request({
    url: `/weapp/ops/callDetail`,
    method: 'get',
    params
  });
}

/**
 * 正在召开的会议（按平台进行汇总）
 * @returns
 */
export function getDashboardConf() {
  return request({
    url: `/weapp/ops/dashboard/conferenceSummary`,
    method: 'get',
  });
}

/**
 * 分页查询告警配置
 * @param params-page 当前页号
 * @param params-size 页内行数
 * @param params-searchBy string,搜索关键字
 * @param params-sourceBy 来源
 * @param params-triggerTypeBy 触发方式,EVENT :设备事件上报,THRESHOLD :测值与阈值比较
 * @returns
 */
export function getAlarmConfig(params) {
  return request({
    url: `/weapp/ops/alarmConfig`,
    method: 'get',
    params
  });
}

/**
 * 更新告警配置
 * @param data-id
 * @param data-code 告警代码
 * @param params-name string,告警名称
 * @param params-severity 告警级别 
 * @param params-reason 告警原因
 * @param params-maintenance 处理建议
 * @returns
 */
export function updateAlarmConfig(data) {
  return request({
    url: `/weapp/ops/alarmConfig`,
    method: 'post',
    data,
    headers: {
      "Content-Type": "application/json"
    }
  });
}

/**
 * 告警来源
 * @returns
 */
export function getAlarmSource() {
  return request({
    url: `/weapp/ops/alarmSource`,
    method: 'get',
  });
}

/**
 * 告警确认
 * @param opsAlarmId 告警ID
 * @param data-verified 是否核实确认
 * @returns
 */
export function verifyAlarm(opsAlarmId,data) {
  return request({
    url: `/weapp/ops/alarm/${opsAlarmId}`,
    method: 'post',
    data,
    headers: {
      "Content-Type": "application/json"
    }
  });
}

/**
 * 告警批量删除
 * @param ids 告警ID集合
 * @returns
 */
export function deleteAlarm(ids) {
  return request({
    url: `/weapp/ops/alarm`,
    method: 'post',
    data:ids,
    headers: {
      "Content-Type": "application/json"
    }
  });
}

/**
 * 会议告警
 * @param conferenceId 会议id
 * @param params-page
 * @param params-size
 * @param params-searchBy 搜索
 * @param params-verifiedBy 是否确认
 * @param params-recoveryBy 是否恢复
 * @param params-severityBy 告警级别
 * @param params-from 开始时间，13位时间戳
 * @param params-to 结束时间，13位时间戳
 * @returns
 */
export function getConfAlarm(conferenceId,params) {
  return request({
    url: `/weapp/ops/conference/${conferenceId}/alarm`,
    method: 'get',
    params
  });
}









