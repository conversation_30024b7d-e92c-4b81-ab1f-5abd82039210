<template>
  <div class="material-storage-wrap">
    <div class="title-wrap"> 素材分布 </div>
    <div class="material-storage-chart-wrap">
      <div
        class="chart-wrap"
        ref="charts"
        v-loading="loading"
        element-loading-spinner="el-icon-loading"
        element-loading-background="transparent"
      ></div>
    </div>
  </div>
</template>

<script>
  import { getMaterialClassifiedNum } from '@/api/dashboard';
  const COLOR = ['#117BF5', '#88CDFE', '#00C8FC', '#3FFCC8', '#71B0FC', '#8072FC', '#FF6A49', 'D94883'];

  export default {
    // 素材存储分布
    name: 'MaterialStorage',
    data() {
      return {
        chart: null,
        loading: false,
      };
    },
    methods: {
      resize() {
        this.$nextTick(() => {
          this.chart && this.chart.resize();
        });
      },
      /**
       * 绘制南丁格尔图
       */
      initChart(data) {
        let option = {
          tooltip: {
            trigger: 'item',
            confine: true,
          },
          series: [
            {
              name: '素材数量',
              type: 'pie',
              data: data,
              label: {
                normal: {
                  textStyle: {
                    color: '#fff',
                  },
                },
              },
              itemStyle: {
                normal: {
                  label: {
                    show: true,
                    formatter: '{b}({d}%)',
                  },
                  labelLine: { show: true },
                },
              },
            },
          ],
        };
        this.updateChart(option);
      },
      updateChart(option) {
        if (!this.chart) {
          this.chart = this.$echarts.init(this.$refs.charts);
        }
        this.chart.setOption(option, true);
      },
      getData() {
        getMaterialClassifiedNum().then((res) => {
          let data = [];
          res.data.forEach((item, index) => {
            data.push({
              name: item.materialType,
              value: item.count || 0,
              itemStyle: {
                color: COLOR[index],
              },
              size: item.size,
            });
          });
          this.$nextTick(() => {
            this.initChart(data);
          });
        });
      },
      refresh() {
        this.getData();
      },
    },
    created() {
      this.getData();
    },
    computed: {
      isEmpty() {
        if (this.data.length == 0 || (this.data.length == 1 && this.data[0].size == '无限制')) {
          return true;
        } else {
          return false;
        }
      },
    },
  };
</script>

<style lang="scss" scoped>
  .material-storage-wrap {
    background: url(../img/box-right-bg.png) no-repeat;
    box-sizing: border-box;
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    .title-wrap {
      font-family: 'YouSheBiaoTiHei';
      font-size: 18px;
      padding: 13px 22px 12px 0px;
      text-align: right;
    }

    .material-storage-chart-wrap {
      height: calc(100% - 40px);
      width: 100%;
      .chart-wrap {
        width: 100%;
        height: 100%;
      }
    }
  }
</style>
