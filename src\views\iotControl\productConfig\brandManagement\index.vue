<template>
  <div class="full-wh">
    <div class="content-top">
      <div class="btn-area">
        <el-button type="primary" :size="size" icon="el-icon-plus" class="mr-10" @click.native="addBrand('新增品牌')" v-if="bRoot">新增</el-button>
        <el-input
          clearable
          :size="size"
          class="mr-10"
          style="width: 160px"
          v-debounce="[
            (e) => {
              getList(e);
            },
          ]"
          @clear="
            () => {
              getList(e);
            }
          "
          v-model="filter.name"
          placeholder="品牌名称/英文名称"
        ></el-input>
      </div>
    </div>
    <div class="content-body">
      <div class="content-detail">
        <div class="content-detail-border">
          <div class="table-position-pagination">
            <el-table :data="dataList" highlight-current-row ref="dataTable" height="100px" v-loading="loading" v-adaptive>
              <el-table-column label="序号" width="70">
                <template slot-scope="scope">
                  {{ scope.$index + 1 }}
                </template>
              </el-table-column>
              <el-table-column prop="name" label="品牌名称" min-width="150" show-overflow-tooltip> </el-table-column>
              <el-table-column prop="enName" label="品牌英文名称" min-width="150" show-overflow-tooltip> </el-table-column>
              <el-table-column align="center" prop="logo" label="品牌图标" min-width="180">
                <template slot-scope="{ row }">
                  <el-image v-if="row.logo" :src="row.logo" :preview-src-list="[row.logo]" style="width: 40px; height: 40px" fit="contain">
                  </el-image>
                  <span v-else>--</span>
                </template>
              </el-table-column>
              <el-table-column prop="remark" label="品牌描述" min-width="260" show-overflow-tooltip> </el-table-column>
              <el-table-column label="操作项" align="center" :show-overflow-tooltip="true" width="260" fixed="right" v-if="bRoot">
                <template slot-scope="{ row }">
                  <el-button size="mini" class="edit-btn" icon="el-icon-edit" @click="editBrand('编辑品牌', row)"> 编辑 </el-button>
                  <el-button size="mini" class="delete-btn" icon="el-icon-delete" @click="handleDeleteBrand(row)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>
    <!-- 编辑品牌 -->
    <el-dialog :title="editBrandDlgTitle" :visible.sync="bEditBrandVisible" width="650px" :close-on-click-modal="false" @close="closeEditBrandDlg">
      <div class="margin-top10" style="width: 500px">
        <el-form label-position="left" label-width="120px" :model="form" :rules="rules" ref="form">
          <el-form-item label="品牌名称" prop="name">
            <el-input type="text" v-model="form.name" size="small"></el-input>
          </el-form-item>
          <el-form-item label="品牌英文名称" prop="enName">
            <el-input type="text" v-model="form.enName" size="small"></el-input>
          </el-form-item>
          <el-form-item label="品牌图标" prop="logo">
            <el-upload action="" drag :show-file-list="false" :auto-upload="false" :multiple="false" :on-change="handleUploadPic">
              <div class="pic-wrap" v-if="form.logo">
                <img :src="form.logo" class="pic" />
              </div>
              <i class="el-icon-upload" v-else></i>
              <div class="el-upload__text">点击上传</div>
              <div class="el-upload__tip"> 仅支持jpg/png/bmp文件,图片大小200KB内 </div>
            </el-upload>
          </el-form-item>

          <el-form-item label="品牌描述" prop="remark">
            <el-input type="text" v-model="form.remark" size="small"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeEditBrandDlg">取消</el-button>
        <el-button class="okBtn" @click="saveBrand">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import { getBrandList, updateBrand, deleteBrand } from '@/api/iotControl';
  import { isRootCompany } from '@/api/user';

  export default {
    components: {},
    data() {
      return {
        size: 'small',
        filter: {
          name: '',
        },
        dataList: [],
        loading: false,
        bEditBrandVisible: false,
        editBrandDlgTitle: '编辑产品分类',
        form: {
          name: '',
          enName: '',
          remark: '',
          logo: '',
          id: '',
        },
        rules: {
          name: [{ required: true, message: '请输入品牌名称', trigger: 'blur' }],
        },
        // 是否根机构
        bRoot: false,
      };
    },
    created() {
      this.getList();
      this.isRootCompany().then((res) => (this.bRoot = res));
    },
    watch: {},
    methods: {
      isRootCompany() {
        return new Promise((resolve, reject) => {
          isRootCompany()
            .then((res) => resolve(res.data))
            .catch((err) => reject(err));
        });
      },
      getList() {
        this.loading = true;
        getBrandList({ name: this.filter.name })
          .then(({ data }) => {
            this.dataList = data;
          })
          .finally(() => {
            this.loading = false;
          });
      },
      handleUploadPic(file, fileList) {
        if (!this.checkFile(file)) {
          this.$message.warning('图片仅支持jpg、png或bmp格式');
          return;
        }
        const bLt200KB = file.size / 1024 < 200;
        if (!bLt200KB) {
          this.$message.warning('图片大小超过200kB');
          return;
        }

        const reader = new FileReader();
        // 设置读取完成后的回调函数
        reader.onload = (e) => {
          // 获取base64编码结果并赋值给表单和预览URL
          this.form.logo = e.target.result;
        };
        // 开始读取文件，转换为base64格式
        reader.readAsDataURL(file.raw);
      },
      checkFile(file) {
        const type = file.raw.type;
        const fileTypeEnum = ['image/png', 'image/jpeg', 'image/bmp'];
        const bCorrectType = fileTypeEnum.find((item) => item === type);
        if (!bCorrectType) {
          return false;
        }
        return true;
      },
      addBrand(title) {
        this.editBrandDlgTitle = title;
        this.bEditBrandVisible = true;
      },
      editBrand(title, row) {
        this.editBrandDlgTitle = title;
        this.bEditBrandVisible = true;
        this.form.id = row.id;
        this.form.name = row.name;
        this.form.enName = row.enName;
        this.form.remark = row.remark;
        this.form.logo = row.logo;
      },
      closeEditBrandDlg() {
        this.bEditBrandVisible = false;
        this.form.id = '';
        this.form.name = '';
        this.form.enName = '';
        this.form.remark = '';
        this.form.logo = '';
      },
      async saveBrand() {
        let bValid = await this.$refs.form.validate();
        if (!bValid) {
          return;
        }
        updateBrand(this.form)
          .then(() => {
            this.$message.success('编辑成功！');
            this.getList();
            this.closeEditBrandDlg();
          })
          .finally(() => {});
      },

      handleDeleteBrand(row) {
        deleteBrand([row.id]).then((res) => {
          this.$message.success('删除成功！');
          this.getList();
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  .app-main > div {
    height: auto;
  }
  .table-wrap {
    margin-bottom: 20px;
  }
  .pic-wrap {
    margin: 10px auto;
    position: relative;
    width: 176px;
    height: 99px;
    text-align: center;

    .pic {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }
  .el-upload__text,
  .el-upload__tip {
    text-align: center;
    height: 15px;
    line-height: 15px;
  }
  ::v-deep .el-upload-dragger {
    width: 300px;
    height: 170px;
  }
</style>
