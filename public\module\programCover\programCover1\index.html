<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
    <link rel="stylesheet" href="40e93b11cbd4174c60b22cba0d630300.css" />
    <style>
      ul {
        margin: 0;
        padding: 0;
      }
      ul li {
        list-style: none;
      }
      body,
      html {
        height: 100%;
        margin: 0;
        user-select: none;
      }
      .wrap {
        width: 100%;
        height: 100%;
        position: relative;
        background-color: white;
        box-sizing: border-box;
      }

      .content-wrap {
        width: 100vw;
        height: 100vh;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        padding: 2vmin 0 2vmin 2vmin;
      }

      .play-list-info {
        display: inline-flex;
        align-items: center;
        border-left: 1px solid #d9ecff;
      }

      .play-list-info .title {
        color: #409eff;
        background: #ecf5ff;
        line-height: 1.5;
        padding: 1vmin 2vmin;
        border-right: 1px solid #d9ecff;
        border-top: 1px solid #d9ecff;
        border-bottom: 1px solid #d9ecff;
        box-sizing: border-box;
      }

      .play-list-info .value {
        color: #000;
        padding: 1vmin 2vmin;
        border-right: 1px solid #d9ecff;
        border-top: 1px solid #d9ecff;
        border-bottom: 1px solid #d9ecff;
        max-width: 200px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        box-sizing: border-box;
        line-height: 1.5;
      }

      /* .content {
        flex: 1;
        overflow: hidden;
      } */

      .program-list {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(30vmin, 1fr));
        padding: 2vmin 0 0 0;
      }

      .program-list li {
        margin: 0 2vmin 2vmin 0;
        cursor: pointer;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        border-radius: 4px;
        border: 1px solid #e6ebf5;
      }

      .program-list li .img-wrap {
        width: 100%;
        height: 21vmin;
        position: relative;
      }

      .program-list li .img-wrap img {
        width: 100%;
        height: 100%;
      }

      .header-mask {
        position: absolute;
        top: 0;
        left: 0;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        background-color: rgba(0, 0, 0, 0.3);
        width: 100%;
        height: 100%;
        border-radius: 4px 4px 0 0;
        transition: top 0.28s ease, opacity 0.28s ease, height 0.28s ease;
      }
      .header-mask .details-btn {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        display: inline-block;
        width: 40%;
        height: 44px;
        font-size: 18px;
        line-height: 44px;
        border-radius: 22px;
        border: 1px solid #fff;
        color: #fff;
        cursor: pointer;
        text-align: center;
        transition: all 0.2s;
      }

      .program-list li .img-wrap:hover .header-mask {
        opacity: 1;
      }

      .border-T {
        border-top: 1px solid #e6ebed;
      }

      .middle-wrap {
        height: 36px;
        line-height: 36px;
        padding: 0 8px;
        display: flex;
        justify-content: space-between;
      }
      .middle-wrap .title {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        flex: 1;
      }
      .middle-wrap .creator {
        color: #999;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        flex-grow: 0;
        max-width: 80px;
        margin-left: 5px;
        font-size: 14px;
      }

      .empty-wrap {
        width: 100vw;
        height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 8vmin;
        color: #409eff;
      }

      [v-cloak] {
        display: none;
      }
    </style>
    <!-- vue -->
    <script src="174365807c06490c848d7b1d45fdc348.js"></script>
    <!-- jquery.js -->
    <script src="13c0a5055cca7b2463b2f73701960b9e.js"></script>
    <script src="page.js"></script>
  </head>

  <body>
    <div id="app" class="wrap" v-cloak>
      <div class="wrap">
        <div class="content-wrap" v-if="Object.keys(playlistInfo).length">
          <div class="play-list-info">
            <div class="title">节目单名称</div>
            <div class="value">{{playlistInfo.playlistName}}</div>
            <div class="title">播放类型</div>
            <div class="value">{{playlistInfo.playType}}</div>
            <div class="title">结束时间</div>
            <div class="value">{{playlistInfo.endTime}}</div>
          </div>

          <div class="content" id="scrollWrap">
            <ul class="program-list">
              <li
                v-for="item in playlistInfo.programs"
                :key="item.pid"
                @click="clickProgram(item)"
              >
                <div class="img-wrap">
                  <!-- img需要用src终端才能下载到 -->
                  <img src='./fe5282a22747f93fe7ee455e04d2a133.png' alt="" v-show="!item.coverImagePath">
                  <img :src="item.coverImagePath" alt="加载失败" v-show="item.coverImagePath"/>
                  <div class="header-mask">
                    <div class="details-btn">播放</div>
                  </div>
                </div>

                <div class="middle-wrap border-T">
                  <span class="title">{{ item.programName }}</span>
                  <span class="creator">{{ item.playTime }}</span>
                </div>
              </li>
            </ul>
          </div>
        </div>
        <div v-else class="empty-wrap">当前无节目单信息</div>
      </div>
    </div>
  </body>

  <script>
    window.$page = page;
    const PLAY_TYPE = [
      {
        label: "空闲",
        value: 30,
      },
      {
        label: "默认播放",
        value: 50,
      },
      {
        label: "播放列表",
        value: 60,
      },
      {
        label: "互动节目",
        value: 80,
      },
      {
        label: "立即播放",
        value: 90,
      },
      {
        label: "U盘下的默认播放",
        value: 150,
      },
      {
        label: "U盘下的播放列表播放",
        value: 160,
      },
      {
        label: "U盘下的互动节目",
        value: 180,
      },
      {
        label: "U盘下的立即播放",
        value: 190,
      },
      {
        label: "休眠中",
        value: 200,
      },
      {
        label: "U盘下的播放列表播放",
        value: 160,
      },
      {
        label: "U盘下的播放列表播放",
        value: 160,
      },
    ];

    // 秒转变为时间  5-> 00:00:05
    function formatSeconds(iSeconds) {
      let theTime = parseInt(iSeconds, 10); // 需要转换的时间秒
      let theTime1 = 0; // 分
      let theTime2 = 0; // 小时
      if (theTime >= 60) {
        theTime1 = parseInt(theTime / 60, 10);
        theTime = parseInt(theTime % 60, 10);
        if (theTime1 >= 60) {
          theTime2 = parseInt(theTime1 / 60, 10);
          theTime1 = parseInt(theTime1 % 60, 10);
        }
      }
      let result = "";
      if (theTime > 0) {
        result = theTime >= 10 ? "" + theTime : "0" + theTime;
      } else {
        result = "00";
      }
      if (theTime1 > 0) {
        theTime1 = theTime1 >= 10 ? "" + theTime1 : "0" + theTime1;
        result = "" + theTime1 + ":" + result;
      } else {
        result = "00:" + result;
      }
      if (theTime2 > 0) {
        theTime2 = theTime2 > 10 ? "" + theTime2 : "0" + theTime2;
        result = "" + theTime2 + ":" + result;
      } else {
        result = "00:" + result;
      }
      return result;
    }
    //时间戳格式化
    Date.prototype.Format = function (fmt) {
      let o = {
        "M+": this.getMonth() + 1,
        "d+": this.getDate(),
        "h+": this.getHours(),
        "m+": this.getMinutes(),
        "s+": this.getSeconds(),
        "q+": Math.floor((this.getMonth() + 3) / 3),
        S: this.getMilliseconds(),
      };
      if (/(y+)/.test(fmt))
        fmt = fmt.replace(
          RegExp.$1,
          (this.getFullYear() + "").substr(4 - RegExp.$1.length)
        );
      for (let k in o)
        if (new RegExp("(" + k + ")").test(fmt))
          fmt = fmt.replace(
            RegExp.$1,
            RegExp.$1.length === 1
              ? o[k]
              : ("00" + o[k]).substr(("" + o[k]).length)
          );
      return fmt;
    };

    const app = new Vue({
      el: "#app",
      data: {
        props: page.props,
        playlistInfo: {
          playType: "互动节目",
          endTime: "永久",
          ppid: 123,
          playlistName: "3244",
          programs: [
            {
              pid: 23,
              programName: "节目1",
              coverImagePath: "",
              playTime: "00:00:09",
            },
            {
              pid: 234,
              programName: "节目2",
              coverImagePath: "",
              playTime: "00:00:09",
            },
            {
              pid: 234,
              programName: "节目3",
              coverImagePath: "",
              playTime: "00:00:09",
            },
            {
              pid: 234,
              programName: "节目4",
              coverImagePath: "",
              playTime: "00:00:09",
            },
            {
              pid: 234,
              programName: "节目5",
              coverImagePath: "",
              playTime: "00:00:09",
            },
            {
              pid: 234,
              programName: "节目6",
              coverImagePath: "",
              playTime: "00:00:09",
            },
            {
              pid: 234,
              programName: "节目7",
              coverImagePath: "",
              playTime: "00:00:09",
            },
            {
              pid: 234,
              programName: "节目8",
              coverImagePath: "",
              playTime: "00:00:09",
            },
            {
              pid: 23,
              programName: "节目9",
              coverImagePath: "",
              playTime: "00:00:09",
            },
            {
              pid: 234,
              programName: "节目10",
              coverImagePath: "",
              playTime: "00:00:09",
            },
          ],
        },
      },
      computed: {},
      created() {
        this.log("created");
        if (this.isWindows()) {
          return;
        }
        this.playlistInfo = {};
        if (window.DSS20AndroidJS && window.DSS20AndroidJS.getPlaylistInfo) {
          let info = window.DSS20AndroidJS.getPlaylistInfo();
          this.log(info);
          info = JSON.parse(info)
          //结束时间（时间戳）
          if (info.endTime === -1) {
            info.endTime = "永久";
          } else {
            info.endTime = new Date(info.endTime).Format("yyyy-MM-dd hh:mm:ss");
          }
          //播放类型
          let res = PLAY_TYPE.find((item) => item.value === info.playType);
          info.playType = (res && res.label) || "";
          //节目播放时间
          info.programs.forEach((program) => {
            program.playTime = formatSeconds(program.playTime);
          });
          this.playlistInfo = info;

          this.log(JSON.stringify(this.playlistInfo));
        } else {
          this.log("获取节目单信息失败");
        }
      },
      mounted() {
        // this.scroll();
      },

      beforeDestory() {
        this.timer && clearInterval(this.timer);
      },
      methods: {
        /*
         * 点击节目
         */
        clickProgram(item) {
          if (window.DSS20AndroidJS && window.DSS20AndroidJS.playProgramByPid) {
            window.DSS20AndroidJS.playProgramByPid(item.pid);
          } else {
            this.log("播放节目失败");
          }
        },
        /*
         * 终端打印
         */
        log(msg) {
          window.DSS20AndroidJS &&
            window.DSS20AndroidJS.htmlLogcat("封面节目：" + msg);
        },
        /*
         * 判断当前环境是否是pc
         */
        isWindows() {
          return window.DSS20AndroidJS === undefined;
        },
        /*
         * 列表滚动
         */
        scroll() {
          let wrapper = $("#scrollWrap");
          let ulWrapper = $("#scrollWrap ul");
          let curHeight = wrapper.height();
          //页面不需要滚动
          if (ulWrapper.height <= curHeight) {
            return;
          }
          function Marquee() {
            //页面滚动到内容结束时，从头开始
            if (curHeight >= ulWrapper.height()) {
              ulWrapper.css("margin-top", 0);
              curHeight = wrapper.height();
            } else {
              let scrollHeight = ulWrapper.find("li").height();
              ulWrapper.stop().animate(
                {
                  marginTop: -scrollHeight,
                },
                1000 * 1,
                () => {
                  curHeight += scrollHeight;
                }
              );
            }

            // () => {
            //     ulWrapper
            //       .css({ marginTop: 0 })
            //       .find("li:first")
            //       .appendTo(ulWrapper);
            //   }
          }
          this.timer && clearInterval(this.timer);
          this.timer = setInterval(Marquee, 1000 * 5);
        },
      },
    });
  </script>
</html>
