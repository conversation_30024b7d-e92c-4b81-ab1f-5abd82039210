<template>
  <div class="sms-config-dialog">
    <el-dialog
      :title="title"
      :visible.sync="visible"
      width="36%"
      :close-on-click-modal="false"
      :destroy-on-close="true"
    >
      <el-form ref="form" :model="smsConfig" :rules="rules" label-position="left" label-width="120px">
        <el-form-item :label="`所属${$t('deptLabel')}`" prop="companyId" v-if="isRootCompany && !isEditState">
          <el-select v-model="smsConfig.companyId" :placeholder="`选择${$t('deptLabel')}`" @change="handleCompanyChange">
            <el-option
              v-for="item in companies"
              :key="item.id"
              :label="item.deptName"
              :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="是否启用">
          <el-switch
            v-model="smsConfig.isOpen"
            active-color="#13ce66"
            inactive-color="#ff4949"
            active-text="启用"
            inactive-text="停用">
          </el-switch>
        </el-form-item>

        <el-form-item label="短信平台" prop="platform">
          <el-select v-model="smsConfig.platform" placeholder="请选择短信平台">
            <el-option label="阿里云" value="阿里云"></el-option>
            <el-option label="腾讯云" value="腾讯云"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="AccessKey" prop="accessKey">
          <el-input v-model="smsConfig.accessKey" size="small" placeholder="请输入AccessKey"/>
        </el-form-item>

        <el-form-item label="SecretKey" prop="secretKey">
          <el-input v-model="smsConfig.secretKey" size="small" placeholder="请输入SecretKey" show-password/>
        </el-form-item>

        <el-form-item label="短信签名" prop="signName">
          <el-input v-model="smsConfig.signName" size="small" placeholder="请输入短信签名"/>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="closeDialog">取消</el-button>
        <el-button type="primary" @click="save">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getCompanyList } from "@/api/system";

export default {
  name: "smsConfigDialog",
  data() {
    return {
      title: '短信配置',
      visible: false,
      smsConfig: {},
      // 排序参数
      sort: {
        properties: null,
        direction: null
      },
      // 邮件加密选项
      encryptions: [
        {
          value: '',
          label: '不加密'
        },
        {
          value: 'ssl',
          label: 'ssl'
        },
        {
          value: 'tls',
          label: 'tls'
        }
      ],
      // 公司列表
      companies: [],
      // 当前用户是否在总公司下
      isRootCompany: false,
      // 当前是编辑还是添加模式
      isEditState: false,
      rules: {
        companyId: [
          { required: true, message: '请选择部门', trigger: 'change' }
        ],
        platform: [
          { required: true, message: '请选择短信平台', trigger: 'change' }
        ],
        accessKey: [
          { required: true, message: '请输入AccessKey', trigger: 'blur' }
        ],
        secretKey: [
          { required: true, message: '请输入SecretKey', trigger: 'blur' }
        ],
        signName: [
          { required: true, message: '请输入短信签名', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    /**
     * 显示对话框
     */
    showDialog(isRootCompany, smsConfig) {
      this.getCompanies()
      this.isRootCompany = isRootCompany
      if(smsConfig) {
        // 编辑
        this.smsConfig = JSON.parse(JSON.stringify(smsConfig))
        this.title = '编辑【' + smsConfig.companyName + '】短信配置'
        this.isEditState = true
      } else {
        // 添加
        this.smsConfig = {
          companyId: null,
          isOpen: true,
          platform: '',
          accessKey: '',
          secretKey: '',
          signName: ''
        }
        this.isEditState = false
        this.title = '短信配置'
      }
      this.visible = true
    },
    handleCompanyChange(companyId) {
      this.smsConfig.companyName = this.companies.find(item => item.id === companyId)?.deptName || ''
    },
    /**
     * 保存配置
     */
    save() {
      this.$refs.form.validate(valid => {
        if (valid) {
          let data = {
            companyId: this.smsConfig.companyId,
            companyName: this.smsConfig.companyName,
            isOpen: this.smsConfig.isOpen,
            id: this.smsConfig.id || new Date().getTime(),
            platform: this.smsConfig.platform,
            signName: this.smsConfig.signName,
            accessKey: this.smsConfig.accessKey,
            secretKey: this.smsConfig.secretKey,
            gmtCreate: this.smsConfig.gmtCreate || new Date().toLocaleString(),
            gmtModified: new Date().toLocaleString()
          }
          setTimeout(() => {
            this.visible = false
            if(this.isEditState) {
              this.$message.success("编辑成功");
            } else {
              this.$message.success("添加成功");
            }
            this.$emit("afterSave", data)
          }, 500);
        }
      })
    },

    /**
     * 获取公司列表
     */
    getCompanies() {
      getCompanyList().then(res => {
        this.companies = res.data
      })
    },

    closeDialog() {
      this.visible = false
      if (this.$refs.form) {
        this.$refs.form.clearValidate()
      }
    }
  }
}
</script>

<style scoped>

</style>
