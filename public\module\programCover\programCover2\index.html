<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Document</title>
  <link rel="stylesheet" href="40e93b11cbd4174c60b22cba0d630300.css" />
  <!-- Link Swiper's CSS -->
  <link rel="stylesheet" href="9a5a098c413aafce427fee0fec00c934.css">
  <style>
    ul {
      margin: 0;
      padding: 0;
    }

    ul li {
      list-style: none;
    }

    body,
    html {
      height: 100%;
      margin: 0;
      user-select: none;
    }

    .wrap {
      width: 100%;
      height: 100%;
      position: relative;
      box-sizing: border-box;
      position: relative;
      display: flex;
      align-items: center;
    }

    .wrap .title {
      position: absolute;
      left: 50%;
      top: 10vh;
      transform: translateX(-50%);
      font-weight: bold;
      text-align: center;
      font-size: 3.4vw;
      width:60%;
      text-overflow: ellipsis;
      overflow: hidden;
      word-break: break-all;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .swiper-father {
      position: relative;
      width: 80%;
      margin: 10vh auto 10vh;
      /* 设置分页位置 */
      --swiper-pagination-bottom: -6vh;
      /* 单独设置按钮颜色 */
      --swiper-navigation-color: white;
      /* 设置按钮大小 */
      --swiper-navigation-size: 5vw;
      /* 设置按钮位置 */
      --swiper-navigation-sides-offset: 2vw;
    }

    .swiper {
      width: 80%;
    }

    .swiper-slide {
      text-align: center;
      background-color: #fff;
      border-radius: 10px;
      overflow: hidden;
    }

    .swiper-slide .img-wrap {
      width: 100%;
      padding: 10px 10px 0;
      box-sizing: border-box;
      position: relative;
      background-color: rgb(247, 247, 247);
      background-origin: content-box;

    }

    .swiper-slide .img-wrap img {
      width: 100%;
      height: 100%;
    }

    .swiper-slide .name {
      padding: 1vh 1vw;
      font-size: 1vw;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .header-mask {
      position: absolute;
      top: 0;
      left: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      background-color: rgba(0, 0, 0, 0.3);
      width: 100%;
      height: 100%;
      border-radius: 4px 4px 0 0;
      transition: top 0.28s ease, opacity 0.28s ease, height 0.28s ease;
    }

    .header-mask .details-btn {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      display: inline-block;
      width: 40%;
      height: 44px;
      font-size: 18px;
      line-height: 44px;
      border-radius: 22px;
      border: 1px solid #fff;
      color: #fff;
      cursor: pointer;
      text-align: center;
      transition: all 0.2s;
    }

    .swiper-slide .img-wrap:hover .header-mask {
      opacity: 1;
    }

    .swiper-pagination {
      font-size: 1.2vw;
    }

    .swiper-button-next,
    .swiper-button-prev {
      font-weight: bold;
    }

    .swiper-button-next,
    .swiper-button-prev,
    .swiper-button-next:focus,
    .swiper-button-prev:focus,
    .swiper-button-prev:active {
      outline: none;
    }

    .empty-wrap {
      font-size: 6vw;
      color: white;
      margin: 0 auto;
    }

    [v-cloak] {
      display: none;
    }
  </style>
  <!-- vue -->
  <script src="174365807c06490c848d7b1d45fdc348.js"></script>
  <!-- Swiper JS -->
  <script src="cccfa479191d42ce495c5e569f56ba6a.js"></script>
  <script src="page.js"></script>
</head>

<body>
  <div id="app" class="wrap" v-cloak>
    <div class="wrap" :style="style">
      <div class="title">{{title}}</div>
      <div class="swiper-father" v-if="programs && programs.length">
        <div class="swiper">
          <div class="swiper-wrapper"
            :style="{justifyContent:programs.length < SLIDES_PER_VIEW ? 'center' : 'flex-start'}">
            <div class="swiper-slide" v-for="item in programs" :key="item.pid" @click="clickProgram(item)">
              <div class="img-wrap">
                <!-- img需要用src终端才能下载到 -->
                <img src='./fe5282a22747f93fe7ee455e04d2a133.png' alt="" v-show="!item.coverImagePath">
                <img :src="item.coverImagePath" alt="加载失败" v-show="item.coverImagePath" />
                <div class="header-mask">
                  <div class="details-btn">播放</div>
                </div>
              </div>
              <div class="name">{{item.programName}}</div>
            </div>
          </div>

        </div>
        <div class="swiper-button-next" tabindex="0" role="button" aria-label="Next slide" aria-disabled="false"></div>
        <div class="swiper-button-prev swiper-button-disabled" tabindex="-1" role="button" aria-label="Previous slide"
          aria-disabled="true"></div>
        <div class="swiper-pagination swiper-pagination-fraction swiper-pagination-horizontal">
          <span class="swiper-pagination-current">1</span>
          /
          <span class="swiper-pagination-total">2</span>
        </div>
      </div>
      <div v-else class="empty-wrap">暂无节目</div>
    </div>
  </div>
</body>

<script>
  const SLIDES_PER_VIEW = 3;
  window.$page = page;

  const app = new Vue({
    el: "#app",
    data: {
      SLIDES_PER_VIEW,
      props: page.props,
      title: '党宣栏目',
      programs: [
        {
          pid: 23,
          programName: "节目1",
          coverImagePath: "",
          playTime: "00:00:09",
        },
        {
          pid: 234,
          programName: "节目2",
          coverImagePath: "",
          playTime: "00:00:09",
        },
        {
          pid: 234,
          programName: "节目3",
          coverImagePath: "",
          playTime: "00:00:09",
        },
        {
          pid: 234,
          programName: "节目4",
          coverImagePath: "",
          playTime: "00:00:09",
        },
        {
          pid: 234,
          programName: "节目5",
          coverImagePath: "",
          playTime: "00:00:09",
        },
        {
          pid: 234,
          programName: "节目6",
          coverImagePath: "",
          playTime: "00:00:09",
        },
        {
          pid: 234,
          programName: "节目7",
          coverImagePath: "",
          playTime: "00:00:09",
        },
        {
          pid: 234,
          programName: "节目8",
          coverImagePath: "",
          playTime: "00:00:09",
        },
        {
          pid: 23,
          programName: "节目9",
          coverImagePath: "",
          playTime: "00:00:09",
        },
        {
          pid: 234,
          programName: "节目10",
          coverImagePath: "",
          playTime: "00:00:09",
        },
      ],
    },
    computed: {
      style() {
        let style = {};
        for (let prop of this.props) {
          if(prop.field === 'title'){
            this.title = prop.value
          }else{
            style[prop.field] = prop.value;
          }
          
        }
        return style;
      },
    },
    created() {
      if (this.isWindows()) {
        return;
      }
      this.playlistInfo = {};
      if (window.DSS20AndroidJS && window.DSS20AndroidJS.getPlaylistInfo) {
        let info = window.DSS20AndroidJS.getPlaylistInfo();
        this.log(info);
        info = JSON.parse(info)
        //节目列表
        this.programs = info.programs || [];

        this.log(JSON.stringify(this.programs));
      } else {
        this.log("获取节目单信息失败");
      }
    },
    mounted() {
      window["update"] = (val, mqtt = null) => {
        this.updateProps(val);
      };

      this.initSwiper()
    },

    beforeDestory() {
      this.timer && clearInterval(this.timer);
    },
    methods: {
      updateProps(props) {
        for (let prop of props) {
          let index = this.getPropIndex(prop.field);
          if (index !== -1) {
            let data = this.props[index];
            data.value = prop.value;
            this.$set(this.props, index, data);
          }
        }
      },
      getPropIndex(name) {
        for (let i = 0; i < this.props.length; i++) {
          if (this.props[i].field === name) {
            return i;
          }
        }
        return -1;
      },
      /*
       * 初始化swiper
       */
      initSwiper() {
        new Swiper(".swiper", {
          slidesPerView: SLIDES_PER_VIEW,
          spaceBetween: 50,
          // centeredSlides:true,
          pagination: {
            el: ".swiper-pagination",
            type: "fraction",
          },
          navigation: {
            nextEl: ".swiper-button-next",
            prevEl: ".swiper-button-prev",
          },
        });
      },
      /*
       * 点击节目
       */
      clickProgram(item) {
        if (window.DSS20AndroidJS && window.DSS20AndroidJS.playProgramByPid) {
          window.DSS20AndroidJS.playProgramByPid(item.pid);
        } else {
          this.log("播放节目失败");
        }
      },
      /*
       * 终端打印
       */
      log(msg) {
        window.DSS20AndroidJS &&
          window.DSS20AndroidJS.htmlLogcat("封面节目2：" + msg);
      },
      /*
       * 判断当前环境是否是pc
       */
      isWindows() {
        return window.DSS20AndroidJS === undefined;
      },
    },
  });
</script>

</html>