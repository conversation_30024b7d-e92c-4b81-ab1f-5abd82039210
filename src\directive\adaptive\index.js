const doResize = async (el, binding, vnode) => {
  const { componentInstance: $table } = await vnode;
  const { value } = binding;
  // 全屏页面没有app-main
  let dom = document.querySelector(".app-main") || document.querySelector("#app")
  if(!dom){
    return;
  }
  const screenHeight = dom.getBoundingClientRect().height;
  // const bottomOffset = (value && value.bottomOffset) || 78;
  const bottomOffset = (value && value.bottomOffset) || 50;

  const topOffset =
    (value && value.topOffset) || el.getBoundingClientRect().top;
  let height = screenHeight - topOffset - bottomOffset;

  if (value?.notTable) {
    el.style.height = height + "px";
    return;
  }

  if (!$table.height) {
    throw new Error(`el-$table must set the height. Such as height='100px'`);
  }
  // console.log('adaptive', height)
  $table.layout.setHeight(height);
  $table.doLayout();
};

export default {
  //指令第一次绑定到元素时调用
  bind(el, binding, vnode) {
    el.resizeListener = async () => {
      await doResize(el, binding, vnode);
    };

    window.addEventListener("resize", el.resizeListener);
  },
  // 被绑定元素插入父节点时调用
  // 绑定默认高度
  async inserted(el, binding, vnode) {
    await doResize(el, binding, vnode);
  },
  // 所在组件的 VNode 更新时调用
  async update(el, binding, vnode) {
    await doResize(el, binding, vnode);
  },
  //指令与元素解绑时调用
  unbind(el) {
    window.removeEventListener("resize", el.resizeListener);
  },
};
