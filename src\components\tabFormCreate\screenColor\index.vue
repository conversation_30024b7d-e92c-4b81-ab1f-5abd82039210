<template>
  <div>
    <el-radio-group v-model="active">
      <el-radio
        v-for="item in SCREEN_COLOR_ENUM"
        :label="item.value"
        :key="item.value"
      >
        <img :src="require(`./img/${item.img}`)" alt="" />
      </el-radio>
    </el-radio-group>
  </div>
</template>

<script>
import { SCREEN_COLOR_ENUM } from "./enum";
export default {
  props: {
    //预定义
    value: String | Array
  },
  data() {
    return { SCREEN_COLOR_ENUM };
  },
  computed: {
    active: {
      get() {
        return this.value;
      },
      set(newValue) {
        //当组件值发生变化后,通过 input 事件更新值
        this.$emit("input", newValue);
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.el-radio-group {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  width: 420px;
  .el-radio {
    display: flex;
    flex-direction: column-reverse;
    align-items: center;
    img {
      margin: 10px 0;
    }
  }
}
</style>
