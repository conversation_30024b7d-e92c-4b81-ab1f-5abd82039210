/** * 终端穿梭框 * 会议指引，获取未绑定会议室的终端列表 */

<template>
  <el-dialog
    title="选择终端"
    :visible.sync="visible"
    width="950px"
    :close-on-click-modal="false"
    @close="close"
  >
    <div class="margin-bottom10 search-wrap">
      <el-input
        placeholder="终端名称"
        suffix-icon="el-icon-search"
        size="small"
        v-model="nameToSearch"
        v-debounce="[e=>{pagination.curPage = 1;getList(e);}]"
        style="width: 150px;"
      ></el-input>
      <el-input
        placeholder="终端id"
        suffix-icon="el-icon-search"
        size="small"
        v-model="macToSearch"
        v-debounce="[e=>{pagination.curPage = 1;getList(e);}]"
        style="width: 150px;"
        class="ml-10"
      ></el-input>
    </div>
    <transfer
      ref="transfer"
      keyword="mac"
      :list="list"
      :chooseList="chooseList"
      :defaultSort="{ prop: sort.prop, order: 'descending' }"
      @sortChange="sortChange"
    >
      <div slot="titleLeft">候选终端列表</div>
      <template slot="leftTable">
        <el-table-column label="序号" width="70">
          <template slot-scope="scope">
            {{ scope.$index + (pagination.curPage - 1) * pagination.size + 1 }}
          </template>
        </el-table-column>
        <el-table-column
          prop="terminalName"
          :show-overflow-tooltip="true"
          label="终端名称"
          sortable="custom"
          width="120"
        >
        </el-table-column>
        <el-table-column
          prop="mac"
          :show-overflow-tooltip="true"
          sortable="custom"
          label="终端id"
        >
        </el-table-column>
      </template>
      <pagination
        slot="pagination"
        :total="pagination.total"
        :page.sync="pagination.curPage"
        :limit.sync="pagination.size"
        :layout="TRANSFER_PAGINATION_LAYOUT"
        @pagination="getList"
        :autoScroll="false"
      />
      <div slot="titleRight">已选终端列表</div>
      <template slot="rightTable">
        <el-table-column type="index" label="序号" width="70">
        </el-table-column>
        <el-table-column
          prop="terminalName"
          :show-overflow-tooltip="true"
          label="终端名称"
          width="120"
          sortable
        >
        </el-table-column>
        <el-table-column
          prop="mac"
          :show-overflow-tooltip="true"
          label="终端id"
          sortable
        >
        </el-table-column>
      </template>
    </transfer>
    <div slot="footer" class="dialog-footer">
      <el-button @click="close">取消</el-button>
      <el-button class="okBtn" @click="handleOk">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import dlg from "@/mixins/dlg";
import transfer from "./index.vue";
import { getUnBindTerminal } from "@/api/conferenceGuide";
import { TRANSFER_PAGINATION_LAYOUT } from "@/utils/enum";

export default {
  mixins: [dlg],
  components: {
    transfer
  },
  props: {
    values: {
      type: Array,
      default: () => [],
      require: false
    }
  },
  data() {
    return {
      // 搜索
      nameToSearch: "",
      macToSearch: "",
      // 终端列表
      list: [],
      chooseList: [],
      // 分页参数
      pagination: {
        curPage: 1,
        size: 20,
        total: 0
      },
      // 排序
      sort: {
        prop: "mac",
        order: "DESC"
      },
      TRANSFER_PAGINATION_LAYOUT
    };
  },
  created() {
    if (this.values && this.values.length) {
      this.chooseList = this.values.map(item => ({
        terminalName: item.terminalName || item.name,
        mac: item.mac
      }));
    }
    this.getList();
  },
  methods: {
    handleOk() {
      let list = this.$refs.transfer.rightList;
      this.$emit("handleSelect", list);
      this.close();
    },
    /**
     * 获取未绑定会议室的终端列表
     */
    getList() {
      getUnBindTerminal({
        page: this.pagination.curPage,
        rows: this.pagination.size,
        nameToSearch: this.nameToSearch,
        macToSearch: this.macToSearch,
        properties: this.sort.prop,
        direction: this.sort.order
      }).then(res => {
        this.list = res.data.rows;
        this.pagination.total = res.data.total;
      });
    },
    sortChange(col) {
      this.sort.order = "ascending" === col.order ? "ASC" : "DESC";
      this.sort.prop = col.prop;
      this.getList();
    }
  }
};
</script>
<style lang="scss" scoped></style>
