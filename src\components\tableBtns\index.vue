<template>
    <div class="table-btns">
        <slot></slot>
        <div class="more-menu">
            <el-button size="mini" icon="el-icon-more-outline" primary plain>更多</el-button>
            <slot name="more">

            </slot>
        </div>
    </div>
</template>
<script>
export default {
    props: {
        btns: {
            type: Array,
            default: () => []
        },
        scope: {
            type: Object,
            default: () => { }
        }
    },
    data() {
        return {

        }
    },
    methods: {
        handleClick(btn, index) {
            console.log(btn, index);
        }
    }
}
</script>
<style lang="scss">
.table-btns {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
}

.more-menu {
    margin-left: 10px;
}
</style>