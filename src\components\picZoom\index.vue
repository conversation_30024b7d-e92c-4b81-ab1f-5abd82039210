<template>
  <div class="zoom-wrap" ref="zoomWrap">
    <img :src="url" alt="加载失败" />
    <slot name="status"></slot>
    <slot name="hover"> </slot>
  </div>
</template>

<script>
  export default {
    props: {
      url: '',
    },
    data() {
      return {};
    },
    methods: {},
  };
</script>

<style lang="scss" scoped>
  .zoom-wrap {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    > img {
      object-fit: contain;
      width: 100%;
      height: 100%;
      border-top-left-radius: 4px;
    }
  }
</style>
