<!-- 堆叠柱状条 -->
<template>
  <el-tooltip placement="right" popper-class="stacked-bar-tooltip">
    <div slot="content">
      <div class="title" v-if="title">{{ title }}</div>
      <div v-for="item in newList" :key="item.type">
        <svg-icon icon-class="circle" :style="{ color: item.bgColor }" class="circle-icon" />
        {{ item.name }}:{{ item.value }}
      </div>
    </div>
    <div class="bar">
      <div class="segment" v-for="item in newList" :key="item.type" :style="{ width: item.percentage + '%', backgroundColor: item.bgColor }"></div>
    </div>
  </el-tooltip>
</template>

<script>
  import { material } from '@/utils/enum';

  const COLOR = ['#117BF5', '#88CDFE', '#00C8FC', '#3FFCC8', '#71B0FC', '#8072FC', '#FF6A49', 'D94883'];
  export default {
    props: {
      list: {
        type: Array,
        default: [],
      },
      title: {
        type: String,
        default: '',
      },
    },
    data() {
      return {};
    },
    computed: {
      newList() {
        let total = 0;
        this.list.forEach((item) => (total += item.value));

        return this.list.map((item, index) => ({
          ...item,
          percentage: Math.round((item.value / total) * 10000) / 100,
          bgColor: COLOR[index],
          name: material.find((m) => m.type === item.type)?.name || '',
        }));
      },
    },
  };
</script>

<style lang="scss" scoped>
  .title {
    margin-left: 5px;
    margin-bottom: 5px;
  }
  .bar {
    display: flex;
    .segment {
      height: 20px;
      cursor: pointer;
    }
    &:hover {
      .segment {
        // 高亮
        filter: brightness(120%);
      }
    }
  }

  .stacked-bar-tooltip {
    background: rgb(21, 37, 55) !important;
  }
  .stacked-bar-tooltip[x-placement^='right'] .popper__arrow {
    border-right-color: rgb(21, 37, 55) !important;
  }
  .stacked-bar-tooltip[x-placement^='right'] .popper__arrow:after {
    border-right-color: rgb(21, 37, 55) !important;
  }
</style>
