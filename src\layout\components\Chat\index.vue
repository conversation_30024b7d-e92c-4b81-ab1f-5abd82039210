<template>
  <el-drawer :visible.sync="visible" direction="rtl" :append-to-body="true" size="50%" :before-close="beforeCloseDrawer">
    <div class="deepseek-wrap">
      <div class="chat-list" ref="chatListRef">
        <div class="chat-wrap" v-for="(chat, index) in chatList" :key="index">
          <div class="question-wrap">
            <div class="question chat-box" v-if="!chat.bEdited">
              <div class="icon-wrap">
                <i class="el-icon-copy-document" @click="doCopy(chat.question, $event)"></i>
                <i class="el-icon-edit" @click="doEdit(chat, index)"></i>
              </div>
              {{ chat.question }}
            </div>
            <div class="input-wrap" v-else>
              <el-input
                type="textarea"
                :ref="'input' + index"
                :autosize="{ minRows: 1 }"
                v-model="chat.newQuestion"
                @keydown.enter.native.prevent="doSend(chat)"
              >
              </el-input>
              <div class="btn-wrap">
                <el-button round @click="doCancelEdit(chat)">取消</el-button>
                <el-button type="primary" round :disabled="!chat.newQuestion || bChating" @click="doSend(chat)">发送</el-button>
              </div>
            </div>
          </div>
          <div class="answer-wrap">
            <div class="deepseek-icon" title="deepseek">
              <svg
                width="100%"
                height="100%"
                viewBox="0 0 30 30"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                xmlns:xlink="http://www.w3.org/1999/xlink"
              >
                <path
                  id="path"
                  d="M27.501 8.46875C27.249 8.3457 27.1406 8.58008 26.9932 8.69922C26.9434 8.73828 26.9004 8.78906 26.8584 8.83398C26.4902 9.22852 26.0605 9.48633 25.5 9.45508C24.6787 9.41016 23.9785 9.66797 23.3594 10.2969C23.2275 9.52148 22.79 9.05859 22.125 8.76172C21.7764 8.60742 21.4238 8.45312 21.1807 8.11719C21.0098 7.87891 20.9639 7.61328 20.8779 7.35156C20.8242 7.19336 20.7695 7.03125 20.5879 7.00391C20.3906 6.97266 20.3135 7.13867 20.2363 7.27734C19.9258 7.84375 19.8066 8.46875 19.8174 9.10156C19.8447 10.5234 20.4453 11.6562 21.6367 12.4629C21.7725 12.5547 21.8076 12.6484 21.7646 12.7832C21.6836 13.0605 21.5869 13.3301 21.501 13.6074C21.4473 13.7852 21.3662 13.8242 21.1768 13.7461C20.5225 13.4727 19.957 13.0684 19.458 12.5781C18.6104 11.7578 17.8438 10.8516 16.8877 10.1426C16.6631 9.97656 16.4395 9.82227 16.207 9.67578C15.2314 8.72656 16.335 7.94727 16.5898 7.85547C16.8574 7.75977 16.6826 7.42773 15.8193 7.43164C14.957 7.43555 14.167 7.72461 13.1611 8.10938C13.0137 8.16797 12.8594 8.21094 12.7002 8.24414C11.7871 8.07227 10.8389 8.0332 9.84766 8.14453C7.98242 8.35352 6.49219 9.23633 5.39648 10.7441C4.08105 12.5547 3.77148 14.6133 4.15039 16.7617C4.54883 19.0234 5.70215 20.8984 7.47559 22.3633C9.31348 23.8809 11.4307 24.625 13.8457 24.4824C15.3125 24.3984 16.9463 24.2012 18.7881 22.6406C19.2529 22.8711 19.7402 22.9629 20.5498 23.0332C21.1729 23.0918 21.7725 23.002 22.2373 22.9062C22.9648 22.752 22.9141 22.0781 22.6514 21.9531C20.5186 20.959 20.9863 21.3633 20.5605 21.0371C21.6445 19.752 23.2783 18.418 23.917 14.0977C23.9668 13.7539 23.9238 13.5391 23.917 13.2598C23.9131 13.0918 23.9512 13.0254 24.1445 13.0059C24.6787 12.9453 25.1973 12.7988 25.6738 12.5352C27.0557 11.7793 27.6123 10.5391 27.7441 9.05078C27.7637 8.82422 27.7402 8.58789 27.501 8.46875ZM15.46 21.8613C13.3926 20.2344 12.3906 19.6992 11.9766 19.7227C11.5898 19.7441 11.6592 20.1875 11.7441 20.4766C11.833 20.7617 11.9492 20.959 12.1123 21.209C12.2246 21.375 12.3018 21.623 12 21.8066C11.334 22.2207 10.1768 21.668 10.1221 21.6406C8.77539 20.8477 7.64941 19.7988 6.85547 18.3652C6.08984 16.9844 5.64453 15.5039 5.57129 13.9238C5.55176 13.541 5.66406 13.4062 6.04297 13.3379C6.54199 13.2461 7.05762 13.2266 7.55664 13.2988C9.66602 13.6074 11.4619 14.5527 12.9668 16.0469C13.8262 16.9004 14.4766 17.918 15.1465 18.9121C15.8584 19.9688 16.625 20.9746 17.6006 21.7988C17.9443 22.0879 18.2197 22.3086 18.4824 22.4707C17.6895 22.5586 16.3652 22.5781 15.46 21.8613ZM16.4502 15.4805C16.4502 15.3105 16.5859 15.1758 16.7568 15.1758C16.7949 15.1758 16.8301 15.1836 16.8613 15.1953C16.9033 15.2109 16.9424 15.2344 16.9727 15.2695C17.0273 15.3223 17.0586 15.4004 17.0586 15.4805C17.0586 15.6504 16.9229 15.7852 16.7529 15.7852C16.582 15.7852 16.4502 15.6504 16.4502 15.4805ZM19.5273 17.0625C19.3301 17.1426 19.1328 17.2129 18.9434 17.2207C18.6494 17.2344 18.3281 17.1152 18.1533 16.9688C17.8828 16.7422 17.6895 16.6152 17.6074 16.2168C17.5732 16.0469 17.5928 15.7852 17.623 15.6348C17.6934 15.3105 17.6152 15.1035 17.3877 14.9141C17.2012 14.7598 16.9658 14.7188 16.7061 14.7188C16.6094 14.7188 16.5205 14.6758 16.4541 14.6406C16.3457 14.5859 16.2568 14.4512 16.3418 14.2852C16.3691 14.2324 16.501 14.1016 16.5322 14.0781C16.8838 13.877 17.29 13.9434 17.666 14.0938C18.0146 14.2363 18.2773 14.498 18.6562 14.8672C19.0439 15.3145 19.1133 15.4395 19.334 15.7734C19.5078 16.0371 19.667 16.3066 19.7754 16.6152C19.8408 16.8066 19.7559 16.9648 19.5273 17.0625Z"
                  fill-rule="nonzero"
                  fill="#4D6BFE"
                ></path>
              </svg>
            </div>
            <div class="answer-box chat-box">
              <div class="status-wrap">
                <i v-if="chat.status === 'loading'" class="el-icon-loading" color="#8f91a8"></i>
                <span @click="toggleShowThink(chat)">
                  {{ chat.status === 'loading' ? '思考中' : chat.status === 'think' ? '正在深度思考' : '已完成深度思考' }}
                  <i class="el-icon-arrow-up" v-if="chat.bShowThink"></i>
                  <i class="el-icon-arrow-down" v-else></i>
                </span>
              </div>

              <div class="think" v-show="chat.think && chat.bShowThink">
                <div class="markdown-body" ref="thinkMarkdownRef"></div>
              </div>
              <div class="answer" v-show="chat.answer">
                <div class="markdown-body" ref="answerMarkdownRef"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="input-wrap">
        <el-input
          type="textarea"
          ref="questionInput"
          :autosize="{ minRows: 1 }"
          placeholder="给DeepSeek发消息"
          v-model="question"
          @keydown.enter.native.prevent="doAsk"
        >
        </el-input>
        <svg-icon v-if="bChating" icon-class="deepseek@stop" @click="stopChating"></svg-icon>
        <el-button class="ask-btn" v-else type="primary" icon="el-icon-top" circle :disabled="!question" @click="doAsk"></el-button>
      </div>
      <div class="scroll-bottom-wrap" @click="scrollToBottom" v-show="bShowScrollToBottomBtn">
        <i class="el-icon-bottom"></i>
      </div>
    </div>
  </el-drawer>
</template>

<script>
  import clipboard from '@/utils/clipboard';
  import { deepseekChat } from '@/api/system';
  import MarkdownIt from 'markdown-it';
  import hljs from 'highlight.js';
  import 'highlight.js/styles/a11y-dark.css';
  import { debounce, throttle } from 'lodash';
  import { updateDOMNode } from './updateDOMNode';
  import { Typewriter } from './typewriter';

  // 初始化 markdown-it 配置
  const md = new MarkdownIt({
    html: true, // 在源码中启用 HTML 标签
    // breaks: true,   // 转换段落里的 '\n' 到 <br>。
    linkify: true, // 将类似 URL 的文本自动转换为链接。
    typographer: true, // 排版优化
    highlight: function (str, lang) {
      if (lang && hljs.getLanguage(lang)) {
        try {
          return '<pre class="hljs"><code>' + hljs.highlight(str, { language: lang }).value + '</code></pre>';
        } catch (__) {}
      }
      return '<pre class="hljs"><code>' + md.utils.escapeHtml(str) + '</code></pre>';
    },
  }).enable('table'); // 启用表格支持

  export default {
    props: {
      visible: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        chatList: [],
        question: '',
        bChating: false,
        bShowScrollToBottomBtn: false,
        typewriter: null,
      };
    },
    watch: {
      visible(newVal) {
        if (!newVal) {
          this.$nextTick(() => {
            // 移除监听滚动事件
            const chatListRef = this.$refs.chatListRef;
            chatListRef && chatListRef.removeEventListener('scroll', this.debounceHandleScroll);
          });
          return;
        }

        this.$nextTick(() => {
          // 输入框获取焦点
          const questionInputRef = this.$refs.questionInput;
          questionInputRef && questionInputRef.focus();
          // 监听滚动事件
          const chatListRef = this.$refs.chatListRef;
          chatListRef && chatListRef.addEventListener('scroll', this.debounceHandleScroll);
        });
      },
    },
    mounted() {
      // 防抖(最后一次才触发)
      this.debounceHandleScroll = debounce(this.handleScroll, 500);
      // 节流（一段时间内只触发一次）
      this.throttleScrollToBottom = throttle(this.scrollToBottom, 100);
      // 初始化打字机类
      this.typewriter = new Typewriter(
        (data, status) => {
          let currentIndex = this.chatList.length - 1;
          const answerMarkdownRef = this.$refs.answerMarkdownRef[currentIndex];
          const thinkMarkdownRef = this.$refs.thinkMarkdownRef[currentIndex];

          let currentChat = this.chatList.at(-1);
          if (status) {
            currentChat.status = status;
          }
          currentChat[currentChat.status] += data;
          let domRef = currentChat.status === 'think' ? thinkMarkdownRef : answerMarkdownRef;
          if (domRef) {
            const tmpDiv = document.createElement('div');
            tmpDiv.innerHTML = md.render(currentChat[currentChat.status]); // 只渲染当前的块
            // removeCursor(markdownRef.value);
            // 对比节点的内容 实现动态更新
            updateDOMNode(domRef, tmpDiv);

            this.$nextTick(() => {
              this.throttleScrollToBottom();
            });
          }
        },
        () => {
          this.bChating = false;
          let currentChat = this.chatList[this.chatList.length - 1];
          currentChat.status = 'finish';
        }
      );
    },
    methods: {
      beforeCloseDrawer(done) {
        if (window.getSelection().toString()) {
          // 如果有选中文本，不关闭
          return;
        }
        this.$emit('close');
      },
      /**
       * 页面滚动
       */
      handleScroll() {
        const { scrollHeight, scrollTop, clientHeight } = this.$refs.chatListRef;
        // 当滚动到底部的距离小于视窗高度时，显示按钮
        this.bShowScrollToBottomBtn = scrollHeight - (scrollTop + clientHeight) > 0;
      },
      /**
       * 复制
       */
      doCopy(str, event) {
        clipboard(str, event);
      },
      /**
       * 编辑
       */
      doEdit(chat, index) {
        chat.bEdited = true;
        chat.newQuestion = chat.question;
        // 当前编辑的输入框获取焦点
        this.$nextTick(() => {
          let inputRef = this.$refs['input' + index];
          inputRef && inputRef[0] && inputRef[0].focus();
        });
      },
      /**
       * 取消编辑
       */
      doCancelEdit(chat) {
        chat.bEdited = false;
      },
      /**
       * 编辑问题，发送
       */
      doSend(chat) {
        if (!chat || !chat.newQuestion || this.bChating) {
          return;
        }
        chat.bEdited = false;
        this.askDeepSeek(chat.newQuestion);
      },
      /**
       * 点击发送按钮
       */
      doAsk() {
        if (this.bChating) {
          return;
        }
        this.askDeepSeek(this.question);
        this.question = '';
      },
      /**
       * 停止当前对话
       */
      stopChating() {
        // this.bChating = false;
        // let currentChat = this.chatList[this.chatList.length - 1];
        // currentChat.status = 'finish';
        // 打字机停止
        this.typewriter.cancel();
      },
      /**
       * 切换是否显示思考内容
       */
      toggleShowThink(chat) {
        chat.bShowThink = !chat.bShowThink;
      },
      askDeepSeek(question) {
        // 问题为空
        if (!question || this.bChating) {
          return;
        }
        console.log(question);

        let isMqConnected = this.$store.state.mqtt.connected;
        if (isMqConnected) {
          this.subscribeMQTT();
        }

        this.bChating = true;
        this.chatList.push({ question, answer: '', think: '', status: 'loading', bEdited: false, newQuestion: question, bShowThink: true });

        this.$nextTick(() => {
          this.scrollToBottom();
        });

        deepseekChat({ question, stream: true }).then(({ data }) => {
          console.log(data);
          // 打字机开启
          this.typewriter.start();
        });

        // fetch(`/ai/ollama/stream?msg=${question}`, {
        //   method: 'GET',
        //   // headers: {
        //   //   'Content-Type': 'application/json',
        //   //   Authorization: `Bearer ${getAccessToken()}`,
        //   // },
        //   // mode:"cors"
        //   // body: JSON.stringify({
        //   //   question: this.question,
        //   //   stream: true,
        //   // }),
        // }) // 替换成实际需要请求的URL
        //   .then((response) => {
        //     const reader = response.body.getReader();
        //     const decoder = new TextDecoder(); // 用于将Uint8Array转换为字符串

        //     let data = '';
        //     let currentChat = this.chatList[currentLength - 1];
        //     console.log(currentChat);
        //     let that = this;

        //     function read() {
        //       reader
        //         .read()
        //         .then(({ done, value }) => {
        //           if (done || !that.bChating) {
        //             console.log('结束');
        //             console.log(data);
        //             that.bChating = false;
        //             currentChat.status = 'finish';
        //             return;
        //           }

        //           // 将读取到的数据（value）解码为字符串并处理
        //           let reply = decoder.decode(value, { stream: true }).replace(/data:|\n/g, '');
        //           console.log(decoder.decode(value, { stream: true }));
        //           if (reply.includes('<think>')) {
        //             currentChat.status = 'think';
        //           }

        //           if (reply.includes('</think>')) {
        //             console.log(reply.split('</think>'));
        //             let replayArr = reply.split('</think>');
        //             data += replayArr[0];
        //             currentChat.think = data;
        //             currentChat.status = 'answer';
        //             data = replayArr[1];
        //             currentChat.answer = data;
        //           } else {
        //             reply.replace(/<think>|<\/think>/g, '');
        //             data += reply;
        //             currentChat[currentChat.status] = data;
        //           }

        //           // 继续读取
        //           that.bChating && read();
        //         })
        //         .catch((error) => {
        //           console.error('读取流时发生错误:', error);
        //         });
        //     }

        //     read();
        //   })
        //   .catch((error) => {
        //     console.error('请求发生错误:', error);
        //   });
      },
      /**
       * 订阅会议所有数据
       */
      subscribeMQTT() {
        const mqClient = this.$store.state.mqtt.client;
        let curUserName = this.$store.getters.name;
        const chatTopic = `dss2/deepseek/chat/${curUserName}`;
        const mqTopics = this.$store.state.mqtt.topics;
        // if not then subscribe
        if (!mqTopics.has(chatTopic)) {
          this.$store.commit('mqtt/SET_TOPIC', chatTopic);
          mqClient.subscribe(chatTopic, { qos: 1, rap: true }, (err) => {
            if (!err) {
              console.log('subscribe success, ', chatTopic);
            } else {
              console.error('subscribe err, ', chatTopic, err);
            }
          });

          mqClient.on('message', async (topic, message) => {
            if (topic === chatTopic) {
              let data = JSON.parse(message.toString());
              console.log(data)
              let status = '';
              if (data.includes('<think>')) {
                status = 'think';
              } else if (data.includes('</think>')) {
                status = 'answer';
              }
              this.typewriter.add(data, status);
            }
          });
        }
      },
      scrollToBottom() {
        let el = this.$refs.chatListRef;
        if (!el) return;
        const { scrollHeight, scrollTop, clientHeight } = el;
        // 没有滚动条
        if (scrollHeight - (scrollTop + clientHeight) <= 0) {
          return;
        }

        el.scrollTo({
          top: el.scrollHeight - el.clientHeight,
          behavior: 'smooth',
        });
      },
    },
  };
</script>

<style lang="scss" scoped>
  @import '@/styles/element-variables.scss';

  ::v-deep .el-drawer__header {
    background-color: rgb(247, 248, 252);
    margin-bottom: 0;
  }
  .deepseek-wrap {
    height: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-direction: column;
    color: white;
    padding: 10px;
    padding-right: 0px;
    background-color: rgb(247, 248, 252);
    position: relative;
    .chat-list {
      width: 100%;
      overflow-y: auto;
      padding-right: 5px;
    }
    .chat-wrap {
      display: flex;
      flex-direction: column;
      flex: 1;

      .answer {
        align-self: flex-start;
        color: black;
      }
      .answer-box {
        flex: 1;
      }
      .chat-box {
        max-width: 100%;
        padding: 12px 16px;
        background-color: #fff;
        border-radius: 10px;
        &.question {
          background-color: rgb(99, 171, 225);
        }
      }
      .question-wrap {
        align-self: flex-end;
        width: 100%;
        text-align: right;
        margin: 14px 0;
        .question {
          position: relative;
          max-width: calc(100% - 80px);
          display: inline-block;
          text-align: left;
          .icon-wrap {
            display: flex;
            position: absolute;
            right: calc(100% + 18px);
            top: 12px;
            opacity: 0;
            color: rgb(63, 63, 63);
            font-size: 18px;
            i {
              padding: 0 5px;
              cursor: pointer;
              &:hover {
                color: $--color-primary;
              }
            }
          }
        }
        &:hover .icon-wrap {
          opacity: 1;
        }
      }
      .answer-wrap {
        display: flex;
        align-items: flex-start;
        .deepseek-icon {
          width: 36px;
          height: 36px;
          border: 1px solid $--color-primary;
          border-radius: 50%;
          margin-right: 10px;
        }
      }
      .think {
        color: #8f91a8;
        padding-left: 10px;
        border-left: 1px solid #8f91a8;
        margin-top: 10px;
        transition: all 0.4s ease;
      }
      .status-wrap {
        color: #8f91a8;
      }
    }
    .input-wrap {
      flex-shrink: 0;
      box-shadow: 0 0 0 0.5px rgba(192, 192, 192, 0.3);
      background-color: white;
      flex-direction: column;
      justify-content: flex-start;
      align-items: flex-start;
      padding: 10px;
      display: flex;
      overflow: hidden;
      border-radius: 12px;
      width: calc(100% - 10px);
      margin: 16px 10px 0 0;
      .el-textarea {
        ::v-deep .el-textarea__inner {
          resize: none;
          background-color: transparent;
          border-color: transparent;
          // color: white;
        }
      }

      .ask-btn {
        align-self: flex-end;
        font-weight: bold;
        font-size: 20px;
        padding: 5px;
      }
      .svg-icon {
        align-self: flex-end;
        font-size: 34px;
        color: $--color-primary;
        cursor: pointer;
      }

      .btn-wrap {
        width: 100%;
        display: flex;
        justify-content: flex-end;
      }
    }
    .chat-list .input-wrap {
      border: 1px solid $--color-primary;
      margin-bottom: 10px;
      background-color: transparentize($--color-primary, 0.9); // 原始颜色减少90%
    }
    .scroll-bottom-wrap {
      position: absolute;
      bottom: 110px;
      right: 50%;
      border: 1px solid rgb(232, 234, 242);
      border-radius: 50%;
      cursor: pointer;
      background: white;
      transition: all 0.2s ease;
      color: rgb(51, 51, 51);
      font-size: 20px;
      padding: 4px 6px;
      &:hover {
        color: $--color-primary;
        border-color: $--color-primary;
        // mix混合两种颜色。第三个可选参数 $weight 决定了混合时更倾向于哪种颜色，默认值为50%
        background: mix($--color-primary, white, 10%);
      }
    }
  }

  /* Markdown 基础样式（可自定义） */
  .markdown-body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    max-width: 100%;
    ::v-deep .hljs {
      padding: 16px;
      overflow-x: auto;
    }
  }
</style>
