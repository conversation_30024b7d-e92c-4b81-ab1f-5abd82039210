Vue.component("marquee", {
  template: `<div class="marquee-wrap" ref="marquee-wrap">
    <div class="scroll" ref="scroll">
      <div class="marquee" ref="getWidth">{{text}}</div>
    </div>
  </div>`,
  props: ["val", "scroll"],
  data: function () {
    return {
      timer: null,
    };
  },
  computed: {
    text() {
      return this.val;
    },
  },
  mounted() {
    if (this.scroll) {
      let timer = setTimeout(() => {
        this.move();
        clearTimeout(timer);
      }, 1000);
    }
  },
  watch: {
    scroll(newValue) {
      if (newValue) {
        clearInterval(this.timer);
        let timer = setTimeout(() => {
          this.move();
          clearTimeout(timer);
        }, 1000);
      } else {
        clearInterval(this.timer);
        this.$refs["scroll"].style.transform = "translateX(" + 0 + "px)";
      }
    },
  },
  methods: {
    move() {
      if (!this.$refs["marquee-wrap"]) {
        return;
      }
      let maxWidth = this.$refs["marquee-wrap"].clientWidth;
      let scroll = this.$refs["scroll"];
      let scrollWidth = scroll.clientWidth;
      let width = this.$refs["getWidth"].scrollWidth;

      if (width <= maxWidth) return;
      // let copy = this.$refs["copy"];
      // copy.innerText = this.text;
      let distance = 0;
      this.timer = setInterval(() => {
        distance -= 1;
        if (-distance >= width) {
          distance = 16;
        }
        scroll.style.transform = "translateX(" + distance + "px)";
      }, 20);
    },
  },
  beforeDestroy() {
    clearInterval(this.timer);
  },
});
