<template>
  <!--查看审核流程-->
  <el-dialog
    :title="title"
    :visible.sync="bDlgShow"
    width="450px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-steps
      direction="vertical"
      process-status="finish"
      :active="curIndex"
      class="audit"
      v-loading="loading"
    >
      <el-step
        v-for="(item, index) in auditor"
        :key="index"
        :title="item.userName"
        :status="item.status"
        @click.native="selectUser(index)"
        style="padding-bottom: 15px"
      >
        <div class="step-row-wrap" slot="description">
          <div
            class="step-row"
            v-for="(items, index) in item.description"
            :key="index"
          >
            <div class="user-wrap">
              <i
                class="el-icon-circle-check"
                style="color: green; font-size: 16px"
                v-if="items.approve"
              ></i>
              <i
                class="el-icon-circle-close"
                style="color: red; font-size: 16px"
                v-if="items.approve !== null && !items.approve"
              ></i>
              <i
                class="el-icon-question"
                style="font-size: 16px; color: #cfcfcf"
                v-if="items.approve == null && item.status !== 'success'"
              ></i>
              <i
                class="el-icon-remove-outline"
                style="font-size: 16px; color: #cfcfcf"
                v-if="items.approve == null && item.status === 'success'"
              ></i>
              审核人: <span class="user">{{ items.userName }}</span>
            </div>
            <div style="padding-left: 20px">
              {{ items.comment }}
            </div>
          </div>
        </div>
      </el-step>
      <notFundData v-if="!auditor.length">无审核链</notFundData>
    </el-steps>
  </el-dialog>
</template>

<script>
import { getApproveChain } from "@/api/info";
import notFundData from "@/components/notFundData";

export default {
  components: {
    notFundData,
  },
  data() {
    return {
      bDlgShow: false,
      auditor: [],
      title: "审核流程",
      curIndex: 0,
      loading: false,
    };
  },
  methods: {
    show(id, type) {
      this.bDlgShow = true;
      this.getChain(id, type);
    },
    handleClose() {
      this.auditor = [];
      this.curIndex = 0;
      this.bDlgShow = false;
    },
    getChain(programId, type) {
      this.loading = true;
      getApproveChain({
        programId,
        type,
      })
        .then((response) => {
          if (!response.data || !response.data.nodes) {
            this.auditor = [];
            return;
          }
          this.title =
            "审核流程" + "(" + (response.data.type ? "全签" : "单签") + ")";
          this.auditor = [];
          let aa = response.data.nodes;
          for (let i = 0; i < aa.length; i++) {
            let description = aa[i].auditors.map((item) => ({
              ...item,
              userName: item.nickname
                ? item.nickname + "(" + item.userName + ")"
                : item.userName,
            }));
            if (1 === aa[i].approve) {
              this.auditor.push({
                userName: "未审核",
                status: "wait",
                description: description,
              });
            } else if (2 === aa[i].approve) {
              this.auditor.push({
                userName: "审核中",
                status: "process ",
                description: description,
              });
            } else if (3 === aa[i].approve) {
              this.auditor.push({
                userName: "审核不通过",
                status: "error",
                description: description,
              });
            } else if (4 === aa[i].approve) {
              this.auditor.push({
                userName: "审核通过",
                status: "success",
                description: description,
              });
            } else {
              this.auditor = [];
            }
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
  },
};
</script>
<style lang="scss" scoped>
.el-steps {
  margin: 0 auto;
}
.step-row-wrap {
  max-height: 200px;
  overflow-y: auto;
}
.step-row {
  margin-bottom: 10px;
  .user-wrap {
    color: #b2bfd6;
    .user {
      color: #51b2fd;
    }
  }
}
</style>
