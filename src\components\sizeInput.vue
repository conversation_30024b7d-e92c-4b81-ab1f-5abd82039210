<template>
  <el-input
    v-model="size"
    size="small"
    class="input-with-select"
    type="number"
    @change="handleChange"
  >
    <el-select
      v-model="unit"
      slot="append"
      placeholder="请选择"
      @change="handleChange"
    >
      <el-option
        v-for="(item, index) in sizeEnum"
        :key="index"
        :label="item.label"
        :value="item.value"
      ></el-option>
    </el-select>
  </el-input>
</template>

<script>
import { sizeToBytes, bytesToSize } from "@/utils/utils";
import { SIZE_ENUM } from "@/utils/enum";
export default {
  props: {
    value: {
      type: Number | String,
      default: null
    },
    sizeEnum: {
      type: Array,
      default: () => SIZE_ENUM
    },
    defaultUnit: {
      type: String,
      default: "gb"
    }
  },
  data() {
    return {
      size: "",
      unit: ""
    };
  },
  watch: {
    value: {
      handler(newVal) {
        if (!newVal) {
          this.size = "";
          this.unit = this.defaultUnit;
        } else {
          const { value, unit } = bytesToSize(newVal);
          this.size = parseInt(value);
          this.unit = unit;
        }
      },
      immediate: true
    }
  },
  methods: {
    handleChange() {
      if (this.size && this.size >= 0) {
        const bytes = sizeToBytes(this.size, this.unit);
        // console.log('size-input', bytes);
        this.$emit("update:value", bytes);
      }else{
        this.size = 0;
        // console.log('size-input', 0);
        this.$emit("update:value", 0);
      }
    }
  }
};
</script>
<style lang="scss" scoped>
::v-deep .el-select .el-input {
  width: 80px;
}
.input-with-select ::v-deep .el-input-group__prepend {
  background-color: #fff;
}
</style>
