!function(e){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).mqtt=e()}}(function(){return function(){return function e(t,r,n){function i(s,a){if(!r[s]){if(!t[s]){var u="function"==typeof require&&require;if(!a&&u)return u(s,!0);if(o)return o(s,!0);var c=new Error("Cannot find module '"+s+"'");throw c.code="MODULE_NOT_FOUND",c}var l=r[s]={exports:{}};t[s][0].call(l.exports,function(e){return i(t[s][1][e]||e)},l,l.exports,e,t,r,n)}return r[s].exports}for(var o="function"==typeof require&&require,s=0;s<n.length;s++)i(n[s]);return i}}()({1:[function(e,t,r){(function(r,n){"use strict";var i=e("events").EventEmitter,o=e("./store"),s=e("mqtt-packet"),a=e("readable-stream").Writable,u=e("inherits"),c=e("reinterval"),l=e("./validations"),f=e("xtend"),h=e("debug")("mqttjs:client"),p=r?r.nextTick:function(e){setTimeout(e,0)},d=n.setImmediate||function(e){p(e)},g={keepalive:60,reschedulePings:!0,protocolId:"MQTT",protocolVersion:4,reconnectPeriod:1e3,connectTimeout:3e4,clean:!0,resubscribe:!0},b=["ECONNREFUSED","EADDRINUSE","ECONNRESET","ENOTFOUND"],m={0:"",1:"Unacceptable protocol version",2:"Identifier rejected",3:"Server unavailable",4:"Bad username or password",5:"Not authorized",16:"No matching subscribers",17:"No subscription existed",128:"Unspecified error",129:"Malformed Packet",130:"Protocol Error",131:"Implementation specific error",132:"Unsupported Protocol Version",133:"Client Identifier not valid",134:"Bad User Name or Password",135:"Not authorized",136:"Server unavailable",137:"Server busy",138:"Banned",139:"Server shutting down",140:"Bad authentication method",141:"Keep Alive timeout",142:"Session taken over",143:"Topic Filter invalid",144:"Topic Name invalid",145:"Packet identifier in use",146:"Packet Identifier not found",147:"Receive Maximum exceeded",148:"Topic Alias invalid",149:"Packet too large",150:"Message rate too high",151:"Quota exceeded",152:"Administrative action",153:"Payload format invalid",154:"Retain not supported",155:"QoS not supported",156:"Use another server",157:"Server moved",158:"Shared Subscriptions not supported",159:"Connection rate exceeded",160:"Maximum connect time",161:"Subscription Identifiers not supported",162:"Wildcard Subscriptions not supported"};function _(e,t,r){h("sendPacket :: packet: %O",t),h("sendPacket :: emitting `packetsend`"),e.emit("packetsend",t),h("sendPacket :: writing to stream");var n=s.writeToStream(t,e.stream,e.options);h("sendPacket :: writeToStream result %s",n),!n&&r?(h("sendPacket :: handle events on `drain` once through callback."),e.stream.once("drain",r)):r&&(h("sendPacket :: invoking cb"),r())}function y(e,t,r,n){h("storeAndSend :: store packet with cmd %s to outgoingStore",t.cmd),e.outgoingStore.put(t,function(i){if(i)return r&&r(i);n(),_(e,t,r)})}function w(e){h("nop ::",e)}function v(e,t){var r,n=this;if(!(this instanceof v))return new v(e,t);for(r in this.options=t||{},g)void 0===this.options[r]?this.options[r]=g[r]:this.options[r]=t[r];h("MqttClient :: options.protocol",t.protocol),h("MqttClient :: options.protocolVersion",t.protocolVersion),h("MqttClient :: options.username",t.username),h("MqttClient :: options.keepalive",t.keepalive),h("MqttClient :: options.reconnectPeriod",t.reconnectPeriod),h("MqttClient :: options.rejectUnauthorized",t.rejectUnauthorized),this.options.clientId="string"==typeof t.clientId?t.clientId:"mqttjs_"+Math.random().toString(16).substr(2,8),h("MqttClient :: clientId",this.options.clientId),this.options.customHandleAcks=5===t.protocolVersion&&t.customHandleAcks?t.customHandleAcks:function(){arguments[3](0)},this.streamBuilder=e,this.outgoingStore=t.outgoingStore||new o,this.incomingStore=t.incomingStore||new o,this.queueQoSZero=void 0===t.queueQoSZero||t.queueQoSZero,this._resubscribeTopics={},this.messageIdToTopic={},this.pingTimer=null,this.connected=!1,this.disconnecting=!1,this.queue=[],this.connackTimer=null,this.reconnectTimer=null,this._storeProcessing=!1,this._packetIdsDuringStoreProcessing={},this.nextId=Math.max(1,Math.floor(65535*Math.random())),this.outgoing={},this._firstConnection=!0,this.on("connect",function(){var e=this.queue;h("connect :: sending queued packets"),function t(){var r=e.shift();h("deliver :: entry %o",r);var i;r&&(i=r.packet,h("deliver :: call _sendPacket for %o",i),n._sendPacket(i,function(e){r.cb&&r.cb(e),t()}))}()}),this.on("close",function(){h("close :: connected set to `false`"),this.connected=!1,h("close :: clearing connackTimer"),clearTimeout(this.connackTimer),h("close :: clearing ping timer"),null!==n.pingTimer&&(n.pingTimer.clear(),n.pingTimer=null),h("close :: calling _setupReconnect"),this._setupReconnect()}),i.call(this),h("MqttClient :: setting up stream"),this._setupStream()}u(v,i),v.prototype._setupStream=function(){var e,t=this,r=new a,n=s.parser(this.options),i=null,o=[];function u(){if(o.length)p(c);else{var e=i;i=null,e()}}function c(){h("work :: getting next packet in queue");var e=o.shift();if(e)h("work :: packet pulled from queue"),t._handlePacket(e,u);else{h("work :: no packets in queue");var r=i;i=null,h("work :: done flag is %s",!!r),r&&r()}}if(h("_setupStream :: calling method to clear reconnect"),this._clearReconnect(),h("_setupStream :: using streamBuilder provided to client to create stream"),this.stream=this.streamBuilder(this),n.on("packet",function(e){h("parser :: on packet push to packets array."),o.push(e)}),r._write=function(e,t,r){i=r,h("writable stream :: parsing buffer"),n.parse(e),c()},h("_setupStream :: pipe stream to writable stream"),this.stream.pipe(r),this.stream.on("error",function(e){h("streamErrorHandler :: error",e.message),b.includes(e.code)?(h("streamErrorHandler :: emitting error"),t.emit("error",e)):w(e)}),this.stream.on("close",function(){var e;h("(%s)stream :: on close",t.options.clientId),(e=t.outgoing)&&(h("flushVolatile :: deleting volatile messages from the queue and setting their callbacks as error function"),Object.keys(e).forEach(function(t){e[t].volatile&&"function"==typeof e[t].cb&&(e[t].cb(new Error("Connection closed")),delete e[t])})),h("stream: emit close to MqttClient"),t.emit("close")}),h("_setupStream: sending packet `connect`"),(e=Object.create(this.options)).cmd="connect",_(this,e),n.on("error",this.emit.bind(this,"error")),this.options.properties){if(!this.options.properties.authenticationMethod&&this.options.properties.authenticationData)return t.end(()=>this.emit("error",new Error("Packet has no Authentication Method"))),this;if(this.options.properties.authenticationMethod&&this.options.authPacket&&"object"==typeof this.options.authPacket)_(this,f({cmd:"auth",reasonCode:0},this.options.authPacket))}this.stream.setMaxListeners(1e3),clearTimeout(this.connackTimer),this.connackTimer=setTimeout(function(){h("!!connectTimeout hit!! Calling _cleanUp with force `true`"),t._cleanUp(!0)},this.options.connectTimeout)},v.prototype._handlePacket=function(e,t){var r=this.options;if(5===r.protocolVersion&&r.properties&&r.properties.maximumPacketSize&&r.properties.maximumPacketSize<e.length)return this.emit("error",new Error("exceeding packets size "+e.cmd)),this.end({reasonCode:149,properties:{reasonString:"Maximum packet size was exceeded"}}),this;switch(h("_handlePacket :: emitting packetreceive"),this.emit("packetreceive",e),e.cmd){case"publish":this._handlePublish(e,t);break;case"puback":case"pubrec":case"pubcomp":case"suback":case"unsuback":this._handleAck(e),t();break;case"pubrel":this._handlePubrel(e,t);break;case"connack":this._handleConnack(e),t();break;case"pingresp":this._handlePingresp(e),t();break;case"disconnect":this._handleDisconnect(e),t()}},v.prototype._checkDisconnecting=function(e){return this.disconnecting&&(e?e(new Error("client disconnecting")):this.emit("error",new Error("client disconnecting"))),this.disconnecting},v.prototype.publish=function(e,t,r,n){var i;h("publish :: message `%s` to topic `%s`",t,e);var o=this.options;"function"==typeof r&&(n=r,r=null);if(r=f({qos:0,retain:!1,dup:!1},r),this._checkDisconnecting(n))return this;switch(i={cmd:"publish",topic:e,payload:t,qos:r.qos,retain:r.retain,messageId:this._nextId(),dup:r.dup},5===o.protocolVersion&&(i.properties=r.properties,(!o.properties&&i.properties&&i.properties.topicAlias||r.properties&&o.properties&&(r.properties.topicAlias&&o.properties.topicAliasMaximum&&r.properties.topicAlias>o.properties.topicAliasMaximum||!o.properties.topicAliasMaximum&&r.properties.topicAlias))&&delete i.properties.topicAlias),h("publish :: qos",r.qos),r.qos){case 1:case 2:this.outgoing[i.messageId]={volatile:!1,cb:n||w},this._storeProcessing?(h("_storeProcessing enabled"),this._packetIdsDuringStoreProcessing[i.messageId]=!1,this._storePacket(i,void 0,r.cbStorePut)):(h("MqttClient:publish: packet cmd: %s",i.cmd),this._sendPacket(i,void 0,r.cbStorePut));break;default:this._storeProcessing?(h("_storeProcessing enabled"),this._storePacket(i,n,r.cbStorePut)):(h("MqttClient:publish: packet cmd: %s",i.cmd),this._sendPacket(i,n,r.cbStorePut))}return this},v.prototype.subscribe=function(){for(var e,t=new Array(arguments.length),r=0;r<arguments.length;r++)t[r]=arguments[r];var n,i=[],o=t.shift(),s=o.resubscribe,a=t.pop()||w,u=t.pop(),c=this,p=this.options.protocolVersion;if(delete o.resubscribe,"string"==typeof o&&(o=[o]),"function"!=typeof a&&(u=a,a=w),null!==(n=l.validateTopics(o)))return d(a,new Error("Invalid topic "+n)),this;if(this._checkDisconnecting(a))return h("subscribe: discconecting true"),this;var g={qos:0};if(5===p&&(g.nl=!1,g.rap=!1,g.rh=0),u=f(g,u),Array.isArray(o)?o.forEach(function(e){if(h("subscribe: array topic %s",e),!c._resubscribeTopics.hasOwnProperty(e)||c._resubscribeTopics[e].qos<u.qos||s){var t={topic:e,qos:u.qos};5===p&&(t.nl=u.nl,t.rap=u.rap,t.rh=u.rh,t.properties=u.properties),h("subscribe: pushing topic `%s` and qos `%s` to subs list",t.topic,t.qos),i.push(t)}}):Object.keys(o).forEach(function(e){if(h("subscribe: object topic %s",e),!c._resubscribeTopics.hasOwnProperty(e)||c._resubscribeTopics[e].qos<o[e].qos||s){var t={topic:e,qos:o[e].qos};5===p&&(t.nl=o[e].nl,t.rap=o[e].rap,t.rh=o[e].rh,t.properties=u.properties),h("subscribe: pushing `%s` to subs list",t),i.push(t)}}),e={cmd:"subscribe",subscriptions:i,qos:1,retain:!1,dup:!1,messageId:this._nextId()},u.properties&&(e.properties=u.properties),i.length){if(this.options.resubscribe){h("subscribe :: resubscribe true");var b=[];i.forEach(function(e){if(c.options.reconnectPeriod>0){var t={qos:e.qos};5===p&&(t.nl=e.nl||!1,t.rap=e.rap||!1,t.rh=e.rh||0,t.properties=e.properties),c._resubscribeTopics[e.topic]=t,b.push(e.topic)}}),c.messageIdToTopic[e.messageId]=b}return this.outgoing[e.messageId]={volatile:!0,cb:function(e,t){if(!e)for(var r=t.granted,n=0;n<r.length;n+=1)i[n].qos=r[n];a(e,i)}},h("subscribe :: call _sendPacket"),this._sendPacket(e),this}a(null,[])},v.prototype.unsubscribe=function(){for(var e={cmd:"unsubscribe",qos:1,messageId:this._nextId()},t=this,r=new Array(arguments.length),n=0;n<arguments.length;n++)r[n]=arguments[n];var i=r.shift(),o=r.pop()||w,s=r.pop();return"string"==typeof i&&(i=[i]),"function"!=typeof o&&(s=o,o=w),this._checkDisconnecting(o)?this:("string"==typeof i?e.unsubscriptions=[i]:Array.isArray(i)&&(e.unsubscriptions=i),this.options.resubscribe&&e.unsubscriptions.forEach(function(e){delete t._resubscribeTopics[e]}),"object"==typeof s&&s.properties&&(e.properties=s.properties),this.outgoing[e.messageId]={volatile:!0,cb:o},h("unsubscribe: call _sendPacket"),this._sendPacket(e),this)},v.prototype.end=function(e,t,r){var n=this;function i(){h("end :: (%s) :: finish :: calling _cleanUp with force %s",n.options.clientId,e),n._cleanUp(e,()=>{h("end :: finish :: calling process.nextTick on closeStores"),p(function(){h("end :: closeStores: closing incoming and outgoing stores"),n.disconnected=!0,n.incomingStore.close(function(e){n.outgoingStore.close(function(t){if(h("end :: closeStores: emitting end"),n.emit("end"),r){let n=e||t;h("end :: closeStores: invoking callback with args"),r(n)}})}),n._deferredReconnect&&n._deferredReconnect()}.bind(n))},t)}return h("end :: (%s)",this.options.clientId),null!=e&&"boolean"==typeof e||(r=t||w,t=e,e=!1,"object"!=typeof t&&(r=t,t=null,"function"!=typeof r&&(r=w))),"object"!=typeof t&&(r=t,t=null),h("end :: cb? %s",!!r),r=r||w,this.disconnecting?(r(),this):(this._clearReconnect(),this.disconnecting=!0,!e&&Object.keys(this.outgoing).length>0?(h("end :: (%s) :: calling finish in 10ms once outgoing is empty",n.options.clientId),this.once("outgoingEmpty",setTimeout.bind(null,i,10))):(h("end :: (%s) :: immediately calling finish",n.options.clientId),i()),this)},v.prototype.removeOutgoingMessage=function(e){var t=this.outgoing[e]?this.outgoing[e].cb:null;return delete this.outgoing[e],this.outgoingStore.del({messageId:e},function(){t(new Error("Message removed"))}),this},v.prototype.reconnect=function(e){h("client reconnect");var t=this,r=function(){e?(t.options.incomingStore=e.incomingStore,t.options.outgoingStore=e.outgoingStore):(t.options.incomingStore=null,t.options.outgoingStore=null),t.incomingStore=t.options.incomingStore||new o,t.outgoingStore=t.options.outgoingStore||new o,t.disconnecting=!1,t.disconnected=!1,t._deferredReconnect=null,t._reconnect()};return this.disconnecting&&!this.disconnected?this._deferredReconnect=r:r(),this},v.prototype._reconnect=function(){h("_reconnect: emitting reconnect to client"),this.emit("reconnect"),this.connected?(this.end(()=>{this._setupStream()}),h("client already connected. disconnecting first.")):(h("_reconnect: calling _setupStream"),this._setupStream())},v.prototype._setupReconnect=function(){var e=this;!e.disconnecting&&!e.reconnectTimer&&e.options.reconnectPeriod>0?(this.reconnecting||(h("_setupReconnect :: emit `offline` state"),this.emit("offline"),h("_setupReconnect :: set `reconnecting` to `true`"),this.reconnecting=!0),h("_setupReconnect :: setting reconnectTimer for %d ms",e.options.reconnectPeriod),e.reconnectTimer=setInterval(function(){h("reconnectTimer :: reconnect triggered!"),e._reconnect()},e.options.reconnectPeriod)):h("_setupReconnect :: doing nothing...")},v.prototype._clearReconnect=function(){h("_clearReconnect : clearing reconnect timer"),this.reconnectTimer&&(clearInterval(this.reconnectTimer),this.reconnectTimer=null)},v.prototype._cleanUp=function(e,t){var r,n=arguments[2];if(t&&(h("_cleanUp :: done callback provided for on stream close"),this.stream.on("close",t)),h("_cleanUp :: forced? %s",e),e)0===this.options.reconnectPeriod&&this.options.clean&&(r=this.outgoing)&&(h("flush: queue exists? %b",!!r),Object.keys(r).forEach(function(e){"function"==typeof r[e].cb&&(r[e].cb(new Error("Connection closed")),delete r[e])})),h("_cleanUp :: (%s) :: destroying stream",this.options.clientId),this.stream.destroy();else{var i=f({cmd:"disconnect"},n);h("_cleanUp :: (%s) :: call _sendPacket with disconnect packet",this.options.clientId),this._sendPacket(i,d.bind(null,this.stream.end.bind(this.stream)))}this.disconnecting||(h("_cleanUp :: client not disconnecting. Clearing and resetting reconnect."),this._clearReconnect(),this._setupReconnect()),null!==this.pingTimer&&(h("_cleanUp :: clearing pingTimer"),this.pingTimer.clear(),this.pingTimer=null),t&&!this.connected&&(h("_cleanUp :: (%s) :: removing stream `done` callback `close` listener",this.options.clientId),this.stream.removeListener("close",t),t())},v.prototype._sendPacket=function(e,t,r){if(h("_sendPacket :: (%s) ::  start",this.options.clientId),r=r||w,!this.connected)return h("_sendPacket :: client not connected. Storing packet offline."),void this._storePacket(e,t,r);switch(this._shiftPingInterval(),e.cmd){case"publish":break;case"pubrel":return void y(this,e,t,r);default:return void _(this,e,t)}switch(e.qos){case 2:case 1:y(this,e,t,r);break;case 0:default:_(this,e,t)}h("_sendPacket :: (%s) ::  end",this.options.clientId)},v.prototype._storePacket=function(e,t,r){h("_storePacket :: packet: %o",e),h("_storePacket :: cb? %s",!!t),r=r||w,0===(e.qos||0)&&this.queueQoSZero||"publish"!==e.cmd?this.queue.push({packet:e,cb:t}):e.qos>0?(t=this.outgoing[e.messageId]?this.outgoing[e.messageId].cb:null,this.outgoingStore.put(e,function(e){if(e)return t&&t(e);r()})):t&&t(new Error("No connection to broker"))},v.prototype._setupPingTimer=function(){h("_setupPingTimer :: keepalive %d (seconds)",this.options.keepalive);var e=this;!this.pingTimer&&this.options.keepalive&&(this.pingResp=!0,this.pingTimer=c(function(){e._checkPing()},1e3*this.options.keepalive))},v.prototype._shiftPingInterval=function(){this.pingTimer&&this.options.keepalive&&this.options.reschedulePings&&this.pingTimer.reschedule(1e3*this.options.keepalive)},v.prototype._checkPing=function(){h("_checkPing :: checking ping..."),this.pingResp?(h("_checkPing :: ping response received. Clearing flag and sending `pingreq`"),this.pingResp=!1,this._sendPacket({cmd:"pingreq"})):(h("_checkPing :: calling _cleanUp with force true"),this._cleanUp(!0))},v.prototype._handlePingresp=function(){this.pingResp=!0},v.prototype._handleConnack=function(e){h("_handleConnack");var t=this.options,r=5===t.protocolVersion?e.reasonCode:e.returnCode;if(clearTimeout(this.connackTimer),e.properties&&(e.properties.topicAliasMaximum&&(t.properties||(t.properties={}),t.properties.topicAliasMaximum=e.properties.topicAliasMaximum),e.properties.serverKeepAlive&&t.keepalive&&(t.keepalive=e.properties.serverKeepAlive,this._shiftPingInterval()),e.properties.maximumPacketSize&&(t.properties||(t.properties={}),t.properties.maximumPacketSize=e.properties.maximumPacketSize)),0===r)this.reconnecting=!1,this._onConnect(e);else if(r>0){var n=new Error("Connection refused: "+m[r]);n.code=r,this.emit("error",n)}},v.prototype._handlePublish=function(e,t){h("_handlePublish: packet %o",e),t=void 0!==t?t:w;var r=e.topic.toString(),n=e.payload,i=e.qos,o=e.messageId,s=this,a=this.options,u=[0,16,128,131,135,144,145,151,153];switch(h("_handlePublish: qos %d",i),i){case 2:a.customHandleAcks(r,n,e,function(r,n){return r instanceof Error||(n=r,r=null),r?s.emit("error",r):-1===u.indexOf(n)?s.emit("error",new Error("Wrong reason code for pubrec")):void(n?s._sendPacket({cmd:"pubrec",messageId:o,reasonCode:n},t):s.incomingStore.put(e,function(){s._sendPacket({cmd:"pubrec",messageId:o},t)}))});break;case 1:a.customHandleAcks(r,n,e,function(i,a){return i instanceof Error||(a=i,i=null),i?s.emit("error",i):-1===u.indexOf(a)?s.emit("error",new Error("Wrong reason code for puback")):(a||s.emit("message",r,n,e),void s.handleMessage(e,function(e){if(e)return t&&t(e);s._sendPacket({cmd:"puback",messageId:o,reasonCode:a},t)}))});break;case 0:this.emit("message",r,n,e),this.handleMessage(e,t);break;default:h("_handlePublish: unknown QoS. Doing nothing.")}},v.prototype.handleMessage=function(e,t){t()},v.prototype._handleAck=function(e){var t,r=e.messageId,n=e.cmd,i=null,o=this.outgoing[r]?this.outgoing[r].cb:null,s=this;if(o){switch(h("_handleAck :: packet type",n),n){case"pubcomp":case"puback":var a=e.reasonCode;a&&a>0&&16!==a&&((t=new Error("Publish error: "+m[a])).code=a,o(t,e)),delete this.outgoing[r],this.outgoingStore.del(e,o);break;case"pubrec":i={cmd:"pubrel",qos:2,messageId:r};var u=e.reasonCode;u&&u>0&&16!==u?((t=new Error("Publish error: "+m[u])).code=u,o(t,e)):this._sendPacket(i);break;case"suback":delete this.outgoing[r];for(var c=0;c<e.granted.length;c++)if(0!=(128&e.granted[c])){var l=this.messageIdToTopic[r];l&&l.forEach(function(e){delete s._resubscribeTopics[e]})}o(null,e);break;case"unsuback":delete this.outgoing[r],o(null);break;default:s.emit("error",new Error("unrecognized packet type"))}this.disconnecting&&0===Object.keys(this.outgoing).length&&this.emit("outgoingEmpty")}else h("_handleAck :: Server sent an ack in error. Ignoring.")},v.prototype._handlePubrel=function(e,t){h("handling pubrel packet"),t=void 0!==t?t:w;var r=this,n={cmd:"pubcomp",messageId:e.messageId};r.incomingStore.get(e,function(e,i){e?r._sendPacket(n,t):(r.emit("message",i.topic,i.payload,i),r.handleMessage(i,function(e){if(e)return t(e);r.incomingStore.del(i,w),r._sendPacket(n,t)}))})},v.prototype._handleDisconnect=function(e){this.emit("disconnect",e)},v.prototype._nextId=function(){var e=this.nextId++;return 65536===this.nextId&&(this.nextId=1),e},v.prototype.getLastMessageId=function(){return 1===this.nextId?65535:this.nextId-1},v.prototype._resubscribe=function(e){h("_resubscribe");var t=Object.keys(this._resubscribeTopics);if(!this._firstConnection&&(this.options.clean||5===this.options.protocolVersion&&!e.sessionPresent)&&t.length>0)if(this.options.resubscribe)if(5===this.options.protocolVersion){h("_resubscribe: protocolVersion 5");for(var r=0;r<t.length;r++){var n={};n[t[r]]=this._resubscribeTopics[t[r]],n.resubscribe=!0,this.subscribe(n,{properties:n[t[r]].properties})}}else this._resubscribeTopics.resubscribe=!0,this.subscribe(this._resubscribeTopics);else this._resubscribeTopics={};this._firstConnection=!1},v.prototype._onConnect=function(e){if(this.disconnected)this.emit("connect",e);else{var t=this;this._setupPingTimer(),this._resubscribe(e),this.connected=!0,function r(){var n=t.outgoingStore.createStream();function i(){t._storeProcessing=!1,t._packetIdsDuringStoreProcessing={}}function o(){n.destroy(),n=null,i()}t.once("close",o),n.on("error",function(e){i(),t.removeListener("close",o),t.emit("error",e)}),n.on("end",function(){var n=!0;for(var s in t._packetIdsDuringStoreProcessing)if(!t._packetIdsDuringStoreProcessing[s]){n=!1;break}n?(i(),t.removeListener("close",o),t.emit("connect",e)):r()}),function e(){if(n){t._storeProcessing=!0;var r,i=n.read(1);i?t._packetIdsDuringStoreProcessing[i.messageId]?e():t.disconnecting||t.reconnectTimer?n.destroy&&n.destroy():(r=t.outgoing[i.messageId]?t.outgoing[i.messageId].cb:null,t.outgoing[i.messageId]={volatile:!1,cb:function(t,n){r&&r(t,n),e()}},t._packetIdsDuringStoreProcessing[i.messageId]=!0,t._sendPacket(i)):n.once("readable",e)}}()}()}},t.exports=v}).call(this,e("_process"),"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./store":7,"./validations":8,_process:44,debug:14,events:27,inherits:29,"mqtt-packet":34,"readable-stream":59,reinterval:60,xtend:68}],2:[function(e,t,r){(function(r){"use strict";var n,i,o,s=e("readable-stream").Transform,a=e("duplexify"),u=!1;t.exports=function(e,t){if(t.hostname=t.hostname||t.host,!t.hostname)throw new Error("Could not determine host. Specify host manually.");var c="MQIsdp"===t.protocolId&&3===t.protocolVersion?"mqttv3.1":"mqtt";!function(e){e.hostname||(e.hostname="localhost"),e.path||(e.path="/"),e.wsOptions||(e.wsOptions={})}(t);var l=function(e,t){var r="alis"===e.protocol?"wss":"ws",n=r+"://"+e.hostname+e.path;return e.port&&80!==e.port&&443!==e.port&&(n=r+"://"+e.hostname+":"+e.port+e.path),"function"==typeof e.transformWsUrl&&(n=e.transformWsUrl(n,e,t)),n}(t,e);return(n=t.my).connectSocket({url:l,protocols:c}),i=function(){var e=new s;return e._write=function(e,t,r){n.sendSocketMessage({data:e.buffer,success:function(){r()},fail:function(){r(new Error)}})},e._flush=function(e){n.closeSocket({success:function(){e()}})},e}(),o=a.obj(),u||(u=!0,n.onSocketOpen(function(){o.setReadable(i),o.setWritable(i),o.emit("connect")}),n.onSocketMessage(function(e){if("string"==typeof e.data){var t=r.from(e.data,"base64");i.push(t)}else{var n=new FileReader;n.addEventListener("load",function(){var e=n.result;e=e instanceof ArrayBuffer?r.from(e):r.from(e,"utf8"),i.push(e)}),n.readAsArrayBuffer(e.data)}}),n.onSocketClose(function(){o.end(),o.destroy()}),n.onSocketError(function(e){o.destroy(e)})),o}}).call(this,e("buffer").Buffer)},{buffer:12,duplexify:16,"readable-stream":59}],3:[function(e,t,r){"use strict";var n=e("net"),i=e("debug")("mqttjs:tcp");t.exports=function(e,t){var r,o;return t.port=t.port||1883,t.hostname=t.hostname||t.host||"localhost",r=t.port,o=t.hostname,i("port %d and host %s",r,o),n.createConnection(r,o)}},{debug:14,net:11}],4:[function(e,t,r){"use strict";var n=e("tls"),i=e("debug")("mqttjs:tls");t.exports=function(e,t){var r;function o(n){t.rejectUnauthorized&&e.emit("error",n),r.end()}return t.port=t.port||8883,t.host=t.hostname||t.host||"localhost",t.servername=t.host,t.rejectUnauthorized=!1!==t.rejectUnauthorized,delete t.path,i("port %d host %s rejectUnauthorized %b",t.port,t.host,t.rejectUnauthorized),(r=n.connect(t)).on("secureConnect",function(){t.rejectUnauthorized&&!r.authorized?r.emit("error",new Error("TLS not authorized")):r.removeListener("error",o)}),r.on("error",o),r}},{debug:14,tls:11}],5:[function(e,t,r){(function(r,n){"use strict";const i=e("ws"),o=e("debug")("mqttjs:ws"),s=e("duplexify"),a=e("readable-stream").Transform;let u=["rejectUnauthorized","ca","cert","key","pfx","passphrase"];const c=void 0!==r&&"browser"===r.title||"function"==typeof __webpack_require__;function l(e,t){let r=e.protocol+"://"+e.hostname+":"+e.port+e.path;return"function"==typeof e.transformWsUrl&&(r=e.transformWsUrl(r,e,t)),r}function f(e){let t=e;return e.hostname||(t.hostname="localhost"),e.port||("wss"===e.protocol?t.port=443:t.port=80),e.path||(t.path="/"),e.wsOptions||(t.wsOptions={}),c||"wss"!==e.protocol||u.forEach(function(r){e.hasOwnProperty(r)&&!e.wsOptions.hasOwnProperty(r)&&(t.wsOptions[r]=e[r])}),t}t.exports=c?function(e,t){let r;o("browserStreamBuilder");const i=function(e){let t=f(e);if(t.hostname||(t.hostname=t.host),!t.hostname){if("undefined"==typeof document)throw new Error("Could not determine host. Specify host manually.");const e=new URL(document.URL);t.hostname=e.hostname,t.port||(t.port=e.port)}return void 0===t.objectMode&&(t.objectMode=!(!0===t.binary||void 0===t.binary)),t}(t).browserBufferSize||524288,u=t.browserBufferTimeout||1e3,c=!t.objectMode;let h=function(e,t){const r="MQIsdp"===t.protocolId&&3===t.protocolVersion?"mqttv3.1":"mqtt";let n=l(t,e),i=new WebSocket(n,[r]);return i.binaryType="arraybuffer",i}(e,t),p=function(e,t,r){let n=new a({objectModeMode:e.objectMode});return n._write=t,n._flush=r,n}(t,function e(t,r,o){h.bufferedAmount>i&&setTimeout(e,u,t,r,o),c&&"string"==typeof t&&(t=n.from(t,"utf8"));try{h.send(t)}catch(e){return o(e)}o()},function(e){h.close(),e()});t.objectMode||(p._writev=y),p.on("close",()=>{h.close()});const d=void 0===h.addEventListener;function g(){r.setReadable(p),r.setWritable(p),r.emit("connect")}function b(){r.end(),r.destroy()}function m(e){r.destroy(e)}function _(e){let t=e.data;t=t instanceof ArrayBuffer?n.from(t):n.from(t,"utf8"),p.push(t)}function y(e,t){const r=new Array(e.length);for(let t=0;t<e.length;t++)"string"==typeof e[t].chunk?r[t]=n.from(e[t],"utf8"):r[t]=e[t].chunk;this._write(n.concat(r),"binary",t)}return h.readyState===h.OPEN?r=p:(r=r=s(void 0,void 0,t),t.objectMode||(r._writev=y),d?h.addEventListener("open",g):h.onopen=g),r.socket=h,d?(h.addEventListener("close",b),h.addEventListener("error",m),h.addEventListener("message",_)):(h.onclose=b,h.onerror=m,h.onmessage=_),r}:function(e,t){o("streamBuilder");let r=f(t);const n=l(r,e);let s=function(e,t,r){o("createWebSocket"),o("protocol: "+r.protocolId+" "+r.protocolVersion);const n="MQIsdp"===r.protocolId&&3===r.protocolVersion?"mqttv3.1":"mqtt";return o("creating new Websocket for url: "+t+" and protocol: "+n),new i(t,[n],r.wsOptions)}(0,n,r),a=i.createWebSocketStream(s,r.wsOptions);return a.url=n,a}}).call(this,e("_process"),e("buffer").Buffer)},{_process:44,buffer:12,debug:14,duplexify:16,"readable-stream":59,ws:67}],6:[function(e,t,r){(function(r){"use strict";var n,i,o,s=e("readable-stream").Transform,a=e("duplexify");t.exports=function(e,t){if(t.hostname=t.hostname||t.host,!t.hostname)throw new Error("Could not determine host. Specify host manually.");var u="MQIsdp"===t.protocolId&&3===t.protocolVersion?"mqttv3.1":"mqtt";!function(e){e.hostname||(e.hostname="localhost"),e.path||(e.path="/"),e.wsOptions||(e.wsOptions={})}(t);var c=function(e,t){var r="wxs"===e.protocol?"wss":"ws",n=r+"://"+e.hostname+e.path;return e.port&&80!==e.port&&443!==e.port&&(n=r+"://"+e.hostname+":"+e.port+e.path),"function"==typeof e.transformWsUrl&&(n=e.transformWsUrl(n,e,t)),n}(t,e);n=wx.connectSocket({url:c,protocols:[u]}),i=function(){var e=new s;return e._write=function(e,t,r){n.send({data:e.buffer,success:function(){r()},fail:function(e){r(new Error(e))}})},e._flush=function(e){n.close({success:function(){e()}})},e}(),(o=a.obj())._destroy=function(e,t){n.close({success:function(){t&&t(e)}})};var l=o.destroy;return o.destroy=function(){o.destroy=l;var e=this;setTimeout(function(){n.close({fail:function(){e._destroy(new Error)}})},0)}.bind(o),n.onOpen(function(){o.setReadable(i),o.setWritable(i),o.emit("connect")}),n.onMessage(function(e){var t=e.data;t=t instanceof ArrayBuffer?r.from(t):r.from(t,"utf8"),i.push(t)}),n.onClose(function(){o.end(),o.destroy()}),n.onError(function(e){o.destroy(new Error(e.errMsg))}),o}}).call(this,e("buffer").Buffer)},{buffer:12,duplexify:16,"readable-stream":59}],7:[function(e,t,r){"use strict";var n=e("xtend"),i=e("readable-stream").Readable,o={objectMode:!0},s={clean:!0};function a(e){if(!(this instanceof a))return new a(e);this.options=e||{},this.options=n(s,e),this._inflights=new Map}a.prototype.put=function(e,t){return this._inflights.set(e.messageId,e),t&&t(),this},a.prototype.createStream=function(){var e=new i(o),t=!1,r=[],n=0;return this._inflights.forEach(function(e,t){r.push(e)}),e._read=function(){!t&&n<r.length?this.push(r[n++]):this.push(null)},e.destroy=function(){if(!t){var e=this;t=!0,setTimeout(function(){e.emit("close")},0)}},e},a.prototype.del=function(e,t){return(e=this._inflights.get(e.messageId))?(this._inflights.delete(e.messageId),t(null,e)):t&&t(new Error("missing packet")),this},a.prototype.get=function(e,t){return(e=this._inflights.get(e.messageId))?t(null,e):t&&t(new Error("missing packet")),this},a.prototype.close=function(e){this.options.clean&&(this._inflights=null),e&&e()},t.exports=a},{"readable-stream":59,xtend:68}],8:[function(e,t,r){"use strict";function n(e){for(var t=e.split("/"),r=0;r<t.length;r++)if("+"!==t[r]){if("#"===t[r])return r===t.length-1;if(-1!==t[r].indexOf("+")||-1!==t[r].indexOf("#"))return!1}return!0}t.exports={validateTopics:function(e){if(0===e.length)return"empty_topic_list";for(var t=0;t<e.length;t++)if(!n(e[t]))return e[t];return null}}},{}],9:[function(e,t,r){(function(r){"use strict";var n=e("../client"),i=e("../store"),o=e("debug")("mqttjs"),s={};function a(e,t){if(o("connecting to an MQTT broker..."),"object"!=typeof e||t||(t=e,e=null),t=t||{},e){if(null===t.protocol)throw new Error("Missing protocol");var r=new URL(e);t.hash=r.hash,t.host=r.host,t.hostname=r.hostname,t.href=r.href,t.origin=r.origin,t.pathname=r.pathname,t.port=Number(r.port)||null,t.protocol=r.protocol,t.username=t.username||r.username||null,t.password=t.password||r.password||null,t.search=r.search,t.searchParams=r.searchParams,t.path=r.pathname+r.search,t.protocol=t.protocol.replace(/:$/,"")}if(function(e){var t;e.auth&&((t=e.auth.match(/^(.+):(.+)$/))?(e.username=t[1],e.password=t[2]):e.username=e.auth)}(t),t.searchParams&&"string"==typeof t.searchParams.get("clientId")&&(t.clientId=t.searchParams.get("clientId")),t.query&&"string"==typeof t.query.clientId&&(t.clientId=t.query.clientId),t.cert&&t.key){if(!t.protocol)throw new Error("Missing secure protocol key");if(-1===["mqtts","wss","wxs","alis"].indexOf(t.protocol))switch(t.protocol){case"mqtt":t.protocol="mqtts";break;case"ws":t.protocol="wss";break;case"wx":t.protocol="wxs";break;case"ali":t.protocol="alis";break;default:throw new Error('Unknown protocol for secure connection: "'+t.protocol+'"!')}}if(!s[t.protocol]){var i=-1!==["mqtts","wss"].indexOf(t.protocol);t.protocol=["mqtt","mqtts","ws","wss","wx","wxs","ali","alis"].filter(function(e,t){return(!i||t%2!=0)&&"function"==typeof s[e]})[0]}if(!1===t.clean&&!t.clientId)throw new Error("Missing clientId for unclean clients");t.protocol&&(t.defaultProtocol=t.protocol);var a=new n(function(e){return t.servers&&(e._reconnectCount&&e._reconnectCount!==t.servers.length||(e._reconnectCount=0),t.host=t.servers[e._reconnectCount].host,t.port=t.servers[e._reconnectCount].port,t.protocol=t.servers[e._reconnectCount].protocol?t.servers[e._reconnectCount].protocol:t.defaultProtocol,t.hostname=t.host,e._reconnectCount++),o("calling streambuilder for",t.protocol),s[t.protocol](e,t)},t);return a.on("error",function(){}),a}void 0!==r&&"browser"!==r.title||"function"!=typeof __webpack_require__?(s.mqtt=e("./tcp"),s.tcp=e("./tcp"),s.ssl=e("./tls"),s.tls=e("./tls"),s.mqtts=e("./tls")):(s.wx=e("./wx"),s.wxs=e("./wx"),s.ali=e("./ali"),s.alis=e("./ali")),s.ws=e("./ws"),s.wss=e("./ws"),t.exports=a,t.exports.connect=a,t.exports.MqttClient=n,t.exports.Store=i}).call(this,e("_process"))},{"../client":1,"../store":7,"./ali":2,"./tcp":3,"./tls":4,"./ws":5,"./wx":6,_process:44,debug:14}],10:[function(e,t,r){"use strict";r.byteLength=function(e){var t=c(e),r=t[0],n=t[1];return 3*(r+n)/4-n},r.toByteArray=function(e){var t,r,n=c(e),s=n[0],a=n[1],u=new o(function(e,t,r){return 3*(t+r)/4-r}(0,s,a)),l=0,f=a>0?s-4:s;for(r=0;r<f;r+=4)t=i[e.charCodeAt(r)]<<18|i[e.charCodeAt(r+1)]<<12|i[e.charCodeAt(r+2)]<<6|i[e.charCodeAt(r+3)],u[l++]=t>>16&255,u[l++]=t>>8&255,u[l++]=255&t;2===a&&(t=i[e.charCodeAt(r)]<<2|i[e.charCodeAt(r+1)]>>4,u[l++]=255&t);1===a&&(t=i[e.charCodeAt(r)]<<10|i[e.charCodeAt(r+1)]<<4|i[e.charCodeAt(r+2)]>>2,u[l++]=t>>8&255,u[l++]=255&t);return u},r.fromByteArray=function(e){for(var t,r=e.length,i=r%3,o=[],s=0,a=r-i;s<a;s+=16383)o.push(l(e,s,s+16383>a?a:s+16383));1===i?(t=e[r-1],o.push(n[t>>2]+n[t<<4&63]+"==")):2===i&&(t=(e[r-2]<<8)+e[r-1],o.push(n[t>>10]+n[t>>4&63]+n[t<<2&63]+"="));return o.join("")};for(var n=[],i=[],o="undefined"!=typeof Uint8Array?Uint8Array:Array,s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",a=0,u=s.length;a<u;++a)n[a]=s[a],i[s.charCodeAt(a)]=a;function c(e){var t=e.length;if(t%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var r=e.indexOf("=");return-1===r&&(r=t),[r,r===t?0:4-r%4]}function l(e,t,r){for(var i,o,s=[],a=t;a<r;a+=3)i=(e[a]<<16&16711680)+(e[a+1]<<8&65280)+(255&e[a+2]),s.push(n[(o=i)>>18&63]+n[o>>12&63]+n[o>>6&63]+n[63&o]);return s.join("")}i["-".charCodeAt(0)]=62,i["_".charCodeAt(0)]=63},{}],11:[function(e,t,r){},{}],12:[function(e,t,r){(function(t){"use strict";var n=e("base64-js"),i=e("ieee754");r.Buffer=t,r.SlowBuffer=function(e){+e!=e&&(e=0);return t.alloc(+e)},r.INSPECT_MAX_BYTES=50;var o=2147483647;function s(e){if(e>o)throw new RangeError('The value "'+e+'" is invalid for option "size"');var r=new Uint8Array(e);return r.__proto__=t.prototype,r}function t(e,t,r){if("number"==typeof e){if("string"==typeof t)throw new TypeError('The "string" argument must be of type string. Received type number');return c(e)}return a(e,t,r)}function a(e,r,n){if("string"==typeof e)return function(e,r){"string"==typeof r&&""!==r||(r="utf8");if(!t.isEncoding(r))throw new TypeError("Unknown encoding: "+r);var n=0|h(e,r),i=s(n),o=i.write(e,r);o!==n&&(i=i.slice(0,o));return i}(e,r);if(ArrayBuffer.isView(e))return l(e);if(null==e)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e);if(q(e,ArrayBuffer)||e&&q(e.buffer,ArrayBuffer))return function(e,r,n){if(r<0||e.byteLength<r)throw new RangeError('"offset" is outside of buffer bounds');if(e.byteLength<r+(n||0))throw new RangeError('"length" is outside of buffer bounds');var i;i=void 0===r&&void 0===n?new Uint8Array(e):void 0===n?new Uint8Array(e,r):new Uint8Array(e,r,n);return i.__proto__=t.prototype,i}(e,r,n);if("number"==typeof e)throw new TypeError('The "value" argument must not be of type number. Received type number');var i=e.valueOf&&e.valueOf();if(null!=i&&i!==e)return t.from(i,r,n);var o=function(e){if(t.isBuffer(e)){var r=0|f(e.length),n=s(r);return 0===n.length?n:(e.copy(n,0,0,r),n)}if(void 0!==e.length)return"number"!=typeof e.length||D(e.length)?s(0):l(e);if("Buffer"===e.type&&Array.isArray(e.data))return l(e.data)}(e);if(o)return o;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof e[Symbol.toPrimitive])return t.from(e[Symbol.toPrimitive]("string"),r,n);throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e)}function u(e){if("number"!=typeof e)throw new TypeError('"size" argument must be of type number');if(e<0)throw new RangeError('The value "'+e+'" is invalid for option "size"')}function c(e){return u(e),s(e<0?0:0|f(e))}function l(e){for(var t=e.length<0?0:0|f(e.length),r=s(t),n=0;n<t;n+=1)r[n]=255&e[n];return r}function f(e){if(e>=o)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+o.toString(16)+" bytes");return 0|e}function h(e,r){if(t.isBuffer(e))return e.length;if(ArrayBuffer.isView(e)||q(e,ArrayBuffer))return e.byteLength;if("string"!=typeof e)throw new TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof e);var n=e.length,i=arguments.length>2&&!0===arguments[2];if(!i&&0===n)return 0;for(var o=!1;;)switch(r){case"ascii":case"latin1":case"binary":return n;case"utf8":case"utf-8":return j(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*n;case"hex":return n>>>1;case"base64":return N(e).length;default:if(o)return i?-1:j(e).length;r=(""+r).toLowerCase(),o=!0}}function p(e,t,r){var n=e[t];e[t]=e[r],e[r]=n}function d(e,r,n,i,o){if(0===e.length)return-1;if("string"==typeof n?(i=n,n=0):n>2147483647?n=2147483647:n<-2147483648&&(n=-2147483648),D(n=+n)&&(n=o?0:e.length-1),n<0&&(n=e.length+n),n>=e.length){if(o)return-1;n=e.length-1}else if(n<0){if(!o)return-1;n=0}if("string"==typeof r&&(r=t.from(r,i)),t.isBuffer(r))return 0===r.length?-1:g(e,r,n,i,o);if("number"==typeof r)return r&=255,"function"==typeof Uint8Array.prototype.indexOf?o?Uint8Array.prototype.indexOf.call(e,r,n):Uint8Array.prototype.lastIndexOf.call(e,r,n):g(e,[r],n,i,o);throw new TypeError("val must be string, number or Buffer")}function g(e,t,r,n,i){var o,s=1,a=e.length,u=t.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(e.length<2||t.length<2)return-1;s=2,a/=2,u/=2,r/=2}function c(e,t){return 1===s?e[t]:e.readUInt16BE(t*s)}if(i){var l=-1;for(o=r;o<a;o++)if(c(e,o)===c(t,-1===l?0:o-l)){if(-1===l&&(l=o),o-l+1===u)return l*s}else-1!==l&&(o-=o-l),l=-1}else for(r+u>a&&(r=a-u),o=r;o>=0;o--){for(var f=!0,h=0;h<u;h++)if(c(e,o+h)!==c(t,h)){f=!1;break}if(f)return o}return-1}function b(e,t,r,n){r=Number(r)||0;var i=e.length-r;n?(n=Number(n))>i&&(n=i):n=i;var o=t.length;n>o/2&&(n=o/2);for(var s=0;s<n;++s){var a=parseInt(t.substr(2*s,2),16);if(D(a))return s;e[r+s]=a}return s}function m(e,t,r,n){return U(j(t,e.length-r),e,r,n)}function _(e,t,r,n){return U(function(e){for(var t=[],r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}(t),e,r,n)}function y(e,t,r,n){return _(e,t,r,n)}function w(e,t,r,n){return U(N(t),e,r,n)}function v(e,t,r,n){return U(function(e,t){for(var r,n,i,o=[],s=0;s<e.length&&!((t-=2)<0);++s)r=e.charCodeAt(s),n=r>>8,i=r%256,o.push(i),o.push(n);return o}(t,e.length-r),e,r,n)}function S(e,t,r){return 0===t&&r===e.length?n.fromByteArray(e):n.fromByteArray(e.slice(t,r))}function k(e,t,r){r=Math.min(e.length,r);for(var n=[],i=t;i<r;){var o,s,a,u,c=e[i],l=null,f=c>239?4:c>223?3:c>191?2:1;if(i+f<=r)switch(f){case 1:c<128&&(l=c);break;case 2:128==(192&(o=e[i+1]))&&(u=(31&c)<<6|63&o)>127&&(l=u);break;case 3:o=e[i+1],s=e[i+2],128==(192&o)&&128==(192&s)&&(u=(15&c)<<12|(63&o)<<6|63&s)>2047&&(u<55296||u>57343)&&(l=u);break;case 4:o=e[i+1],s=e[i+2],a=e[i+3],128==(192&o)&&128==(192&s)&&128==(192&a)&&(u=(15&c)<<18|(63&o)<<12|(63&s)<<6|63&a)>65535&&u<1114112&&(l=u)}null===l?(l=65533,f=1):l>65535&&(l-=65536,n.push(l>>>10&1023|55296),l=56320|1023&l),n.push(l),i+=f}return function(e){var t=e.length;if(t<=E)return String.fromCharCode.apply(String,e);var r="",n=0;for(;n<t;)r+=String.fromCharCode.apply(String,e.slice(n,n+=E));return r}(n)}r.kMaxLength=o,t.TYPED_ARRAY_SUPPORT=function(){try{var e=new Uint8Array(1);return e.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===e.foo()}catch(e){return!1}}(),t.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(t.prototype,"parent",{enumerable:!0,get:function(){if(t.isBuffer(this))return this.buffer}}),Object.defineProperty(t.prototype,"offset",{enumerable:!0,get:function(){if(t.isBuffer(this))return this.byteOffset}}),"undefined"!=typeof Symbol&&null!=Symbol.species&&t[Symbol.species]===t&&Object.defineProperty(t,Symbol.species,{value:null,configurable:!0,enumerable:!1,writable:!1}),t.poolSize=8192,t.from=function(e,t,r){return a(e,t,r)},t.prototype.__proto__=Uint8Array.prototype,t.__proto__=Uint8Array,t.alloc=function(e,t,r){return function(e,t,r){return u(e),e<=0?s(e):void 0!==t?"string"==typeof r?s(e).fill(t,r):s(e).fill(t):s(e)}(e,t,r)},t.allocUnsafe=function(e){return c(e)},t.allocUnsafeSlow=function(e){return c(e)},t.isBuffer=function(e){return null!=e&&!0===e._isBuffer&&e!==t.prototype},t.compare=function(e,r){if(q(e,Uint8Array)&&(e=t.from(e,e.offset,e.byteLength)),q(r,Uint8Array)&&(r=t.from(r,r.offset,r.byteLength)),!t.isBuffer(e)||!t.isBuffer(r))throw new TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(e===r)return 0;for(var n=e.length,i=r.length,o=0,s=Math.min(n,i);o<s;++o)if(e[o]!==r[o]){n=e[o],i=r[o];break}return n<i?-1:i<n?1:0},t.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},t.concat=function(e,r){if(!Array.isArray(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return t.alloc(0);var n;if(void 0===r)for(r=0,n=0;n<e.length;++n)r+=e[n].length;var i=t.allocUnsafe(r),o=0;for(n=0;n<e.length;++n){var s=e[n];if(q(s,Uint8Array)&&(s=t.from(s)),!t.isBuffer(s))throw new TypeError('"list" argument must be an Array of Buffers');s.copy(i,o),o+=s.length}return i},t.byteLength=h,t.prototype._isBuffer=!0,t.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)p(this,t,t+1);return this},t.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)p(this,t,t+3),p(this,t+1,t+2);return this},t.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)p(this,t,t+7),p(this,t+1,t+6),p(this,t+2,t+5),p(this,t+3,t+4);return this},t.prototype.toString=function(){var e=this.length;return 0===e?"":0===arguments.length?k(this,0,e):function(e,t,r){var n=!1;if((void 0===t||t<0)&&(t=0),t>this.length)return"";if((void 0===r||r>this.length)&&(r=this.length),r<=0)return"";if((r>>>=0)<=(t>>>=0))return"";for(e||(e="utf8");;)switch(e){case"hex":return T(this,t,r);case"utf8":case"utf-8":return k(this,t,r);case"ascii":return I(this,t,r);case"latin1":case"binary":return C(this,t,r);case"base64":return S(this,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return R(this,t,r);default:if(n)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),n=!0}}.apply(this,arguments)},t.prototype.toLocaleString=t.prototype.toString,t.prototype.equals=function(e){if(!t.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e||0===t.compare(this,e)},t.prototype.inspect=function(){var e="",t=r.INSPECT_MAX_BYTES;return e=this.toString("hex",0,t).replace(/(.{2})/g,"$1 ").trim(),this.length>t&&(e+=" ... "),"<Buffer "+e+">"},t.prototype.compare=function(e,r,n,i,o){if(q(e,Uint8Array)&&(e=t.from(e,e.offset,e.byteLength)),!t.isBuffer(e))throw new TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof e);if(void 0===r&&(r=0),void 0===n&&(n=e?e.length:0),void 0===i&&(i=0),void 0===o&&(o=this.length),r<0||n>e.length||i<0||o>this.length)throw new RangeError("out of range index");if(i>=o&&r>=n)return 0;if(i>=o)return-1;if(r>=n)return 1;if(r>>>=0,n>>>=0,i>>>=0,o>>>=0,this===e)return 0;for(var s=o-i,a=n-r,u=Math.min(s,a),c=this.slice(i,o),l=e.slice(r,n),f=0;f<u;++f)if(c[f]!==l[f]){s=c[f],a=l[f];break}return s<a?-1:a<s?1:0},t.prototype.includes=function(e,t,r){return-1!==this.indexOf(e,t,r)},t.prototype.indexOf=function(e,t,r){return d(this,e,t,r,!0)},t.prototype.lastIndexOf=function(e,t,r){return d(this,e,t,r,!1)},t.prototype.write=function(e,t,r,n){if(void 0===t)n="utf8",r=this.length,t=0;else if(void 0===r&&"string"==typeof t)n=t,r=this.length,t=0;else{if(!isFinite(t))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");t>>>=0,isFinite(r)?(r>>>=0,void 0===n&&(n="utf8")):(n=r,r=void 0)}var i=this.length-t;if((void 0===r||r>i)&&(r=i),e.length>0&&(r<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var o=!1;;)switch(n){case"hex":return b(this,e,t,r);case"utf8":case"utf-8":return m(this,e,t,r);case"ascii":return _(this,e,t,r);case"latin1":case"binary":return y(this,e,t,r);case"base64":return w(this,e,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return v(this,e,t,r);default:if(o)throw new TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),o=!0}},t.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var E=4096;function I(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(127&e[i]);return n}function C(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(e[i]);return n}function T(e,t,r){var n=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>n)&&(r=n);for(var i="",o=t;o<r;++o)i+=L(e[o]);return i}function R(e,t,r){for(var n=e.slice(t,r),i="",o=0;o<n.length;o+=2)i+=String.fromCharCode(n[o]+256*n[o+1]);return i}function x(e,t,r){if(e%1!=0||e<0)throw new RangeError("offset is not uint");if(e+t>r)throw new RangeError("Trying to access beyond buffer length")}function A(e,r,n,i,o,s){if(!t.isBuffer(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(r>o||r<s)throw new RangeError('"value" argument is out of bounds');if(n+i>e.length)throw new RangeError("Index out of range")}function M(e,t,r,n,i,o){if(r+n>e.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("Index out of range")}function P(e,t,r,n,o){return t=+t,r>>>=0,o||M(e,0,r,4),i.write(e,t,r,n,23,4),r+4}function O(e,t,r,n,o){return t=+t,r>>>=0,o||M(e,0,r,8),i.write(e,t,r,n,52,8),r+8}t.prototype.slice=function(e,r){var n=this.length;e=~~e,r=void 0===r?n:~~r,e<0?(e+=n)<0&&(e=0):e>n&&(e=n),r<0?(r+=n)<0&&(r=0):r>n&&(r=n),r<e&&(r=e);var i=this.subarray(e,r);return i.__proto__=t.prototype,i},t.prototype.readUIntLE=function(e,t,r){e>>>=0,t>>>=0,r||x(e,t,this.length);for(var n=this[e],i=1,o=0;++o<t&&(i*=256);)n+=this[e+o]*i;return n},t.prototype.readUIntBE=function(e,t,r){e>>>=0,t>>>=0,r||x(e,t,this.length);for(var n=this[e+--t],i=1;t>0&&(i*=256);)n+=this[e+--t]*i;return n},t.prototype.readUInt8=function(e,t){return e>>>=0,t||x(e,1,this.length),this[e]},t.prototype.readUInt16LE=function(e,t){return e>>>=0,t||x(e,2,this.length),this[e]|this[e+1]<<8},t.prototype.readUInt16BE=function(e,t){return e>>>=0,t||x(e,2,this.length),this[e]<<8|this[e+1]},t.prototype.readUInt32LE=function(e,t){return e>>>=0,t||x(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},t.prototype.readUInt32BE=function(e,t){return e>>>=0,t||x(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},t.prototype.readIntLE=function(e,t,r){e>>>=0,t>>>=0,r||x(e,t,this.length);for(var n=this[e],i=1,o=0;++o<t&&(i*=256);)n+=this[e+o]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*t)),n},t.prototype.readIntBE=function(e,t,r){e>>>=0,t>>>=0,r||x(e,t,this.length);for(var n=t,i=1,o=this[e+--n];n>0&&(i*=256);)o+=this[e+--n]*i;return o>=(i*=128)&&(o-=Math.pow(2,8*t)),o},t.prototype.readInt8=function(e,t){return e>>>=0,t||x(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},t.prototype.readInt16LE=function(e,t){e>>>=0,t||x(e,2,this.length);var r=this[e]|this[e+1]<<8;return 32768&r?4294901760|r:r},t.prototype.readInt16BE=function(e,t){e>>>=0,t||x(e,2,this.length);var r=this[e+1]|this[e]<<8;return 32768&r?4294901760|r:r},t.prototype.readInt32LE=function(e,t){return e>>>=0,t||x(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},t.prototype.readInt32BE=function(e,t){return e>>>=0,t||x(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},t.prototype.readFloatLE=function(e,t){return e>>>=0,t||x(e,4,this.length),i.read(this,e,!0,23,4)},t.prototype.readFloatBE=function(e,t){return e>>>=0,t||x(e,4,this.length),i.read(this,e,!1,23,4)},t.prototype.readDoubleLE=function(e,t){return e>>>=0,t||x(e,8,this.length),i.read(this,e,!0,52,8)},t.prototype.readDoubleBE=function(e,t){return e>>>=0,t||x(e,8,this.length),i.read(this,e,!1,52,8)},t.prototype.writeUIntLE=function(e,t,r,n){(e=+e,t>>>=0,r>>>=0,n)||A(this,e,t,r,Math.pow(2,8*r)-1,0);var i=1,o=0;for(this[t]=255&e;++o<r&&(i*=256);)this[t+o]=e/i&255;return t+r},t.prototype.writeUIntBE=function(e,t,r,n){(e=+e,t>>>=0,r>>>=0,n)||A(this,e,t,r,Math.pow(2,8*r)-1,0);var i=r-1,o=1;for(this[t+i]=255&e;--i>=0&&(o*=256);)this[t+i]=e/o&255;return t+r},t.prototype.writeUInt8=function(e,t,r){return e=+e,t>>>=0,r||A(this,e,t,1,255,0),this[t]=255&e,t+1},t.prototype.writeUInt16LE=function(e,t,r){return e=+e,t>>>=0,r||A(this,e,t,2,65535,0),this[t]=255&e,this[t+1]=e>>>8,t+2},t.prototype.writeUInt16BE=function(e,t,r){return e=+e,t>>>=0,r||A(this,e,t,2,65535,0),this[t]=e>>>8,this[t+1]=255&e,t+2},t.prototype.writeUInt32LE=function(e,t,r){return e=+e,t>>>=0,r||A(this,e,t,4,4294967295,0),this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e,t+4},t.prototype.writeUInt32BE=function(e,t,r){return e=+e,t>>>=0,r||A(this,e,t,4,4294967295,0),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},t.prototype.writeIntLE=function(e,t,r,n){if(e=+e,t>>>=0,!n){var i=Math.pow(2,8*r-1);A(this,e,t,r,i-1,-i)}var o=0,s=1,a=0;for(this[t]=255&e;++o<r&&(s*=256);)e<0&&0===a&&0!==this[t+o-1]&&(a=1),this[t+o]=(e/s>>0)-a&255;return t+r},t.prototype.writeIntBE=function(e,t,r,n){if(e=+e,t>>>=0,!n){var i=Math.pow(2,8*r-1);A(this,e,t,r,i-1,-i)}var o=r-1,s=1,a=0;for(this[t+o]=255&e;--o>=0&&(s*=256);)e<0&&0===a&&0!==this[t+o+1]&&(a=1),this[t+o]=(e/s>>0)-a&255;return t+r},t.prototype.writeInt8=function(e,t,r){return e=+e,t>>>=0,r||A(this,e,t,1,127,-128),e<0&&(e=255+e+1),this[t]=255&e,t+1},t.prototype.writeInt16LE=function(e,t,r){return e=+e,t>>>=0,r||A(this,e,t,2,32767,-32768),this[t]=255&e,this[t+1]=e>>>8,t+2},t.prototype.writeInt16BE=function(e,t,r){return e=+e,t>>>=0,r||A(this,e,t,2,32767,-32768),this[t]=e>>>8,this[t+1]=255&e,t+2},t.prototype.writeInt32LE=function(e,t,r){return e=+e,t>>>=0,r||A(this,e,t,4,2147483647,-2147483648),this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24,t+4},t.prototype.writeInt32BE=function(e,t,r){return e=+e,t>>>=0,r||A(this,e,t,4,2147483647,-2147483648),e<0&&(e=4294967295+e+1),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},t.prototype.writeFloatLE=function(e,t,r){return P(this,e,t,!0,r)},t.prototype.writeFloatBE=function(e,t,r){return P(this,e,t,!1,r)},t.prototype.writeDoubleLE=function(e,t,r){return O(this,e,t,!0,r)},t.prototype.writeDoubleBE=function(e,t,r){return O(this,e,t,!1,r)},t.prototype.copy=function(e,r,n,i){if(!t.isBuffer(e))throw new TypeError("argument should be a Buffer");if(n||(n=0),i||0===i||(i=this.length),r>=e.length&&(r=e.length),r||(r=0),i>0&&i<n&&(i=n),i===n)return 0;if(0===e.length||0===this.length)return 0;if(r<0)throw new RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw new RangeError("Index out of range");if(i<0)throw new RangeError("sourceEnd out of bounds");i>this.length&&(i=this.length),e.length-r<i-n&&(i=e.length-r+n);var o=i-n;if(this===e&&"function"==typeof Uint8Array.prototype.copyWithin)this.copyWithin(r,n,i);else if(this===e&&n<r&&r<i)for(var s=o-1;s>=0;--s)e[s+r]=this[s+n];else Uint8Array.prototype.set.call(e,this.subarray(n,i),r);return o},t.prototype.fill=function(e,r,n,i){if("string"==typeof e){if("string"==typeof r?(i=r,r=0,n=this.length):"string"==typeof n&&(i=n,n=this.length),void 0!==i&&"string"!=typeof i)throw new TypeError("encoding must be a string");if("string"==typeof i&&!t.isEncoding(i))throw new TypeError("Unknown encoding: "+i);if(1===e.length){var o=e.charCodeAt(0);("utf8"===i&&o<128||"latin1"===i)&&(e=o)}}else"number"==typeof e&&(e&=255);if(r<0||this.length<r||this.length<n)throw new RangeError("Out of range index");if(n<=r)return this;var s;if(r>>>=0,n=void 0===n?this.length:n>>>0,e||(e=0),"number"==typeof e)for(s=r;s<n;++s)this[s]=e;else{var a=t.isBuffer(e)?e:t.from(e,i),u=a.length;if(0===u)throw new TypeError('The value "'+e+'" is invalid for argument "value"');for(s=0;s<n-r;++s)this[s+r]=a[s%u]}return this};var B=/[^+/0-9A-Za-z-_]/g;function L(e){return e<16?"0"+e.toString(16):e.toString(16)}function j(e,t){var r;t=t||1/0;for(var n=e.length,i=null,o=[],s=0;s<n;++s){if((r=e.charCodeAt(s))>55295&&r<57344){if(!i){if(r>56319){(t-=3)>-1&&o.push(239,191,189);continue}if(s+1===n){(t-=3)>-1&&o.push(239,191,189);continue}i=r;continue}if(r<56320){(t-=3)>-1&&o.push(239,191,189),i=r;continue}r=65536+(i-55296<<10|r-56320)}else i&&(t-=3)>-1&&o.push(239,191,189);if(i=null,r<128){if((t-=1)<0)break;o.push(r)}else if(r<2048){if((t-=2)<0)break;o.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;o.push(r>>12|224,r>>6&63|128,63&r|128)}else{if(!(r<1114112))throw new Error("Invalid code point");if((t-=4)<0)break;o.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}}return o}function N(e){return n.toByteArray(function(e){if((e=(e=e.split("=")[0]).trim().replace(B,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function U(e,t,r,n){for(var i=0;i<n&&!(i+r>=t.length||i>=e.length);++i)t[i+r]=e[i];return i}function q(e,t){return e instanceof t||null!=e&&null!=e.constructor&&null!=e.constructor.name&&e.constructor.name===t.name}function D(e){return e!=e}}).call(this,e("buffer").Buffer)},{"base64-js":10,buffer:12,ieee754:28}],13:[function(e,t,r){(function(e){function t(e){return Object.prototype.toString.call(e)}r.isArray=function(e){return Array.isArray?Array.isArray(e):"[object Array]"===t(e)},r.isBoolean=function(e){return"boolean"==typeof e},r.isNull=function(e){return null===e},r.isNullOrUndefined=function(e){return null==e},r.isNumber=function(e){return"number"==typeof e},r.isString=function(e){return"string"==typeof e},r.isSymbol=function(e){return"symbol"==typeof e},r.isUndefined=function(e){return void 0===e},r.isRegExp=function(e){return"[object RegExp]"===t(e)},r.isObject=function(e){return"object"==typeof e&&null!==e},r.isDate=function(e){return"[object Date]"===t(e)},r.isError=function(e){return"[object Error]"===t(e)||e instanceof Error},r.isFunction=function(e){return"function"==typeof e},r.isPrimitive=function(e){return null===e||"boolean"==typeof e||"number"==typeof e||"string"==typeof e||"symbol"==typeof e||void 0===e},r.isBuffer=e.isBuffer}).call(this,{isBuffer:e("../../is-buffer/index.js")})},{"../../is-buffer/index.js":30}],14:[function(e,t,r){(function(n){r.log=function(...e){return"object"==typeof console&&console.log&&console.log(...e)},r.formatArgs=function(e){if(e[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+e[0]+(this.useColors?"%c ":" ")+"+"+t.exports.humanize(this.diff),!this.useColors)return;const r="color: "+this.color;e.splice(1,0,r,"color: inherit");let n=0,i=0;e[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&"%c"===e&&(i=++n)}),e.splice(i,0,r)},r.save=function(e){try{e?r.storage.setItem("debug",e):r.storage.removeItem("debug")}catch(e){}},r.load=function(){let e;try{e=r.storage.getItem("debug")}catch(e){}!e&&void 0!==n&&"env"in n&&(e=n.env.DEBUG);return e},r.useColors=function(){if("undefined"!=typeof window&&window.process&&("renderer"===window.process.type||window.process.__nwjs))return!0;if("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;return"undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)},r.storage=function(){try{return localStorage}catch(e){}}(),r.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.exports=e("./common")(r);const{formatters:i}=t.exports;i.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}}).call(this,e("_process"))},{"./common":15,_process:44}],15:[function(e,t,r){t.exports=function(t){function r(e){let t=0;for(let r=0;r<e.length;r++)t=(t<<5)-t+e.charCodeAt(r),t|=0;return n.colors[Math.abs(t)%n.colors.length]}function n(e){let t;function s(...e){if(!s.enabled)return;const r=s,i=Number(new Date),o=i-(t||i);r.diff=o,r.prev=t,r.curr=i,t=i,e[0]=n.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let a=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(t,i)=>{if("%%"===t)return t;a++;const o=n.formatters[i];if("function"==typeof o){const n=e[a];t=o.call(r,n),e.splice(a,1),a--}return t}),n.formatArgs.call(r,e),(r.log||n.log).apply(r,e)}return s.namespace=e,s.enabled=n.enabled(e),s.useColors=n.useColors(),s.color=r(e),s.destroy=i,s.extend=o,"function"==typeof n.init&&n.init(s),n.instances.push(s),s}function i(){const e=n.instances.indexOf(this);return-1!==e&&(n.instances.splice(e,1),!0)}function o(e,t){const r=n(this.namespace+(void 0===t?":":t)+e);return r.log=this.log,r}function s(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,"*")}return n.debug=n,n.default=n,n.coerce=function(e){return e instanceof Error?e.stack||e.message:e},n.disable=function(){const e=[...n.names.map(s),...n.skips.map(s).map(e=>"-"+e)].join(",");return n.enable(""),e},n.enable=function(e){let t;n.save(e),n.names=[],n.skips=[];const r=("string"==typeof e?e:"").split(/[\s,]+/),i=r.length;for(t=0;t<i;t++)r[t]&&("-"===(e=r[t].replace(/\*/g,".*?"))[0]?n.skips.push(new RegExp("^"+e.substr(1)+"$")):n.names.push(new RegExp("^"+e+"$")));for(t=0;t<n.instances.length;t++){const e=n.instances[t];e.enabled=n.enabled(e.namespace)}},n.enabled=function(e){if("*"===e[e.length-1])return!0;let t,r;for(t=0,r=n.skips.length;t<r;t++)if(n.skips[t].test(e))return!1;for(t=0,r=n.names.length;t<r;t++)if(n.names[t].test(e))return!0;return!1},n.humanize=e("ms"),Object.keys(t).forEach(e=>{n[e]=t[e]}),n.instances=[],n.names=[],n.skips=[],n.formatters={},n.selectColor=r,n.enable(n.load()),n}},{ms:41}],16:[function(e,t,r){(function(r,n){var i=e("readable-stream"),o=e("end-of-stream"),s=e("inherits"),a=e("stream-shift"),u=n.from&&n.from!==Uint8Array.from?n.from([0]):new n([0]),c=function(e,t){e._corked?e.once("uncork",t):t()},l=function(e,t){return function(r){r?function(e,t){e._autoDestroy&&e.destroy(t)}(e,"premature close"===r.message?null:r):t&&!e._ended&&e.end()}},f=function(e,t,r){if(!(this instanceof f))return new f(e,t,r);i.Duplex.call(this,r),this._writable=null,this._readable=null,this._readable2=null,this._autoDestroy=!r||!1!==r.autoDestroy,this._forwardDestroy=!r||!1!==r.destroy,this._forwardEnd=!r||!1!==r.end,this._corked=1,this._ondrain=null,this._drained=!1,this._forwarding=!1,this._unwrite=null,this._unread=null,this._ended=!1,this.destroyed=!1,e&&this.setWritable(e),t&&this.setReadable(t)};s(f,i.Duplex),f.obj=function(e,t,r){return r||(r={}),r.objectMode=!0,r.highWaterMark=16,new f(e,t,r)},f.prototype.cork=function(){1==++this._corked&&this.emit("cork")},f.prototype.uncork=function(){this._corked&&0==--this._corked&&this.emit("uncork")},f.prototype.setWritable=function(e){if(this._unwrite&&this._unwrite(),this.destroyed)e&&e.destroy&&e.destroy();else if(null!==e&&!1!==e){var t=this,n=o(e,{writable:!0,readable:!1},l(this,this._forwardEnd)),i=function(){var e=t._ondrain;t._ondrain=null,e&&e()};this._unwrite&&r.nextTick(i),this._writable=e,this._writable.on("drain",i),this._unwrite=function(){t._writable.removeListener("drain",i),n()},this.uncork()}else this.end()},f.prototype.setReadable=function(e){if(this._unread&&this._unread(),this.destroyed)e&&e.destroy&&e.destroy();else{if(null===e||!1===e)return this.push(null),void this.resume();var t,r=this,n=o(e,{writable:!1,readable:!0},l(this)),s=function(){r._forward()},a=function(){r.push(null)};this._drained=!0,this._readable=e,this._readable2=e._readableState?e:(t=e,new i.Readable({objectMode:!0,highWaterMark:16}).wrap(t)),this._readable2.on("readable",s),this._readable2.on("end",a),this._unread=function(){r._readable2.removeListener("readable",s),r._readable2.removeListener("end",a),n()},this._forward()}},f.prototype._read=function(){this._drained=!0,this._forward()},f.prototype._forward=function(){if(!this._forwarding&&this._readable2&&this._drained){var e;for(this._forwarding=!0;this._drained&&null!==(e=a(this._readable2));)this.destroyed||(this._drained=this.push(e));this._forwarding=!1}},f.prototype.destroy=function(e){if(!this.destroyed){this.destroyed=!0;var t=this;r.nextTick(function(){t._destroy(e)})}},f.prototype._destroy=function(e){if(e){var t=this._ondrain;this._ondrain=null,t?t(e):this.emit("error",e)}this._forwardDestroy&&(this._readable&&this._readable.destroy&&this._readable.destroy(),this._writable&&this._writable.destroy&&this._writable.destroy()),this.emit("close")},f.prototype._write=function(e,t,r){return this.destroyed?r():this._corked?c(this,this._write.bind(this,e,t,r)):e===u?this._finish(r):this._writable?void(!1===this._writable.write(e)?this._ondrain=r:r()):r()},f.prototype._finish=function(e){var t=this;this.emit("preend"),c(this,function(){var r,n;r=t._forwardEnd&&t._writable,n=function(){!1===t._writableState.prefinished&&(t._writableState.prefinished=!0),t.emit("prefinish"),c(t,e)},r?r._writableState&&r._writableState.finished?n():r._writableState?r.end(n):(r.end(),n()):n()})},f.prototype.end=function(e,t,r){return"function"==typeof e?this.end(null,null,e):"function"==typeof t?this.end(e,null,t):(this._ended=!0,e&&this.write(e),this._writableState.ending||this.write(u),i.Writable.prototype.end.call(this,r))},t.exports=f}).call(this,e("_process"),e("buffer").Buffer)},{_process:44,buffer:12,"end-of-stream":26,inherits:29,"readable-stream":25,"stream-shift":62}],17:[function(e,t,r){"use strict";var n=e("process-nextick-args"),i=Object.keys||function(e){var t=[];for(var r in e)t.push(r);return t};t.exports=f;var o=Object.create(e("core-util-is"));o.inherits=e("inherits");var s=e("./_stream_readable"),a=e("./_stream_writable");o.inherits(f,s);for(var u=i(a.prototype),c=0;c<u.length;c++){var l=u[c];f.prototype[l]||(f.prototype[l]=a.prototype[l])}function f(e){if(!(this instanceof f))return new f(e);s.call(this,e),a.call(this,e),e&&!1===e.readable&&(this.readable=!1),e&&!1===e.writable&&(this.writable=!1),this.allowHalfOpen=!0,e&&!1===e.allowHalfOpen&&(this.allowHalfOpen=!1),this.once("end",h)}function h(){this.allowHalfOpen||this._writableState.ended||n.nextTick(p,this)}function p(e){e.end()}Object.defineProperty(f.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),Object.defineProperty(f.prototype,"destroyed",{get:function(){return void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed&&this._writableState.destroyed)},set:function(e){void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed=e,this._writableState.destroyed=e)}}),f.prototype._destroy=function(e,t){this.push(null),this.end(),n.nextTick(t,e)}},{"./_stream_readable":19,"./_stream_writable":21,"core-util-is":13,inherits:29,"process-nextick-args":43}],18:[function(e,t,r){"use strict";t.exports=o;var n=e("./_stream_transform"),i=Object.create(e("core-util-is"));function o(e){if(!(this instanceof o))return new o(e);n.call(this,e)}i.inherits=e("inherits"),i.inherits(o,n),o.prototype._transform=function(e,t,r){r(null,e)}},{"./_stream_transform":20,"core-util-is":13,inherits:29}],19:[function(e,t,r){(function(r,n){"use strict";var i=e("process-nextick-args");t.exports=y;var o,s=e("isarray");y.ReadableState=_;e("events").EventEmitter;var a=function(e,t){return e.listeners(t).length},u=e("./internal/streams/stream"),c=e("safe-buffer").Buffer,l=n.Uint8Array||function(){};var f=Object.create(e("core-util-is"));f.inherits=e("inherits");var h=e("util"),p=void 0;p=h&&h.debuglog?h.debuglog("stream"):function(){};var d,g=e("./internal/streams/BufferList"),b=e("./internal/streams/destroy");f.inherits(y,u);var m=["error","close","destroy","pause","resume"];function _(t,r){o=o||e("./_stream_duplex"),t=t||{};var n=r instanceof o;this.objectMode=!!t.objectMode,n&&(this.objectMode=this.objectMode||!!t.readableObjectMode);var i=t.highWaterMark,s=t.readableHighWaterMark,a=this.objectMode?16:16384;this.highWaterMark=i||0===i?i:n&&(s||0===s)?s:a,this.highWaterMark=Math.floor(this.highWaterMark),this.buffer=new g,this.length=0,this.pipes=null,this.pipesCount=0,this.flowing=null,this.ended=!1,this.endEmitted=!1,this.reading=!1,this.sync=!0,this.needReadable=!1,this.emittedReadable=!1,this.readableListening=!1,this.resumeScheduled=!1,this.destroyed=!1,this.defaultEncoding=t.defaultEncoding||"utf8",this.awaitDrain=0,this.readingMore=!1,this.decoder=null,this.encoding=null,t.encoding&&(d||(d=e("string_decoder/").StringDecoder),this.decoder=new d(t.encoding),this.encoding=t.encoding)}function y(t){if(o=o||e("./_stream_duplex"),!(this instanceof y))return new y(t);this._readableState=new _(t,this),this.readable=!0,t&&("function"==typeof t.read&&(this._read=t.read),"function"==typeof t.destroy&&(this._destroy=t.destroy)),u.call(this)}function w(e,t,r,n,i){var o,s=e._readableState;null===t?(s.reading=!1,function(e,t){if(t.ended)return;if(t.decoder){var r=t.decoder.end();r&&r.length&&(t.buffer.push(r),t.length+=t.objectMode?1:r.length)}t.ended=!0,E(e)}(e,s)):(i||(o=function(e,t){var r;n=t,c.isBuffer(n)||n instanceof l||"string"==typeof t||void 0===t||e.objectMode||(r=new TypeError("Invalid non-string/buffer chunk"));var n;return r}(s,t)),o?e.emit("error",o):s.objectMode||t&&t.length>0?("string"==typeof t||s.objectMode||Object.getPrototypeOf(t)===c.prototype||(t=function(e){return c.from(e)}(t)),n?s.endEmitted?e.emit("error",new Error("stream.unshift() after end event")):v(e,s,t,!0):s.ended?e.emit("error",new Error("stream.push() after EOF")):(s.reading=!1,s.decoder&&!r?(t=s.decoder.write(t),s.objectMode||0!==t.length?v(e,s,t,!1):C(e,s)):v(e,s,t,!1))):n||(s.reading=!1));return function(e){return!e.ended&&(e.needReadable||e.length<e.highWaterMark||0===e.length)}(s)}function v(e,t,r,n){t.flowing&&0===t.length&&!t.sync?(e.emit("data",r),e.read(0)):(t.length+=t.objectMode?1:r.length,n?t.buffer.unshift(r):t.buffer.push(r),t.needReadable&&E(e)),C(e,t)}Object.defineProperty(y.prototype,"destroyed",{get:function(){return void 0!==this._readableState&&this._readableState.destroyed},set:function(e){this._readableState&&(this._readableState.destroyed=e)}}),y.prototype.destroy=b.destroy,y.prototype._undestroy=b.undestroy,y.prototype._destroy=function(e,t){this.push(null),t(e)},y.prototype.push=function(e,t){var r,n=this._readableState;return n.objectMode?r=!0:"string"==typeof e&&((t=t||n.defaultEncoding)!==n.encoding&&(e=c.from(e,t),t=""),r=!0),w(this,e,t,!1,r)},y.prototype.unshift=function(e){return w(this,e,null,!0,!1)},y.prototype.isPaused=function(){return!1===this._readableState.flowing},y.prototype.setEncoding=function(t){return d||(d=e("string_decoder/").StringDecoder),this._readableState.decoder=new d(t),this._readableState.encoding=t,this};var S=8388608;function k(e,t){return e<=0||0===t.length&&t.ended?0:t.objectMode?1:e!=e?t.flowing&&t.length?t.buffer.head.data.length:t.length:(e>t.highWaterMark&&(t.highWaterMark=function(e){return e>=S?e=S:(e--,e|=e>>>1,e|=e>>>2,e|=e>>>4,e|=e>>>8,e|=e>>>16,e++),e}(e)),e<=t.length?e:t.ended?t.length:(t.needReadable=!0,0))}function E(e){var t=e._readableState;t.needReadable=!1,t.emittedReadable||(p("emitReadable",t.flowing),t.emittedReadable=!0,t.sync?i.nextTick(I,e):I(e))}function I(e){p("emit readable"),e.emit("readable"),A(e)}function C(e,t){t.readingMore||(t.readingMore=!0,i.nextTick(T,e,t))}function T(e,t){for(var r=t.length;!t.reading&&!t.flowing&&!t.ended&&t.length<t.highWaterMark&&(p("maybeReadMore read 0"),e.read(0),r!==t.length);)r=t.length;t.readingMore=!1}function R(e){p("readable nexttick read 0"),e.read(0)}function x(e,t){t.reading||(p("resume read 0"),e.read(0)),t.resumeScheduled=!1,t.awaitDrain=0,e.emit("resume"),A(e),t.flowing&&!t.reading&&e.read(0)}function A(e){var t=e._readableState;for(p("flow",t.flowing);t.flowing&&null!==e.read(););}function M(e,t){return 0===t.length?null:(t.objectMode?r=t.buffer.shift():!e||e>=t.length?(r=t.decoder?t.buffer.join(""):1===t.buffer.length?t.buffer.head.data:t.buffer.concat(t.length),t.buffer.clear()):r=function(e,t,r){var n;e<t.head.data.length?(n=t.head.data.slice(0,e),t.head.data=t.head.data.slice(e)):n=e===t.head.data.length?t.shift():r?function(e,t){var r=t.head,n=1,i=r.data;e-=i.length;for(;r=r.next;){var o=r.data,s=e>o.length?o.length:e;if(s===o.length?i+=o:i+=o.slice(0,e),0===(e-=s)){s===o.length?(++n,r.next?t.head=r.next:t.head=t.tail=null):(t.head=r,r.data=o.slice(s));break}++n}return t.length-=n,i}(e,t):function(e,t){var r=c.allocUnsafe(e),n=t.head,i=1;n.data.copy(r),e-=n.data.length;for(;n=n.next;){var o=n.data,s=e>o.length?o.length:e;if(o.copy(r,r.length-e,0,s),0===(e-=s)){s===o.length?(++i,n.next?t.head=n.next:t.head=t.tail=null):(t.head=n,n.data=o.slice(s));break}++i}return t.length-=i,r}(e,t);return n}(e,t.buffer,t.decoder),r);var r}function P(e){var t=e._readableState;if(t.length>0)throw new Error('"endReadable()" called on non-empty stream');t.endEmitted||(t.ended=!0,i.nextTick(O,t,e))}function O(e,t){e.endEmitted||0!==e.length||(e.endEmitted=!0,t.readable=!1,t.emit("end"))}function B(e,t){for(var r=0,n=e.length;r<n;r++)if(e[r]===t)return r;return-1}y.prototype.read=function(e){p("read",e),e=parseInt(e,10);var t=this._readableState,r=e;if(0!==e&&(t.emittedReadable=!1),0===e&&t.needReadable&&(t.length>=t.highWaterMark||t.ended))return p("read: emitReadable",t.length,t.ended),0===t.length&&t.ended?P(this):E(this),null;if(0===(e=k(e,t))&&t.ended)return 0===t.length&&P(this),null;var n,i=t.needReadable;return p("need readable",i),(0===t.length||t.length-e<t.highWaterMark)&&p("length less than watermark",i=!0),t.ended||t.reading?p("reading or ended",i=!1):i&&(p("do read"),t.reading=!0,t.sync=!0,0===t.length&&(t.needReadable=!0),this._read(t.highWaterMark),t.sync=!1,t.reading||(e=k(r,t))),null===(n=e>0?M(e,t):null)?(t.needReadable=!0,e=0):t.length-=e,0===t.length&&(t.ended||(t.needReadable=!0),r!==e&&t.ended&&P(this)),null!==n&&this.emit("data",n),n},y.prototype._read=function(e){this.emit("error",new Error("_read() is not implemented"))},y.prototype.pipe=function(e,t){var n=this,o=this._readableState;switch(o.pipesCount){case 0:o.pipes=e;break;case 1:o.pipes=[o.pipes,e];break;default:o.pipes.push(e)}o.pipesCount+=1,p("pipe count=%d opts=%j",o.pipesCount,t);var u=(!t||!1!==t.end)&&e!==r.stdout&&e!==r.stderr?l:y;function c(t,r){p("onunpipe"),t===n&&r&&!1===r.hasUnpiped&&(r.hasUnpiped=!0,p("cleanup"),e.removeListener("close",m),e.removeListener("finish",_),e.removeListener("drain",f),e.removeListener("error",b),e.removeListener("unpipe",c),n.removeListener("end",l),n.removeListener("end",y),n.removeListener("data",g),h=!0,!o.awaitDrain||e._writableState&&!e._writableState.needDrain||f())}function l(){p("onend"),e.end()}o.endEmitted?i.nextTick(u):n.once("end",u),e.on("unpipe",c);var f=function(e){return function(){var t=e._readableState;p("pipeOnDrain",t.awaitDrain),t.awaitDrain&&t.awaitDrain--,0===t.awaitDrain&&a(e,"data")&&(t.flowing=!0,A(e))}}(n);e.on("drain",f);var h=!1;var d=!1;function g(t){p("ondata"),d=!1,!1!==e.write(t)||d||((1===o.pipesCount&&o.pipes===e||o.pipesCount>1&&-1!==B(o.pipes,e))&&!h&&(p("false write response, pause",n._readableState.awaitDrain),n._readableState.awaitDrain++,d=!0),n.pause())}function b(t){p("onerror",t),y(),e.removeListener("error",b),0===a(e,"error")&&e.emit("error",t)}function m(){e.removeListener("finish",_),y()}function _(){p("onfinish"),e.removeListener("close",m),y()}function y(){p("unpipe"),n.unpipe(e)}return n.on("data",g),function(e,t,r){if("function"==typeof e.prependListener)return e.prependListener(t,r);e._events&&e._events[t]?s(e._events[t])?e._events[t].unshift(r):e._events[t]=[r,e._events[t]]:e.on(t,r)}(e,"error",b),e.once("close",m),e.once("finish",_),e.emit("pipe",n),o.flowing||(p("pipe resume"),n.resume()),e},y.prototype.unpipe=function(e){var t=this._readableState,r={hasUnpiped:!1};if(0===t.pipesCount)return this;if(1===t.pipesCount)return e&&e!==t.pipes?this:(e||(e=t.pipes),t.pipes=null,t.pipesCount=0,t.flowing=!1,e&&e.emit("unpipe",this,r),this);if(!e){var n=t.pipes,i=t.pipesCount;t.pipes=null,t.pipesCount=0,t.flowing=!1;for(var o=0;o<i;o++)n[o].emit("unpipe",this,r);return this}var s=B(t.pipes,e);return-1===s?this:(t.pipes.splice(s,1),t.pipesCount-=1,1===t.pipesCount&&(t.pipes=t.pipes[0]),e.emit("unpipe",this,r),this)},y.prototype.on=function(e,t){var r=u.prototype.on.call(this,e,t);if("data"===e)!1!==this._readableState.flowing&&this.resume();else if("readable"===e){var n=this._readableState;n.endEmitted||n.readableListening||(n.readableListening=n.needReadable=!0,n.emittedReadable=!1,n.reading?n.length&&E(this):i.nextTick(R,this))}return r},y.prototype.addListener=y.prototype.on,y.prototype.resume=function(){var e=this._readableState;return e.flowing||(p("resume"),e.flowing=!0,function(e,t){t.resumeScheduled||(t.resumeScheduled=!0,i.nextTick(x,e,t))}(this,e)),this},y.prototype.pause=function(){return p("call pause flowing=%j",this._readableState.flowing),!1!==this._readableState.flowing&&(p("pause"),this._readableState.flowing=!1,this.emit("pause")),this},y.prototype.wrap=function(e){var t=this,r=this._readableState,n=!1;for(var i in e.on("end",function(){if(p("wrapped end"),r.decoder&&!r.ended){var e=r.decoder.end();e&&e.length&&t.push(e)}t.push(null)}),e.on("data",function(i){(p("wrapped data"),r.decoder&&(i=r.decoder.write(i)),!r.objectMode||null!==i&&void 0!==i)&&((r.objectMode||i&&i.length)&&(t.push(i)||(n=!0,e.pause())))}),e)void 0===this[i]&&"function"==typeof e[i]&&(this[i]=function(t){return function(){return e[t].apply(e,arguments)}}(i));for(var o=0;o<m.length;o++)e.on(m[o],this.emit.bind(this,m[o]));return this._read=function(t){p("wrapped _read",t),n&&(n=!1,e.resume())},this},Object.defineProperty(y.prototype,"readableHighWaterMark",{enumerable:!1,get:function(){return this._readableState.highWaterMark}}),y._fromList=M}).call(this,e("_process"),"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"./_stream_duplex":17,"./internal/streams/BufferList":22,"./internal/streams/destroy":23,"./internal/streams/stream":24,_process:44,"core-util-is":13,events:27,inherits:29,isarray:31,"process-nextick-args":43,"safe-buffer":61,"string_decoder/":63,util:11}],20:[function(e,t,r){"use strict";t.exports=o;var n=e("./_stream_duplex"),i=Object.create(e("core-util-is"));function o(e){if(!(this instanceof o))return new o(e);n.call(this,e),this._transformState={afterTransform:function(e,t){var r=this._transformState;r.transforming=!1;var n=r.writecb;if(!n)return this.emit("error",new Error("write callback called multiple times"));r.writechunk=null,r.writecb=null,null!=t&&this.push(t),n(e);var i=this._readableState;i.reading=!1,(i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}.bind(this),needTransform:!1,transforming:!1,writecb:null,writechunk:null,writeencoding:null},this._readableState.needReadable=!0,this._readableState.sync=!1,e&&("function"==typeof e.transform&&(this._transform=e.transform),"function"==typeof e.flush&&(this._flush=e.flush)),this.on("prefinish",s)}function s(){var e=this;"function"==typeof this._flush?this._flush(function(t,r){a(e,t,r)}):a(this,null,null)}function a(e,t,r){if(t)return e.emit("error",t);if(null!=r&&e.push(r),e._writableState.length)throw new Error("Calling transform done when ws.length != 0");if(e._transformState.transforming)throw new Error("Calling transform done when still transforming");return e.push(null)}i.inherits=e("inherits"),i.inherits(o,n),o.prototype.push=function(e,t){return this._transformState.needTransform=!1,n.prototype.push.call(this,e,t)},o.prototype._transform=function(e,t,r){throw new Error("_transform() is not implemented")},o.prototype._write=function(e,t,r){var n=this._transformState;if(n.writecb=r,n.writechunk=e,n.writeencoding=t,!n.transforming){var i=this._readableState;(n.needTransform||i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}},o.prototype._read=function(e){var t=this._transformState;null!==t.writechunk&&t.writecb&&!t.transforming?(t.transforming=!0,this._transform(t.writechunk,t.writeencoding,t.afterTransform)):t.needTransform=!0},o.prototype._destroy=function(e,t){var r=this;n.prototype._destroy.call(this,e,function(e){t(e),r.emit("close")})}},{"./_stream_duplex":17,"core-util-is":13,inherits:29}],21:[function(e,t,r){(function(r,n,i){"use strict";var o=e("process-nextick-args");function s(e){var t=this;this.next=null,this.entry=null,this.finish=function(){!function(e,t,r){var n=e.entry;e.entry=null;for(;n;){var i=n.callback;t.pendingcb--,i(r),n=n.next}t.corkedRequestsFree?t.corkedRequestsFree.next=e:t.corkedRequestsFree=e}(t,e)}}t.exports=_;var a,u=!r.browser&&["v0.10","v0.9."].indexOf(r.version.slice(0,5))>-1?i:o.nextTick;_.WritableState=m;var c=Object.create(e("core-util-is"));c.inherits=e("inherits");var l={deprecate:e("util-deprecate")},f=e("./internal/streams/stream"),h=e("safe-buffer").Buffer,p=n.Uint8Array||function(){};var d,g=e("./internal/streams/destroy");function b(){}function m(t,r){a=a||e("./_stream_duplex"),t=t||{};var n=r instanceof a;this.objectMode=!!t.objectMode,n&&(this.objectMode=this.objectMode||!!t.writableObjectMode);var i=t.highWaterMark,c=t.writableHighWaterMark,l=this.objectMode?16:16384;this.highWaterMark=i||0===i?i:n&&(c||0===c)?c:l,this.highWaterMark=Math.floor(this.highWaterMark),this.finalCalled=!1,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1,this.destroyed=!1;var f=!1===t.decodeStrings;this.decodeStrings=!f,this.defaultEncoding=t.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=function(e){!function(e,t){var r=e._writableState,n=r.sync,i=r.writecb;if(function(e){e.writing=!1,e.writecb=null,e.length-=e.writelen,e.writelen=0}(r),t)!function(e,t,r,n,i){--t.pendingcb,r?(o.nextTick(i,n),o.nextTick(E,e,t),e._writableState.errorEmitted=!0,e.emit("error",n)):(i(n),e._writableState.errorEmitted=!0,e.emit("error",n),E(e,t))}(e,r,n,t,i);else{var s=S(r);s||r.corked||r.bufferProcessing||!r.bufferedRequest||v(e,r),n?u(w,e,r,s,i):w(e,r,s,i)}}(r,e)},this.writecb=null,this.writelen=0,this.bufferedRequest=null,this.lastBufferedRequest=null,this.pendingcb=0,this.prefinished=!1,this.errorEmitted=!1,this.bufferedRequestCount=0,this.corkedRequestsFree=new s(this)}function _(t){if(a=a||e("./_stream_duplex"),!(d.call(_,this)||this instanceof a))return new _(t);this._writableState=new m(t,this),this.writable=!0,t&&("function"==typeof t.write&&(this._write=t.write),"function"==typeof t.writev&&(this._writev=t.writev),"function"==typeof t.destroy&&(this._destroy=t.destroy),"function"==typeof t.final&&(this._final=t.final)),f.call(this)}function y(e,t,r,n,i,o,s){t.writelen=n,t.writecb=s,t.writing=!0,t.sync=!0,r?e._writev(i,t.onwrite):e._write(i,o,t.onwrite),t.sync=!1}function w(e,t,r,n){r||function(e,t){0===t.length&&t.needDrain&&(t.needDrain=!1,e.emit("drain"))}(e,t),t.pendingcb--,n(),E(e,t)}function v(e,t){t.bufferProcessing=!0;var r=t.bufferedRequest;if(e._writev&&r&&r.next){var n=t.bufferedRequestCount,i=new Array(n),o=t.corkedRequestsFree;o.entry=r;for(var a=0,u=!0;r;)i[a]=r,r.isBuf||(u=!1),r=r.next,a+=1;i.allBuffers=u,y(e,t,!0,t.length,i,"",o.finish),t.pendingcb++,t.lastBufferedRequest=null,o.next?(t.corkedRequestsFree=o.next,o.next=null):t.corkedRequestsFree=new s(t),t.bufferedRequestCount=0}else{for(;r;){var c=r.chunk,l=r.encoding,f=r.callback;if(y(e,t,!1,t.objectMode?1:c.length,c,l,f),r=r.next,t.bufferedRequestCount--,t.writing)break}null===r&&(t.lastBufferedRequest=null)}t.bufferedRequest=r,t.bufferProcessing=!1}function S(e){return e.ending&&0===e.length&&null===e.bufferedRequest&&!e.finished&&!e.writing}function k(e,t){e._final(function(r){t.pendingcb--,r&&e.emit("error",r),t.prefinished=!0,e.emit("prefinish"),E(e,t)})}function E(e,t){var r=S(t);return r&&(!function(e,t){t.prefinished||t.finalCalled||("function"==typeof e._final?(t.pendingcb++,t.finalCalled=!0,o.nextTick(k,e,t)):(t.prefinished=!0,e.emit("prefinish")))}(e,t),0===t.pendingcb&&(t.finished=!0,e.emit("finish"))),r}c.inherits(_,f),m.prototype.getBuffer=function(){for(var e=this.bufferedRequest,t=[];e;)t.push(e),e=e.next;return t},function(){try{Object.defineProperty(m.prototype,"buffer",{get:l.deprecate(function(){return this.getBuffer()},"_writableState.buffer is deprecated. Use _writableState.getBuffer instead.","DEP0003")})}catch(e){}}(),"function"==typeof Symbol&&Symbol.hasInstance&&"function"==typeof Function.prototype[Symbol.hasInstance]?(d=Function.prototype[Symbol.hasInstance],Object.defineProperty(_,Symbol.hasInstance,{value:function(e){return!!d.call(this,e)||this===_&&(e&&e._writableState instanceof m)}})):d=function(e){return e instanceof this},_.prototype.pipe=function(){this.emit("error",new Error("Cannot pipe, not readable"))},_.prototype.write=function(e,t,r){var n,i=this._writableState,s=!1,a=!i.objectMode&&(n=e,h.isBuffer(n)||n instanceof p);return a&&!h.isBuffer(e)&&(e=function(e){return h.from(e)}(e)),"function"==typeof t&&(r=t,t=null),a?t="buffer":t||(t=i.defaultEncoding),"function"!=typeof r&&(r=b),i.ended?function(e,t){var r=new Error("write after end");e.emit("error",r),o.nextTick(t,r)}(this,r):(a||function(e,t,r,n){var i=!0,s=!1;return null===r?s=new TypeError("May not write null values to stream"):"string"==typeof r||void 0===r||t.objectMode||(s=new TypeError("Invalid non-string/buffer chunk")),s&&(e.emit("error",s),o.nextTick(n,s),i=!1),i}(this,i,e,r))&&(i.pendingcb++,s=function(e,t,r,n,i,o){if(!r){var s=function(e,t,r){e.objectMode||!1===e.decodeStrings||"string"!=typeof t||(t=h.from(t,r));return t}(t,n,i);n!==s&&(r=!0,i="buffer",n=s)}var a=t.objectMode?1:n.length;t.length+=a;var u=t.length<t.highWaterMark;u||(t.needDrain=!0);if(t.writing||t.corked){var c=t.lastBufferedRequest;t.lastBufferedRequest={chunk:n,encoding:i,isBuf:r,callback:o,next:null},c?c.next=t.lastBufferedRequest:t.bufferedRequest=t.lastBufferedRequest,t.bufferedRequestCount+=1}else y(e,t,!1,a,n,i,o);return u}(this,i,a,e,t,r)),s},_.prototype.cork=function(){this._writableState.corked++},_.prototype.uncork=function(){var e=this._writableState;e.corked&&(e.corked--,e.writing||e.corked||e.finished||e.bufferProcessing||!e.bufferedRequest||v(this,e))},_.prototype.setDefaultEncoding=function(e){if("string"==typeof e&&(e=e.toLowerCase()),!(["hex","utf8","utf-8","ascii","binary","base64","ucs2","ucs-2","utf16le","utf-16le","raw"].indexOf((e+"").toLowerCase())>-1))throw new TypeError("Unknown encoding: "+e);return this._writableState.defaultEncoding=e,this},Object.defineProperty(_.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),_.prototype._write=function(e,t,r){r(new Error("_write() is not implemented"))},_.prototype._writev=null,_.prototype.end=function(e,t,r){var n=this._writableState;"function"==typeof e?(r=e,e=null,t=null):"function"==typeof t&&(r=t,t=null),null!==e&&void 0!==e&&this.write(e,t),n.corked&&(n.corked=1,this.uncork()),n.ending||n.finished||function(e,t,r){t.ending=!0,E(e,t),r&&(t.finished?o.nextTick(r):e.once("finish",r));t.ended=!0,e.writable=!1}(this,n,r)},Object.defineProperty(_.prototype,"destroyed",{get:function(){return void 0!==this._writableState&&this._writableState.destroyed},set:function(e){this._writableState&&(this._writableState.destroyed=e)}}),_.prototype.destroy=g.destroy,_.prototype._undestroy=g.undestroy,_.prototype._destroy=function(e,t){this.end(),t(e)}}).call(this,e("_process"),"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{},e("timers").setImmediate)},{"./_stream_duplex":17,"./internal/streams/destroy":23,"./internal/streams/stream":24,_process:44,"core-util-is":13,inherits:29,"process-nextick-args":43,"safe-buffer":61,timers:64,"util-deprecate":65}],22:[function(e,t,r){"use strict";var n=e("safe-buffer").Buffer,i=e("util");t.exports=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.head=null,this.tail=null,this.length=0}return e.prototype.push=function(e){var t={data:e,next:null};this.length>0?this.tail.next=t:this.head=t,this.tail=t,++this.length},e.prototype.unshift=function(e){var t={data:e,next:this.head};0===this.length&&(this.tail=t),this.head=t,++this.length},e.prototype.shift=function(){if(0!==this.length){var e=this.head.data;return 1===this.length?this.head=this.tail=null:this.head=this.head.next,--this.length,e}},e.prototype.clear=function(){this.head=this.tail=null,this.length=0},e.prototype.join=function(e){if(0===this.length)return"";for(var t=this.head,r=""+t.data;t=t.next;)r+=e+t.data;return r},e.prototype.concat=function(e){if(0===this.length)return n.alloc(0);if(1===this.length)return this.head.data;for(var t,r,i,o=n.allocUnsafe(e>>>0),s=this.head,a=0;s;)t=s.data,r=o,i=a,t.copy(r,i),a+=s.data.length,s=s.next;return o},e}(),i&&i.inspect&&i.inspect.custom&&(t.exports.prototype[i.inspect.custom]=function(){var e=i.inspect({length:this.length});return this.constructor.name+" "+e})},{"safe-buffer":61,util:11}],23:[function(e,t,r){"use strict";var n=e("process-nextick-args");function i(e,t){e.emit("error",t)}t.exports={destroy:function(e,t){var r=this,o=this._readableState&&this._readableState.destroyed,s=this._writableState&&this._writableState.destroyed;return o||s?(t?t(e):!e||this._writableState&&this._writableState.errorEmitted||n.nextTick(i,this,e),this):(this._readableState&&(this._readableState.destroyed=!0),this._writableState&&(this._writableState.destroyed=!0),this._destroy(e||null,function(e){!t&&e?(n.nextTick(i,r,e),r._writableState&&(r._writableState.errorEmitted=!0)):t&&t(e)}),this)},undestroy:function(){this._readableState&&(this._readableState.destroyed=!1,this._readableState.reading=!1,this._readableState.ended=!1,this._readableState.endEmitted=!1),this._writableState&&(this._writableState.destroyed=!1,this._writableState.ended=!1,this._writableState.ending=!1,this._writableState.finished=!1,this._writableState.errorEmitted=!1)}}},{"process-nextick-args":43}],24:[function(e,t,r){t.exports=e("events").EventEmitter},{events:27}],25:[function(e,t,r){(r=t.exports=e("./lib/_stream_readable.js")).Stream=r,r.Readable=r,r.Writable=e("./lib/_stream_writable.js"),r.Duplex=e("./lib/_stream_duplex.js"),r.Transform=e("./lib/_stream_transform.js"),r.PassThrough=e("./lib/_stream_passthrough.js")},{"./lib/_stream_duplex.js":17,"./lib/_stream_passthrough.js":18,"./lib/_stream_readable.js":19,"./lib/_stream_transform.js":20,"./lib/_stream_writable.js":21}],26:[function(e,t,r){(function(r){var n=e("once"),i=function(){},o=function(e,t,s){if("function"==typeof t)return o(e,null,t);t||(t={}),s=n(s||i);var a=e._writableState,u=e._readableState,c=t.readable||!1!==t.readable&&e.readable,l=t.writable||!1!==t.writable&&e.writable,f=!1,h=function(){e.writable||p()},p=function(){l=!1,c||s.call(e)},d=function(){c=!1,l||s.call(e)},g=function(t){s.call(e,t?new Error("exited with error code: "+t):null)},b=function(t){s.call(e,t)},m=function(){r.nextTick(_)},_=function(){if(!f)return(!c||u&&u.ended&&!u.destroyed)&&(!l||a&&a.ended&&!a.destroyed)?void 0:s.call(e,new Error("premature close"))},y=function(){e.req.on("finish",p)};return!function(e){return e.setHeader&&"function"==typeof e.abort}(e)?l&&!a&&(e.on("end",h),e.on("close",h)):(e.on("complete",p),e.on("abort",m),e.req?y():e.on("request",y)),function(e){return e.stdio&&Array.isArray(e.stdio)&&3===e.stdio.length}(e)&&e.on("exit",g),e.on("end",d),e.on("finish",p),!1!==t.error&&e.on("error",b),e.on("close",m),function(){f=!0,e.removeListener("complete",p),e.removeListener("abort",m),e.removeListener("request",y),e.req&&e.req.removeListener("finish",p),e.removeListener("end",h),e.removeListener("close",h),e.removeListener("finish",p),e.removeListener("exit",g),e.removeListener("end",d),e.removeListener("error",b),e.removeListener("close",m)}};t.exports=o}).call(this,e("_process"))},{_process:44,once:42}],27:[function(e,t,r){var n=Object.create||function(e){var t=function(){};return t.prototype=e,new t},i=Object.keys||function(e){var t=[];for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.push(r);return r},o=Function.prototype.bind||function(e){var t=this;return function(){return t.apply(e,arguments)}};function s(){this._events&&Object.prototype.hasOwnProperty.call(this,"_events")||(this._events=n(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0}t.exports=s,s.EventEmitter=s,s.prototype._events=void 0,s.prototype._maxListeners=void 0;var a,u=10;try{var c={};Object.defineProperty&&Object.defineProperty(c,"x",{value:0}),a=0===c.x}catch(e){a=!1}function l(e){return void 0===e._maxListeners?s.defaultMaxListeners:e._maxListeners}function f(e,t,r,i){var o,s,a;if("function"!=typeof r)throw new TypeError('"listener" argument must be a function');if((s=e._events)?(s.newListener&&(e.emit("newListener",t,r.listener?r.listener:r),s=e._events),a=s[t]):(s=e._events=n(null),e._eventsCount=0),a){if("function"==typeof a?a=s[t]=i?[r,a]:[a,r]:i?a.unshift(r):a.push(r),!a.warned&&(o=l(e))&&o>0&&a.length>o){a.warned=!0;var u=new Error("Possible EventEmitter memory leak detected. "+a.length+' "'+String(t)+'" listeners added. Use emitter.setMaxListeners() to increase limit.');u.name="MaxListenersExceededWarning",u.emitter=e,u.type=t,u.count=a.length,"object"==typeof console&&console.warn&&console.warn("%s: %s",u.name,u.message)}}else a=s[t]=r,++e._eventsCount;return e}function h(){if(!this.fired)switch(this.target.removeListener(this.type,this.wrapFn),this.fired=!0,arguments.length){case 0:return this.listener.call(this.target);case 1:return this.listener.call(this.target,arguments[0]);case 2:return this.listener.call(this.target,arguments[0],arguments[1]);case 3:return this.listener.call(this.target,arguments[0],arguments[1],arguments[2]);default:for(var e=new Array(arguments.length),t=0;t<e.length;++t)e[t]=arguments[t];this.listener.apply(this.target,e)}}function p(e,t,r){var n={fired:!1,wrapFn:void 0,target:e,type:t,listener:r},i=o.call(h,n);return i.listener=r,n.wrapFn=i,i}function d(e,t,r){var n=e._events;if(!n)return[];var i=n[t];return i?"function"==typeof i?r?[i.listener||i]:[i]:r?function(e){for(var t=new Array(e.length),r=0;r<t.length;++r)t[r]=e[r].listener||e[r];return t}(i):b(i,i.length):[]}function g(e){var t=this._events;if(t){var r=t[e];if("function"==typeof r)return 1;if(r)return r.length}return 0}function b(e,t){for(var r=new Array(t),n=0;n<t;++n)r[n]=e[n];return r}a?Object.defineProperty(s,"defaultMaxListeners",{enumerable:!0,get:function(){return u},set:function(e){if("number"!=typeof e||e<0||e!=e)throw new TypeError('"defaultMaxListeners" must be a positive number');u=e}}):s.defaultMaxListeners=u,s.prototype.setMaxListeners=function(e){if("number"!=typeof e||e<0||isNaN(e))throw new TypeError('"n" argument must be a positive number');return this._maxListeners=e,this},s.prototype.getMaxListeners=function(){return l(this)},s.prototype.emit=function(e){var t,r,n,i,o,s,a="error"===e;if(s=this._events)a=a&&null==s.error;else if(!a)return!1;if(a){if(arguments.length>1&&(t=arguments[1]),t instanceof Error)throw t;var u=new Error('Unhandled "error" event. ('+t+")");throw u.context=t,u}if(!(r=s[e]))return!1;var c="function"==typeof r;switch(n=arguments.length){case 1:!function(e,t,r){if(t)e.call(r);else for(var n=e.length,i=b(e,n),o=0;o<n;++o)i[o].call(r)}(r,c,this);break;case 2:!function(e,t,r,n){if(t)e.call(r,n);else for(var i=e.length,o=b(e,i),s=0;s<i;++s)o[s].call(r,n)}(r,c,this,arguments[1]);break;case 3:!function(e,t,r,n,i){if(t)e.call(r,n,i);else for(var o=e.length,s=b(e,o),a=0;a<o;++a)s[a].call(r,n,i)}(r,c,this,arguments[1],arguments[2]);break;case 4:!function(e,t,r,n,i,o){if(t)e.call(r,n,i,o);else for(var s=e.length,a=b(e,s),u=0;u<s;++u)a[u].call(r,n,i,o)}(r,c,this,arguments[1],arguments[2],arguments[3]);break;default:for(i=new Array(n-1),o=1;o<n;o++)i[o-1]=arguments[o];!function(e,t,r,n){if(t)e.apply(r,n);else for(var i=e.length,o=b(e,i),s=0;s<i;++s)o[s].apply(r,n)}(r,c,this,i)}return!0},s.prototype.addListener=function(e,t){return f(this,e,t,!1)},s.prototype.on=s.prototype.addListener,s.prototype.prependListener=function(e,t){return f(this,e,t,!0)},s.prototype.once=function(e,t){if("function"!=typeof t)throw new TypeError('"listener" argument must be a function');return this.on(e,p(this,e,t)),this},s.prototype.prependOnceListener=function(e,t){if("function"!=typeof t)throw new TypeError('"listener" argument must be a function');return this.prependListener(e,p(this,e,t)),this},s.prototype.removeListener=function(e,t){var r,i,o,s,a;if("function"!=typeof t)throw new TypeError('"listener" argument must be a function');if(!(i=this._events))return this;if(!(r=i[e]))return this;if(r===t||r.listener===t)0==--this._eventsCount?this._events=n(null):(delete i[e],i.removeListener&&this.emit("removeListener",e,r.listener||t));else if("function"!=typeof r){for(o=-1,s=r.length-1;s>=0;s--)if(r[s]===t||r[s].listener===t){a=r[s].listener,o=s;break}if(o<0)return this;0===o?r.shift():function(e,t){for(var r=t,n=r+1,i=e.length;n<i;r+=1,n+=1)e[r]=e[n];e.pop()}(r,o),1===r.length&&(i[e]=r[0]),i.removeListener&&this.emit("removeListener",e,a||t)}return this},s.prototype.removeAllListeners=function(e){var t,r,o;if(!(r=this._events))return this;if(!r.removeListener)return 0===arguments.length?(this._events=n(null),this._eventsCount=0):r[e]&&(0==--this._eventsCount?this._events=n(null):delete r[e]),this;if(0===arguments.length){var s,a=i(r);for(o=0;o<a.length;++o)"removeListener"!==(s=a[o])&&this.removeAllListeners(s);return this.removeAllListeners("removeListener"),this._events=n(null),this._eventsCount=0,this}if("function"==typeof(t=r[e]))this.removeListener(e,t);else if(t)for(o=t.length-1;o>=0;o--)this.removeListener(e,t[o]);return this},s.prototype.listeners=function(e){return d(this,e,!0)},s.prototype.rawListeners=function(e){return d(this,e,!1)},s.listenerCount=function(e,t){return"function"==typeof e.listenerCount?e.listenerCount(t):g.call(e,t)},s.prototype.listenerCount=g,s.prototype.eventNames=function(){return this._eventsCount>0?Reflect.ownKeys(this._events):[]}},{}],28:[function(e,t,r){r.read=function(e,t,r,n,i){var o,s,a=8*i-n-1,u=(1<<a)-1,c=u>>1,l=-7,f=r?i-1:0,h=r?-1:1,p=e[t+f];for(f+=h,o=p&(1<<-l)-1,p>>=-l,l+=a;l>0;o=256*o+e[t+f],f+=h,l-=8);for(s=o&(1<<-l)-1,o>>=-l,l+=n;l>0;s=256*s+e[t+f],f+=h,l-=8);if(0===o)o=1-c;else{if(o===u)return s?NaN:1/0*(p?-1:1);s+=Math.pow(2,n),o-=c}return(p?-1:1)*s*Math.pow(2,o-n)},r.write=function(e,t,r,n,i,o){var s,a,u,c=8*o-i-1,l=(1<<c)-1,f=l>>1,h=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,p=n?0:o-1,d=n?1:-1,g=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(a=isNaN(t)?1:0,s=l):(s=Math.floor(Math.log(t)/Math.LN2),t*(u=Math.pow(2,-s))<1&&(s--,u*=2),(t+=s+f>=1?h/u:h*Math.pow(2,1-f))*u>=2&&(s++,u/=2),s+f>=l?(a=0,s=l):s+f>=1?(a=(t*u-1)*Math.pow(2,i),s+=f):(a=t*Math.pow(2,f-1)*Math.pow(2,i),s=0));i>=8;e[r+p]=255&a,p+=d,a/=256,i-=8);for(s=s<<i|a,c+=i;c>0;e[r+p]=255&s,p+=d,s/=256,c-=8);e[r+p-d]|=128*g}},{}],29:[function(e,t,r){"function"==typeof Object.create?t.exports=function(e,t){t&&(e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}))}:t.exports=function(e,t){if(t){e.super_=t;var r=function(){};r.prototype=t.prototype,e.prototype=new r,e.prototype.constructor=e}}},{}],30:[function(e,t,r){function n(e){return!!e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}t.exports=function(e){return null!=e&&(n(e)||function(e){return"function"==typeof e.readFloatLE&&"function"==typeof e.slice&&n(e.slice(0,0))}(e)||!!e._isBuffer)}},{}],31:[function(e,t,r){var n={}.toString;t.exports=Array.isArray||function(e){return"[object Array]"==n.call(e)}},{}],32:[function(e,t,r){(function(e){const r=t.exports;r.types={0:"reserved",1:"connect",2:"connack",3:"publish",4:"puback",5:"pubrec",6:"pubrel",7:"pubcomp",8:"subscribe",9:"suback",10:"unsubscribe",11:"unsuback",12:"pingreq",13:"pingresp",14:"disconnect",15:"auth"},r.codes={};for(const e in r.types){const t=r.types[e];r.codes[t]=e}r.CMD_SHIFT=4,r.CMD_MASK=240,r.DUP_MASK=8,r.QOS_MASK=3,r.QOS_SHIFT=1,r.RETAIN_MASK=1,r.VARBYTEINT_MASK=127,r.VARBYTEINT_FIN_MASK=128,r.SESSIONPRESENT_MASK=1,r.SESSIONPRESENT_HEADER=e.from([r.SESSIONPRESENT_MASK]),r.CONNACK_HEADER=e.from([r.codes.connack<<r.CMD_SHIFT]),r.USERNAME_MASK=128,r.PASSWORD_MASK=64,r.WILL_RETAIN_MASK=32,r.WILL_QOS_MASK=24,r.WILL_QOS_SHIFT=3,r.WILL_FLAG_MASK=4,r.CLEAN_SESSION_MASK=2,r.CONNECT_HEADER=e.from([r.codes.connect<<r.CMD_SHIFT]),r.properties={sessionExpiryInterval:17,willDelayInterval:24,receiveMaximum:33,maximumPacketSize:39,topicAliasMaximum:34,requestResponseInformation:25,requestProblemInformation:23,userProperties:38,authenticationMethod:21,authenticationData:22,payloadFormatIndicator:1,messageExpiryInterval:2,contentType:3,responseTopic:8,correlationData:9,maximumQoS:36,retainAvailable:37,assignedClientIdentifier:18,reasonString:31,wildcardSubscriptionAvailable:40,subscriptionIdentifiersAvailable:41,sharedSubscriptionAvailable:42,serverKeepAlive:19,responseInformation:26,serverReference:28,topicAlias:35,subscriptionIdentifier:11},r.propertiesCodes={};for(const e in r.properties){const t=r.properties[e];r.propertiesCodes[t]=e}function n(t){return[0,1,2].map(n=>[0,1].map(i=>[0,1].map(o=>{const s=e.alloc(1);return s.writeUInt8(r.codes[t]<<r.CMD_SHIFT|(i?r.DUP_MASK:0)|n<<r.QOS_SHIFT|o,0,!0),s})))}r.propertiesTypes={sessionExpiryInterval:"int32",willDelayInterval:"int32",receiveMaximum:"int16",maximumPacketSize:"int32",topicAliasMaximum:"int16",requestResponseInformation:"byte",requestProblemInformation:"byte",userProperties:"pair",authenticationMethod:"string",authenticationData:"binary",payloadFormatIndicator:"byte",messageExpiryInterval:"int32",contentType:"string",responseTopic:"string",correlationData:"binary",maximumQoS:"int8",retainAvailable:"byte",assignedClientIdentifier:"string",reasonString:"string",wildcardSubscriptionAvailable:"byte",subscriptionIdentifiersAvailable:"byte",sharedSubscriptionAvailable:"byte",serverKeepAlive:"int16",responseInformation:"string",serverReference:"string",topicAlias:"int16",subscriptionIdentifier:"var"},r.PUBLISH_HEADER=n("publish"),r.SUBSCRIBE_HEADER=n("subscribe"),r.SUBSCRIBE_OPTIONS_QOS_MASK=3,r.SUBSCRIBE_OPTIONS_NL_MASK=1,r.SUBSCRIBE_OPTIONS_NL_SHIFT=2,r.SUBSCRIBE_OPTIONS_RAP_MASK=1,r.SUBSCRIBE_OPTIONS_RAP_SHIFT=3,r.SUBSCRIBE_OPTIONS_RH_MASK=3,r.SUBSCRIBE_OPTIONS_RH_SHIFT=4,r.SUBSCRIBE_OPTIONS_RH=[0,16,32],r.SUBSCRIBE_OPTIONS_NL=4,r.SUBSCRIBE_OPTIONS_RAP=8,r.SUBSCRIBE_OPTIONS_QOS=[0,1,2],r.UNSUBSCRIBE_HEADER=n("unsubscribe"),r.ACKS={unsuback:n("unsuback"),puback:n("puback"),pubcomp:n("pubcomp"),pubrel:n("pubrel"),pubrec:n("pubrec")},r.SUBACK_HEADER=e.from([r.codes.suback<<r.CMD_SHIFT]),r.VERSION3=e.from([3]),r.VERSION4=e.from([4]),r.VERSION5=e.from([5]),r.QOS=[0,1,2].map(t=>e.from([t])),r.EMPTY={pingreq:e.from([r.codes.pingreq<<4,0]),pingresp:e.from([r.codes.pingresp<<4,0]),disconnect:e.from([r.codes.disconnect<<4,0])}}).call(this,e("buffer").Buffer)},{buffer:12}],33:[function(e,t,r){(function(r){const n=e("./writeToStream"),i=e("events");class o extends i{constructor(){super(),this._array=new Array(20),this._i=0}write(e){return this._array[this._i++]=e,!0}concat(){let e=0;const t=new Array(this._array.length),n=this._array;let i,o=0;for(i=0;i<n.length&&void 0!==n[i];i++)"string"!=typeof n[i]?t[i]=n[i].length:t[i]=r.byteLength(n[i]),e+=t[i];const s=r.allocUnsafe(e);for(i=0;i<n.length&&void 0!==n[i];i++)"string"!=typeof n[i]?(n[i].copy(s,o),o+=t[i]):(s.write(n[i],o),o+=t[i]);return s}}t.exports=function(e,t){const r=new o;return n(e,r,t),r.concat()}}).call(this,e("buffer").Buffer)},{"./writeToStream":40,buffer:12,events:27}],34:[function(e,t,r){r.parser=e("./parser").parser,r.generate=e("./generate"),r.writeToStream=e("./writeToStream")},{"./generate":33,"./parser":39,"./writeToStream":40}],35:[function(e,t,r){"use strict";const{Buffer:n}=e("buffer"),i=Symbol.for("BufferList");function o(e){if(!(this instanceof o))return new o(e);o._init.call(this,e)}o._init=function(e){Object.defineProperty(this,i,{value:!0}),this._bufs=[],this.length=0,e&&this.append(e)},o.prototype._new=function(e){return new o(e)},o.prototype._offset=function(e){if(0===e)return[0,0];let t=0;for(let r=0;r<this._bufs.length;r++){const n=t+this._bufs[r].length;if(e<n||r===this._bufs.length-1)return[r,e-t];t=n}},o.prototype._reverseOffset=function(e){const t=e[0];let r=e[1];for(let e=0;e<t;e++)r+=this._bufs[e].length;return r},o.prototype.get=function(e){if(e>this.length||e<0)return;const t=this._offset(e);return this._bufs[t[0]][t[1]]},o.prototype.slice=function(e,t){return"number"==typeof e&&e<0&&(e+=this.length),"number"==typeof t&&t<0&&(t+=this.length),this.copy(null,0,e,t)},o.prototype.copy=function(e,t,r,i){if(("number"!=typeof r||r<0)&&(r=0),("number"!=typeof i||i>this.length)&&(i=this.length),r>=this.length)return e||n.alloc(0);if(i<=0)return e||n.alloc(0);const o=!!e,s=this._offset(r),a=i-r;let u=a,c=o&&t||0,l=s[1];if(0===r&&i===this.length){if(!o)return 1===this._bufs.length?this._bufs[0]:n.concat(this._bufs,this.length);for(let t=0;t<this._bufs.length;t++)this._bufs[t].copy(e,c),c+=this._bufs[t].length;return e}if(u<=this._bufs[s[0]].length-l)return o?this._bufs[s[0]].copy(e,t,l,l+u):this._bufs[s[0]].slice(l,l+u);o||(e=n.allocUnsafe(a));for(let t=s[0];t<this._bufs.length;t++){const r=this._bufs[t].length-l;if(!(u>r)){this._bufs[t].copy(e,c,l,l+u),c+=r;break}this._bufs[t].copy(e,c,l),c+=r,u-=r,l&&(l=0)}return e.length>c?e.slice(0,c):e},o.prototype.shallowSlice=function(e,t){if(e=e||0,t="number"!=typeof t?this.length:t,e<0&&(e+=this.length),t<0&&(t+=this.length),e===t)return this._new();const r=this._offset(e),n=this._offset(t),i=this._bufs.slice(r[0],n[0]+1);return 0===n[1]?i.pop():i[i.length-1]=i[i.length-1].slice(0,n[1]),0!==r[1]&&(i[0]=i[0].slice(r[1])),this._new(i)},o.prototype.toString=function(e,t,r){return this.slice(t,r).toString(e)},o.prototype.consume=function(e){if(e=Math.trunc(e),Number.isNaN(e)||e<=0)return this;for(;this._bufs.length;){if(!(e>=this._bufs[0].length)){this._bufs[0]=this._bufs[0].slice(e),this.length-=e;break}e-=this._bufs[0].length,this.length-=this._bufs[0].length,this._bufs.shift()}return this},o.prototype.duplicate=function(){const e=this._new();for(let t=0;t<this._bufs.length;t++)e.append(this._bufs[t]);return e},o.prototype.append=function(e){if(null==e)return this;if(e.buffer)this._appendBuffer(n.from(e.buffer,e.byteOffset,e.byteLength));else if(Array.isArray(e))for(let t=0;t<e.length;t++)this.append(e[t]);else if(this._isBufferList(e))for(let t=0;t<e._bufs.length;t++)this.append(e._bufs[t]);else"number"==typeof e&&(e=e.toString()),this._appendBuffer(n.from(e));return this},o.prototype._appendBuffer=function(e){this._bufs.push(e),this.length+=e.length},o.prototype.indexOf=function(e,t,r){if(void 0===r&&"string"==typeof t&&(r=t,t=void 0),"function"==typeof e||Array.isArray(e))throw new TypeError('The "value" argument must be one of type string, Buffer, BufferList, or Uint8Array.');if("number"==typeof e?e=n.from([e]):"string"==typeof e?e=n.from(e,r):this._isBufferList(e)?e=e.slice():Array.isArray(e.buffer)?e=n.from(e.buffer,e.byteOffset,e.byteLength):n.isBuffer(e)||(e=n.from(e)),t=Number(t||0),isNaN(t)&&(t=0),t<0&&(t=this.length+t),t<0&&(t=0),0===e.length)return t>this.length?this.length:t;const i=this._offset(t);let o=i[0],s=i[1];for(;o<this._bufs.length;o++){const t=this._bufs[o];for(;s<t.length;){if(t.length-s>=e.length){const r=t.indexOf(e,s);if(-1!==r)return this._reverseOffset([o,r]);s=t.length-e.length+1}else{const t=this._reverseOffset([o,s]);if(this._match(t,e))return t;s++}}s=0}return-1},o.prototype._match=function(e,t){if(this.length-e<t.length)return!1;for(let r=0;r<t.length;r++)if(this.get(e+r)!==t[r])return!1;return!0},function(){const e={readDoubleBE:8,readDoubleLE:8,readFloatBE:4,readFloatLE:4,readInt32BE:4,readInt32LE:4,readUInt32BE:4,readUInt32LE:4,readInt16BE:2,readInt16LE:2,readUInt16BE:2,readUInt16LE:2,readInt8:1,readUInt8:1,readIntBE:null,readIntLE:null,readUIntBE:null,readUIntLE:null};for(const t in e)!function(t){o.prototype[t]=null===e[t]?function(e,r){return this.slice(e,e+r)[t](0,r)}:function(r){return this.slice(r,r+e[t])[t](0)}}(t)}(),o.prototype._isBufferList=function(e){return e instanceof o||o.isBufferList(e)},o.isBufferList=function(e){return null!=e&&e[i]},t.exports=o},{buffer:12}],36:[function(e,t,r){"use strict";const n=e("readable-stream").Duplex,i=e("inherits"),o=e("./BufferList");function s(e){if(!(this instanceof s))return new s(e);if("function"==typeof e){this._callback=e;const t=function(e){this._callback&&(this._callback(e),this._callback=null)}.bind(this);this.on("pipe",function(e){e.on("error",t)}),this.on("unpipe",function(e){e.removeListener("error",t)}),e=null}o._init.call(this,e),n.call(this)}i(s,n),Object.assign(s.prototype,o.prototype),s.prototype._new=function(e){return new s(e)},s.prototype._write=function(e,t,r){this._appendBuffer(e),"function"==typeof r&&r()},s.prototype._read=function(e){if(!this.length)return this.push(null);e=Math.min(e,this.length),this.push(this.slice(0,e)),this.consume(e)},s.prototype.end=function(e){n.prototype.end.call(this,e),this._callback&&(this._callback(null,this.slice()),this._callback=null)},s.prototype._destroy=function(e,t){this._bufs.length=0,this.length=0,t(e)},s.prototype._isBufferList=function(e){return e instanceof s||e instanceof o||s.isBufferList(e)},s.isBufferList=o.isBufferList,t.exports=s,t.exports.BufferListStream=s,t.exports.BufferList=o},{"./BufferList":35,inherits:29,"readable-stream":59}],37:[function(e,t,r){(function(e){const r=65536,n={},i=e.isBuffer(e.from([1,2]).subarray(0,1));function o(t){const r=e.allocUnsafe(2);return r.writeUInt8(t>>8,0),r.writeUInt8(255&t,1),r}t.exports={cache:n,generateCache:function(){for(let e=0;e<r;e++)n[e]=o(e)},generateNumber:o,genBufVariableByteInt:function(t){let r=0,n=0;const o=e.allocUnsafe(4);do{r=t%128|0,(t=t/128|0)>0&&(r|=128),o.writeUInt8(r,n++)}while(t>0&&n<4);return t>0&&(n=0),i?o.subarray(0,n):o.slice(0,n)},generate4ByteBuffer:function(t){const r=e.allocUnsafe(4);return r.writeUInt32BE(t,0),r}}}).call(this,e("buffer").Buffer)},{buffer:12}],38:[function(e,t,r){t.exports=class{constructor(){this.cmd=null,this.retain=!1,this.qos=0,this.dup=!1,this.length=-1,this.topic=null,this.payload=null}}},{}],39:[function(e,t,r){const n=e("bl"),i=e("events"),o=e("./packet"),s=e("./constants"),a=e("debug")("mqtt-packet:parser");class u extends i{constructor(){super(),this.parser=this.constructor.parser}static parser(e){return this instanceof u?(this.settings=e||{},this._states=["_parseHeader","_parseLength","_parsePayload","_newPacket"],this._resetState(),this):(new u).parser(e)}_resetState(){a("_resetState: resetting packet, error, _list, and _stateCounter"),this.packet=new o,this.error=null,this._list=n(),this._stateCounter=0}parse(e){for(this.error&&this._resetState(),this._list.append(e),a("parse: current state: %s",this._states[this._stateCounter]);(-1!==this.packet.length||this._list.length>0)&&this[this._states[this._stateCounter]]()&&!this.error;)this._stateCounter++,a("parse: state complete. _stateCounter is now: %d",this._stateCounter),a("parse: packet.length: %d, buffer list length: %d",this.packet.length,this._list.length),this._stateCounter>=this._states.length&&(this._stateCounter=0);return a("parse: exited while loop. packet: %d, buffer list length: %d",this.packet.length,this._list.length),this._list.length}_parseHeader(){const e=this._list.readUInt8(0);return this.packet.cmd=s.types[e>>s.CMD_SHIFT],this.packet.retain=0!=(e&s.RETAIN_MASK),this.packet.qos=e>>s.QOS_SHIFT&s.QOS_MASK,this.packet.dup=0!=(e&s.DUP_MASK),a("_parseHeader: packet: %o",this.packet),this._list.consume(1),!0}_parseLength(){const e=this._parseVarByteNum(!0);return e?(this.packet.length=e.value,this._list.consume(e.bytes)):this._emitError(new Error("Invalid length")),a("_parseLength %d",e.value),!!e}_parsePayload(){a("_parsePayload: payload %O",this._list);let e=!1;if(0===this.packet.length||this._list.length>=this.packet.length){switch(this._pos=0,this.packet.cmd){case"connect":this._parseConnect();break;case"connack":this._parseConnack();break;case"publish":this._parsePublish();break;case"puback":case"pubrec":case"pubrel":case"pubcomp":this._parseConfirmation();break;case"subscribe":this._parseSubscribe();break;case"suback":this._parseSuback();break;case"unsubscribe":this._parseUnsubscribe();break;case"unsuback":this._parseUnsuback();break;case"pingreq":case"pingresp":break;case"disconnect":this._parseDisconnect();break;case"auth":this._parseAuth();break;default:this._emitError(new Error("Not supported"))}e=!0}return a("_parsePayload complete result: %s",e),e}_parseConnect(){let e,t,r,n;a("_parseConnect");const i={},o=this.packet,u=this._parseString();if(null===u)return this._emitError(new Error("Cannot parse protocolId"));if("MQTT"!==u&&"MQIsdp"!==u)return this._emitError(new Error("Invalid protocolId"));if(o.protocolId=u,this._pos>=this._list.length)return this._emitError(new Error("Packet too short"));if(o.protocolVersion=this._list.readUInt8(this._pos),3!==o.protocolVersion&&4!==o.protocolVersion&&5!==o.protocolVersion)return this._emitError(new Error("Invalid protocol version"));if(this._pos++,this._pos>=this._list.length)return this._emitError(new Error("Packet too short"));if(i.username=this._list.readUInt8(this._pos)&s.USERNAME_MASK,i.password=this._list.readUInt8(this._pos)&s.PASSWORD_MASK,i.will=this._list.readUInt8(this._pos)&s.WILL_FLAG_MASK,i.will&&(o.will={},o.will.retain=0!=(this._list.readUInt8(this._pos)&s.WILL_RETAIN_MASK),o.will.qos=(this._list.readUInt8(this._pos)&s.WILL_QOS_MASK)>>s.WILL_QOS_SHIFT),o.clean=0!=(this._list.readUInt8(this._pos)&s.CLEAN_SESSION_MASK),this._pos++,o.keepalive=this._parseNum(),-1===o.keepalive)return this._emitError(new Error("Packet too short"));if(5===o.protocolVersion){const e=this._parseProperties();Object.getOwnPropertyNames(e).length&&(o.properties=e)}const c=this._parseString();if(null===c)return this._emitError(new Error("Packet too short"));if(o.clientId=c,a("_parseConnect: packet.clientId: %s",o.clientId),i.will){if(5===o.protocolVersion){const e=this._parseProperties();Object.getOwnPropertyNames(e).length&&(o.will.properties=e)}if(null===(e=this._parseString()))return this._emitError(new Error("Cannot parse will topic"));if(o.will.topic=e,a("_parseConnect: packet.will.topic: %s",o.will.topic),null===(t=this._parseBuffer()))return this._emitError(new Error("Cannot parse will payload"));o.will.payload=t,a("_parseConnect: packet.will.paylaod: %s",o.will.payload)}if(i.username){if(null===(n=this._parseString()))return this._emitError(new Error("Cannot parse username"));o.username=n,a("_parseConnect: packet.username: %s",o.username)}if(i.password){if(null===(r=this._parseBuffer()))return this._emitError(new Error("Cannot parse password"));o.password=r}return this.settings=o,a("_parseConnect: complete"),o}_parseConnack(){a("_parseConnack");const e=this.packet;if(this._list.length<2)return null;if(e.sessionPresent=!!(this._list.readUInt8(this._pos++)&s.SESSIONPRESENT_MASK),5===this.settings.protocolVersion?e.reasonCode=this._list.readUInt8(this._pos++):e.returnCode=this._list.readUInt8(this._pos++),-1===e.returnCode||-1===e.reasonCode)return this._emitError(new Error("Cannot parse return code"));if(5===this.settings.protocolVersion){const t=this._parseProperties();Object.getOwnPropertyNames(t).length&&(e.properties=t)}a("_parseConnack: complete")}_parsePublish(){a("_parsePublish");const e=this.packet;if(e.topic=this._parseString(),null===e.topic)return this._emitError(new Error("Cannot parse topic"));if(!(e.qos>0)||this._parseMessageId()){if(5===this.settings.protocolVersion){const t=this._parseProperties();Object.getOwnPropertyNames(t).length&&(e.properties=t)}e.payload=this._list.slice(this._pos,e.length),a("_parsePublish: payload from buffer list: %o",e.payload)}}_parseSubscribe(){a("_parseSubscribe");const e=this.packet;let t,r,n,i,o,u,c;if(1!==e.qos)return this._emitError(new Error("Wrong subscribe header"));if(e.subscriptions=[],this._parseMessageId()){if(5===this.settings.protocolVersion){const t=this._parseProperties();Object.getOwnPropertyNames(t).length&&(e.properties=t)}for(;this._pos<e.length;){if(null===(t=this._parseString()))return this._emitError(new Error("Cannot parse topic"));if(this._pos>=e.length)return this._emitError(new Error("Malformed Subscribe Payload"));n=(r=this._parseByte())&s.SUBSCRIBE_OPTIONS_QOS_MASK,u=0!=(r>>s.SUBSCRIBE_OPTIONS_NL_SHIFT&s.SUBSCRIBE_OPTIONS_NL_MASK),o=0!=(r>>s.SUBSCRIBE_OPTIONS_RAP_SHIFT&s.SUBSCRIBE_OPTIONS_RAP_MASK),i=r>>s.SUBSCRIBE_OPTIONS_RH_SHIFT&s.SUBSCRIBE_OPTIONS_RH_MASK,c={topic:t,qos:n},5===this.settings.protocolVersion&&(c.nl=u,c.rap=o,c.rh=i),a("_parseSubscribe: push subscription `%s` to subscription",c),e.subscriptions.push(c)}}}_parseSuback(){a("_parseSuback");const e=this.packet;if(this.packet.granted=[],this._parseMessageId()){if(5===this.settings.protocolVersion){const t=this._parseProperties();Object.getOwnPropertyNames(t).length&&(e.properties=t)}for(;this._pos<this.packet.length;)this.packet.granted.push(this._list.readUInt8(this._pos++))}}_parseUnsubscribe(){a("_parseUnsubscribe");const e=this.packet;if(e.unsubscriptions=[],this._parseMessageId()){if(5===this.settings.protocolVersion){const t=this._parseProperties();Object.getOwnPropertyNames(t).length&&(e.properties=t)}for(;this._pos<e.length;){const t=this._parseString();if(null===t)return this._emitError(new Error("Cannot parse topic"));a("_parseUnsubscribe: push topic `%s` to unsubscriptions",t),e.unsubscriptions.push(t)}}}_parseUnsuback(){a("_parseUnsuback");const e=this.packet;if(!this._parseMessageId())return this._emitError(new Error("Cannot parse messageId"));if(5===this.settings.protocolVersion){const t=this._parseProperties();for(Object.getOwnPropertyNames(t).length&&(e.properties=t),e.granted=[];this._pos<this.packet.length;)this.packet.granted.push(this._list.readUInt8(this._pos++))}}_parseConfirmation(){a("_parseConfirmation: packet.cmd: `%s`",this.packet.cmd);const e=this.packet;if(this._parseMessageId(),5===this.settings.protocolVersion&&(e.length>2&&(e.reasonCode=this._parseByte(),a("_parseConfirmation: packet.reasonCode `%d`",e.reasonCode)),e.length>3)){const t=this._parseProperties();Object.getOwnPropertyNames(t).length&&(e.properties=t)}return!0}_parseDisconnect(){const e=this.packet;if(a("_parseDisconnect"),5===this.settings.protocolVersion){e.reasonCode=this._parseByte();const t=this._parseProperties();Object.getOwnPropertyNames(t).length&&(e.properties=t)}return a("_parseDisconnect result: true"),!0}_parseAuth(){a("_parseAuth");const e=this.packet;if(5!==this.settings.protocolVersion)return this._emitError(new Error("Not supported auth packet for this version MQTT"));e.reasonCode=this._parseByte();const t=this._parseProperties();return Object.getOwnPropertyNames(t).length&&(e.properties=t),a("_parseAuth: result: true"),!0}_parseMessageId(){const e=this.packet;return e.messageId=this._parseNum(),null===e.messageId?(this._emitError(new Error("Cannot parse messageId")),!1):(a("_parseMessageId: packet.messageId %d",e.messageId),!0)}_parseString(e){const t=this._parseNum(),r=t+this._pos;if(-1===t||r>this._list.length||r>this.packet.length)return null;const n=this._list.toString("utf8",this._pos,r);return this._pos+=t,a("_parseString: result: %s",n),n}_parseStringPair(){return a("_parseStringPair"),{name:this._parseString(),value:this._parseString()}}_parseBuffer(){const e=this._parseNum(),t=e+this._pos;if(-1===e||t>this._list.length||t>this.packet.length)return null;const r=this._list.slice(this._pos,t);return this._pos+=e,a("_parseBuffer: result: %o",r),r}_parseNum(){if(this._list.length-this._pos<2)return-1;const e=this._list.readUInt16BE(this._pos);return this._pos+=2,a("_parseNum: result: %s",e),e}_parse4ByteNum(){if(this._list.length-this._pos<4)return-1;const e=this._list.readUInt32BE(this._pos);return this._pos+=4,a("_parse4ByteNum: result: %s",e),e}_parseVarByteNum(e){a("_parseVarByteNum");let t,r=0,n=1,i=0,o=!1;const u=this._pos?this._pos:0;for(;r<5;){if(i+=n*((t=this._list.readUInt8(u+r++))&s.VARBYTEINT_MASK),n*=128,0==(t&s.VARBYTEINT_FIN_MASK)){o=!0;break}if(this._list.length<=r)break}return u&&(this._pos+=r),a("_parseVarByteNum: result: %o",o=!!o&&(e?{bytes:r,value:i}:i)),o}_parseByte(){const e=this._list.readUInt8(this._pos);return this._pos++,a("_parseByte: result: %o",e),e}_parseByType(e){switch(a("_parseByType: type: %s",e),e){case"byte":return 0!==this._parseByte();case"int8":return this._parseByte();case"int16":return this._parseNum();case"int32":return this._parse4ByteNum();case"var":return this._parseVarByteNum();case"string":return this._parseString();case"pair":return this._parseStringPair();case"binary":return this._parseBuffer()}}_parseProperties(){a("_parseProperties");const e=this._parseVarByteNum(),t=this._pos+e,r={};for(;this._pos<t;){const e=this._parseByte(),t=s.propertiesCodes[e];if(!t)return this._emitError(new Error("Unknown property")),!1;if("userProperties"!==t)r[t]?Array.isArray(r[t])?r[t].push(this._parseByType(s.propertiesTypes[t])):(r[t]=[r[t]],r[t].push(this._parseByType(s.propertiesTypes[t]))):r[t]=this._parseByType(s.propertiesTypes[t]);else{r[t]||(r[t]=Object.create(null));const e=this._parseByType(s.propertiesTypes[t]);if(r[t][e.name])if(Array.isArray(r[t][e.name]))r[t][e.name].push(e.value);else{const n=r[t][e.name];r[t][e.name]=[n],r[t][e.name].push(e.value)}else r[t][e.name]=e.value}}return r}_newPacket(){return a("_newPacket"),this.packet&&(this._list.consume(this.packet.length),a("_newPacket: parser emit packet: packet.cmd: %s, packet.payload: %s, packet.length: %d",this.packet.cmd,this.packet.payload,this.packet.length),this.emit("packet",this.packet)),a("_newPacket: new packet"),this.packet=new o,this._pos=0,!0}_emitError(e){a("_emitError"),this.error=e,this.emit("error",e)}}t.exports=u},{"./constants":32,"./packet":38,bl:36,debug:14,events:27}],40:[function(e,t,r){(function(r){const n=e("./constants"),i=r.allocUnsafe(0),o=r.from([0]),s=e("./numbers"),a=e("process-nextick-args").nextTick,u=e("debug")("mqtt-packet:writeToStream"),c=s.cache,l=s.generateNumber,f=s.generateCache,h=s.genBufVariableByteInt,p=s.generate4ByteBuffer;let d=S,g=!0;function b(e,t,s){switch(u("generate called"),t.cork&&(t.cork(),a(m,t)),g&&(g=!1,f()),u("generate: packet.cmd: %s",e.cmd),e.cmd){case"connect":return function(e,t,i){const o=e||{},s=o.protocolId||"MQTT",a=o.protocolVersion||4,u=o.will;let c=o.clean;const l=o.keepalive||0,f=o.clientId||"",h=o.username,p=o.password,g=o.properties;void 0===c&&(c=!0);let b=0;if(!s||"string"!=typeof s&&!r.isBuffer(s))return t.emit("error",new Error("Invalid protocolId")),!1;b+=s.length+2;if(3!==a&&4!==a&&5!==a)return t.emit("error",new Error("Invalid protocol version")),!1;b+=1;if("string"!=typeof f&&!r.isBuffer(f)||!f&&4!==a||!f&&!c){if(a<4)return t.emit("error",new Error("clientId must be supplied before 3.1.1")),!1;if(1*c==0)return t.emit("error",new Error("clientId must be given if cleanSession set to 0")),!1}else b+=f.length+2;if("number"!=typeof l||l<0||l>65535||l%1!=0)return t.emit("error",new Error("Invalid keepalive")),!1;b+=2;if(b+=1,5===a){var m=I(t,g);if(!m)return!1;b+=m.length}if(u){if("object"!=typeof u)return t.emit("error",new Error("Invalid will")),!1;if(!u.topic||"string"!=typeof u.topic)return t.emit("error",new Error("Invalid will topic")),!1;if(b+=r.byteLength(u.topic)+2,b+=2,u.payload){if(!(u.payload.length>=0))return t.emit("error",new Error("Invalid will payload")),!1;"string"==typeof u.payload?b+=r.byteLength(u.payload):b+=u.payload.length}var _={};if(5===a){if(!(_=I(t,u.properties)))return!1;b+=_.length}}let v=!1;if(null!=h){if(!A(h))return t.emit("error",new Error("Invalid username")),!1;v=!0,b+=r.byteLength(h)+2}if(null!=p){if(!v)return t.emit("error",new Error("Username is required to use password")),!1;if(!A(p))return t.emit("error",new Error("Invalid password")),!1;b+=x(p)+2}t.write(n.CONNECT_HEADER),y(t,b),E(t,s),t.write(4===a?n.VERSION4:5===a?n.VERSION5:n.VERSION3);let S=0;S|=null!=h?n.USERNAME_MASK:0,S|=null!=p?n.PASSWORD_MASK:0,S|=u&&u.retain?n.WILL_RETAIN_MASK:0,S|=u&&u.qos?u.qos<<n.WILL_QOS_SHIFT:0,S|=u?n.WILL_FLAG_MASK:0,S|=c?n.CLEAN_SESSION_MASK:0,t.write(r.from([S])),d(t,l),5===a&&m.write();E(t,f),u&&(5===a&&_.write(),w(t,u.topic),E(t,u.payload));null!=h&&E(t,h);null!=p&&E(t,p);return!0}(e,t);case"connack":return function(e,t,i){const s=i?i.protocolVersion:4,a=e||{},u=5===s?a.reasonCode:a.returnCode,c=a.properties;let l=2;if("number"!=typeof u)return t.emit("error",new Error("Invalid return code")),!1;let f=null;if(5===s){if(!(f=I(t,c)))return!1;l+=f.length}t.write(n.CONNACK_HEADER),y(t,l),t.write(a.sessionPresent?n.SESSIONPRESENT_HEADER:o),t.write(r.from([u])),null!=f&&f.write();return!0}(e,t,s);case"publish":return function(e,t,o){u("publish: packet: %o",e);const s=o?o.protocolVersion:4,a=e||{},c=a.qos||0,l=a.retain?n.RETAIN_MASK:0,f=a.topic,h=a.payload||i,p=a.messageId,g=a.properties;let b=0;if("string"==typeof f)b+=r.byteLength(f)+2;else{if(!r.isBuffer(f))return t.emit("error",new Error("Invalid topic")),!1;b+=f.length+2}r.isBuffer(h)?b+=h.length:b+=r.byteLength(h);if(c&&"number"!=typeof p)return t.emit("error",new Error("Invalid messageId")),!1;c&&(b+=2);let m=null;if(5===s){if(!(m=I(t,g)))return!1;b+=m.length}t.write(n.PUBLISH_HEADER[c][a.dup?1:0][l?1:0]),y(t,b),d(t,x(f)),t.write(f),c>0&&d(t,p);null!=m&&m.write();return u("publish: payload: %o",h),t.write(h)}(e,t,s);case"puback":case"pubrec":case"pubrel":case"pubcomp":return function(e,t,i){const o=i?i.protocolVersion:4,s=e||{},a=s.cmd||"puback",u=s.messageId,c=s.dup&&"pubrel"===a?n.DUP_MASK:0;let l=0;const f=s.reasonCode,h=s.properties;let p=5===o?3:2;"pubrel"===a&&(l=1);if("number"!=typeof u)return t.emit("error",new Error("Invalid messageId")),!1;let g=null;if(5===o&&"object"==typeof h){if(!(g=C(t,h,i,p)))return!1;p+=g.length}t.write(n.ACKS[a][l][c][0]),y(t,p),d(t,u),5===o&&t.write(r.from([f]));null!==g&&g.write();return!0}(e,t,s);case"subscribe":return function(e,t,i){u("subscribe: packet: ");const o=i?i.protocolVersion:4,s=e||{},a=s.dup?n.DUP_MASK:0,c=s.messageId,l=s.subscriptions,f=s.properties;let h=0;if("number"!=typeof c)return t.emit("error",new Error("Invalid messageId")),!1;h+=2;let p=null;if(5===o){if(!(p=I(t,f)))return!1;h+=p.length}if("object"!=typeof l||!l.length)return t.emit("error",new Error("Invalid subscriptions")),!1;for(let e=0;e<l.length;e+=1){const n=l[e].topic,i=l[e].qos;if("string"!=typeof n)return t.emit("error",new Error("Invalid subscriptions - invalid topic")),!1;if("number"!=typeof i)return t.emit("error",new Error("Invalid subscriptions - invalid qos")),!1;if(5===o){const r=l[e].nl||!1;if("boolean"!=typeof r)return t.emit("error",new Error("Invalid subscriptions - invalid No Local")),!1;const n=l[e].rap||!1;if("boolean"!=typeof n)return t.emit("error",new Error("Invalid subscriptions - invalid Retain as Published")),!1;const i=l[e].rh||0;if("number"!=typeof i||i>2)return t.emit("error",new Error("Invalid subscriptions - invalid Retain Handling")),!1}h+=r.byteLength(n)+2+1}u("subscribe: writing to stream: %o",n.SUBSCRIBE_HEADER),t.write(n.SUBSCRIBE_HEADER[1][a?1:0][0]),y(t,h),d(t,c),null!==p&&p.write();let g=!0;for(const e of l){const i=e.topic,s=e.qos,a=+e.nl,u=+e.rap,c=e.rh;let l;w(t,i),l=n.SUBSCRIBE_OPTIONS_QOS[s],5===o&&(l|=a?n.SUBSCRIBE_OPTIONS_NL:0,l|=u?n.SUBSCRIBE_OPTIONS_RAP:0,l|=c?n.SUBSCRIBE_OPTIONS_RH[c]:0),g=t.write(r.from([l]))}return g}(e,t,s);case"suback":return function(e,t,i){const o=i?i.protocolVersion:4,s=e||{},a=s.messageId,u=s.granted,c=s.properties;let l=0;if("number"!=typeof a)return t.emit("error",new Error("Invalid messageId")),!1;l+=2;if("object"!=typeof u||!u.length)return t.emit("error",new Error("Invalid qos vector")),!1;for(let e=0;e<u.length;e+=1){if("number"!=typeof u[e])return t.emit("error",new Error("Invalid qos vector")),!1;l+=1}let f=null;if(5===o){if(!(f=C(t,c,i,l)))return!1;l+=f.length}t.write(n.SUBACK_HEADER),y(t,l),d(t,a),null!==f&&f.write();return t.write(r.from(u))}(e,t,s);case"unsubscribe":return function(e,t,i){const o=i?i.protocolVersion:4,s=e||{},a=s.messageId,u=s.dup?n.DUP_MASK:0,c=s.unsubscriptions,l=s.properties;let f=0;if("number"!=typeof a)return t.emit("error",new Error("Invalid messageId")),!1;f+=2;if("object"!=typeof c||!c.length)return t.emit("error",new Error("Invalid unsubscriptions")),!1;for(let e=0;e<c.length;e+=1){if("string"!=typeof c[e])return t.emit("error",new Error("Invalid unsubscriptions")),!1;f+=r.byteLength(c[e])+2}let h=null;if(5===o){if(!(h=I(t,l)))return!1;f+=h.length}t.write(n.UNSUBSCRIBE_HEADER[1][u?1:0][0]),y(t,f),d(t,a),null!==h&&h.write();let p=!0;for(let e=0;e<c.length;e++)p=w(t,c[e]);return p}(e,t,s);case"unsuback":return function(e,t,i){const o=i?i.protocolVersion:4,s=e||{},a=s.messageId,u=s.dup?n.DUP_MASK:0,c=s.granted,l=s.properties,f=s.cmd;let h=2;if("number"!=typeof a)return t.emit("error",new Error("Invalid messageId")),!1;if(5===o){if("object"!=typeof c||!c.length)return t.emit("error",new Error("Invalid qos vector")),!1;for(let e=0;e<c.length;e+=1){if("number"!=typeof c[e])return t.emit("error",new Error("Invalid qos vector")),!1;h+=1}}let p=null;if(5===o){if(!(p=C(t,l,i,h)))return!1;h+=p.length}t.write(n.ACKS[f][0][u][0]),y(t,h),d(t,a),null!==p&&p.write();5===o&&t.write(r.from(c));return!0}(e,t,s);case"pingreq":case"pingresp":return function(e,t,r){return t.write(n.EMPTY[e.cmd])}(e,t);case"disconnect":return function(e,t,i){const o=i?i.protocolVersion:4,s=e||{},a=s.reasonCode,u=s.properties;let c=5===o?1:0,l=null;if(5===o){if(!(l=C(t,u,i,c)))return!1;c+=l.length}t.write(r.from([n.codes.disconnect<<4])),y(t,c),5===o&&t.write(r.from([a]));null!==l&&l.write();return!0}(e,t,s);case"auth":return function(e,t,i){const o=i?i.protocolVersion:4,s=e||{},a=s.reasonCode,u=s.properties;let c=5===o?1:0;5!==o&&t.emit("error",new Error("Invalid mqtt version for auth packet"));const l=C(t,u,i,c);if(!l)return!1;c+=l.length,t.write(r.from([n.codes.auth<<4])),y(t,c),t.write(r.from([a])),null!==l&&l.write();return!0}(e,t,s);default:return t.emit("error",new Error("Unknown command")),!1}}function m(e){e.uncork()}Object.defineProperty(b,"cacheNumbers",{get:()=>d===S,set(e){e?(c&&0!==Object.keys(c).length||(g=!0),d=S):(g=!1,d=k)}});const _={};function y(e,t){let r=_[t];r||(r=h(t),t<16384&&(_[t]=r)),u("writeVarByteInt: writing to stream: %o",r),e.write(r)}function w(e,t){const n=r.byteLength(t);return d(e,n),u("writeString: %s",t),e.write(t,"utf8")}function v(e,t,r){w(e,t),w(e,r)}function S(e,t){return u("writeNumberCached: number: %d",t),u("writeNumberCached: %o",c[t]),e.write(c[t])}function k(e,t){const r=l(t);return u("writeNumberGenerated: %o",r),e.write(r)}function E(e,t){"string"==typeof t?w(e,t):t?(d(e,t.length),e.write(t)):d(e,0)}function I(e,t){if("object"!=typeof t||null!=t.length)return{length:1,write(){R(e,{},0)}};let i=0;function o(t,i){let o=0;switch(n.propertiesTypes[t]){case"byte":if("boolean"!=typeof i)return e.emit("error",new Error(`Invalid ${t}: ${i}`)),!1;o+=2;break;case"int8":if("number"!=typeof i||i<0||i>255)return e.emit("error",new Error(`Invalid ${t}: ${i}`)),!1;o+=2;break;case"binary":if(i&&null===i)return e.emit("error",new Error(`Invalid ${t}: ${i}`)),!1;o+=1+r.byteLength(i)+2;break;case"int16":if("number"!=typeof i||i<0||i>65535)return e.emit("error",new Error(`Invalid ${t}: ${i}`)),!1;o+=3;break;case"int32":if("number"!=typeof i||i<0||i>4294967295)return e.emit("error",new Error(`Invalid ${t}: ${i}`)),!1;o+=5;break;case"var":if("number"!=typeof i||i<0||i>268435455)return e.emit("error",new Error(`Invalid ${t}: ${i}`)),!1;o+=1+r.byteLength(h(i));break;case"string":if("string"!=typeof i)return e.emit("error",new Error(`Invalid ${t}: ${i}`)),!1;o+=3+r.byteLength(i.toString());break;case"pair":if("object"!=typeof i)return e.emit("error",new Error(`Invalid ${t}: ${i}`)),!1;o+=Object.getOwnPropertyNames(i).reduce((e,t)=>{const n=i[t];return Array.isArray(n)?e+=n.reduce((e,n)=>e+=3+r.byteLength(t.toString())+2+r.byteLength(n.toString()),0):e+=3+r.byteLength(t.toString())+2+r.byteLength(i[t].toString()),e},0);break;default:return e.emit("error",new Error(`Invalid property ${t}: ${i}`)),!1}return o}if(t)for(const e in t){let r=0,n=0;const s=t[e];if(Array.isArray(s))for(let t=0;t<s.length;t++){if(!(n=o(e,s[t])))return!1;r+=n}else{if(!(n=o(e,s)))return!1;r=n}if(!r)return!1;i+=r}return{length:r.byteLength(h(i))+i,write(){R(e,t,i)}}}function C(e,t,r,n){const i=["reasonString","userProperties"],o=r&&r.properties&&r.properties.maximumPacketSize?r.properties.maximumPacketSize:0;let s=I(e,t);if(o)for(;n+s.length>o;){const r=i.shift();if(!r||!t[r])return!1;delete t[r],s=I(e,t)}return s}function T(e,t,i){switch(n.propertiesTypes[t]){case"byte":e.write(r.from([n.properties[t]])),e.write(r.from([+i]));break;case"int8":e.write(r.from([n.properties[t]])),e.write(r.from([i]));break;case"binary":e.write(r.from([n.properties[t]])),E(e,i);break;case"int16":e.write(r.from([n.properties[t]])),d(e,i);break;case"int32":e.write(r.from([n.properties[t]])),function(e,t){const r=p(t);u("write4ByteNumber: %o",r),e.write(r)}(e,i);break;case"var":e.write(r.from([n.properties[t]])),y(e,i);break;case"string":e.write(r.from([n.properties[t]])),w(e,i);break;case"pair":Object.getOwnPropertyNames(i).forEach(o=>{const s=i[o];Array.isArray(s)?s.forEach(i=>{e.write(r.from([n.properties[t]])),v(e,o.toString(),i.toString())}):(e.write(r.from([n.properties[t]])),v(e,o.toString(),s.toString()))});break;default:return e.emit("error",new Error(`Invalid property ${t} value: ${i}`)),!1}}function R(e,t,r){y(e,r);for(const r in t)if(Object.prototype.hasOwnProperty.call(t,r)&&null!==t[r]){const n=t[r];if(Array.isArray(n))for(let t=0;t<n.length;t++)T(e,r,n[t]);else T(e,r,n)}}function x(e){return e?e instanceof r?e.length:r.byteLength(e):0}function A(e){return"string"==typeof e||e instanceof r}t.exports=b}).call(this,e("buffer").Buffer)},{"./constants":32,"./numbers":37,buffer:12,debug:14,"process-nextick-args":43}],41:[function(e,t,r){var n=1e3,i=60*n,o=60*i,s=24*o,a=7*s,u=365.25*s;function c(e,t,r,n){var i=t>=1.5*r;return Math.round(e/r)+" "+n+(i?"s":"")}t.exports=function(e,t){t=t||{};var r=typeof e;if("string"===r&&e.length>0)return function(e){if((e=String(e)).length>100)return;var t=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(!t)return;var r=parseFloat(t[1]);switch((t[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return r*u;case"weeks":case"week":case"w":return r*a;case"days":case"day":case"d":return r*s;case"hours":case"hour":case"hrs":case"hr":case"h":return r*o;case"minutes":case"minute":case"mins":case"min":case"m":return r*i;case"seconds":case"second":case"secs":case"sec":case"s":return r*n;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return r;default:return}}(e);if("number"===r&&isFinite(e))return t.long?function(e){var t=Math.abs(e);if(t>=s)return c(e,t,s,"day");if(t>=o)return c(e,t,o,"hour");if(t>=i)return c(e,t,i,"minute");if(t>=n)return c(e,t,n,"second");return e+" ms"}(e):function(e){var t=Math.abs(e);if(t>=s)return Math.round(e/s)+"d";if(t>=o)return Math.round(e/o)+"h";if(t>=i)return Math.round(e/i)+"m";if(t>=n)return Math.round(e/n)+"s";return e+"ms"}(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},{}],42:[function(e,t,r){var n=e("wrappy");function i(e){var t=function(){return t.called?t.value:(t.called=!0,t.value=e.apply(this,arguments))};return t.called=!1,t}function o(e){var t=function(){if(t.called)throw new Error(t.onceError);return t.called=!0,t.value=e.apply(this,arguments)},r=e.name||"Function wrapped with `once`";return t.onceError=r+" shouldn't be called more than once",t.called=!1,t}t.exports=n(i),t.exports.strict=n(o),i.proto=i(function(){Object.defineProperty(Function.prototype,"once",{value:function(){return i(this)},configurable:!0}),Object.defineProperty(Function.prototype,"onceStrict",{value:function(){return o(this)},configurable:!0})})},{wrappy:66}],43:[function(e,t,r){(function(e){"use strict";void 0===e||!e.version||0===e.version.indexOf("v0.")||0===e.version.indexOf("v1.")&&0!==e.version.indexOf("v1.8.")?t.exports={nextTick:function(t,r,n,i){if("function"!=typeof t)throw new TypeError('"callback" argument must be a function');var o,s,a=arguments.length;switch(a){case 0:case 1:return e.nextTick(t);case 2:return e.nextTick(function(){t.call(null,r)});case 3:return e.nextTick(function(){t.call(null,r,n)});case 4:return e.nextTick(function(){t.call(null,r,n,i)});default:for(o=new Array(a-1),s=0;s<o.length;)o[s++]=arguments[s];return e.nextTick(function(){t.apply(null,o)})}}}:t.exports=e}).call(this,e("_process"))},{_process:44}],44:[function(e,t,r){var n,i,o=t.exports={};function s(){throw new Error("setTimeout has not been defined")}function a(){throw new Error("clearTimeout has not been defined")}function u(e){if(n===setTimeout)return setTimeout(e,0);if((n===s||!n)&&setTimeout)return n=setTimeout,setTimeout(e,0);try{return n(e,0)}catch(t){try{return n.call(null,e,0)}catch(t){return n.call(this,e,0)}}}!function(){try{n="function"==typeof setTimeout?setTimeout:s}catch(e){n=s}try{i="function"==typeof clearTimeout?clearTimeout:a}catch(e){i=a}}();var c,l=[],f=!1,h=-1;function p(){f&&c&&(f=!1,c.length?l=c.concat(l):h=-1,l.length&&d())}function d(){if(!f){var e=u(p);f=!0;for(var t=l.length;t;){for(c=l,l=[];++h<t;)c&&c[h].run();h=-1,t=l.length}c=null,f=!1,function(e){if(i===clearTimeout)return clearTimeout(e);if((i===a||!i)&&clearTimeout)return i=clearTimeout,clearTimeout(e);try{i(e)}catch(t){try{return i.call(null,e)}catch(t){return i.call(this,e)}}}(e)}}function g(e,t){this.fun=e,this.array=t}function b(){}o.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];l.push(new g(e,t)),1!==l.length||f||u(d)},g.prototype.run=function(){this.fun.apply(null,this.array)},o.title="browser",o.browser=!0,o.env={},o.argv=[],o.version="",o.versions={},o.on=b,o.addListener=b,o.once=b,o.off=b,o.removeListener=b,o.removeAllListeners=b,o.emit=b,o.prependListener=b,o.prependOnceListener=b,o.listeners=function(e){return[]},o.binding=function(e){throw new Error("process.binding is not supported")},o.cwd=function(){return"/"},o.chdir=function(e){throw new Error("process.chdir is not supported")},o.umask=function(){return 0}},{}],45:[function(e,t,r){"use strict";var n={};function i(e,t,r){r||(r=Error);var i=function(e){var r,n;function i(r,n,i){return e.call(this,function(e,r,n){return"string"==typeof t?t:t(e,r,n)}(r,n,i))||this}return n=e,(r=i).prototype=Object.create(n.prototype),r.prototype.constructor=r,r.__proto__=n,i}(r);i.prototype.name=r.name,i.prototype.code=e,n[e]=i}function o(e,t){if(Array.isArray(e)){var r=e.length;return e=e.map(function(e){return String(e)}),r>2?"one of ".concat(t," ").concat(e.slice(0,r-1).join(", "),", or ")+e[r-1]:2===r?"one of ".concat(t," ").concat(e[0]," or ").concat(e[1]):"of ".concat(t," ").concat(e[0])}return"of ".concat(t," ").concat(String(e))}i("ERR_INVALID_OPT_VALUE",function(e,t){return'The value "'+t+'" is invalid for option "'+e+'"'},TypeError),i("ERR_INVALID_ARG_TYPE",function(e,t,r){var n,i,s,a;if("string"==typeof t&&(i="not ",t.substr(!s||s<0?0:+s,i.length)===i)?(n="must not be",t=t.replace(/^not /,"")):n="must be",function(e,t,r){return(void 0===r||r>e.length)&&(r=e.length),e.substring(r-t.length,r)===t}(e," argument"))a="The ".concat(e," ").concat(n," ").concat(o(t,"type"));else{var u=function(e,t,r){return"number"!=typeof r&&(r=0),!(r+t.length>e.length)&&-1!==e.indexOf(t,r)}(e,".")?"property":"argument";a='The "'.concat(e,'" ').concat(u," ").concat(n," ").concat(o(t,"type"))}return a+=". Received type ".concat(typeof r)},TypeError),i("ERR_STREAM_PUSH_AFTER_EOF","stream.push() after EOF"),i("ERR_METHOD_NOT_IMPLEMENTED",function(e){return"The "+e+" method is not implemented"}),i("ERR_STREAM_PREMATURE_CLOSE","Premature close"),i("ERR_STREAM_DESTROYED",function(e){return"Cannot call "+e+" after a stream was destroyed"}),i("ERR_MULTIPLE_CALLBACK","Callback called multiple times"),i("ERR_STREAM_CANNOT_PIPE","Cannot pipe, not readable"),i("ERR_STREAM_WRITE_AFTER_END","write after end"),i("ERR_STREAM_NULL_VALUES","May not write null values to stream",TypeError),i("ERR_UNKNOWN_ENCODING",function(e){return"Unknown encoding: "+e},TypeError),i("ERR_STREAM_UNSHIFT_AFTER_END_EVENT","stream.unshift() after end event"),t.exports.codes=n},{}],46:[function(e,t,r){(function(r){"use strict";var n=Object.keys||function(e){var t=[];for(var r in e)t.push(r);return t};t.exports=c;var i=e("./_stream_readable"),o=e("./_stream_writable");e("inherits")(c,i);for(var s=n(o.prototype),a=0;a<s.length;a++){var u=s[a];c.prototype[u]||(c.prototype[u]=o.prototype[u])}function c(e){if(!(this instanceof c))return new c(e);i.call(this,e),o.call(this,e),this.allowHalfOpen=!0,e&&(!1===e.readable&&(this.readable=!1),!1===e.writable&&(this.writable=!1),!1===e.allowHalfOpen&&(this.allowHalfOpen=!1,this.once("end",l)))}function l(){this._writableState.ended||r.nextTick(f,this)}function f(e){e.end()}Object.defineProperty(c.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),Object.defineProperty(c.prototype,"writableBuffer",{enumerable:!1,get:function(){return this._writableState&&this._writableState.getBuffer()}}),Object.defineProperty(c.prototype,"writableLength",{enumerable:!1,get:function(){return this._writableState.length}}),Object.defineProperty(c.prototype,"destroyed",{enumerable:!1,get:function(){return void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed&&this._writableState.destroyed)},set:function(e){void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed=e,this._writableState.destroyed=e)}})}).call(this,e("_process"))},{"./_stream_readable":48,"./_stream_writable":50,_process:44,inherits:29}],47:[function(e,t,r){"use strict";t.exports=i;var n=e("./_stream_transform");function i(e){if(!(this instanceof i))return new i(e);n.call(this,e)}e("inherits")(i,n),i.prototype._transform=function(e,t,r){r(null,e)}},{"./_stream_transform":49,inherits:29}],48:[function(e,t,r){(function(r,n){"use strict";var i;t.exports=I,I.ReadableState=E;e("events").EventEmitter;var o=function(e,t){return e.listeners(t).length},s=e("./internal/streams/stream"),a=e("buffer").Buffer,u=n.Uint8Array||function(){};var c,l=e("util");c=l&&l.debuglog?l.debuglog("stream"):function(){};var f,h,p,d=e("./internal/streams/buffer_list"),g=e("./internal/streams/destroy"),b=e("./internal/streams/state").getHighWaterMark,m=e("../errors").codes,_=m.ERR_INVALID_ARG_TYPE,y=m.ERR_STREAM_PUSH_AFTER_EOF,w=m.ERR_METHOD_NOT_IMPLEMENTED,v=m.ERR_STREAM_UNSHIFT_AFTER_END_EVENT;e("inherits")(I,s);var S=g.errorOrDestroy,k=["error","close","destroy","pause","resume"];function E(t,r,n){i=i||e("./_stream_duplex"),t=t||{},"boolean"!=typeof n&&(n=r instanceof i),this.objectMode=!!t.objectMode,n&&(this.objectMode=this.objectMode||!!t.readableObjectMode),this.highWaterMark=b(this,t,"readableHighWaterMark",n),this.buffer=new d,this.length=0,this.pipes=null,this.pipesCount=0,this.flowing=null,this.ended=!1,this.endEmitted=!1,this.reading=!1,this.sync=!0,this.needReadable=!1,this.emittedReadable=!1,this.readableListening=!1,this.resumeScheduled=!1,this.paused=!0,this.emitClose=!1!==t.emitClose,this.autoDestroy=!!t.autoDestroy,this.destroyed=!1,this.defaultEncoding=t.defaultEncoding||"utf8",this.awaitDrain=0,this.readingMore=!1,this.decoder=null,this.encoding=null,t.encoding&&(f||(f=e("string_decoder/").StringDecoder),this.decoder=new f(t.encoding),this.encoding=t.encoding)}function I(t){if(i=i||e("./_stream_duplex"),!(this instanceof I))return new I(t);var r=this instanceof i;this._readableState=new E(t,this,r),this.readable=!0,t&&("function"==typeof t.read&&(this._read=t.read),"function"==typeof t.destroy&&(this._destroy=t.destroy)),s.call(this)}function C(e,t,r,n,i){c("readableAddChunk",t);var o,s=e._readableState;if(null===t)s.reading=!1,function(e,t){if(c("onEofChunk"),t.ended)return;if(t.decoder){var r=t.decoder.end();r&&r.length&&(t.buffer.push(r),t.length+=t.objectMode?1:r.length)}t.ended=!0,t.sync?A(e):(t.needReadable=!1,t.emittedReadable||(t.emittedReadable=!0,M(e)))}(e,s);else if(i||(o=function(e,t){var r;n=t,a.isBuffer(n)||n instanceof u||"string"==typeof t||void 0===t||e.objectMode||(r=new _("chunk",["string","Buffer","Uint8Array"],t));var n;return r}(s,t)),o)S(e,o);else if(s.objectMode||t&&t.length>0)if("string"==typeof t||s.objectMode||Object.getPrototypeOf(t)===a.prototype||(t=function(e){return a.from(e)}(t)),n)s.endEmitted?S(e,new v):T(e,s,t,!0);else if(s.ended)S(e,new y);else{if(s.destroyed)return!1;s.reading=!1,s.decoder&&!r?(t=s.decoder.write(t),s.objectMode||0!==t.length?T(e,s,t,!1):P(e,s)):T(e,s,t,!1)}else n||(s.reading=!1,P(e,s));return!s.ended&&(s.length<s.highWaterMark||0===s.length)}function T(e,t,r,n){t.flowing&&0===t.length&&!t.sync?(t.awaitDrain=0,e.emit("data",r)):(t.length+=t.objectMode?1:r.length,n?t.buffer.unshift(r):t.buffer.push(r),t.needReadable&&A(e)),P(e,t)}Object.defineProperty(I.prototype,"destroyed",{enumerable:!1,get:function(){return void 0!==this._readableState&&this._readableState.destroyed},set:function(e){this._readableState&&(this._readableState.destroyed=e)}}),I.prototype.destroy=g.destroy,I.prototype._undestroy=g.undestroy,I.prototype._destroy=function(e,t){t(e)},I.prototype.push=function(e,t){var r,n=this._readableState;return n.objectMode?r=!0:"string"==typeof e&&((t=t||n.defaultEncoding)!==n.encoding&&(e=a.from(e,t),t=""),r=!0),C(this,e,t,!1,r)},I.prototype.unshift=function(e){return C(this,e,null,!0,!1)},I.prototype.isPaused=function(){return!1===this._readableState.flowing},I.prototype.setEncoding=function(t){f||(f=e("string_decoder/").StringDecoder);var r=new f(t);this._readableState.decoder=r,this._readableState.encoding=this._readableState.decoder.encoding;for(var n=this._readableState.buffer.head,i="";null!==n;)i+=r.write(n.data),n=n.next;return this._readableState.buffer.clear(),""!==i&&this._readableState.buffer.push(i),this._readableState.length=i.length,this};var R=1073741824;function x(e,t){return e<=0||0===t.length&&t.ended?0:t.objectMode?1:e!=e?t.flowing&&t.length?t.buffer.head.data.length:t.length:(e>t.highWaterMark&&(t.highWaterMark=function(e){return e>=R?e=R:(e--,e|=e>>>1,e|=e>>>2,e|=e>>>4,e|=e>>>8,e|=e>>>16,e++),e}(e)),e<=t.length?e:t.ended?t.length:(t.needReadable=!0,0))}function A(e){var t=e._readableState;c("emitReadable",t.needReadable,t.emittedReadable),t.needReadable=!1,t.emittedReadable||(c("emitReadable",t.flowing),t.emittedReadable=!0,r.nextTick(M,e))}function M(e){var t=e._readableState;c("emitReadable_",t.destroyed,t.length,t.ended),t.destroyed||!t.length&&!t.ended||(e.emit("readable"),t.emittedReadable=!1),t.needReadable=!t.flowing&&!t.ended&&t.length<=t.highWaterMark,N(e)}function P(e,t){t.readingMore||(t.readingMore=!0,r.nextTick(O,e,t))}function O(e,t){for(;!t.reading&&!t.ended&&(t.length<t.highWaterMark||t.flowing&&0===t.length);){var r=t.length;if(c("maybeReadMore read 0"),e.read(0),r===t.length)break}t.readingMore=!1}function B(e){var t=e._readableState;t.readableListening=e.listenerCount("readable")>0,t.resumeScheduled&&!t.paused?t.flowing=!0:e.listenerCount("data")>0&&e.resume()}function L(e){c("readable nexttick read 0"),e.read(0)}function j(e,t){c("resume",t.reading),t.reading||e.read(0),t.resumeScheduled=!1,e.emit("resume"),N(e),t.flowing&&!t.reading&&e.read(0)}function N(e){var t=e._readableState;for(c("flow",t.flowing);t.flowing&&null!==e.read(););}function U(e,t){return 0===t.length?null:(t.objectMode?r=t.buffer.shift():!e||e>=t.length?(r=t.decoder?t.buffer.join(""):1===t.buffer.length?t.buffer.first():t.buffer.concat(t.length),t.buffer.clear()):r=t.buffer.consume(e,t.decoder),r);var r}function q(e){var t=e._readableState;c("endReadable",t.endEmitted),t.endEmitted||(t.ended=!0,r.nextTick(D,t,e))}function D(e,t){if(c("endReadableNT",e.endEmitted,e.length),!e.endEmitted&&0===e.length&&(e.endEmitted=!0,t.readable=!1,t.emit("end"),e.autoDestroy)){var r=t._writableState;(!r||r.autoDestroy&&r.finished)&&t.destroy()}}function F(e,t){for(var r=0,n=e.length;r<n;r++)if(e[r]===t)return r;return-1}I.prototype.read=function(e){c("read",e),e=parseInt(e,10);var t=this._readableState,r=e;if(0!==e&&(t.emittedReadable=!1),0===e&&t.needReadable&&((0!==t.highWaterMark?t.length>=t.highWaterMark:t.length>0)||t.ended))return c("read: emitReadable",t.length,t.ended),0===t.length&&t.ended?q(this):A(this),null;if(0===(e=x(e,t))&&t.ended)return 0===t.length&&q(this),null;var n,i=t.needReadable;return c("need readable",i),(0===t.length||t.length-e<t.highWaterMark)&&c("length less than watermark",i=!0),t.ended||t.reading?c("reading or ended",i=!1):i&&(c("do read"),t.reading=!0,t.sync=!0,0===t.length&&(t.needReadable=!0),this._read(t.highWaterMark),t.sync=!1,t.reading||(e=x(r,t))),null===(n=e>0?U(e,t):null)?(t.needReadable=t.length<=t.highWaterMark,e=0):(t.length-=e,t.awaitDrain=0),0===t.length&&(t.ended||(t.needReadable=!0),r!==e&&t.ended&&q(this)),null!==n&&this.emit("data",n),n},I.prototype._read=function(e){S(this,new w("_read()"))},I.prototype.pipe=function(e,t){var n=this,i=this._readableState;switch(i.pipesCount){case 0:i.pipes=e;break;case 1:i.pipes=[i.pipes,e];break;default:i.pipes.push(e)}i.pipesCount+=1,c("pipe count=%d opts=%j",i.pipesCount,t);var s=(!t||!1!==t.end)&&e!==r.stdout&&e!==r.stderr?u:b;function a(t,r){c("onunpipe"),t===n&&r&&!1===r.hasUnpiped&&(r.hasUnpiped=!0,c("cleanup"),e.removeListener("close",d),e.removeListener("finish",g),e.removeListener("drain",l),e.removeListener("error",p),e.removeListener("unpipe",a),n.removeListener("end",u),n.removeListener("end",b),n.removeListener("data",h),f=!0,!i.awaitDrain||e._writableState&&!e._writableState.needDrain||l())}function u(){c("onend"),e.end()}i.endEmitted?r.nextTick(s):n.once("end",s),e.on("unpipe",a);var l=function(e){return function(){var t=e._readableState;c("pipeOnDrain",t.awaitDrain),t.awaitDrain&&t.awaitDrain--,0===t.awaitDrain&&o(e,"data")&&(t.flowing=!0,N(e))}}(n);e.on("drain",l);var f=!1;function h(t){c("ondata");var r=e.write(t);c("dest.write",r),!1===r&&((1===i.pipesCount&&i.pipes===e||i.pipesCount>1&&-1!==F(i.pipes,e))&&!f&&(c("false write response, pause",i.awaitDrain),i.awaitDrain++),n.pause())}function p(t){c("onerror",t),b(),e.removeListener("error",p),0===o(e,"error")&&S(e,t)}function d(){e.removeListener("finish",g),b()}function g(){c("onfinish"),e.removeListener("close",d),b()}function b(){c("unpipe"),n.unpipe(e)}return n.on("data",h),function(e,t,r){if("function"==typeof e.prependListener)return e.prependListener(t,r);e._events&&e._events[t]?Array.isArray(e._events[t])?e._events[t].unshift(r):e._events[t]=[r,e._events[t]]:e.on(t,r)}(e,"error",p),e.once("close",d),e.once("finish",g),e.emit("pipe",n),i.flowing||(c("pipe resume"),n.resume()),e},I.prototype.unpipe=function(e){var t=this._readableState,r={hasUnpiped:!1};if(0===t.pipesCount)return this;if(1===t.pipesCount)return e&&e!==t.pipes?this:(e||(e=t.pipes),t.pipes=null,t.pipesCount=0,t.flowing=!1,e&&e.emit("unpipe",this,r),this);if(!e){var n=t.pipes,i=t.pipesCount;t.pipes=null,t.pipesCount=0,t.flowing=!1;for(var o=0;o<i;o++)n[o].emit("unpipe",this,{hasUnpiped:!1});return this}var s=F(t.pipes,e);return-1===s?this:(t.pipes.splice(s,1),t.pipesCount-=1,1===t.pipesCount&&(t.pipes=t.pipes[0]),e.emit("unpipe",this,r),this)},I.prototype.on=function(e,t){var n=s.prototype.on.call(this,e,t),i=this._readableState;return"data"===e?(i.readableListening=this.listenerCount("readable")>0,!1!==i.flowing&&this.resume()):"readable"===e&&(i.endEmitted||i.readableListening||(i.readableListening=i.needReadable=!0,i.flowing=!1,i.emittedReadable=!1,c("on readable",i.length,i.reading),i.length?A(this):i.reading||r.nextTick(L,this))),n},I.prototype.addListener=I.prototype.on,I.prototype.removeListener=function(e,t){var n=s.prototype.removeListener.call(this,e,t);return"readable"===e&&r.nextTick(B,this),n},I.prototype.removeAllListeners=function(e){var t=s.prototype.removeAllListeners.apply(this,arguments);return"readable"!==e&&void 0!==e||r.nextTick(B,this),t},I.prototype.resume=function(){var e=this._readableState;return e.flowing||(c("resume"),e.flowing=!e.readableListening,function(e,t){t.resumeScheduled||(t.resumeScheduled=!0,r.nextTick(j,e,t))}(this,e)),e.paused=!1,this},I.prototype.pause=function(){return c("call pause flowing=%j",this._readableState.flowing),!1!==this._readableState.flowing&&(c("pause"),this._readableState.flowing=!1,this.emit("pause")),this._readableState.paused=!0,this},I.prototype.wrap=function(e){var t=this,r=this._readableState,n=!1;for(var i in e.on("end",function(){if(c("wrapped end"),r.decoder&&!r.ended){var e=r.decoder.end();e&&e.length&&t.push(e)}t.push(null)}),e.on("data",function(i){(c("wrapped data"),r.decoder&&(i=r.decoder.write(i)),!r.objectMode||null!==i&&void 0!==i)&&((r.objectMode||i&&i.length)&&(t.push(i)||(n=!0,e.pause())))}),e)void 0===this[i]&&"function"==typeof e[i]&&(this[i]=function(t){return function(){return e[t].apply(e,arguments)}}(i));for(var o=0;o<k.length;o++)e.on(k[o],this.emit.bind(this,k[o]));return this._read=function(t){c("wrapped _read",t),n&&(n=!1,e.resume())},this},"function"==typeof Symbol&&(I.prototype[Symbol.asyncIterator]=function(){return void 0===h&&(h=e("./internal/streams/async_iterator")),h(this)}),Object.defineProperty(I.prototype,"readableHighWaterMark",{enumerable:!1,get:function(){return this._readableState.highWaterMark}}),Object.defineProperty(I.prototype,"readableBuffer",{enumerable:!1,get:function(){return this._readableState&&this._readableState.buffer}}),Object.defineProperty(I.prototype,"readableFlowing",{enumerable:!1,get:function(){return this._readableState.flowing},set:function(e){this._readableState&&(this._readableState.flowing=e)}}),I._fromList=U,Object.defineProperty(I.prototype,"readableLength",{enumerable:!1,get:function(){return this._readableState.length}}),"function"==typeof Symbol&&(I.from=function(t,r){return void 0===p&&(p=e("./internal/streams/from")),p(I,t,r)})}).call(this,e("_process"),"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"../errors":45,"./_stream_duplex":46,"./internal/streams/async_iterator":51,"./internal/streams/buffer_list":52,"./internal/streams/destroy":53,"./internal/streams/from":55,"./internal/streams/state":57,"./internal/streams/stream":58,_process:44,buffer:12,events:27,inherits:29,"string_decoder/":63,util:11}],49:[function(e,t,r){"use strict";t.exports=c;var n=e("../errors").codes,i=n.ERR_METHOD_NOT_IMPLEMENTED,o=n.ERR_MULTIPLE_CALLBACK,s=n.ERR_TRANSFORM_ALREADY_TRANSFORMING,a=n.ERR_TRANSFORM_WITH_LENGTH_0,u=e("./_stream_duplex");function c(e){if(!(this instanceof c))return new c(e);u.call(this,e),this._transformState={afterTransform:function(e,t){var r=this._transformState;r.transforming=!1;var n=r.writecb;if(null===n)return this.emit("error",new o);r.writechunk=null,r.writecb=null,null!=t&&this.push(t),n(e);var i=this._readableState;i.reading=!1,(i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}.bind(this),needTransform:!1,transforming:!1,writecb:null,writechunk:null,writeencoding:null},this._readableState.needReadable=!0,this._readableState.sync=!1,e&&("function"==typeof e.transform&&(this._transform=e.transform),"function"==typeof e.flush&&(this._flush=e.flush)),this.on("prefinish",l)}function l(){var e=this;"function"!=typeof this._flush||this._readableState.destroyed?f(this,null,null):this._flush(function(t,r){f(e,t,r)})}function f(e,t,r){if(t)return e.emit("error",t);if(null!=r&&e.push(r),e._writableState.length)throw new a;if(e._transformState.transforming)throw new s;return e.push(null)}e("inherits")(c,u),c.prototype.push=function(e,t){return this._transformState.needTransform=!1,u.prototype.push.call(this,e,t)},c.prototype._transform=function(e,t,r){r(new i("_transform()"))},c.prototype._write=function(e,t,r){var n=this._transformState;if(n.writecb=r,n.writechunk=e,n.writeencoding=t,!n.transforming){var i=this._readableState;(n.needTransform||i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}},c.prototype._read=function(e){var t=this._transformState;null===t.writechunk||t.transforming?t.needTransform=!0:(t.transforming=!0,this._transform(t.writechunk,t.writeencoding,t.afterTransform))},c.prototype._destroy=function(e,t){u.prototype._destroy.call(this,e,function(e){t(e)})}},{"../errors":45,"./_stream_duplex":46,inherits:29}],50:[function(e,t,r){(function(r,n){"use strict";function i(e){var t=this;this.next=null,this.entry=null,this.finish=function(){!function(e,t,r){var n=e.entry;e.entry=null;for(;n;){var i=n.callback;t.pendingcb--,i(r),n=n.next}t.corkedRequestsFree.next=e}(t,e)}}var o;t.exports=I,I.WritableState=E;var s={deprecate:e("util-deprecate")},a=e("./internal/streams/stream"),u=e("buffer").Buffer,c=n.Uint8Array||function(){};var l,f=e("./internal/streams/destroy"),h=e("./internal/streams/state").getHighWaterMark,p=e("../errors").codes,d=p.ERR_INVALID_ARG_TYPE,g=p.ERR_METHOD_NOT_IMPLEMENTED,b=p.ERR_MULTIPLE_CALLBACK,m=p.ERR_STREAM_CANNOT_PIPE,_=p.ERR_STREAM_DESTROYED,y=p.ERR_STREAM_NULL_VALUES,w=p.ERR_STREAM_WRITE_AFTER_END,v=p.ERR_UNKNOWN_ENCODING,S=f.errorOrDestroy;function k(){}function E(t,n,s){o=o||e("./_stream_duplex"),t=t||{},"boolean"!=typeof s&&(s=n instanceof o),this.objectMode=!!t.objectMode,s&&(this.objectMode=this.objectMode||!!t.writableObjectMode),this.highWaterMark=h(this,t,"writableHighWaterMark",s),this.finalCalled=!1,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1,this.destroyed=!1;var a=!1===t.decodeStrings;this.decodeStrings=!a,this.defaultEncoding=t.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=function(e){!function(e,t){var n=e._writableState,i=n.sync,o=n.writecb;if("function"!=typeof o)throw new b;if(function(e){e.writing=!1,e.writecb=null,e.length-=e.writelen,e.writelen=0}(n),t)!function(e,t,n,i,o){--t.pendingcb,n?(r.nextTick(o,i),r.nextTick(M,e,t),e._writableState.errorEmitted=!0,S(e,i)):(o(i),e._writableState.errorEmitted=!0,S(e,i),M(e,t))}(e,n,i,t,o);else{var s=x(n)||e.destroyed;s||n.corked||n.bufferProcessing||!n.bufferedRequest||R(e,n),i?r.nextTick(T,e,n,s,o):T(e,n,s,o)}}(n,e)},this.writecb=null,this.writelen=0,this.bufferedRequest=null,this.lastBufferedRequest=null,this.pendingcb=0,this.prefinished=!1,this.errorEmitted=!1,this.emitClose=!1!==t.emitClose,this.autoDestroy=!!t.autoDestroy,this.bufferedRequestCount=0,this.corkedRequestsFree=new i(this)}function I(t){var r=this instanceof(o=o||e("./_stream_duplex"));if(!r&&!l.call(I,this))return new I(t);this._writableState=new E(t,this,r),this.writable=!0,t&&("function"==typeof t.write&&(this._write=t.write),"function"==typeof t.writev&&(this._writev=t.writev),"function"==typeof t.destroy&&(this._destroy=t.destroy),"function"==typeof t.final&&(this._final=t.final)),a.call(this)}function C(e,t,r,n,i,o,s){t.writelen=n,t.writecb=s,t.writing=!0,t.sync=!0,t.destroyed?t.onwrite(new _("write")):r?e._writev(i,t.onwrite):e._write(i,o,t.onwrite),t.sync=!1}function T(e,t,r,n){r||function(e,t){0===t.length&&t.needDrain&&(t.needDrain=!1,e.emit("drain"))}(e,t),t.pendingcb--,n(),M(e,t)}function R(e,t){t.bufferProcessing=!0;var r=t.bufferedRequest;if(e._writev&&r&&r.next){var n=t.bufferedRequestCount,o=new Array(n),s=t.corkedRequestsFree;s.entry=r;for(var a=0,u=!0;r;)o[a]=r,r.isBuf||(u=!1),r=r.next,a+=1;o.allBuffers=u,C(e,t,!0,t.length,o,"",s.finish),t.pendingcb++,t.lastBufferedRequest=null,s.next?(t.corkedRequestsFree=s.next,s.next=null):t.corkedRequestsFree=new i(t),t.bufferedRequestCount=0}else{for(;r;){var c=r.chunk,l=r.encoding,f=r.callback;if(C(e,t,!1,t.objectMode?1:c.length,c,l,f),r=r.next,t.bufferedRequestCount--,t.writing)break}null===r&&(t.lastBufferedRequest=null)}t.bufferedRequest=r,t.bufferProcessing=!1}function x(e){return e.ending&&0===e.length&&null===e.bufferedRequest&&!e.finished&&!e.writing}function A(e,t){e._final(function(r){t.pendingcb--,r&&S(e,r),t.prefinished=!0,e.emit("prefinish"),M(e,t)})}function M(e,t){var n=x(t);if(n&&(function(e,t){t.prefinished||t.finalCalled||("function"!=typeof e._final||t.destroyed?(t.prefinished=!0,e.emit("prefinish")):(t.pendingcb++,t.finalCalled=!0,r.nextTick(A,e,t)))}(e,t),0===t.pendingcb&&(t.finished=!0,e.emit("finish"),t.autoDestroy))){var i=e._readableState;(!i||i.autoDestroy&&i.endEmitted)&&e.destroy()}return n}e("inherits")(I,a),E.prototype.getBuffer=function(){for(var e=this.bufferedRequest,t=[];e;)t.push(e),e=e.next;return t},function(){try{Object.defineProperty(E.prototype,"buffer",{get:s.deprecate(function(){return this.getBuffer()},"_writableState.buffer is deprecated. Use _writableState.getBuffer instead.","DEP0003")})}catch(e){}}(),"function"==typeof Symbol&&Symbol.hasInstance&&"function"==typeof Function.prototype[Symbol.hasInstance]?(l=Function.prototype[Symbol.hasInstance],Object.defineProperty(I,Symbol.hasInstance,{value:function(e){return!!l.call(this,e)||this===I&&(e&&e._writableState instanceof E)}})):l=function(e){return e instanceof this},I.prototype.pipe=function(){S(this,new m)},I.prototype.write=function(e,t,n){var i,o=this._writableState,s=!1,a=!o.objectMode&&(i=e,u.isBuffer(i)||i instanceof c);return a&&!u.isBuffer(e)&&(e=function(e){return u.from(e)}(e)),"function"==typeof t&&(n=t,t=null),a?t="buffer":t||(t=o.defaultEncoding),"function"!=typeof n&&(n=k),o.ending?function(e,t){var n=new w;S(e,n),r.nextTick(t,n)}(this,n):(a||function(e,t,n,i){var o;return null===n?o=new y:"string"==typeof n||t.objectMode||(o=new d("chunk",["string","Buffer"],n)),!o||(S(e,o),r.nextTick(i,o),!1)}(this,o,e,n))&&(o.pendingcb++,s=function(e,t,r,n,i,o){if(!r){var s=function(e,t,r){e.objectMode||!1===e.decodeStrings||"string"!=typeof t||(t=u.from(t,r));return t}(t,n,i);n!==s&&(r=!0,i="buffer",n=s)}var a=t.objectMode?1:n.length;t.length+=a;var c=t.length<t.highWaterMark;c||(t.needDrain=!0);if(t.writing||t.corked){var l=t.lastBufferedRequest;t.lastBufferedRequest={chunk:n,encoding:i,isBuf:r,callback:o,next:null},l?l.next=t.lastBufferedRequest:t.bufferedRequest=t.lastBufferedRequest,t.bufferedRequestCount+=1}else C(e,t,!1,a,n,i,o);return c}(this,o,a,e,t,n)),s},I.prototype.cork=function(){this._writableState.corked++},I.prototype.uncork=function(){var e=this._writableState;e.corked&&(e.corked--,e.writing||e.corked||e.bufferProcessing||!e.bufferedRequest||R(this,e))},I.prototype.setDefaultEncoding=function(e){if("string"==typeof e&&(e=e.toLowerCase()),!(["hex","utf8","utf-8","ascii","binary","base64","ucs2","ucs-2","utf16le","utf-16le","raw"].indexOf((e+"").toLowerCase())>-1))throw new v(e);return this._writableState.defaultEncoding=e,this},Object.defineProperty(I.prototype,"writableBuffer",{enumerable:!1,get:function(){return this._writableState&&this._writableState.getBuffer()}}),Object.defineProperty(I.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),I.prototype._write=function(e,t,r){r(new g("_write()"))},I.prototype._writev=null,I.prototype.end=function(e,t,n){var i=this._writableState;return"function"==typeof e?(n=e,e=null,t=null):"function"==typeof t&&(n=t,t=null),null!==e&&void 0!==e&&this.write(e,t),i.corked&&(i.corked=1,this.uncork()),i.ending||function(e,t,n){t.ending=!0,M(e,t),n&&(t.finished?r.nextTick(n):e.once("finish",n));t.ended=!0,e.writable=!1}(this,i,n),this},Object.defineProperty(I.prototype,"writableLength",{enumerable:!1,get:function(){return this._writableState.length}}),Object.defineProperty(I.prototype,"destroyed",{enumerable:!1,get:function(){return void 0!==this._writableState&&this._writableState.destroyed},set:function(e){this._writableState&&(this._writableState.destroyed=e)}}),I.prototype.destroy=f.destroy,I.prototype._undestroy=f.undestroy,I.prototype._destroy=function(e,t){t(e)}}).call(this,e("_process"),"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"../errors":45,"./_stream_duplex":46,"./internal/streams/destroy":53,"./internal/streams/state":57,"./internal/streams/stream":58,_process:44,buffer:12,inherits:29,"util-deprecate":65}],51:[function(e,t,r){(function(r){"use strict";var n;function i(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var o=e("./end-of-stream"),s=Symbol("lastResolve"),a=Symbol("lastReject"),u=Symbol("error"),c=Symbol("ended"),l=Symbol("lastPromise"),f=Symbol("handlePromise"),h=Symbol("stream");function p(e,t){return{value:e,done:t}}function d(e){var t=e[s];if(null!==t){var r=e[h].read();null!==r&&(e[l]=null,e[s]=null,e[a]=null,t(p(r,!1)))}}var g=Object.getPrototypeOf(function(){}),b=Object.setPrototypeOf((i(n={get stream(){return this[h]},next:function(){var e=this,t=this[u];if(null!==t)return Promise.reject(t);if(this[c])return Promise.resolve(p(void 0,!0));if(this[h].destroyed)return new Promise(function(t,n){r.nextTick(function(){e[u]?n(e[u]):t(p(void 0,!0))})});var n,i=this[l];if(i)n=new Promise(function(e,t){return function(r,n){e.then(function(){t[c]?r(p(void 0,!0)):t[f](r,n)},n)}}(i,this));else{var o=this[h].read();if(null!==o)return Promise.resolve(p(o,!1));n=new Promise(this[f])}return this[l]=n,n}},Symbol.asyncIterator,function(){return this}),i(n,"return",function(){var e=this;return new Promise(function(t,r){e[h].destroy(null,function(e){e?r(e):t(p(void 0,!0))})})}),n),g);t.exports=function(e){var t,n=Object.create(b,(i(t={},h,{value:e,writable:!0}),i(t,s,{value:null,writable:!0}),i(t,a,{value:null,writable:!0}),i(t,u,{value:null,writable:!0}),i(t,c,{value:e._readableState.endEmitted,writable:!0}),i(t,f,{value:function(e,t){var r=n[h].read();r?(n[l]=null,n[s]=null,n[a]=null,e(p(r,!1))):(n[s]=e,n[a]=t)},writable:!0}),t));return n[l]=null,o(e,function(e){if(e&&"ERR_STREAM_PREMATURE_CLOSE"!==e.code){var t=n[a];return null!==t&&(n[l]=null,n[s]=null,n[a]=null,t(e)),void(n[u]=e)}var r=n[s];null!==r&&(n[l]=null,n[s]=null,n[a]=null,r(p(void 0,!0))),n[c]=!0}),e.on("readable",function(e){r.nextTick(d,e)}.bind(null,n)),n}}).call(this,e("_process"))},{"./end-of-stream":54,_process:44}],52:[function(e,t,r){"use strict";function n(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function i(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function o(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}var s=e("buffer").Buffer,a=e("util").inspect,u=a&&a.custom||"inspect";t.exports=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.head=null,this.tail=null,this.length=0}var t,r,c;return t=e,(r=[{key:"push",value:function(e){var t={data:e,next:null};this.length>0?this.tail.next=t:this.head=t,this.tail=t,++this.length}},{key:"unshift",value:function(e){var t={data:e,next:this.head};0===this.length&&(this.tail=t),this.head=t,++this.length}},{key:"shift",value:function(){if(0!==this.length){var e=this.head.data;return 1===this.length?this.head=this.tail=null:this.head=this.head.next,--this.length,e}}},{key:"clear",value:function(){this.head=this.tail=null,this.length=0}},{key:"join",value:function(e){if(0===this.length)return"";for(var t=this.head,r=""+t.data;t=t.next;)r+=e+t.data;return r}},{key:"concat",value:function(e){if(0===this.length)return s.alloc(0);for(var t,r,n,i=s.allocUnsafe(e>>>0),o=this.head,a=0;o;)t=o.data,r=i,n=a,s.prototype.copy.call(t,r,n),a+=o.data.length,o=o.next;return i}},{key:"consume",value:function(e,t){var r;return e<this.head.data.length?(r=this.head.data.slice(0,e),this.head.data=this.head.data.slice(e)):r=e===this.head.data.length?this.shift():t?this._getString(e):this._getBuffer(e),r}},{key:"first",value:function(){return this.head.data}},{key:"_getString",value:function(e){var t=this.head,r=1,n=t.data;for(e-=n.length;t=t.next;){var i=t.data,o=e>i.length?i.length:e;if(o===i.length?n+=i:n+=i.slice(0,e),0===(e-=o)){o===i.length?(++r,t.next?this.head=t.next:this.head=this.tail=null):(this.head=t,t.data=i.slice(o));break}++r}return this.length-=r,n}},{key:"_getBuffer",value:function(e){var t=s.allocUnsafe(e),r=this.head,n=1;for(r.data.copy(t),e-=r.data.length;r=r.next;){var i=r.data,o=e>i.length?i.length:e;if(i.copy(t,t.length-e,0,o),0===(e-=o)){o===i.length?(++n,r.next?this.head=r.next:this.head=this.tail=null):(this.head=r,r.data=i.slice(o));break}++n}return this.length-=n,t}},{key:u,value:function(e,t){return a(this,function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?n(Object(r),!0).forEach(function(t){i(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},t,{depth:0,customInspect:!1}))}}])&&o(t.prototype,r),c&&o(t,c),e}()},{buffer:12,util:11}],53:[function(e,t,r){(function(e){"use strict";function r(e,t){i(e,t),n(e)}function n(e){e._writableState&&!e._writableState.emitClose||e._readableState&&!e._readableState.emitClose||e.emit("close")}function i(e,t){e.emit("error",t)}t.exports={destroy:function(t,o){var s=this,a=this._readableState&&this._readableState.destroyed,u=this._writableState&&this._writableState.destroyed;return a||u?(o?o(t):t&&(this._writableState?this._writableState.errorEmitted||(this._writableState.errorEmitted=!0,e.nextTick(i,this,t)):e.nextTick(i,this,t)),this):(this._readableState&&(this._readableState.destroyed=!0),this._writableState&&(this._writableState.destroyed=!0),this._destroy(t||null,function(t){!o&&t?s._writableState?s._writableState.errorEmitted?e.nextTick(n,s):(s._writableState.errorEmitted=!0,e.nextTick(r,s,t)):e.nextTick(r,s,t):o?(e.nextTick(n,s),o(t)):e.nextTick(n,s)}),this)},undestroy:function(){this._readableState&&(this._readableState.destroyed=!1,this._readableState.reading=!1,this._readableState.ended=!1,this._readableState.endEmitted=!1),this._writableState&&(this._writableState.destroyed=!1,this._writableState.ended=!1,this._writableState.ending=!1,this._writableState.finalCalled=!1,this._writableState.prefinished=!1,this._writableState.finished=!1,this._writableState.errorEmitted=!1)},errorOrDestroy:function(e,t){var r=e._readableState,n=e._writableState;r&&r.autoDestroy||n&&n.autoDestroy?e.destroy(t):e.emit("error",t)}}}).call(this,e("_process"))},{_process:44}],54:[function(e,t,r){"use strict";var n=e("../../../errors").codes.ERR_STREAM_PREMATURE_CLOSE;function i(){}t.exports=function e(t,r,o){if("function"==typeof r)return e(t,null,r);r||(r={}),o=function(e){var t=!1;return function(){if(!t){t=!0;for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];e.apply(this,n)}}}(o||i);var s=r.readable||!1!==r.readable&&t.readable,a=r.writable||!1!==r.writable&&t.writable,u=function(){t.writable||l()},c=t._writableState&&t._writableState.finished,l=function(){a=!1,c=!0,s||o.call(t)},f=t._readableState&&t._readableState.endEmitted,h=function(){s=!1,f=!0,a||o.call(t)},p=function(e){o.call(t,e)},d=function(){var e;return s&&!f?(t._readableState&&t._readableState.ended||(e=new n),o.call(t,e)):a&&!c?(t._writableState&&t._writableState.ended||(e=new n),o.call(t,e)):void 0},g=function(){t.req.on("finish",l)};return function(e){return e.setHeader&&"function"==typeof e.abort}(t)?(t.on("complete",l),t.on("abort",d),t.req?g():t.on("request",g)):a&&!t._writableState&&(t.on("end",u),t.on("close",u)),t.on("end",h),t.on("finish",l),!1!==r.error&&t.on("error",p),t.on("close",d),function(){t.removeListener("complete",l),t.removeListener("abort",d),t.removeListener("request",g),t.req&&t.req.removeListener("finish",l),t.removeListener("end",u),t.removeListener("close",u),t.removeListener("finish",l),t.removeListener("end",h),t.removeListener("error",p),t.removeListener("close",d)}}},{"../../../errors":45}],55:[function(e,t,r){t.exports=function(){throw new Error("Readable.from is not available in the browser")}},{}],56:[function(e,t,r){"use strict";var n;var i=e("../../../errors").codes,o=i.ERR_MISSING_ARGS,s=i.ERR_STREAM_DESTROYED;function a(e){if(e)throw e}function u(e){e()}function c(e,t){return e.pipe(t)}t.exports=function(){for(var t=arguments.length,r=new Array(t),i=0;i<t;i++)r[i]=arguments[i];var l,f=function(e){return e.length?"function"!=typeof e[e.length-1]?a:e.pop():a}(r);if(Array.isArray(r[0])&&(r=r[0]),r.length<2)throw new o("streams");var h=r.map(function(t,i){var o=i<r.length-1;return function(t,r,i,o){o=function(e){var t=!1;return function(){t||(t=!0,e.apply(void 0,arguments))}}(o);var a=!1;t.on("close",function(){a=!0}),void 0===n&&(n=e("./end-of-stream")),n(t,{readable:r,writable:i},function(e){if(e)return o(e);a=!0,o()});var u=!1;return function(e){if(!a&&!u)return u=!0,function(e){return e.setHeader&&"function"==typeof e.abort}(t)?t.abort():"function"==typeof t.destroy?t.destroy():void o(e||new s("pipe"))}}(t,o,i>0,function(e){l||(l=e),e&&h.forEach(u),o||(h.forEach(u),f(l))})});return r.reduce(c)}},{"../../../errors":45,"./end-of-stream":54}],57:[function(e,t,r){"use strict";var n=e("../../../errors").codes.ERR_INVALID_OPT_VALUE;t.exports={getHighWaterMark:function(e,t,r,i){var o=function(e,t,r){return null!=e.highWaterMark?e.highWaterMark:t?e[r]:null}(t,i,r);if(null!=o){if(!isFinite(o)||Math.floor(o)!==o||o<0)throw new n(i?r:"highWaterMark",o);return Math.floor(o)}return e.objectMode?16:16384}}},{"../../../errors":45}],58:[function(e,t,r){arguments[4][24][0].apply(r,arguments)},{dup:24,events:27}],59:[function(e,t,r){(r=t.exports=e("./lib/_stream_readable.js")).Stream=r,r.Readable=r,r.Writable=e("./lib/_stream_writable.js"),r.Duplex=e("./lib/_stream_duplex.js"),r.Transform=e("./lib/_stream_transform.js"),r.PassThrough=e("./lib/_stream_passthrough.js"),r.finished=e("./lib/internal/streams/end-of-stream.js"),r.pipeline=e("./lib/internal/streams/pipeline.js")},{"./lib/_stream_duplex.js":46,"./lib/_stream_passthrough.js":47,"./lib/_stream_readable.js":48,"./lib/_stream_transform.js":49,"./lib/_stream_writable.js":50,"./lib/internal/streams/end-of-stream.js":54,"./lib/internal/streams/pipeline.js":56}],60:[function(e,t,r){"use strict";t.exports=function(){if("function"!=typeof arguments[0])throw new Error("callback needed");if("number"!=typeof arguments[1])throw new Error("interval needed");var e;if(arguments.length>0){e=new Array(arguments.length-2);for(var t=0;t<e.length;t++)e[t]=arguments[t+2]}return new function(e,t,r){var n=this;this._callback=e,this._args=r,this._interval=setInterval(e,t,this._args),this.reschedule=function(e){e||(e=n._interval),n._interval&&clearInterval(n._interval),n._interval=setInterval(n._callback,e,n._args)},this.clear=function(){n._interval&&(clearInterval(n._interval),n._interval=void 0)},this.destroy=function(){n._interval&&clearInterval(n._interval),n._callback=void 0,n._interval=void 0,n._args=void 0}}(arguments[0],arguments[1],e)}},{}],61:[function(e,t,r){var n=e("buffer"),i=n.Buffer;function o(e,t){for(var r in e)t[r]=e[r]}function s(e,t,r){return i(e,t,r)}i.from&&i.alloc&&i.allocUnsafe&&i.allocUnsafeSlow?t.exports=n:(o(n,r),r.Buffer=s),o(i,s),s.from=function(e,t,r){if("number"==typeof e)throw new TypeError("Argument must not be a number");return i(e,t,r)},s.alloc=function(e,t,r){if("number"!=typeof e)throw new TypeError("Argument must be a number");var n=i(e);return void 0!==t?"string"==typeof r?n.fill(t,r):n.fill(t):n.fill(0),n},s.allocUnsafe=function(e){if("number"!=typeof e)throw new TypeError("Argument must be a number");return i(e)},s.allocUnsafeSlow=function(e){if("number"!=typeof e)throw new TypeError("Argument must be a number");return n.SlowBuffer(e)}},{buffer:12}],62:[function(e,t,r){t.exports=function(e){var t=e._readableState;return t?t.objectMode||"number"==typeof e._duplexState?e.read():e.read((r=t,r.buffer.length?r.buffer.head?r.buffer.head.data.length:r.buffer[0].length:r.length)):null;var r}},{}],63:[function(e,t,r){"use strict";var n=e("safe-buffer").Buffer,i=n.isEncoding||function(e){switch((e=""+e)&&e.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};function o(e){var t;switch(this.encoding=function(e){var t=function(e){if(!e)return"utf8";for(var t;;)switch(e){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return e;default:if(t)return;e=(""+e).toLowerCase(),t=!0}}(e);if("string"!=typeof t&&(n.isEncoding===i||!i(e)))throw new Error("Unknown encoding: "+e);return t||e}(e),this.encoding){case"utf16le":this.text=u,this.end=c,t=4;break;case"utf8":this.fillLast=a,t=4;break;case"base64":this.text=l,this.end=f,t=3;break;default:return this.write=h,void(this.end=p)}this.lastNeed=0,this.lastTotal=0,this.lastChar=n.allocUnsafe(t)}function s(e){return e<=127?0:e>>5==6?2:e>>4==14?3:e>>3==30?4:e>>6==2?-1:-2}function a(e){var t=this.lastTotal-this.lastNeed,r=function(e,t,r){if(128!=(192&t[0]))return e.lastNeed=0,"�";if(e.lastNeed>1&&t.length>1){if(128!=(192&t[1]))return e.lastNeed=1,"�";if(e.lastNeed>2&&t.length>2&&128!=(192&t[2]))return e.lastNeed=2,"�"}}(this,e);return void 0!==r?r:this.lastNeed<=e.length?(e.copy(this.lastChar,t,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal)):(e.copy(this.lastChar,t,0,e.length),void(this.lastNeed-=e.length))}function u(e,t){if((e.length-t)%2==0){var r=e.toString("utf16le",t);if(r){var n=r.charCodeAt(r.length-1);if(n>=55296&&n<=56319)return this.lastNeed=2,this.lastTotal=4,this.lastChar[0]=e[e.length-2],this.lastChar[1]=e[e.length-1],r.slice(0,-1)}return r}return this.lastNeed=1,this.lastTotal=2,this.lastChar[0]=e[e.length-1],e.toString("utf16le",t,e.length-1)}function c(e){var t=e&&e.length?this.write(e):"";if(this.lastNeed){var r=this.lastTotal-this.lastNeed;return t+this.lastChar.toString("utf16le",0,r)}return t}function l(e,t){var r=(e.length-t)%3;return 0===r?e.toString("base64",t):(this.lastNeed=3-r,this.lastTotal=3,1===r?this.lastChar[0]=e[e.length-1]:(this.lastChar[0]=e[e.length-2],this.lastChar[1]=e[e.length-1]),e.toString("base64",t,e.length-r))}function f(e){var t=e&&e.length?this.write(e):"";return this.lastNeed?t+this.lastChar.toString("base64",0,3-this.lastNeed):t}function h(e){return e.toString(this.encoding)}function p(e){return e&&e.length?this.write(e):""}r.StringDecoder=o,o.prototype.write=function(e){if(0===e.length)return"";var t,r;if(this.lastNeed){if(void 0===(t=this.fillLast(e)))return"";r=this.lastNeed,this.lastNeed=0}else r=0;return r<e.length?t?t+this.text(e,r):this.text(e,r):t||""},o.prototype.end=function(e){var t=e&&e.length?this.write(e):"";return this.lastNeed?t+"�":t},o.prototype.text=function(e,t){var r=function(e,t,r){var n=t.length-1;if(n<r)return 0;var i=s(t[n]);if(i>=0)return i>0&&(e.lastNeed=i-1),i;if(--n<r||-2===i)return 0;if((i=s(t[n]))>=0)return i>0&&(e.lastNeed=i-2),i;if(--n<r||-2===i)return 0;if((i=s(t[n]))>=0)return i>0&&(2===i?i=0:e.lastNeed=i-3),i;return 0}(this,e,t);if(!this.lastNeed)return e.toString("utf8",t);this.lastTotal=r;var n=e.length-(r-this.lastNeed);return e.copy(this.lastChar,0,n),e.toString("utf8",t,n)},o.prototype.fillLast=function(e){if(this.lastNeed<=e.length)return e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,e.length),this.lastNeed-=e.length}},{"safe-buffer":61}],64:[function(e,t,r){(function(t,n){var i=e("process/browser.js").nextTick,o=Function.prototype.apply,s=Array.prototype.slice,a={},u=0;function c(e,t){this._id=e,this._clearFn=t}r.setTimeout=function(){return new c(o.call(setTimeout,window,arguments),clearTimeout)},r.setInterval=function(){return new c(o.call(setInterval,window,arguments),clearInterval)},r.clearTimeout=r.clearInterval=function(e){e.close()},c.prototype.unref=c.prototype.ref=function(){},c.prototype.close=function(){this._clearFn.call(window,this._id)},r.enroll=function(e,t){clearTimeout(e._idleTimeoutId),e._idleTimeout=t},r.unenroll=function(e){clearTimeout(e._idleTimeoutId),e._idleTimeout=-1},r._unrefActive=r.active=function(e){clearTimeout(e._idleTimeoutId);var t=e._idleTimeout;t>=0&&(e._idleTimeoutId=setTimeout(function(){e._onTimeout&&e._onTimeout()},t))},r.setImmediate="function"==typeof t?t:function(e){var t=u++,n=!(arguments.length<2)&&s.call(arguments,1);return a[t]=!0,i(function(){a[t]&&(n?e.apply(null,n):e.call(null),r.clearImmediate(t))}),t},r.clearImmediate="function"==typeof n?n:function(e){delete a[e]}}).call(this,e("timers").setImmediate,e("timers").clearImmediate)},{"process/browser.js":44,timers:64}],65:[function(e,t,r){(function(e){function r(t){try{if(!e.localStorage)return!1}catch(e){return!1}var r=e.localStorage[t];return null!=r&&"true"===String(r).toLowerCase()}t.exports=function(e,t){if(r("noDeprecation"))return e;var n=!1;return function(){if(!n){if(r("throwDeprecation"))throw new Error(t);r("traceDeprecation")?console.trace(t):console.warn(t),n=!0}return e.apply(this,arguments)}}}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],66:[function(e,t,r){t.exports=function e(t,r){if(t&&r)return e(t)(r);if("function"!=typeof t)throw new TypeError("need wrapper function");Object.keys(t).forEach(function(e){n[e]=t[e]});return n;function n(){for(var e=new Array(arguments.length),r=0;r<e.length;r++)e[r]=arguments[r];var n=t.apply(this,e),i=e[e.length-1];return"function"==typeof n&&n!==i&&Object.keys(i).forEach(function(e){n[e]=i[e]}),n}}},{}],67:[function(e,t,r){"use strict";t.exports=function(){throw new Error("ws does not work in the browser. Browser clients must use the native WebSocket object")}},{}],68:[function(e,t,r){t.exports=function(){for(var e={},t=0;t<arguments.length;t++){var r=arguments[t];for(var i in r)n.call(r,i)&&(e[i]=r[i])}return e};var n=Object.prototype.hasOwnProperty},{}]},{},[9])(9)});