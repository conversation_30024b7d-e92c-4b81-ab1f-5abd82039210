<template>
  <div class="wrap">
    <el-card class="card">
      <el-tabs v-model="activeName" @tab-click="handleTabClick">
        <el-tab-pane label="告警记录" name="alarmRecord">
          <div class="content-top">
            <div class="btn-area">
              <div class="date-picker-wrap">
                <el-button icon="el-icon-check" class="" type="primary" :size="size" @click="confirmAllRecord" v-if="showConfirmBtn">
                  确认
                </el-button>
                <el-button class="mr-10" icon="el-icon-delete" type="danger" :size="size" @click="batchDeleteRecord" v-if="showDeleteBtn">
                  删除</el-button
                >
                <el-date-picker
                  :size="size"
                  v-model="recordFilter.timeRange"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="timestamp"
                  class="mr-10"
                  @change="
                    {
                      recordPage.current = 1;
                      getRecordList();
                    }
                  "
                >
                </el-date-picker>
                <el-select
                  :size="size"
                  v-model="recordFilter.confirmed"
                  placeholder="是否确认"
                  class="mr-10"
                  style="width: 100px"
                  clearable
                  @change="changeConfirmed()"
                >
                  <el-option v-for="item in CONFIRMED_ENUM" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                </el-select>
                <el-select
                  :size="size"
                  v-model="recordFilter.level"
                  placeholder="告警级别"
                  class="mr-10"
                  style="width: 100px"
                  clearable
                  @change="
                    {
                      recordPage.current = 1;
                      getRecordList();
                    }
                  "
                >
                  <el-option v-for="item in ALARM_LEVEL" :key="item.value" :label="item.label" :value="item.value"> </el-option>
                </el-select>
              </div>
              <el-select
                :size="size"
                v-model="recordFilter.alarmType"
                placeholder="告警类别"
                class="mr-10"
                style="width: 140px"
                clearable
                @change="
                  {
                    recordPage.current = 1;
                    getRecordList();
                  }
                "
              >
                <el-option v-for="item in ALARM_ENUM" :key="item.value" :label="item.label" :value="item.value"> </el-option>
              </el-select>
              <el-select
                :size="size"
                v-model="recordFilter.alarmId"
                placeholder="告警规则"
                class="mr-10"
                style="width: 180px"
                clearable
                filterable
                @change="
                  {
                    recordPage.current = 1;
                    getRecordList();
                  }
                "
              >
                <el-option v-for="item in allRule" :key="item.value" :label="item.label" :value="item.value"> </el-option>
              </el-select>
              <el-select
                :size="size"
                v-model="recordFilter.spaceId"
                placeholder="所属空间"
                class="mr-10"
                style="width: 180px"
                clearable
                filterable
                @change="
                  {
                    recordPage.current = 1;
                    getRecordList();
                  }
                "
              >
                <el-option v-for="item in spaceList" :key="item.id" :label="item.spaceName" :value="item.id"> </el-option>
              </el-select>
              <el-select
                :size="size"
                v-model="recordFilter.deviceId"
                placeholder="告警设备"
                class="mr-10"
                style="width: 180px"
                clearable
                filterable
                @change="
                  {
                    recordPage.current = 1;
                    getRecordList();
                  }
                "
              >
                <el-option v-for="item in allDeviceList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
              </el-select>
              <el-input
                placeholder="告警描述"
                suffix-icon="el-icon-search"
                clearable
                v-model="recordFilter.desc"
                v-debounce="[
                  (e) => {
                    recordPage.current = 1;
                    getRecordList();
                  },
                ]"
                @clear="
                  () => {
                    recordPage.current = 1;
                    getRecordList();
                  }
                "
                size="small"
                class="search-input"
              >
              </el-input>
            </div>
          </div>
          <div class="content-body">
            <el-table
              row-key="id"
              :data="recordList"
              highlight-current-row
              @sort-change="doRecordSortChange"
              :default-sort="{ prop: 'gmtCreate', order: 'descending' }"
              height="100px"
              v-loading="recordLoading"
              ref="recordList"
              v-adaptive="{ bottomOffset: 70, topOffset: 220 }"
              @selection-change="handleSelectionChange"
            >
              <el-table-column type="selection" width="45" fixed="left" reserve-selection></el-table-column>
              <el-table-column label="序号" width="70" align="center" fixed="left">
                <template slot-scope="scope">
                  {{ scope.$index + (recordPage.current - 1) * recordPage.size + 1 }}
                </template>
              </el-table-column>
              <el-table-column prop="level" label="告警级别" width="80" align="center" fixed="left" show-overflow-tooltip>
                <template slot-scope="{ row }">
                  <el-tag :type="getAlarmLevelTagType(row)">
                    {{ formatRuleLevel(row) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="告警设备" width="160" align="left" show-overflow-tooltip fixed="left">
                <template slot-scope="{ row }">
                  {{ row.deviceList ? row.deviceList.map((item) => item.deviceName).join(',') : '--' }}
                </template>
              </el-table-column>
              <el-table-column label="所属空间" width="160" align="left" show-overflow-tooltip>
                <template slot-scope="{ row }">
                  {{ row.spaceList ? row.spaceList.map((item) => item.spaceName).join(',') : '--' }}
                </template>
              </el-table-column>
              <el-table-column prop="name" label="告警名称" width="160" show-overflow-tooltip> </el-table-column>
              <el-table-column prop="description" label="告警描述" min-width="140" show-overflow-tooltip></el-table-column>
              <el-table-column label="告警时间" width="150" align="center" show-overflow-tooltip>
                <template slot-scope="{ row }">
                  {{ row.alarmTime ? new Date(row.alarmTime).Format('yyyy-MM-dd hh:mm') : '-' }}
                </template>
              </el-table-column>
              <el-table-column label="告警类别" width="150" align="center" show-overflow-tooltip>
                <template slot-scope="{ row }">
                  <el-tag :type="getAlarmTypeTag(row)">
                    {{ ALARM_ENUM.find((item) => row.type === item.value).label }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="是否确认" width="120" align="center" show-overflow-tooltip>
                <template slot-scope="{ row }">
                  <svg-icon icon-class="circle" :style="{ color: row.confirmed ? '#1ba784' : '#fb8b05' }" class="circle-icon" />
                  <span> {{ row.confirmed ? '已确认' : '未确认' }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="confirmName" label="确认人" width="120" align="center" show-overflow-tooltip>
                <template slot-scope="{ row }">
                  {{ row.confirmName ? row.confirmName : '--' }}
                </template>
              </el-table-column>
              <el-table-column label="操作项" align="center" :show-overflow-tooltip="true" width="280" fixed="right">
                <template slot-scope="{ row }">
                  <el-button
                    class="edit-btn"
                    icon="el-icon-check"
                    :size="size"
                    :disabled="row.confirmed"
                    @click="confirmRecord(row)"
                    v-if="showConfirmBtn"
                    >确认</el-button
                  >
                  <el-button class="purple-btn" icon="el-icon-notebook-2" :size="size" @click="viewRecord(row)">详情</el-button>
                  <el-button class="delete-btn" icon="el-icon-delete" :size="size" @click="deleteRecord(row)" v-action:IotAlarmCenter|deleteAlarm
                    >删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <pagination :total="recordPage.total" :page.sync="recordPage.current" :limit.sync="recordPage.size" @pagination="getRecordList" />
          </div>
        </el-tab-pane>
        <el-tab-pane label="平台告警规则" name="alarmRule">
          <div class="content-top">
            <div class="btn-area">
              <el-button
                class="mr-10"
                type="primary"
                :size="size"
                icon="el-icon-plus"
                @click.native="handleEditRule(undefined)"
                v-action:IotAlarmCenter|addAlarmRule
              >
                添加平台告警规则
              </el-button>
              <el-select
                v-model="ruleFilter.level"
                :size="size"
                placeholder="告警规则级别"
                class="mr-10"
                style="width: 150px"
                @change="
                  {
                    rulePage.current = 1;
                    getRuleList();
                  }
                "
                clearable
              >
                <el-option :label="item.label" :value="item.value" v-for="item in ALARM_LEVEL" :key="item.value"></el-option>
              </el-select>
              <el-input
                placeholder="告警规则名称"
                suffix-icon="el-icon-search"
                :size="size"
                style="width: 150px"
                v-model="ruleFilter.name"
                v-debounce="[
                  (e) => {
                    rulePage.current = 1;
                    getRuleList(e);
                  },
                ]"
                class="mr-10"
              />
            </div>
          </div>
          <div class="content-body">
            <el-table
              :data="ruleList"
              highlight-current-row
              @sort-change="doRuleSortChange"
              :default-sort="{ prop: 'gmtCreate', order: 'descending' }"
              height="100px"
              v-loading="ruleLoading"
              v-adaptive="{ bottomOffset: 70, topOffset: 180 }"
            >
              <el-table-column label="序号" width="70" align="center" fixed="left">
                <template slot-scope="scope">
                  {{ scope.$index + (rulePage.current - 1) * rulePage.size + 1 }}
                </template>
              </el-table-column>
              <el-table-column fixed="left" prop="level" label="告警级别" min-width="80" align="center" show-overflow-tooltip>
                <template slot-scope="{ row }">
                  <el-tag :type="getAlarmLevelTagType(row)">
                    {{ formatRuleLevel(row) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column fixed="left" prop="name" label="规则名称" width="180" show-overflow-tooltip> </el-table-column>
              <el-table-column prop="description" label="规则描述" min-width="120" show-overflow-tooltip> </el-table-column>
              <el-table-column label="最近告警时间" min-width="150" align="center" show-overflow-tooltip>
                <template slot-scope="{ row }">
                  {{ row.alarmLastTime ? new Date(row.alarmLastTime).Format('yyyy-MM-dd hh:mm') : '-' }}
                </template>
              </el-table-column>
              <el-table-column label="是否全部确认" min-width="120" align="center" show-overflow-tooltip>
                <template slot-scope="{ row }">
                  <svg-icon icon-class="circle" :style="{ color: row.alarmConfirmed ? '#1ba784' : '#fb8b05' }" class="circle-icon" />
                  <span> {{ row.alarmConfirmed ? '已确认' : '未确认' }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="valid" label="是否启用" min-width="100" align="center" show-overflow-tooltip>
                <template slot-scope="{ row }">
                  {{ row.valid ? '已启用' : '未启用' }}
                </template>
              </el-table-column>
              <el-table-column prop="createWorkOrder" label="自动创建工单" width="100" align="center" show-overflow-tooltip>
                <template slot-scope="{ row }">
                  {{ row.createWorkOrder ? '已启用' : '--' }}
                </template>
              </el-table-column>
              <el-table-column prop="workOrderManagerName" label="工单受理人" width="100" align="center" show-overflow-tooltip>
                <template slot-scope="{ row }">
                  {{ row.workOrderManagerName }}
                </template>
              </el-table-column>
              <el-table-column prop="ruleId" label="规则ID" align="center" min-width="100" show-overflow-tooltip> </el-table-column>
              <el-table-column label="操作项" align="center" :show-overflow-tooltip="true" width="300" fixed="right">
                <template slot-scope="{ row }">
                  <el-button
                    class="edit-btn"
                    icon="el-icon-check"
                    :size="size"
                    @click="confirmRule(row)"
                    :disabled="row.alarmConfirmed"
                    v-action:IotAlarmCenter|confirmAlarm
                  >
                    确认
                  </el-button>
                  <el-button class="add-btn" icon="el-icon-notebook-2" :size="size" @click="handleEditRule(row)">
                    {{ hasEditAlarmRulePermission ? '编辑' : '详情' }}</el-button
                  >
                  <el-dropdown v-if="hasEditOrDeletePermission">
                    <el-button class="purple-btn ml-10" :size="size">更多<i class="el-icon-arrow-down el-icon--right"></i> </el-button>
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item @click.native.stop="setValid(row)" v-action:IotAlarmCenter|editAlarmRule>
                        {{ row.valid ? '禁用' : '启用' }}
                      </el-dropdown-item>
                      <el-dropdown-item @click.native.stop="deleteRule(row)" v-action:IotAlarmCenter|deleteAlarmRule> 删除 </el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </template>
              </el-table-column>
            </el-table>
            <pagination :total="rulePage.total" :page.sync="rulePage.current" :limit.sync="rulePage.size" @pagination="getRuleList" /> </div
        ></el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script>
  import { hasPermission } from '@/directive/permission/index.js';
  import { ALARM_LEVEL, CONFIRMED_ENUM, ALARM_ENUM } from '../../enum';
  import {
    getAlarmRule,
    setEnableRule,
    deleteAlarmRule,
    deleteAlarmRecord,
    confirmByRule,
    getAlarmRecord,
    confirmAlarm,
    getAllAlarmRule,
    deviceGetAllList,
    confirmAllAlarm,
    batchDeleteAlarmRecord,
    getAllGatewaySpace,
  } from '@/api/iotControl';

  export default {
    components: {},
    data() {
      return {
        CONFIRMED_ENUM,
        ALARM_LEVEL,
        ALARM_ENUM,
        size: 'small',
        ruleFilter: {
          name: '',
          level: '',
          creator: '',
        },
        rulePage: {
          total: 0,
          current: 1,
          size: 20,
        },
        recordFilter: {
          alarmId: '',
          deviceId: '',
          timeRange: [undefined, undefined],
          confirmed: false,
          alarmType: '',
          desc: '',
          spaceId: null,
          level: null,
        },
        recordPage: {
          total: 0,
          current: 1,
          size: 20,
        },
        ruleList: [],
        recordList: [],
        ruleLoading: false,
        recordLoading: false,
        ruleSort: {
          direction: 'DESC',
          properties: 'gmtCreate',
        },
        recordSort: {
          direction: 'DESC',
          properties: 'gmtCreate',
        },
        activeName: 'alarmRecord',
        allRule: [],
        allDeviceList: [],
        spaceList: [],
        multipleSelection: [],
        timer: null,
      };
    },
    created() {
      let confirmed = localStorage.getItem('alarmRecordFilterConfirmed');
      if (confirmed === '') {
        this.recordFilter.confirmed = '';
      } else {
        this.recordFilter.confirmed = confirmed === 'true';
      }
      localStorage.setItem('alarmRecordFilterConfirmed', this.recordFilter.confirmed);
      this.getRuleList();
      this.getDeviceList();
      this.getRecordList();
      this.getAllRule();
      this.getSpaceList();
      this.timer = setInterval(() => {
        this.getRecordList(true);
      }, 10 * 1000);
    },
    beforeDestroy() {
      clearInterval(this.timer);
    },
    methods: {
      changeConfirmed() {
        localStorage.setItem('alarmRecordFilterConfirmed', this.recordFilter.confirmed);
        this.recordPage.current = 1;
        this.getRecordList();
      },
      getSpaceList() {
        getAllGatewaySpace().then(({ data }) => {
          this.spaceList = data || [];
        });
      },
      handleTabClick(tab, event) {
        if (tab.index === '0') {
          this.getRecordList();
          this.getAllRule();
        } else if (tab.index === '1') {
          this.getRuleList();
        }
      },
      getDeviceList() {
        deviceGetAllList().then(({ data }) => {
          this.allDeviceList = data.map((device) => ({ label: device.deviceName, value: device.deviceId }));
        });
      },
      getAllRule() {
        getAllAlarmRule().then(({ data }) => {
          this.allRule = data.map((rule) => ({ label: rule.name, value: rule.id }));
        });
      },
      /**
       * 添加|编辑报警规则
       * @param {*} row
       */
      handleEditRule(row) {
        this.$router.push({ name: 'editAlarmRule', params: { id: row?.id } });
      },
      getRuleList() {
        this.ruleLoading = true;
        let params = {
          page: this.rulePage.current,
          size: this.rulePage.size,
          direction: this.ruleSort.direction,
          properties: this.ruleSort.properties,
          name: this.ruleFilter.name,
          level: this.ruleFilter.level,
          creator: this.ruleFilter.creator,
        };
        getAlarmRule(params).then(({ data }) => {
          this.ruleList = data.rows;
          this.rulePage.total = data.total;
          this.ruleLoading = false;
        });
      },
      /**
       * 根据规则id批量确认报警信息
       */
      confirmRule(row) {
        let params = {
          alarmId: row.id,
        };
        confirmByRule(params).then(() => {
          this.$message.success('操作成功!');
          this.getRuleList();
        });
      },
      doRuleSortChange(col) {
        this.sort.direction = 'ascending' === col.order ? 'ASC' : 'DESC';
        this.sort.properties = col.prop;
        this.getRuleList();
      },
      deleteRule(row) {
        if (row) {
          this.$confirm('确定要删除所选告警规则吗?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }).then(() => {
            deleteAlarmRule([row.id]).then(() => {
              this.$message.success('删除成功!');
              this.getRuleList();
            });
          });
        }
      },
      /**
       * 设置规则是否禁用/启用
       * @param {*} valid
       */
      setValid(row) {
        setEnableRule(row.ruleId, !row.valid).then(() => {
          this.$message.success('操作成功!');
          this.getRuleList();
        });
      },
      getRecordList(reserveSelected = false) {
        if (this.recordList.length === 0) {
          this.recordLoading = true;
        }
        let params = {
          page: this.recordPage.current,
          size: this.recordPage.size,
          direction: this.recordSort.direction,
          properties: this.recordSort.properties,
          deviceId: this.recordFilter.deviceId,
          alarmId: this.recordFilter.alarmId,
          alarmType: this.recordFilter.alarmType,
          confirmed: this.recordFilter.confirmed,
          startTime: this.recordFilter.timeRange ? this.recordFilter.timeRange[0] : '',
          endTime: this.recordFilter.timeRange ? this.recordFilter.timeRange[1] : '',
          desc: this.recordFilter.desc,
          spaceId: this.recordFilter.spaceId,
          level: this.recordFilter.level,
        };
        getAlarmRecord(params)
          .then(({ data }) => {
            this.recordList = data.rows;
            this.recordPage.total = data.total;
            //取消选中
            this.$nextTick(() => {
              if (!reserveSelected) {
                this.multipleSelection = [];
                this.$refs.recordList.clearSelection();
              }
            });
          })
          .finally(() => {
            this.recordLoading = false;
          });
      },

      /**
       * 排序变化
       * @param col
       */
      doRecordSortChange(col) {
        this.sort.direction = 'ascending' === col.order ? 'ASC' : 'DESC';
        this.sort.properties = col.prop;
        this.getRecordList();
      },

      confirmAllRecord() {
        if (this.multipleSelection.length == 0) {
          this.$message.warning('请先选择告警记录!');
          return;
        }
        confirmAllAlarm(this.multipleSelection.map((item) => item.id)).then(() => {
          this.$message.success('操作成功!');
          this.getRecordList();
        });
      },
      /**
       * 批量删除告警记录
       */
      batchDeleteRecord() {
        const ids = this.multipleSelection.map((item) => item.id);
        if (ids == null || ids.length == 0) {
          this.$message.warning('请先选择告警记录!');
          return;
        }
        batchDeleteAlarmRecord(ids).then(() => {
          this.$message.success('操作成功!');
          this.getRecordList();
        });
      },
      confirmRecord(row) {
        confirmAlarm([row.id]).then(() => {
          this.$message.success('操作成功!');
          this.getRecordList();
        });
      },
      deleteRecord(row) {
        if (row) {
          this.$confirm('确定要删除所选告警记录吗?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          }).then(() => {
            deleteAlarmRecord([row.id]).then(() => {
              this.$message.success('删除成功!');
              this.getRecordList();
            });
          });
        }
      },

      // 格式化报警级别
      formatRuleLevel(row) {
        let level = this.ALARM_LEVEL.find((item) => item.value === row.level);
        if (level) {
          return level.label;
        }
        return '';
      },
      getAlarmLevelTagType(row) {
        if (row.level == 'ERROR') {
          return 'info';
        } else if (row.level == 'WARN') {
          return 'warning';
        } else if (row.level == 'FATAL') {
          return 'danger';
        }
      },
      getAlarmTypeTag(row) {
        if (row.type == 'platform_automation_alarm') {
          return '';
        } else if (row.type == 'device_alarm_event') {
          return 'danger';
        }
      },
      viewRecord(row) {
        this.$router.push({ name: 'alarmRecordDetail', params: { id: row.id } });
      },
      handleSelectionChange(val) {
        this.multipleSelection = val;
      },
    },
    computed: {
      showConfirmBtn() {
        return hasPermission(undefined, 'IotAlarmCenter', 'confirmAlarm');
      },
      showDeleteBtn() {
        return hasPermission(undefined, 'IotAlarmCenter', 'deleteAlarmRule');
      },
      hasEditAlarmRulePermission() {
        return hasPermission(undefined, 'IotAlarmCenter', 'editAlarmRule');
      },
      hasEditOrDeletePermission() {
        return hasPermission(undefined, 'IotAlarmCenter', 'editAlarmRule') || hasPermission(undefined, 'IotAlarmCenter', 'deleteAlarmRule');
      },
    },
  };
</script>

<style lang="scss" scoped>
  .wrap {
    margin: 10px 0px 20px 0px;
    height: 100%;
  }
  .search-input {
    width: 160px;
  }
  .card {
    height: 100%;
    ::v-deep .el-card__body {
      height: 100%;
      padding: 20px 20px 0 20px;
      .el-tabs {
        height: 100%;
        display: flex;
        flex-direction: column;
        .el-tabs__content {
          flex: 1;
          height: 0;
          .el-tab-pane {
            height: 100%;
            display: flex;
            overflow: hidden;
            flex-direction: column;
            .content-body {
              flex: 1;
            }
          }
        }
      }
    }
  }
  .content-top .btn-area {
    margin: 0 0 10px 0;
  }
  .date-picker-wrap {
    width: 100%;
    margin-bottom: 8px;
  }
</style>
