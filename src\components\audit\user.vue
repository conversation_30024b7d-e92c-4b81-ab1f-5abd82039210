<template>
  <el-dialog
    title="选择用户"
    :visible.sync="visible"
    width="1000px"
    :close-on-click-modal="false"
    @close="close"
  >
    <div class="margin-bottom10 search-wrap">
      <el-input
        placeholder="用户名"
        suffix-icon="el-icon-search"
        size="small"
        v-model="szName"
        v-debounce="[
          (e) => {
            pagination.curPage = 1;
            getUserList(e);
          },
        ]"
        style="width: 200px;"
      />
    </div>
    <transfer
      ref="transfer"
      keyword="username"
      :list="userList"
      :disabedList="disabedList"
      :chooseList="chooseUserList"
      :defaultSort="{ prop: sort.prop, order: 'descending' }"
      @sortChange="sortChange"
    >
      <div slot="titleLeft">候选用户</div>
      <template slot="leftTable">
        <el-table-column label="序号" width="60">
          <template slot-scope="scope">
            {{ scope.$index + (pagination.curPage - 1) * pagination.size + 1 }}
          </template>
        </el-table-column>
        <el-table-column
          prop="username"
          sortable="custom"
          :show-overflow-tooltip="true"
          label="用户名"
          width="80"
        >
        </el-table-column>
        <el-table-column
          prop="nickname"
          :show-overflow-tooltip="true"
          label="昵称"
          width="80"
        >
        </el-table-column>
        <el-table-column
          prop="departmentName"
          :show-overflow-tooltip="true"
          :label="`所属${$t('deptLabel')}`"
        >
        </el-table-column>
        <el-table-column
          prop="videoTerminal"
          :show-overflow-tooltip="true"
          label="终端"
        >
        <template slot-scope="scope">
          {{scope.row.videoTerminal ? scope.row.videoTerminal.name : ''}}
        </template>
        </el-table-column>
      </template>
      <pagination
        slot="pagination"
        :total="pagination.total"
        :page.sync="pagination.curPage"
        :limit.sync="pagination.size"
        :layout="TRANSFER_PAGINATION_LAYOUT"
        @pagination="getUserList"
        :autoScroll="false"
      />
      <div slot="titleRight">已选用户</div>
      <template slot="rightTable">
        <el-table-column type="index" label="序号" min-width="70">
        </el-table-column>
        <el-table-column
          prop="username"
          sortable
          :show-overflow-tooltip="true"
          label="用户名"
          width="150"
        />
        <el-table-column
          prop="nickname"
          :show-overflow-tooltip="true"
          label="昵称"
          min-width="80"
        >
        </el-table-column>
      </template>
    </transfer>
    <div slot="footer" class="dialog-footer">
      <el-button @click="close">取消</el-button>
      <el-button class="okBtn" @click="handleOk">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import dlg from "@/mixins/dlg";
import transfer from "../transfer/index.vue";
import { getUsersInCompany } from "@/api/system";
import { TRANSFER_PAGINATION_LAYOUT } from "@/utils/enum";

export default {
  mixins: [dlg],
  components: {
    transfer
  },
  props: {
    users: {
      type: Array,
      default: () => [],
      require: false
    },
    curUsers: {
      type: Array,
      default: () => [],
      require: false
    },
    companyId: {
      type: String | Number,
      default: ""
    }
  },
  data() {
    return {
      // 用户名搜索
      szName: "",
      // 用户列表
      userList: [],
      chooseUserList: [],
      disabedList: [],

      // 分页参数
      pagination: {
        curPage: 1,
        size: 20,
        total: 0
      },
      sort: {
        prop: "username",
        order: "DESC"
      },
      TRANSFER_PAGINATION_LAYOUT
    };
  },
  created() {
    if (this.curUsers && this.curUsers.length) {
      this.chooseUserList = this.curUsers.map(item => ({
        username: item.username,
        nickname: item.nickname,
        id: item.id
      }));
    }
    if (this.users && this.users.length) {
      this.disabedList = this.users.map(item => ({
        username: item.username,
        nickname: item.nickname,
        id: item.id
      }));
    }
    this.getUserList();
  },
  methods: {
    handleOk() {
      let list = this.$refs.transfer.rightList;
      this.$emit("handleSelect", list);
      this.close();
    },
    /**
     * 获取用户列表
     */
    getUserList() {
      getUsersInCompany(
        this.companyId,
        this.pagination.curPage,
        this.pagination.size,
        this.szName,
        this.sort.order,
        this.sort.prop
      ).then(res => {
        this.userList = res.data.rows;
        this.pagination.total = res.data.total;
      });
    },
    sortChange(col) {
      this.sort.order = "ascending" === col.order ? "ASC" : "DESC";
      this.sort.prop = col.prop;
      this.getUserList();
    }
  }
};
</script>
<style lang="scss" scoped></style>
