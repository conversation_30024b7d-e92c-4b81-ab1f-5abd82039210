<template>
  <!--审核弹框-->
  <el-dialog
    :title="type|auditNameFilter"
    :visible.sync="bDlgShow"
    width="450px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="approve-desc">审核意见</div>
    <el-radio-group v-model="bApprove" class="margin-top10">
      <el-radio :label="true">审核通过</el-radio>
      <el-radio :label="false">审核不通过</el-radio>
    </el-radio-group>
    <el-input
      type="textarea"
      v-model="szApproveDesc"
      class="margin-top10"
    ></el-input>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button class="okBtn" @click="audit">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { updateApproveResult } from "@/api/info";
export default {
  data() {
    return {
      bApprove: true,
      szApproveDesc: "",
      id: -1,
      bDlgShow: false,
      type: "conference",
    };
  },
  methods: {
    show(id, type) {
      this.id = id;
      this.type = type;
      this.bDlgShow = true;
    },
    handleClose() {
      this.bApprove = true;
      this.szApproveDesc = "";
      this.bDlgShow = false;
    },
    audit() {
      if (!this.bApprove && this.szApproveDesc === "") {
        this.$message.warning("审核意见不能为空！");
        return;
      }
      updateApproveResult({
        resourceId: this.id,
        type: this.type,
        approve: this.bApprove,
        approveDesc: this.szApproveDesc
      })
        .then(() => {
          this.$message.success("审核成功");
          this.handleClose();
          this.$emit("refresh");
        })
        .catch(() => {
          // do nothing
        });
    }
  }
};
</script>
<style lang="scss" scoped></style>
