import { setConfCohost, confControlSelectPic,cancelSubtitle } from '@/api/conferenceGuide'
import { getFormatRooms } from '../enum';

// 会场会控操作
export default {
  methods: {
    /**
       *  更多下拉列表
       */
    handleCommand(command, row) {
      let params = {
        method: command,
        rooms: row,
      };
      this.$emit('confControl', params);
    },
    /**
       * 选看会场
       */
    selectPicConf(row) {
      this.$evtBus.$emit('selectPicConf', row);
    },
    /**
       * 选看会议多画面
       */
    selectConfMultiPic(row) {
      if(!row || !Object.keys(row).length){
        return;
      }
      confControlSelectPic({
        conferenceId: this.id,
        venue: {
          id: row.id,
          type: row.type,
          terminalId: row.terminalId,
        },
        vcPic: null
      }).then(() => {
        this.$message.success('选看会议多画面成功！');
      })
    },
    /**
     * 会场多画面轮询
     */
    selectPicPoll(row) {
      this.$evtBus.$emit('selectPicPoll', row);
    },
    /**
     * 判断是否可以更改轮询状态
     * @param newStatus 新状态
     * @param prevStatus 之前的状态
     */
    checkPicPollStatus(newStatus, prevStatus) {
      if (newStatus === 'START' && ['SET', 'STOP'].includes(prevStatus)) {
        return true
      }
      if (newStatus === 'STOP' && ['START'].includes(prevStatus)) {
        return true
      }
      if (newStatus === 'CANCEL' && ['START', 'SET', 'STOP'].includes(prevStatus)) {
        return true
      }

      return false
    },
    /**
     * 开始/取消会场轮询
     * @param row 会场
     * @param status START：开始 STOP：停止 CANCEL：取消
     */
    togglePicPoll(row, status) {
      if (this.bPoll) {
        this.$message.warning('会议多画面轮询正在进行，无法设置会场多画面轮询');
        return;
      }
      let prevStatus = (row.poll && row.poll.pollStatus) || (row.picPoll && row.picPoll.pollStatus)
      // 判断是否可以更改轮询状态
      if (!this.checkPicPollStatus(status, prevStatus)) {
        return;
      }

      this.$emit('confControl', {
        method: 'changePicPollStatus',
        rooms: row,
        args: status,
      });
    },
    /**
       * 替换会场
       * @param {*} row
       */
    replaceConf(row) {
      this.$evtBus.$emit('replaceConf', row);
    },
    /**
     * 主备切换
     * @param {*} row
     */
    switchoverPrimaryStandby(row) {
      this.$evtBus.$emit('switchoverPrimaryStandby', row);
    },
    /**
     * 查看会场实时（网络）信息
     * @param {*} row
     */
    checkRealtimeInfo(row) {
      this.$emit('confControl', {
        method: 'checkRealTimeInfo',
        rooms: row,
      });
    },
    /**
     * 查看会场预览图
     * @param {*} row
     */
    checkPreviewImage(row) {
      this.$emit('confControl', {
        method: 'checkPreviewImage',
        rooms: row,
      });
    },
    /**
     * 等候室控制
     */
    waitingRoomControl(row, cmdType) {
      this.$emit('confControl', {
        method: 'waitingRoomControl',
        rooms: row,
        args: cmdType,
      });
    },
    /**
     * 设置/取消设置预监终端
     */
    togglePreMonitor(row) {
      this.$emit('confControl', {
        method: 'togglePreMonitor',
        rooms: row,
        args: !row.isPreMonitor,
      });
    },
    /**
     * 设置单元会议多画面
     */
    setUnitConfScreen(row) {
      this.$evtBus.$emit('setUnitConfScreen', row);
    },
    /**
     * 设置单元会议多画面轮询
     */
    setUnitConfScreenPoll(row) {
      this.$evtBus.$emit('setScreenPoll', row);
    },
    /**
     * 开始/暂停/取消单元会议多画面轮询
     * @param row 会场
     * @param status START：开始 STOP：停止 CANCEL：取消
     */
    toggleUnitConfPicPoll(row, status) {
      let prevStatus = row.poll && row.poll.pollStatus
      // 判断是否可以更改轮询状态
      if (!this.checkPicPollStatus(status, prevStatus)) {
        return;
      }
      this.$evtBus.$emit('toggleScreenPoll', row, status);
    },
    /**
     * 设置联席主持人/取消联席主持人
     */
    toggleConfCohost(row) {
      setConfCohost({
        conferenceId: this.id,
        venues: [{
          id: row.id,
          type: row.type,
          terminalId: row.terminalId
        }],
        cohost: !row.cohost
      }).then(res => {
        this.$message.success('操作成功!');
      })
    },
    /**
     * 设置会场字幕
     */
    setSubtitle(row) {
      this.$emit('confControl', {
        method: 'setSubtitle',
        rooms: [row],
      });
    },
    /**
     * 取消会场字幕
     */
    cancelSubtitle(row) {
      if (row && row.voice) {
        this.$message.error('语音入会终端无法设置字幕');
        return;
      }

      cancelSubtitle({
        conferenceId: this.id,
        venues: getFormatRooms(row),
      }).then((res) => {
        this.$message.success('字幕取消成功！');
      });
    }
  }
}
