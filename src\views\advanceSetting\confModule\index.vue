<template>
  <div v-loading="loading">
    <div class="content-top">
      <div class="btn-area">
        <el-button type="primary" @click="doUpload" icon="el-icon-plus" size="small">导入</el-button>
      </div>
    </div>

    <div class="content-body">
      <el-table
        v-adaptive
        :data="list"
        height="300px"
        highlight-current-row
        @selection-change="handleSelectionChange"
        row-key="id"
        @row-click="clickRow"
        ref="table"
      >
        <el-table-column align="center" fixed="left" label="名称" min-width="200" prop="name" show-overflow-tooltip> </el-table-column>
        <el-table-column align="center" fixed="left" label="图片" min-width="200" prop="name" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <el-image :src="row.img" fit="fill" style="width: 100px; height: 50px" @click="preview(row.img)"></el-image>
          </template>
        </el-table-column>
        <el-table-column :show-overflow-tooltip="true" align="center" fixed="right" label="操作项" width="280">
          <template slot-scope="{ row }"  >
            <el-button size="mini" class="add-btn" icon="el-icon-download" @click.stop="doExport(row)">导出</el-button>
            <el-button size="mini" class="edit-btn" icon="el-icon-edit-outline" @click.stop="doEdit(row)">编辑</el-button>
            <el-button size="mini" class="delete-btn" icon="el-icon-delete" @click.stop="doDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <upload-dlg ref="uploadDlgRef" @refresh="getList"></upload-dlg>
  </div>
</template>

<script>
  import { getConfModule, deleteConfModule } from '@/api/advance';
  import { exportFile } from '@/utils/utils';
  import uploadDlg from './modules/uploadDlg';

  export default {
    components: {
      uploadDlg,
    },
    data() {
      return {
        loading: false,
        // 列表数据
        list: [],
        // 选中的数据
        multipleSelection: [],
      };
    },
    created() {
      this.getList();
    },
    methods: {
      clickRow(row) {
        this.$refs.table.toggleRowSelection(row);
      },
      handleSelectionChange(val) {
        this.multipleSelection = val;
      },
      getList() {
        getConfModule().then(({ data }) => {
          this.list = data;
          console.log(data);
        });
      },
      /**
       * 导出会议指引模板
       */
      doExport(row) {
        exportFile({ url: `/oauth/module/export?id=${row.id}`, method: 'POST', name: `${row.name}.zip` });
      },
      /**
       * 删除会议指引模板
       */
      doDelete(row) {
        this.$confirm(`确定删除会议指引模板【${row.name}】?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          deleteConfModule(row.id).then((res) => {
            this.$message.success('删除成功！');
            this.getList();
          });
        });
      },
      /**
       * 导入会议指引模板
       */
      doUpload() {
        this.$refs.uploadDlgRef && this.$refs.uploadDlgRef.show();
      },
      /**
       * 图片预览
       */
      preview(url){
        window.open(window.location.origin + '/' + url);
      },
      /**
       * 编辑会议指引模板
       */
      doEdit(row){
        this.$refs.uploadDlgRef && this.$refs.uploadDlgRef.show(row.id);
      }
    },
  };
</script>

<style lang="scss" scoped>
  .app-main > div {
    height: auto;
  }
</style>
