<template>
    <div class="">
        <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll">全选</el-checkbox>
        <div style="margin: 10px 0;"></div>
        <el-checkbox-group v-model="checked" :min="min">
            <el-checkbox v-for="item in options" :label="item.value" :key="item.value">{{item.label}}</el-checkbox>
        </el-checkbox-group>
    </div>
  </template>
  
  <script>
  export default {
    props: {
      //预定义
      value: Array,
      options: {
        type: Array,
        default: () => []
      },
      // 可被勾选的 checkbox 的最小数量
      min:Number
    },
    data() {
      return { 
      };
    },
    computed: {
      checked: {
        get() {
          return this.value;
        },
        set(newValue) {
          //当组件值发生变化后,通过 input 事件更新值
          this.$emit("input", newValue);
        }
      },
      isIndeterminate(){
        return this.checked.length > 0 && this.checked.length < this.options.length
      },
      checkAll:{
        get() {
            return this.options.length === this.checked.length
        },
        set(newValue) {
          this.checked = newValue ? this.options.map(option=>option.value) : []
        }
        
      }
    },
  };
  </script>
  <style lang="scss" scoped>
  </style>
  