<!-- 用户穿梭框 -->
<!-- 会管中使用 -->
<template>
  <el-dialog title="选择用户" :visible.sync="visible" width="1200px" custom-class="user-dialog" :close-on-click-modal="false" @close="close">
    <div class="margin-bottom10 search-wrap">
      <el-input
        placeholder="用户名/昵称"
        suffix-icon="el-icon-search"
        size="small"
        v-model="szName"
        clearable
        style="width: 200px"
        v-debounce="[
          (e) => {
            pagination.curPage = 1;
            getUserList(e);
          },
        ]"
        @clear="
          () => {
            pagination.curPage = 1;
            getUserList();
          }
        "
      />
      <dept-cascader :deptValue.sync="search.deptId" :bInit="false" :bOwn="bOwn" :bCompany="bCompany" class="ml-10"></dept-cascader>
    </div>
    <transfer
      ref="transfer"
      keyword="username"
      :list="userList"
      :chooseList="chooseUserList"
      class="user-transfer"
      :defaultSort="{ prop: sort.prop, order: 'ascending' }"
      @sortChange="sortChange"
      :isMulti="isMulti"
      @rightMove="rightMove"
      v-loading="loading"
    >
      <div slot="titleLeft">候选用户</div>
      <template slot="leftTable">
        <el-table-column label="序号" width="50" show-overflow-tooltip align="center">
          <template slot-scope="scope">
            {{ scope.$index + (pagination.curPage - 1) * pagination.size + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="username" sortable="custom" :show-overflow-tooltip="true" label="用户名" min-width="80"> </el-table-column>
        <el-table-column prop="nickname" sortable="custom" :show-overflow-tooltip="true" label="昵称" min-width="80"> </el-table-column>
        <el-table-column prop="departmentName" :show-overflow-tooltip="true" :label="`所属${$t('deptLabel')}`" min-width="80"> </el-table-column>
        <el-table-column prop="videoTerminal" :show-overflow-tooltip="true" label="视频终端" min-width="80" v-if="showTerminal">
          <template slot-scope="scope">
            {{ scope.row.videoTerminal ? scope.row.videoTerminal.name : '' }}
          </template>
        </el-table-column>
        <el-table-column :show-overflow-tooltip="true" label="终端状态" min-width="80" align="center" v-if="showTerminal">
          <template slot-scope="{ row }">
            {{ formatTerminalStatus(row.terminalStatus).label }}
          </template>
        </el-table-column>
      </template>
      <pagination
        slot="pagination"
        :total="pagination.total"
        :page.sync="pagination.curPage"
        :limit.sync="pagination.size"
        :layout="TRANSFER_PAGINATION_LAYOUT"
        @pagination="getUserList"
        :autoScroll="false"
      />
      <div slot="titleRight">已选用户</div>
      <template slot="rightTable">
        <el-table-column type="index" label="序号" min-width="50" align="center"> </el-table-column>
        <el-table-column prop="username" sortable :show-overflow-tooltip="true" label="用户名" min-width="80" />
        <el-table-column prop="nickname" sortable :show-overflow-tooltip="true" label="昵称" min-width="80"> </el-table-column>
        <el-table-column prop="departmentName" :show-overflow-tooltip="true" :label="`所属${$t('deptLabel')}`" v-if="showDept"> </el-table-column>
        <el-table-column prop="videoTerminal" :show-overflow-tooltip="true" label="视频终端" v-if="showTerminal">
          <template slot-scope="scope">
            {{ scope.row.videoTerminal ? scope.row.videoTerminal.name : '' }}
          </template>
        </el-table-column>
        <el-table-column prop="joinType" :show-overflow-tooltip="true" label="软终端入会" min-width="120" v-if="showJoinType" align="center">
          <template slot-scope="{ row }">
            <template v-if="!inConf">
              <el-checkbox :value="row.join" @change="changeJoinType(...arguments, row)"></el-checkbox>
            </template>
            <template v-else>
              <div v-if="chooseUserList.findIndex((item) => item.id == row.id) != -1"> - </div>
              <el-checkbox :value="row.join" @change="changeJoinType(...arguments, row)" v-else></el-checkbox>
            </template>
          </template>
        </el-table-column>
      </template>
    </transfer>
    <div slot="footer" class="dialog-footer">
      <el-button @click="close">取消</el-button>
      <el-button plain @click="handleOk(false)" v-if="showApplyBtn">应用</el-button>
      <el-button class="okBtn" @click="handleOk">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import deptCascader from '@/components/deptCascader';
  import dlg from '@/mixins/dlg';
  import transfer from './index.vue';
  import { getDeptUsers } from '@/api/user';
  import { TRANSFER_PAGINATION_LAYOUT } from '@/utils/enum';
  import { getTerminalStatus } from '@/api/conferenceGuide';
  export default {
    mixins: [dlg],
    components: {
      transfer,
      deptCascader,
    },
    props: {
      users: {
        type: Array,
        default: () => [],
        require: false,
      },
      // 查询机构时根机构是否包含所有子级机构,true为不包含
      bOwn: {
        type: Boolean,
        default: true,
      },
      // 查询机构时是否从公司开始查询，true为从公司开始查询
      bCompany: {
        type: Boolean,
        default: true,
      },
      // 是否多选
      isMulti: {
        type: Boolean,
        default: true,
      },
      showDept: {
        type: Boolean,
        default: true,
      },
      showTerminal: {
        type: Boolean,
        default: true,
      },
      // 一下两项用于控制与会人是否软终端入会，inConf false 预约界面 true 会控页面
      showJoinType: {
        type: Boolean,
        default: false,
      },
      inConf: {
        type: Boolean,
        default: true,
      },
      showApplyBtn: {
        type: Boolean,
        default: false,
      },
      // 是否考虑特殊部门的用户,false不考虑，查所有的，只有预约会议才会使用该字段
      vcSpecial: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        loading: false,
        // 用户名搜索
        szName: '',
        // 用户列表
        userList: [],
        chooseUserList: [],

        // 分页参数
        pagination: {
          curPage: 1,
          size: 20,
          total: 0,
        },
        sort: {
          prop: 'username',
          order: 'ASC',
        },
        TRANSFER_PAGINATION_LAYOUT,
        // 是否绑定终端
        bindTerminal: '',
        bindTerminalOption: [
          {
            label: '已绑定终端',
            value: true,
          },
          {
            label: '未绑定终端',
            value: false,
          },
        ],
        search: {
          deptId: '',
          excludeChild: true,
        },
      };
    },
    created() {
      if (this.users && this.users.length) {
        // 已选用户添加软终端入会字段 join
        this.users.forEach((user) => {
          if (user.join === null || user.join == undefined) {
            user.join = true;
          }
        });

        this.chooseUserList = this.users.map((item) => ({
          username: item.username,
          id: item.id,
          ...item,
        }));
      }
      this.getUserList();
    },
    watch: {
      bindTerminal() {
        this.pagination.curPage = 1;
        this.getUserList();
      },
      'search.deptId': function () {
        this.pagination.curPage = 1;
        this.getUserList();
      },
      'search.excludeChild': function () {
        this.pagination.curPage = 1;
        this.getUserList();
      },
    },
    methods: {
      /**
       * 默认新添加的用户软终端入会
       * @param {*} val
       */
      rightMove(val) {
        val.forEach((user) => {
          if (user.join == undefined) {
            this.$set(user, 'join', true);
          }
        });
      },
      changeJoinType() {
        let val = arguments[0];
        let row = arguments[2];
        this.$set(row, 'join', val);
      },
      /**
       * 格式化软终端状态
       * @param {Object} statusObj
       */
      formatTerminalStatus(statusObj) {
        let clz = '',
          label = '';
        if (statusObj) {
          // 查询成功
          if (statusObj.successQuery) {
            if (statusObj.sipStatus || statusObj.gkStatus) {
              clz = 'online';
              label = '在线';
              if (statusObj.connect == true) {
                clz = 'in-meeting';
                label = '呼叫中';
              } else if (statusObj.connect == false) {
                clz = 'out-meeting';
                label = '空闲';
              }
            } else {
              clz = 'offline';
              label = '离线';
            }
          } else {
            clz = 'unknown';
            label = '';
          }
        }

        return {
          clz,
          label,
        };
      },
      handleOk(closeDlg = true) {
        let list = this.$refs.transfer.rightList;
        if (!this.isMulti && list.length > 1) {
          this.$message.warning('只能选择单个用户!');
          return;
        }
        this.$emit('handleSelect', list);
        if (closeDlg) {
          this.close();
        }
      },
      /**
       * 获取用户列表
       */
      getUserList() {
        this.loading = true;
        let deptId = Array.isArray(this.search.deptId) ? this.search.deptId[this.search.deptId.length - 1] : this.search.deptId;
        getDeptUsers({
          page: this.pagination.curPage,
          size: this.pagination.size,
          direction: this.sort.order,
          property: this.sort.prop,
          nameToSearch: this.szName,
          includeChild: false,
          deptId,
          vcSpecial: this.vcSpecial,
        })
          .then((res) => {
            // 显示终端状态时才去查询终端状态
            if (this.showTerminal) {
              res.data.rows.forEach((user) => {
                user.terminalStatus = null;
                // 获取用户绑定终端的状态
                if (user.videoTerminal && user.videoTerminal.id) {
                  getTerminalStatus(user.videoTerminal.id).then((res) => {
                    user.terminalStatus = res.data;
                  });
                }
              });
            }

            this.userList = res.data.rows;
            this.pagination.total = res.data.total;
          })
          .finally(() => (this.loading = false));
      },
      sortChange(col) {
        this.sort.order = 'ascending' === col.order ? 'ASC' : 'DESC';
        this.sort.prop = col.prop;
        this.getUserList();
      },
    },
  };
</script>
<style lang="scss" scoped>
  .user-dialog {
  }

  .b-soft-join {
    margin: 0 20px;
  }
</style>
