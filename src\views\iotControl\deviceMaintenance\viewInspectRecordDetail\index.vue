<template>
  <div class="inspect-record-detail">
    <div class="header">
      <div class="btn-wrap">
        <el-button @click="back" size="small">返回</el-button>
        <el-button type="primary" @click="exportReport" size="small" v-if="showType === 'report'">导出报表</el-button>
      </div>
      <el-radio-group v-model="showType" size="small">
        <el-radio-button label="detail">详情</el-radio-button>
        <el-tooltip class="item" effect="dark" content="巡检进行中，无法查看报表" placement="top" v-if="inspectInfo.taskStatus === 'underway'">
          <el-radio-button label="report" :disabled="inspectInfo.taskStatus === 'underway'">报表</el-radio-button>
        </el-tooltip>
        <el-radio-button label="report" v-else>报表</el-radio-button>
      </el-radio-group>
    </div>
    <!-- 报表 -->
    <div class="report-wrap" v-if="showType === 'report'">
      <div ref="report" class="report">
        <div class="report-header">
          <div class="report-title">
            <el-tag :type="getStatusType(inspectInfo.taskStatus).type"> {{ getStatusType(inspectInfo.taskStatus).label }}</el-tag>
            设备巡检报告
          </div>
          <div class="report-info">
            <div class="info-item">
              <span class="label">巡检名称：</span>
              <span class="value">{{ inspectInfo.name }}</span>
            </div>
            <div class="info-item">
              <span class="label">执行时间：</span>
              <span class="value">{{ formatDate(inspectInfo.taskTime) }}</span>
            </div>
            <div class="info-item">
              <span class="label">巡检人员：</span>
              <span class="value">{{ inspectInfo.inspectorNickName }}</span>
            </div>
            <div class="info-item">
              <span class="label">所属空间：</span>
              <span class="value">{{ getSpaceName }}</span>
            </div>
          </div>
        </div>

        <div class="report-section">
          <div class="section-title">巡检概览</div>
          <div class="overview-cards">
            <div class="overview-card normal">
              <div class="card-value">{{ inspectDetails.length }}</div>
              <div class="card-label">总计巡检</div>
            </div>
            <div class="overview-card success">
              <div class="card-value">{{ inspectFinishedDevice.length }}</div>
              <div class="card-label">巡检成功</div>
            </div>
            <div class="overview-card warning">
              <div class="card-value">{{ inspectTimeoutDevice.length }}</div>
              <div class="card-label">巡检失败</div>
            </div>
            <div class="overview-card abnormal">
              <div class="card-value">{{ inspectAbNormalDeviceIds.length }}</div>
              <div class="card-label">异常设备</div>
            </div>
          </div>
        </div>

        <div class="report-section">
          <div class="section-title">异常类型分析</div>
          <div class="chart-container">
            <div class="status-chart" ref="reportAbnormalTypeBarChart"></div>
            <div class="abnormal-count">
              <i class="el-icon-warning" style="color: #f56c6c; margin-right: 5px"></i>
              <span style="font-weight: bold; color: #f56c6c">共巡检出异常项：</span>
              <span style="font-weight: bold; color: #f56c6c">{{ getAllAbnormalCount() }}项</span>
              <div style="margin-top: 10px; color: #696969; font-size: 14px">
                其中 <el-tag type="danger">{{ getMaxAbnormalType() }}</el-tag> 项最多，请重点关注！
              </div>
            </div>
          </div>
        </div>
        <div class="report-section">
          <div class="section-title">设备在线状态</div>
          <div class="chart-container">
            <div class="status-chart" ref="reportStatusPieChart"></div>
            <div class="status-chart" ref="reportStatusBarChart"></div>
          </div>
        </div>
        <div class="report-section">
          <div class="section-title">设备寿命状态</div>
          <div class="chart-container">
            <div class="status-chart" ref="reportLifespanPieChart"></div>
            <div class="status-chart" ref="reportLifespanBarChart"></div>
          </div>
        </div>
        <div class="report-section" style="margin-top: 100px">
          <div class="section-title">设备保修状态</div>
          <div class="chart-container">
            <div class="status-chart" ref="reportWarrantyPieChart"></div>
            <div class="status-chart" ref="reportWarrantyBarChart"></div>
          </div>
        </div>
        <div class="report-section">
          <div class="card-header">
            <div class="title">
              <i class="el-icon-warning"></i>
              离线设备列表
            </div>
            <div class="tags">
              <el-tag class="tag offline"> 离线: {{ offlineDevice.length }}台</el-tag>
            </div>
          </div>
          <div class="list-content">
            <template v-if="offlineDevice.length">
              <div class="list-subheader offline"> 离线设备 ({{ offlineDevice.length }}台) </div>
              <el-row :gutter="20">
                <el-col :span="8" v-for="(device, index) in offlineDevice" :key="index">
                  <div class="device-item offline">
                    <i class="el-icon-warning-outline"></i>
                    <div class="device-info">
                      <el-tooltip :content="device.deviceName" placement="top">
                        <div class="device-name">{{ device.deviceName }}</div>
                      </el-tooltip>
                      <div class="device-meta">
                        <span class="type">{{ device.deviceTypeName }}</span>
                      </div>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </template>
            <template v-if="offlineDevice.length === 0">
              <div class="success-subheader">
                <i class="el-icon-success" style="color: #91cd77"></i>
                所有设备均在线
              </div>
            </template>
          </div>
        </div>
        <div class="report-section">
          <div class="card-header">
            <div class="title">
              <i class="el-icon-timer"></i>
              异常寿命状态设备
            </div>
            <div class="tags">
              <el-tag class="tag renew">建议更换: {{ renewDevice.length }}台</el-tag>
              <el-tag class="tag overdue">超期运行: {{ overdueDevice.length }}台</el-tag>
              <el-tag class="tag lifespan-unknown">寿命未知: {{ lifespanUnknownDevice.length }}台</el-tag>
            </div>
          </div>
          <div class="list-content">
            <template v-if="renewDevice.length">
              <div class="list-subheader renew"> 建议更换 ({{ renewDevice.length }}台) </div>
              <el-row :gutter="20">
                <el-col :span="8" v-for="(device, index) in renewDevice" :key="index">
                  <div class="device-item renew">
                    <i class="el-icon-timer"></i>
                    <div class="device-info">
                      <el-tooltip :content="device.deviceName" placement="top">
                        <div class="device-name">{{ device.deviceName }}</div>
                      </el-tooltip>
                      <div class="device-meta">
                        <span class="type">{{ device.deviceTypeName }}</span>
                      </div>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </template>
            <template v-if="overdueDevice.length">
              <div class="list-subheader overdue"> 超期运行 ({{ overdueDevice.length }}台) </div>
              <el-row :gutter="20">
                <el-col :span="8" v-for="(device, index) in overdueDevice" :key="index">
                  <div class="device-item overdue">
                    <i class="el-icon-timer"></i>
                    <div class="device-info">
                      <el-tooltip :content="device.deviceName" placement="top">
                        <div class="device-name">{{ device.deviceName }}</div>
                      </el-tooltip>
                      <div class="device-meta">
                        <span class="type">{{ device.deviceTypeName }}</span>
                      </div>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </template>
            <template v-if="lifespanUnknownDevice.length">
              <div class="list-subheader lifespan-unknown"> 寿命未知设备 ({{ lifespanUnknownDevice.length }}台) </div>
              <el-row :gutter="20">
                <el-col :span="8" v-for="(device, index) in lifespanUnknownDevice" :key="index">
                  <div class="device-item lifespan-unknown">
                    <i class="el-icon-timer"></i>
                    <div class="device-info">
                      <el-tooltip :content="device.deviceName" placement="top">
                        <div class="device-name">{{ device.deviceName }}</div>
                      </el-tooltip>
                      <div class="device-meta">
                        <span class="type">{{ device.deviceTypeName }}</span>
                      </div>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </template>
            <template v-if="lifespanUnknownDevice.length === 0 && overdueDevice.length === 0 && renewDevice.length === 0">
              <div class="success-subheader">
                <i class="el-icon-success" style="color: #91cd77"></i>
                所有设备寿命均正常
              </div>
            </template>
          </div>
        </div>
        <div class="report-section">
          <div class="card-header">
            <div class="title">
              <i class="el-icon-setting"></i>
              异常保修状态设备
            </div>
            <div class="tags">
              <el-tag class="tag warranty-expiring">即将到期: {{ warrantyExpiringDevice.length }}台</el-tag>
              <el-tag class="tag warranty-overdue">过保: {{ warrantyOverdueDevice.length }}台</el-tag>
              <el-tag class="tag warranty-unknown">保修未知: {{ warrantyUnknownDevice.length }}台</el-tag>
            </div>
          </div>
          <div class="list-content">
            <template v-if="warrantyExpiringDevice.length">
              <div class="list-subheader warranty-expiring"> 即将到期设备 ({{ warrantyExpiringDevice.length }}台) </div>
              <el-row :gutter="20">
                <el-col :span="8" v-for="(device, index) in warrantyExpiringDevice" :key="index">
                  <div class="device-item warranty-expiring">
                    <i class="el-icon-setting"></i>
                    <div class="device-info">
                      <el-tooltip :content="device.deviceName" placement="top">
                        <div class="device-name">{{ device.deviceName }}</div>
                      </el-tooltip>
                      <div class="device-meta">
                        <span class="type">{{ device.deviceTypeName }}</span>
                      </div>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </template>
            <template v-if="warrantyOverdueDevice.length">
              <div class="list-subheader warranty-overdue"> 过保设备 ({{ warrantyOverdueDevice.length }}台) </div>
              <el-row :gutter="20">
                <el-col :span="8" v-for="(device, index) in warrantyOverdueDevice" :key="index">
                  <div class="device-item warranty-overdue">
                    <i class="el-icon-setting"></i>
                    <div class="device-info">
                      <el-tooltip :content="device.deviceName" placement="top">
                        <div class="device-name">{{ device.deviceName }}</div>
                      </el-tooltip>
                      <div class="device-meta">
                        <span class="type">{{ device.deviceTypeName }}</span>
                      </div>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </template>
            <template v-if="warrantyUnknownDevice.length">
              <div class="list-subheader warranty-unknown"> 保修未知设备 ({{ warrantyUnknownDevice.length }}台) </div>
              <el-row :gutter="20">
                <el-col :span="8" v-for="(device, index) in warrantyUnknownDevice" :key="index">
                  <div class="device-item warranty-unknown">
                    <i class="el-icon-setting"></i>
                    <div class="device-info">
                      <el-tooltip :content="device.deviceName" placement="top">
                        <div class="device-name">{{ device.deviceName }}</div>
                      </el-tooltip>
                      <div class="device-meta">
                        <span class="type">{{ device.deviceTypeName }}</span>
                      </div>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </template>
            <template v-if="warrantyUnknownDevice.length === 0 && warrantyOverdueDevice.length === 0 && warrantyExpiringDevice.length === 0">
              <div class="success-subheader">
                <i class="el-icon-success" style="color: #91cd77"></i>
                所有设备均在保修中
              </div>
            </template>
          </div>
        </div>
        <div class="report-section">
          <div class="section-header">
            <div class="title">
              <i class="el-icon-warning"></i>
              设备属性异常
            </div>
            <el-tag type="danger">属性异常：{{ abnormalPropDevice.length }}台</el-tag>
          </div>
          <el-row v-if="abnormalPropDevice.length">
            <el-col :span="24" v-for="(device, index) in abnormalPropDevice" :key="index">
              <div class="prop-card">
                <div class="card-header">
                  <div class="device-info">
                    <div class="device-name">{{ device.deviceName }}</div>
                    <div class="device-status">
                      <el-tag :class="device.onlineStatus">{{ ONLINE_STATUS_ENUM[device.onlineStatus] }}</el-tag>
                    </div>
                  </div>
                </div>
                <div class="card-content">
                  <el-table
                    :data="device.inspectionParams.filter((param) => ['NotAsExpected', 'Unknown'].includes(param.resultStatus))"
                    style="width: 100%"
                    height="100%"
                  >
                    <el-table-column prop="name" label="属性名称" min-width="120" show-overflow-tooltip> </el-table-column>
                    <el-table-column label="异常原因" min-width="120" prop="reason" show-overflow-tooltip> </el-table-column>
                    <el-table-column label="预期值" min-width="120" show-overflow-tooltip>
                      <template slot-scope="{ row }">
                        {{
                          PROPERTY_OPT_ENUM.find((item) => item.value === row.operator)
                            ? PROPERTY_OPT_ENUM.find((item) => item.value === row.operator).symbol
                            : ''
                        }}
                        {{ getPropertyValueLabel(row, 'threshold') }}
                      </template>
                    </el-table-column>
                    <el-table-column label="实际值" min-width="100" show-overflow-tooltip prop="value">
                      <template slot-scope="{ row }">
                        {{ row.value === 'unknown' ? 'unknown' : getPropertyValueLabel(row, 'value') }}
                      </template>
                    </el-table-column>
                    <el-table-column label="描述" min-width="100" show-overflow-tooltip prop="description"> </el-table-column>
                  </el-table>
                </div>
              </div>
            </el-col>
          </el-row>
          <div class="success-subheader" v-if="abnormalPropDevice.length === 0">
            <i class="el-icon-success" style="color: #91cd77"></i>
            所有设备属性均正常
          </div>
        </div>
      </div>
    </div>
    <!-- 详情 -->
    <div v-if="showType === 'detail'" class="detail">
      <div class="basic-info-card">
        <div class="title">
          <el-tag :type="getStatusType(inspectInfo.taskStatus).type"> {{ getStatusType(inspectInfo.taskStatus).label }}</el-tag>
          {{ inspectInfo.name }}
        </div>
        <div class="info-content">
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="info-item">
                <span class="label">执行时间：</span>
                <span class="value">{{ formatDate(inspectInfo.taskTime) }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">所属空间：</span>
                <el-tooltip :content="getSpaceName" placement="top">
                  <span class="value">{{ getSpaceName }}</span>
                </el-tooltip>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="info-item">
                <span class="label">巡检人员：</span>
                <el-tooltip :content="inspectInfo.inspectorNickName" placement="top">
                  <span class="value">{{ inspectInfo.inspectorNickName }}</span>
                </el-tooltip>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
      <div class="statistics-cards">
        <el-row :gutter="20">
          <el-col :span="4">
            <div class="stat-card">
              <div class="stat-icon">
                <svg-icon icon-class="iot@device"></svg-icon>
              </div>
              <div class="stat-info">
                <div class="stat-label">总计巡检</div>
                <div class="stat-value">
                  <countTo :startVal="0" :endVal="inspectDetails.length" :duration="2000"></countTo>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card success">
              <div class="stat-icon">
                <svg-icon icon-class="inspect@finished"></svg-icon>
              </div>
              <div class="stat-info">
                <div class="stat-label">巡检成功</div>
                <div class="stat-value">
                  <countTo :startVal="0" :endVal="inspectFinishedDevice.length" :duration="2000"></countTo>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card">
              <div class="stat-icon">
                <svg-icon icon-class="inspect@ready"></svg-icon>
              </div>
              <div class="stat-info">
                <div class="stat-label">待巡检</div>
                <div class="stat-value">
                  <countTo :startVal="0" :endVal="inspectReadyDevice.length" :duration="2000"></countTo>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card">
              <div class="stat-icon">
                <svg-icon icon-class="inspect@timeout"></svg-icon>
              </div>
              <div class="stat-info">
                <div class="stat-label">巡检失败</div>
                <div class="stat-value">
                  <countTo :startVal="0" :endVal="inspectTimeoutDevice.length" :duration="2000"></countTo>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card warning">
              <div class="stat-icon">
                <svg-icon icon-class="inspect@normal"></svg-icon>
              </div>
              <div class="stat-info">
                <div class="stat-label">正常设备</div>
                <div class="stat-value">
                  <countTo :startVal="0" :endVal="inspectNormalDeviceIds.length" :duration="2000"></countTo>
                </div>
              </div>
            </div>
          </el-col>
          <el-col :span="4">
            <div class="stat-card warning">
              <div class="stat-icon">
                <svg-icon icon-class="iot@totalAlarm"></svg-icon>
              </div>
              <div class="stat-info">
                <div class="stat-label">异常设备</div>
                <div class="stat-value">
                  <countTo :startVal="0" :endVal="inspectAbNormalDeviceIds.length" :duration="2000"></countTo>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <div class="pending-devices-section" v-if="inspectTimeoutDevice.length">
        <div class="section-header">
          <div class="title">
            <i class="el-icon-warning-outline"></i>
            巡检失败设备
          </div>
          <el-tag type="warning">巡检失败：{{ inspectTimeoutDevice.length }}台</el-tag>
        </div>
        <div class="devices-list">
          <el-row :gutter="20">
            <el-col :span="4" v-for="(device, index) in inspectTimeoutDevice" :key="index">
              <div class="device-card">
                <div class="device-info">
                  <el-tooltip :content="device.deviceName" placement="top">
                    <div class="device-name">{{ device.deviceName }}</div>
                  </el-tooltip>
                  <div class="device-type">{{ device.deviceTypeName }}</div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
      <div class="pending-devices-section" v-if="inspectReadyDevice.length">
        <div class="section-header">
          <div class="title">
            <i class="el-icon-warning-outline"></i>
            待巡检设备
          </div>
          <el-tag type="warning">待巡检：{{ inspectReadyDevice.length }}台</el-tag>
        </div>
        <div class="devices-list">
          <el-row :gutter="20">
            <el-col :span="4" v-for="(device, index) in inspectReadyDevice" :key="index">
              <div class="device-card">
                <div class="device-info">
                  <el-tooltip :content="device.deviceName" placement="top">
                    <div class="device-name">{{ device.deviceName }}</div>
                  </el-tooltip>
                  <div class="device-type">{{ device.deviceTypeName }}</div>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
      <div class="charts-section">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="chart-card">
              <div class="chart-header">
                <div class="chart-title">
                  <i class="el-icon-pie-chart"></i>
                  设备在线状态分布
                </div>
              </div>
              <div class="chart-content">
                <div class="chart" ref="statusChart"></div>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="chart-card">
              <div class="chart-header">
                <div class="chart-title">
                  <i class="el-icon-time"></i>
                  设备寿命状态分布
                </div>
              </div>
              <div class="chart-content">
                <div class="chart" ref="lifespanChart"></div>
              </div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="chart-card">
              <div class="chart-header">
                <div class="chart-title">
                  <i class="el-icon-setting"></i>
                  设备保修状态分布
                </div>
              </div>
              <div class="chart-content">
                <div class="chart" ref="warrantyChart"></div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
      <el-row :gutter="20" class="device-list-wrap">
        <el-col :span="8">
          <div class="device-list-card">
            <div class="card-header">
              <div class="title">
                <i class="el-icon-warning"></i>
                离线设备列表
              </div>
              <div class="tags">
                <el-tag class="tag offline" size="mini"> 离线: {{ offlineDevice.length }}台</el-tag>
              </div>
            </div>
            <div class="list-content">
              <template v-if="offlineDevice.length">
                <div class="list-subheader offline"> 离线设备 ({{ offlineDevice.length }}台) </div>
                <el-row :gutter="20">
                  <el-col :span="12" v-for="(device, index) in offlineDevice" :key="index">
                    <div class="device-item offline">
                      <i class="el-icon-warning-outline"></i>
                      <div class="device-info">
                        <el-tooltip :content="device.deviceName" placement="top" :open-delay="800">
                          <div class="device-name">{{ device.deviceName }}</div>
                        </el-tooltip>
                        <el-tooltip :content="device.deviceTypeName" placement="top" :open-delay="800">
                          <div class="device-meta">
                            {{ device.deviceTypeName }}
                          </div>
                        </el-tooltip>
                      </div>
                    </div>
                  </el-col>
                </el-row>
              </template>
              <template v-if="offlineDevice.length === 0">
                <div class="success-subheader">
                  <i class="el-icon-success" style="color: #91cd77"></i>
                  所有设备均在线
                </div>
              </template>
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="device-list-card">
            <div class="card-header">
              <div class="title">
                <i class="el-icon-timer"></i>
                异常寿命状态设备
              </div>
              <div class="tags">
                <el-tag class="tag renew" size="mini">建议更换: {{ renewDevice.length }}台</el-tag>
                <el-tag class="tag overdue" size="mini">超期运行: {{ overdueDevice.length }}台</el-tag>
                <el-tag class="tag lifespan-unknown" size="mini">寿命未知: {{ lifespanUnknownDevice.length }}台</el-tag>
              </div>
            </div>
            <div class="list-content">
              <template v-if="renewDevice.length">
                <div class="list-subheader renew"> 建议更换 ({{ renewDevice.length }}台) </div>
                <el-row :gutter="20">
                  <el-col :span="12" v-for="(device, index) in renewDevice" :key="index">
                    <div class="device-item renew">
                      <i class="el-icon-timer"></i>
                      <div class="device-info">
                        <el-tooltip :content="device.deviceName" placement="top" :open-delay="800">
                          <div class="device-name">{{ device.deviceName }}</div>
                        </el-tooltip>
                        <el-tooltip :content="device.deviceTypeName" placement="top" :open-delay="800">
                          <div class="device-meta">
                            {{ device.deviceTypeName }}
                          </div>
                        </el-tooltip>
                      </div>
                    </div>
                  </el-col>
                </el-row>
              </template>
              <template v-if="overdueDevice.length">
                <div class="list-subheader overdue"> 超期运行 ({{ overdueDevice.length }}台) </div>
                <el-row :gutter="20">
                  <el-col :span="12" v-for="(device, index) in overdueDevice" :key="index">
                    <div class="device-item overdue">
                      <i class="el-icon-timer"></i>
                      <div class="device-info">
                        <el-tooltip :content="device.deviceName" placement="top" :open-delay="800">
                          <div class="device-name">{{ device.deviceName }}</div>
                        </el-tooltip>
                        <el-tooltip :content="device.deviceTypeName" placement="top" :open-delay="800">
                          <div class="device-meta">
                            {{ device.deviceTypeName }}
                          </div>
                        </el-tooltip>
                      </div>
                    </div>
                  </el-col>
                </el-row>
              </template>
              <template v-if="lifespanUnknownDevice.length">
                <div class="list-subheader lifespan-unknown"> 寿命未知设备 ({{ lifespanUnknownDevice.length }}台) </div>
                <el-row :gutter="20">
                  <el-col :span="12" v-for="(device, index) in lifespanUnknownDevice" :key="index">
                    <div class="device-item lifespan-unknown">
                      <i class="el-icon-timer"></i>
                      <div class="device-info">
                        <el-tooltip :content="device.deviceName" placement="top" :open-delay="800">
                          <div class="device-name">{{ device.deviceName }}</div>
                        </el-tooltip>
                        <el-tooltip :content="device.deviceTypeName" placement="top" :open-delay="800">
                          <div class="device-meta">
                            {{ device.deviceTypeName }}
                          </div>
                        </el-tooltip>
                      </div>
                    </div>
                  </el-col>
                </el-row>
              </template>
              <template v-if="lifespanUnknownDevice.length === 0 && overdueDevice.length === 0 && renewDevice.length === 0">
                <div class="success-subheader">
                  <i class="el-icon-success" style="color: #91cd77"></i>
                  所有设备寿命均正常
                </div>
              </template>
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="device-list-card">
            <div class="card-header">
              <div class="title">
                <i class="el-icon-setting"></i>
                异常保修状态设备
              </div>
              <div class="tags">
                <el-tag class="tag warranty-expiring" size="mini">即将到期: {{ warrantyExpiringDevice.length }}台</el-tag>
                <el-tag class="tag warranty-overdue" size="mini">过保: {{ warrantyOverdueDevice.length }}台</el-tag>
                <el-tag class="tag warranty-unknown" size="mini">保修未知: {{ warrantyUnknownDevice.length }}台</el-tag>
              </div>
            </div>
            <div class="list-content">
              <template v-if="warrantyExpiringDevice.length">
                <div class="list-subheader warranty-expiring"> 即将到期设备 ({{ warrantyExpiringDevice.length }}台) </div>
                <el-row :gutter="20">
                  <el-col :span="12" v-for="(device, index) in warrantyExpiringDevice" :key="index">
                    <div class="device-item warranty-expiring">
                      <i class="el-icon-setting"></i>
                      <div class="device-info">
                        <el-tooltip :content="device.deviceName" placement="top" :open-delay="800">
                          <div class="device-name">{{ device.deviceName }}</div>
                        </el-tooltip>
                        <el-tooltip :content="device.deviceTypeName" placement="top" :open-delay="800">
                          <div class="device-meta">
                            {{ device.deviceTypeName }}
                          </div>
                        </el-tooltip>
                      </div>
                    </div>
                  </el-col>
                </el-row>
              </template>
              <template v-if="warrantyOverdueDevice.length">
                <div class="list-subheader warranty-overdue"> 过保设备 ({{ warrantyOverdueDevice.length }}台) </div>
                <el-row :gutter="20">
                  <el-col :span="12" v-for="(device, index) in warrantyOverdueDevice" :key="index">
                    <div class="device-item warranty-overdue">
                      <i class="el-icon-setting"></i>
                      <div class="device-info">
                        <el-tooltip :content="device.deviceName" placement="top" :open-delay="800">
                          <div class="device-name">{{ device.deviceName }}</div>
                        </el-tooltip>
                        <el-tooltip :content="device.deviceTypeName" placement="top" :open-delay="800">
                          <div class="device-meta">
                            {{ device.deviceTypeName }}
                          </div>
                        </el-tooltip>
                      </div>
                    </div>
                  </el-col>
                </el-row>
              </template>
              <template v-if="warrantyUnknownDevice.length">
                <div class="list-subheader warranty-unknown"> 保修未知设备 ({{ warrantyUnknownDevice.length }}台) </div>
                <el-row :gutter="20">
                  <el-col :span="12" v-for="(device, index) in warrantyUnknownDevice" :key="index">
                    <div class="device-item warranty-unknown">
                      <i class="el-icon-setting"></i>
                      <div class="device-info">
                        <el-tooltip :content="device.deviceName" placement="top" :open-delay="800">
                          <div class="device-name">{{ device.deviceName }}</div>
                        </el-tooltip>
                        <el-tooltip :content="device.deviceTypeName" placement="top" :open-delay="800">
                          <div class="device-meta">
                            {{ device.deviceTypeName }}
                          </div>
                        </el-tooltip>
                      </div>
                    </div>
                  </el-col>
                </el-row>
              </template>
              <template v-if="warrantyUnknownDevice.length === 0 && warrantyOverdueDevice.length === 0 && warrantyExpiringDevice.length === 0">
                <div class="success-subheader">
                  <i class="el-icon-success" style="color: #91cd77"></i>
                  所有设备均在保修中
                </div>
              </template>
            </div>
          </div>
        </el-col>
      </el-row>
      <div class="not-as-expected-props-section">
        <div class="section-header">
          <div class="title">
            <i class="el-icon-warning"></i>
            设备属性异常
          </div>
          <el-tag type="danger">属性异常：{{ abnormalPropDevice.length }}台</el-tag>
        </div>
        <el-row :gutter="10" v-if="abnormalPropDevice.length">
          <el-col :span="12" v-for="(device, index) in abnormalPropDevice" :key="index">
            <div class="prop-card">
              <div class="card-header">
                <div class="device-info">
                  <div class="device-name">{{ device.deviceName }}</div>
                  <div class="device-status">
                    <el-tag :class="device.onlineStatus">{{ ONLINE_STATUS_ENUM[device.onlineStatus] }}</el-tag>
                  </div>
                </div>
              </div>
              <div class="card-content">
                <el-table
                  :data="device.inspectionParams.filter((param) => ['NotAsExpected', 'Unknown'].includes(param.resultStatus))"
                  style="width: 100%"
                  height="100%"
                >
                  <el-table-column prop="name" label="属性名称" min-width="100" show-overflow-tooltip> </el-table-column>
                  <el-table-column label="异常原因" min-width="100" prop="reason" show-overflow-tooltip> </el-table-column>
                  <el-table-column label="预期值" min-width="100" show-overflow-tooltip>
                    <template slot-scope="{ row }">
                      {{
                        PROPERTY_OPT_ENUM.find((item) => item.value === row.operator)
                          ? PROPERTY_OPT_ENUM.find((item) => item.value === row.operator).symbol
                          : ''
                      }}
                      {{ getPropertyValueLabel(row, 'threshold') }}
                    </template>
                  </el-table-column>
                  <el-table-column label="实际值" min-width="100" show-overflow-tooltip prop="value">
                    <template slot-scope="{ row }">
                      {{ row.value === 'unknown' ? 'unknown' : getPropertyValueLabel(row, 'value') }}
                    </template>
                  </el-table-column>
                  <el-table-column label="描述" min-width="100" show-overflow-tooltip prop="description"> </el-table-column>
                </el-table>
              </div>
            </div>
          </el-col>
        </el-row>
        <div class="success-subheader" v-if="abnormalPropDevice.length === 0">
          <i class="el-icon-success" style="color: #91cd77"></i>
          所有设备属性均正常
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import { getInspectRecordAnalysis } from '@/api/iotControl';
  import countTo from 'vue-count-to';
  import { PROPERTY_OPT_ENUM, INSPECT_RECORD_STATUS_ENUM } from '@/views/iotControl/enum';
  import { htmlToPdf } from '@/utils/htmlToPdf';
  import { htmlToJpeg } from '@/utils/htmlToJpeg';
  export default {
    components: {
      countTo,
    },
    data() {
      return {
        id: '',
        showType: 'detail',
        PROPERTY_OPT_ENUM,
        ONLINE_STATUS_ENUM: {
          Online: '在线',
          Offline: '离线',
        },
        // 巡检记录基本信息
        inspectInfo: {},
        // 正常设备
        inspectNormalDeviceIds: [],
        // 异常设备
        inspectAbNormalDeviceIds: [],
        inspectDetails: [],
        //巡检成功设备
        inspectFinishedDevice: [],
        //巡检超时设备
        inspectTimeoutDevice: [],
        //待巡检设备
        inspectReadyDevice: [],
        //巡检中设备
        inspectUnderwayDevice: [],
        //离线设备
        offlineDevice: [],
        //在线设备
        onlineDevice: [],
        //全新设备
        freshDevice: [],
        //良好设备
        goodDevice: [],
        //建议更换设备
        renewDevice: [],
        //超期运行设备
        overdueDevice: [],
        //寿命状态未知状态设备
        lifespanUnknownDevice: [],
        //保修状态
        //保修中设备
        warrantyUnderDevice: [],
        //即将到期设备
        warrantyExpiringDevice: [],
        //过保设备
        warrantyOverdueDevice: [],
        //保修状态未知设备
        warrantyUnknownDevice: [],
        //属性异常设备
        abnormalPropDevice: [],
        //属性异常数量
        abnormalPropCount: 0,
        inspectPrepare: {},
        statusChart: null,
        lifespanChart: null,
        warrantyChart: null,
        reportStatusBarChart: null,
        reportStatusPieChart: null,
        reportLifespanBarChart: null,
        reportLifespanPieChart: null,
        reportWarrantyBarChart: null,
        reportWarrantyPieChart: null,
        reportAbnormalTypeBarChart: null,
      };
    },
    created() {
      this.$nextTick(() => {
        this.id = this.$route.params.id;
        this.getInspectRecordDetail();
      });
    },
    mounted() {
      window.addEventListener('resize', this.resize);
    },
    computed: {
      getSpaceName() {
        if (!this.inspectInfo.spaceList) return '';
        return this.inspectInfo.spaceList.map((item) => item.spaceName).join(',');
      },
    },
    methods: {
      resize() {
        this.$nextTick(() => {
          this.statusChart && this.statusChart.resize();
          this.lifespanChart && this.lifespanChart.resize();
          this.warrantyChart && this.warrantyChart.resize();
        });
      },
      getInspectRecordDetail() {
        getInspectRecordAnalysis(this.id).then(({ data }) => {
          this.inspectInfo = data.inspectInfo;
          this.inspectDetails = data.inspectDetails;
          this.inspectNormalDeviceIds = data.inspectNormalDeviceIds;
          this.inspectAbNormalDeviceIds = data.inspectAbNormalDeviceIds;
          this.getInspectPrepare(data.productList);
        });
      },
      //刷新巡检记录
      refreshInspectRecord() {
        getInspectRecordAnalysis(this.id).then(({ data }) => {
          this.inspectNormalDeviceIds = [];
          this.inspectAbNormalDeviceIds = [];
          this.inspectDetails = [];
          this.inspectFinishedDevice = [];
          this.inspectTimeoutDevice = [];
          this.inspectReadyDevice = [];
          this.inspectUnderwayDevice = [];
          this.offlineDevice = [];
          this.onlineDevice = [];
          this.freshDevice = [];
          this.goodDevice = [];
          this.renewDevice = [];
          this.overdueDevice = [];
          this.lifespanUnknownDevice = [];
          this.warrantyUnderDevice = [];
          this.warrantyExpiringDevice = [];
          this.warrantyOverdueDevice = [];
          this.warrantyUnknownDevice = [];
          this.abnormalPropDevice = [];
          this.abnormalPropCount = 0;
          this.inspectInfo = data.inspectInfo;
          this.inspectDetails = data.inspectDetails;
          this.inspectNormalDeviceIds = data.inspectNormalDeviceIds;
          this.inspectAbNormalDeviceIds = data.inspectAbNormalDeviceIds;
          this.formatData(this.inspectDetails);
          this.initCharts();
          this.initReportCharts();
          setTimeout(() => {
            if (this.inspectInfo.taskStatus === 'underway') {
              this.refreshInspectRecord();
            }
          }, 3000);
        });
      },
      getInspectPrepare(productList) {
        this.inspectPrepare.productList = productList;
        this.formatData(this.inspectDetails);
        this.initCharts();
        this.initReportCharts();
        if (this.inspectInfo.taskStatus === 'underway') {
          setTimeout(() => {
            this.refreshInspectRecord();
          }, 3000);
        }
      },
      //处理巡检记录
      formatData(inspectDetails) {
        inspectDetails.forEach((device) => {
          if (device.inspectionStatus === 'finished') {
            this.inspectFinishedDevice.push(device);
          }
          if (device.inspectionStatus === 'underway') {
            this.inspectUnderwayDevice.push(device);
          }
          if (device.inspectionStatus === 'timeout') {
            this.inspectTimeoutDevice.push(device);
          }
          if (device.inspectionStatus === 'ready') {
            this.inspectReadyDevice.push(device);
          }
          if (device.onlineStatus === 'Offline') {
            this.offlineDevice.push(device);
          }
          if (device.onlineStatus === 'Online') {
            this.onlineDevice.push(device);
          }
          if (device.lifespanStatus === 'fresh') {
            this.freshDevice.push(device);
          }
          if (device.lifespanStatus === 'good') {
            this.goodDevice.push(device);
          }
          if (device.lifespanStatus === 'renew') {
            this.renewDevice.push(device);
          }
          if (device.lifespanStatus === 'overdue') {
            this.overdueDevice.push(device);
          }
          if (device.lifespanStatus === 'Unknown') {
            this.lifespanUnknownDevice.push(device);
          }
          if (device.warrantyStatus === 'under') {
            this.warrantyUnderDevice.push(device);
          }
          if (device.warrantyStatus === 'expiring') {
            this.warrantyExpiringDevice.push(device);
          }
          if (device.warrantyStatus === 'overdue') {
            this.warrantyOverdueDevice.push(device);
          }
          if (device.warrantyStatus === 'Unknown') {
            this.warrantyUnknownDevice.push(device);
          }
          // 属性异常设备处理，只保留metricType为propertyKey且resultStatus异常的
          const propertyParams = device.inspectionParams.filter(
            (param) => param.metricType === 'propertyKey' && ['NotAsExpected', 'Unknown'].includes(param.resultStatus)
          );

          if (propertyParams.length > 0) {
            this.abnormalPropCount += propertyParams.length; // 增加属性异常项数量
            const product = this.inspectPrepare.productList?.find((product) => product.code === device.code);

            this.abnormalPropDevice.push({
              ...device,
              inspectionParams: propertyParams.map((param) => ({
                ...param,
                name: product?.properties?.find((prop) => prop.address === param.metric)?.name,
                property: product?.properties?.find((prop) => prop.address === param.metric),
              })),
            });
          }
        });
      },
      // 获取所有异常项数量
      getAllAbnormalCount() {
        return (
          this.offlineDevice.length +
          this.renewDevice.length +
          this.overdueDevice.length +
          this.lifespanUnknownDevice.length +
          this.abnormalPropCount +
          this.warrantyExpiringDevice.length +
          this.warrantyOverdueDevice.length +
          this.warrantyUnknownDevice.length
        );
      },
      // 获取异常项最多的类型
      getMaxAbnormalType() {
        let abnormalType = [
          { type: '在线状态异常', count: this.offlineDevice.length },
          { type: '寿命状态异常', count: this.renewDevice.length + this.overdueDevice.length + this.lifespanUnknownDevice.length },
          { type: '保修状态异常', count: this.warrantyExpiringDevice.length + this.warrantyOverdueDevice.length + this.warrantyUnknownDevice.length },
          { type: '属性状态异常', count: this.abnormalPropCount },
        ];
        return abnormalType.sort((a, b) => b.count - a.count)[0].type;
      },
      // 详情图表
      initCharts() {
        if (this.$refs.statusChart) {
          this.statusChart = this.$echarts.init(this.$refs.statusChart);
          this.updateStatusChart();
        }

        if (this.$refs.lifespanChart) {
          this.lifespanChart = this.$echarts.init(this.$refs.lifespanChart);
          this.updateLifespanChart();
        }
        if (this.$refs.warrantyChart) {
          this.warrantyChart = this.$echarts.init(this.$refs.warrantyChart);
          this.updateWarrantyChart();
        }
      },
      updateStatusChart() {
        if (!this.statusChart) return;
        const option = {
          tooltip: {
            trigger: 'item',
            formatter: '{b}: {c}台 ({d}%)',
          },
          legend: {
            orient: 'vertical',
            right: '0%',
            top: 'center',
            formatter: (name) => {
              const item = this.statusChart.getOption().series[0].data.find((d) => d.name === name);
              const total = this.statusChart.getOption().series[0].data.reduce((sum, d) => sum + d.value, 0);
              let percent = ((item.value / total) * 100).toFixed(2);
              if (percent === 'NaN') {
                percent = 0;
              }
              return `{name|${name}} {count|${item.value}台} {percent|(${percent}%)}`;
            },
            textStyle: {
              rich: {
                name: {
                  color: '#000',
                  fontSize: 12,
                  lineHeight: 16,
                },
                count: {
                  color: '#000',
                  fontSize: 12,
                  lineHeight: 16,
                },
                percent: {
                  color: '#999',
                  fontSize: 12,
                  lineHeight: 16,
                },
              },
            },
          },
          series: [
            {
              name: '在线状态',
              type: 'pie',
              radius: ['38%', '60%'],
              center: ['27%', '50%'],
              label: {
                show: false,
              },
              data: [
                {
                  name: '在线',
                  value: this.onlineDevice.length,
                  itemStyle: { color: '#91cd77' },
                },
                { name: '离线', value: this.offlineDevice.length, itemStyle: { color: '#909399' } },
              ],
            },
          ],
        };

        this.statusChart.setOption(option);
      },

      updateLifespanChart() {
        if (!this.lifespanChart) return;
        const option = {
          tooltip: {
            trigger: 'item',
            formatter: '{b}: {c}台 ({d}%)',
          },
          legend: {
            orient: 'vertical',
            right: '0%',
            top: 'center',
            formatter: (name) => {
              const item = this.lifespanChart.getOption().series[0].data.find((d) => d.name === name);
              const total = this.lifespanChart.getOption().series[0].data.reduce((sum, d) => sum + d.value, 0);
              let percent = ((item.value / total) * 100).toFixed(2);
              if (percent === 'NaN') {
                percent = 0;
              }
              return `{name|${name}} {count|${item.value}台} {percent|(${percent}%)}`;
            },
            textStyle: {
              rich: {
                name: {
                  color: '#000',
                  fontSize: 12,
                  lineHeight: 16,
                },
                count: {
                  color: '#000',
                  fontSize: 12,
                  lineHeight: 16,
                },
                percent: {
                  color: '#999',
                  fontSize: 12,
                  lineHeight: 16,
                },
              },
            },
          },
          series: [
            {
              name: '寿命状态',
              type: 'pie',
              radius: ['38%', '60%'],
              center: ['27%', '50%'],
              label: {
                show: false,
              },
              data: [
                {
                  name: '全新',
                  value: this.freshDevice.length,
                  itemStyle: { color: '#3dcca6' },
                },
                { name: '良好', value: this.goodDevice.length, itemStyle: { color: '#87CEFA' } },
                { name: '建议更换', value: this.renewDevice.length, itemStyle: { color: '#fabe46' } },
                {
                  name: '超期运行',
                  value: this.overdueDevice.length,
                  itemStyle: { color: '#f56c6c' },
                },
                {
                  name: '寿命未知',
                  value: this.lifespanUnknownDevice.length,
                  itemStyle: { color: '#bea9e9' },
                },
              ],
            },
          ],
        };

        this.lifespanChart.setOption(option);
      },
      updateWarrantyChart() {
        if (!this.warrantyChart) return;
        const option = {
          tooltip: {
            trigger: 'item',
            formatter: '{b}: {c}台 ({d}%)',
          },
          legend: {
            orient: 'vertical',
            right: '0%',
            top: 'center',
            formatter: (name) => {
              const item = this.warrantyChart.getOption().series[0].data.find((d) => d.name === name);
              const total = this.warrantyChart.getOption().series[0].data.reduce((sum, d) => sum + d.value, 0);
              let percent = ((item.value / total) * 100).toFixed(2);
              if (percent === 'NaN') {
                percent = 0;
              }
              return `{name|${name}} {count|${item.value}台} {percent|(${percent}%)}`;
            },
            textStyle: {
              rich: {
                name: {
                  color: '#000',
                  fontSize: 12,
                  lineHeight: 16,
                },
                count: {
                  color: '#000',
                  fontSize: 12,
                  lineHeight: 16,
                },
                percent: {
                  color: '#999',
                  fontSize: 12,
                  lineHeight: 16,
                },
              },
            },
          },
          series: [
            {
              name: '保修状态',
              type: 'pie',
              radius: ['38%', '60%'],
              center: ['27%', '50%'],
              label: {
                show: false,
              },
              data: [
                {
                  name: '保修中',
                  value: this.warrantyUnderDevice.length,
                  itemStyle: { color: '#87CEFA' },
                },
                { name: '即将到期', value: this.warrantyExpiringDevice.length, itemStyle: { color: '#fabe46' } },
                {
                  name: '过保',
                  value: this.warrantyOverdueDevice.length,
                  itemStyle: { color: '#f56c6c' },
                },
                {
                  name: '保修未知',
                  value: this.warrantyUnknownDevice.length,
                  itemStyle: { color: '#bea9e9' },
                },
              ],
            },
          ],
        };

        this.warrantyChart.setOption(option);
      },
      formatDate(inputDate) {
        const date = new Date(inputDate);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        // 格式化为 MM/dd hh:mm
        return `${year}-${month}-${day} ${hours}:${minutes}`;
      },
      getStatusType(status) {
        return (
          INSPECT_RECORD_STATUS_ENUM.find((item) => item.value === status) || {
            label: '未开始',
            value: 'ready',
            type: '',
          }
        );
      },
      exportReport() {
        //导出为图片
        htmlToJpeg(this.$refs.report);
      },
      back() {
        this.$router.back();
      },
      initReportCharts() {
        if (this.$refs.reportStatusBarChart) {
          this.reportStatusBarChart = this.$echarts.init(this.$refs.reportStatusBarChart);
          this.updateReportStatusBarChart();
        }

        if (this.$refs.reportStatusPieChart) {
          this.reportStatusPieChart = this.$echarts.init(this.$refs.reportStatusPieChart);
          this.updateReportStatusPieChart();
        }

        if (this.$refs.reportAbnormalTypeBarChart) {
          this.reportAbnormalTypeBarChart = this.$echarts.init(this.$refs.reportAbnormalTypeBarChart);
          this.updateReportAbnormalTypeBarChart();
        }

        if (this.$refs.reportLifespanPieChart) {
          this.reportLifespanPieChart = this.$echarts.init(this.$refs.reportLifespanPieChart);
          this.updateReportLifespanPieChart();
        }

        if (this.$refs.reportLifespanBarChart) {
          this.reportLifespanBarChart = this.$echarts.init(this.$refs.reportLifespanBarChart);
          this.updateReportLifespanBarChart();
        }
        if (this.$refs.reportWarrantyBarChart) {
          this.reportWarrantyBarChart = this.$echarts.init(this.$refs.reportWarrantyBarChart);
          this.updateReportWarrantyBarChart();
        }
        if (this.$refs.reportWarrantyPieChart) {
          this.reportWarrantyPieChart = this.$echarts.init(this.$refs.reportWarrantyPieChart);
          this.updateReportWarrantyPieChart();
        }
      },
      updateReportAbnormalTypeBarChart() {
        if (!this.reportAbnormalTypeBarChart) return;
        const option = {
          tooltip: {
            trigger: 'axis',
            formatter: '{b}: {c}项 ',
          },
          yAxis: {
            type: 'category',
            data: ['在线状态异常', '寿命状态异常', '保修状态异常', '属性状态异常'],
            axisTick: { alignWithLabel: true },
          },
          xAxis: {
            type: 'value',
            name: '数量(项)',
            minInterval: 1,
            splitLine: {
              show: false,
            },
          },
          grid: {
            left: '10',
            right: '70',
            top: '0',
            bottom: '0',
            containLabel: true,
          },
          series: [
            {
              name: '异常项',
              type: 'bar',
              barMaxWidth: 15,
              data: [
                {
                  name: '在线状态异常',
                  value: this.offlineDevice.length,
                  itemStyle: { color: '#bea9e9' },
                },
                {
                  name: '寿命状态异常',
                  value: this.renewDevice.length + this.overdueDevice.length + this.lifespanUnknownDevice.length,
                  itemStyle: { color: '#FFA500' },
                },
                {
                  name: '保修状态异常',
                  value: this.warrantyExpiringDevice.length + this.warrantyOverdueDevice.length + this.warrantyUnknownDevice.length,
                  itemStyle: { color: '#00BFFF' },
                },
                {
                  name: '属性状态异常',
                  value: this.abnormalPropCount,
                  itemStyle: { color: '#4169E1' },
                },
              ],
              label: {
                show: true,
                position: 'right',
                formatter: '{c}项',
                fontSize: 12,
                color: '#696969',
              },
            },
          ],
        };

        this.reportAbnormalTypeBarChart.setOption(option);
      },
      updateReportStatusPieChart() {
        if (!this.reportStatusPieChart) return;
        const option = {
          tooltip: {
            trigger: 'item',
            formatter: '{b}: {c}台 ({d}%)',
          },
          series: [
            {
              name: '在线状态',
              type: 'pie',
              radius: ['38%', '55%'],
              left: '0%',
              width: '100%',
              label: {
                alignTo: 'edge',
                formatter: '{b}\n{number|{c}台({d}%)}',
                minMargin: 5,
                edgeDistance: 10,
                lineHeight: 20,
                fontSize: 12,
                color: '#696969',
                rich: {
                  number: {
                    fontSize: 12,
                  },
                },
              },
              labelLayout: (params) => {
                const isLeft = params.labelRect.x < this.reportStatusPieChart.getWidth() / 2;
                const points = params.labelLinePoints;
                points[2][0] = isLeft ? params.labelRect.x : params.labelRect.x + params.labelRect.width;
                return {
                  labelLinePoints: points,
                };
              },
              data: [
                {
                  name: '在线设备',
                  value: this.onlineDevice.length,
                  itemStyle: { color: '#91cd77' },
                },
                {
                  name: '离线设备',
                  value: this.offlineDevice.length,
                  itemStyle: { color: '#909399' },
                },
              ],
            },
          ],
        };

        this.reportStatusPieChart.setOption(option);
      },
      updateReportStatusBarChart() {
        if (!this.reportStatusBarChart) return;
        const option = {
          tooltip: {
            trigger: 'axis',
            formatter: '{b}: {c}台 ',
          },
          xAxis: {
            type: 'category',
            data: ['在线设备', '离线设备'],
            axisTick: { alignWithLabel: true },
          },
          yAxis: {
            type: 'value',
            name: '数量（台）',
            minInterval: 1,
            splitLine: {
              show: false,
            },
          },
          grid: {
            left: '30',
            right: '1%',
            top: '40',
            bottom: '0',
            containLabel: true,
          },
          series: [
            {
              name: '在线状态',
              type: 'bar',
              barMaxWidth: 15,
              data: [
                {
                  name: '在线设备',
                  value: this.onlineDevice.length,
                  itemStyle: { color: '#91cd77' },
                },
                {
                  name: '离线设备',
                  value: this.offlineDevice.length,
                  itemStyle: { color: '#909399' },
                },
              ],
              label: {
                show: true,
                position: 'top',
                formatter: '{c}台',
                fontSize: 12,
                color: '#696969',
              },
            },
          ],
        };

        this.reportStatusBarChart.setOption(option);
      },
      updateReportLifespanPieChart() {
        if (!this.reportLifespanPieChart) return;
        const option = {
          tooltip: {
            trigger: 'item',
            formatter: '{b}: {c}台 ({d}%)',
          },
          series: [
            {
              name: '寿命状态',
              type: 'pie',
              radius: ['38%', '55%'],
              left: '0%',
              width: '100%',
              label: {
                alignTo: 'edge',
                formatter: '{b}\n{number|{c}台({d}%)}',
                minMargin: 5,
                edgeDistance: 10,
                lineHeight: 20,
                fontSize: 12,
                color: '#696969',
                rich: {
                  number: {
                    fontSize: 12,
                  },
                },
              },
              labelLayout: (params) => {
                const isLeft = params.labelRect.x < this.reportLifespanPieChart.getWidth() / 2;
                const points = params.labelLinePoints;
                points[2][0] = isLeft ? params.labelRect.x : params.labelRect.x + params.labelRect.width;
                return {
                  labelLinePoints: points,
                };
              },
              data: [
                {
                  name: '全新',
                  value: this.freshDevice.length,
                  itemStyle: { color: '#3dcca6' },
                },
                {
                  name: '良好',
                  value: this.goodDevice.length,
                  itemStyle: { color: '#87CEFA' },
                },
                {
                  name: '建议更换',
                  value: this.renewDevice.length,
                  itemStyle: { color: '#fabe46' },
                },
                {
                  name: '超期运行',
                  value: this.overdueDevice.length,
                  itemStyle: { color: '#f56c6c' },
                },
                {
                  name: '寿命未知',
                  value: this.lifespanUnknownDevice.length,
                  itemStyle: { color: '#bea9e9' },
                },
              ],
            },
          ],
        };

        this.reportLifespanPieChart.setOption(option);
      },
      updateReportLifespanBarChart() {
        if (!this.reportLifespanBarChart) return;
        const option = {
          tooltip: {
            trigger: 'axis',
            formatter: '{b}: {c}台 ',
          },
          xAxis: {
            type: 'category',
            data: ['全新', '良好', '建议更换', '超期运行', '寿命未知'],
            axisTick: { alignWithLabel: true },
            axisLabel: {
              interval: 0,
            },
          },
          yAxis: {
            type: 'value',
            name: '数量（台）',
            minInterval: 1,
            splitLine: {
              show: false,
            },
          },
          grid: {
            left: '30',
            right: '1%',
            top: '40',
            bottom: '0',
            containLabel: true,
          },
          series: [
            {
              name: '寿命状态',
              type: 'bar',
              barMaxWidth: 15,
              data: [
                {
                  name: '全新',
                  value: this.freshDevice.length,
                  itemStyle: { color: '#3dcca6' },
                },
                {
                  name: '良好',
                  value: this.goodDevice.length,
                  itemStyle: { color: '#87CEFA' },
                },
                {
                  name: '建议更换',
                  value: this.renewDevice.length,
                  itemStyle: { color: '#fabe46' },
                },
                {
                  name: '超期运行',
                  value: this.overdueDevice.length,
                  itemStyle: { color: '#f56c6c' },
                },
                {
                  name: '寿命未知',
                  value: this.lifespanUnknownDevice.length,
                  itemStyle: { color: '#bea9e9' },
                },
              ],
              label: {
                show: true,
                position: 'top',
                formatter: '{c}台',
                fontSize: 12,
                color: '#696969',
              },
            },
          ],
        };

        this.reportLifespanBarChart.setOption(option);
      },
      updateReportWarrantyPieChart() {
        if (!this.reportWarrantyPieChart) return;
        const option = {
          tooltip: {
            trigger: 'item',
            formatter: '{b}: {c}台 ({d}%)',
          },
          series: [
            {
              name: '保修状态',
              type: 'pie',
              radius: ['38%', '55%'],
              left: '0%',
              width: '100%',
              label: {
                alignTo: 'edge',
                formatter: '{b}\n{number|{c}台({d}%)}',
                minMargin: 5,
                edgeDistance: 10,
                lineHeight: 20,
                fontSize: 12,
                color: '#696969',
                rich: {
                  number: {
                    fontSize: 12,
                  },
                },
              },
              labelLayout: (params) => {
                const isLeft = params.labelRect.x < this.reportLifespanPieChart.getWidth() / 2;
                const points = params.labelLinePoints;
                points[2][0] = isLeft ? params.labelRect.x : params.labelRect.x + params.labelRect.width;
                return {
                  labelLinePoints: points,
                };
              },
              data: [
                {
                  name: '保修中',
                  value: this.warrantyUnderDevice.length,
                  itemStyle: { color: '#87CEFA' },
                },
                {
                  name: '即将到期',
                  value: this.warrantyExpiringDevice.length,
                  itemStyle: { color: '#fabe46' },
                },
                {
                  name: '过保',
                  value: this.warrantyOverdueDevice.length,
                  itemStyle: { color: '#f56c6c' },
                },
                {
                  name: '保修未知',
                  value: this.warrantyUnknownDevice.length,
                  itemStyle: { color: '#bea9e9' },
                },
              ],
            },
          ],
        };

        this.reportWarrantyPieChart.setOption(option);
      },
      updateReportWarrantyBarChart() {
        if (!this.reportWarrantyBarChart) return;
        const option = {
          tooltip: {
            trigger: 'axis',
            formatter: '{b}: {c}台 ',
          },
          xAxis: {
            type: 'category',
            data: ['保修中', '即将到期', '过保', '保修未知'],
            axisTick: { alignWithLabel: true },
            axisLabel: {
              interval: 0,
            },
          },
          yAxis: {
            type: 'value',
            name: '数量（台）',
            minInterval: 1,
            splitLine: {
              show: false,
            },
          },
          grid: {
            left: '30',
            right: '1%',
            top: '40',
            bottom: '0',
            containLabel: true,
          },
          series: [
            {
              name: '保修状态',
              type: 'bar',
              barMaxWidth: 15,
              data: [
                {
                  name: '保修中',
                  value: this.warrantyUnderDevice.length,
                  itemStyle: { color: '#87CEFA' },
                },
                {
                  name: '即将到期',
                  value: this.warrantyExpiringDevice.length,
                  itemStyle: { color: '#fabe46' },
                },
                {
                  name: '过保',
                  value: this.warrantyOverdueDevice.length,
                  itemStyle: { color: '#f56c6c' },
                },
                {
                  name: '保修未知',
                  value: this.warrantyUnknownDevice.length,
                  itemStyle: { color: '#bea9e9' },
                },
              ],
              label: {
                show: true,
                position: 'top',
                formatter: '{c}台',
                fontSize: 12,
                color: '#696969',
              },
            },
          ],
        };

        this.reportWarrantyBarChart.setOption(option);
      },
      // 获取属性显示值
      getPropertyValueLabel(row, type) {
        const property = row.property;
        if (!property) return row[type];
        const dataType = property.constraint.dataType;
        switch (dataType) {
          case 'BOOLEAN':
            return row[type] ? property.constraint.trueText : property.constraint.falseText;
          case 'ENUM':
          case 'NUMBER_ENUM':
            return Object.entries(property.constraint.range).find(([label, value]) => value === row[type])[0] || row[type];
          case 'INTEGER':
          case 'DECIMAL':
            return property.constraint.unit ? `${row[type]}${property.constraint.unit}` : row[type];
          default:
            return row[type];
        }
      },
    },
    watch: {
      showType: {
        handler(newVal) {
          if (newVal === 'report') {
            this.$nextTick(() => {
              this.initReportCharts();
            });
          }
          if (newVal === 'detail') {
            this.$nextTick(() => {
              this.initCharts();
            });
          }
        },
      },
      //监测路由变化
      $route: function (to, from) {
        if (to.path !== from.path) {
          this.$nextTick(() => {
            this.id = this.$route.params.id;
            this.getInspectRecordDetail();
          });
        }
      },
    },
    beforeDestroy() {
      if (this.statusChart) {
        this.statusChart.dispose();
        this.statusChart = null;
      }
      if (this.lifespanChart) {
        this.lifespanChart.dispose();
        this.lifespanChart = null;
      }
      if (this.warrantyChart) {
        this.warrantyChart.dispose();
        this.warrantyChart = null;
      }
      if (this.reportAbnormalTypeBarChart) {
        this.reportAbnormalTypeBarChart.dispose();
        this.reportAbnormalTypeBarChart = null;
      }
      if (this.reportStatusPieChart) {
        this.reportStatusPieChart.dispose();
        this.reportStatusPieChart = null;
      }
      if (this.reportStatusBarChart) {
        this.reportStatusBarChart.dispose();
        this.reportStatusBarChart = null;
      }
      if (this.reportLifespanBarChart) {
        this.reportLifespanBarChart.dispose();
        this.reportLifespanBarChart = null;
      }
      if (this.reportLifespanPieChart) {
        this.reportLifespanPieChart.dispose();
        this.reportLifespanPieChart = null;
      }
      if (this.reportWarrantyBarChart) {
        this.reportWarrantyBarChart.dispose();
        this.reportWarrantyBarChart = null;
      }
      if (this.reportWarrantyPieChart) {
        this.reportWarrantyPieChart.dispose();
        this.reportWarrantyPieChart = null;
      }

      window.removeEventListener('resize', this.resize);
    },
  };
</script>

<style lang="scss" scoped>
  .inspect-record-detail {
    padding: 14px;
    min-width: 1150px;
    color: black;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100%;
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
      width: 100%;
    }
    // 详情
    .detail {
      width: 100%;
      flex: 1;
      min-height: 0;
      overflow-y: auto;
      overflow-x: hidden;
      padding: 5px 10px;
    }
    .basic-info-card {
      box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
      border-radius: 8px;
      padding: 12px 16px;
      .title {
        font-weight: 600;
        font-size: 18px;
        .el-tag {
          margin-right: 8px;
        }
      }
      .info-content {
        margin-top: 10px;
        .info-item {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          line-height: 2;
          .label {
            color: #696969;
            margin-right: 8px;
          }
          .value {
            color: #303133;
            font-weight: 500;
          }
        }
      }
    }

    .statistics-cards {
      margin-top: 10px;

      .stat-card {
        padding: 16px;
        border-radius: 8px;
        box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
        display: flex;
        align-items: center;
        transition: all 0.3s;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
        }

        .stat-icon {
          .svg-icon {
            font-size: 40px;
          }
          margin-right: 18px;
        }

        .stat-info {
          .stat-value {
            font-size: 18px;
            font-weight: 600;
            line-height: 2;
          }
          .stat-label {
            font-size: 14px;
          }
        }
      }
    }
    .pending-devices-section {
      border-radius: 8px;
      box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
      margin-top: 10px;
      padding: 12px 16px;
      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .title {
          font-size: 16px;
          font-weight: 600;
          display: flex;
          align-items: center;

          i {
            margin-right: 6px;
            color: #ffba00;
          }
        }
      }

      .devices-list {
        margin-top: 10px;
        .device-card {
          background: #fdf6ec;
          border: 1px solid #ffba00;
          border-radius: 6px;
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 10px;

          .device-info {
            padding: 8px 14px;
            max-width: 100%;
            .device-name {
              font-size: 14px;
              color: #303133;
              font-weight: 500;
              margin-bottom: 4px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              max-width: 100%;
            }

            .device-type {
              font-size: 12px;
              color: #696969;
            }
          }
        }
      }
    }

    .charts-section {
      margin-top: 10px;

      .chart-card {
        padding: 12px 16px;
        border-radius: 8px;
        box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;

        .chart-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 10px;

          .chart-title {
            font-size: 16px;
            font-weight: 600;
            display: flex;
            align-items: center;

            i {
              margin-right: 6px;
              color: rgb(78, 201, 225);
            }
          }
        }

        .chart-content {
          height: 180px;
          display: flex;
          justify-content: center;
          align-items: center;
          .chart {
            width: 500px;
            height: 100%;
            display: flex;
            justify-content: center;
          }
        }
      }
    }
    .device-list-card .list-content {
      height: 200px;
    }

    .device-list-card,
    .report-section {
      margin-top: 10px;
      padding: 10px 16px 12px 16px;
      border-radius: 8px;
      box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: space-between;
        flex-wrap: wrap;
        height: 46px;

        .title {
          font-size: 16px;
          font-weight: 600;
          display: flex;
          align-items: center;

          i {
            margin-right: 6px;
            color: #f56c6c;
          }
        }

        .tags {
          display: flex;
          align-items: center;
          .tag {
            margin-right: 8px;
          }
          .offline {
            color: #808080;
            background-color: rgba(144, 147, 153, 0.3);
            border: 1px solid #909399;
          }
          .renew {
            color: #808080;
            background-color: rgba(250, 190, 70, 0.3);
            border: 1px solid #fabe46;
          }
          .overdue {
            color: #808080;
            background-color: rgba(245, 108, 108, 0.3);
            border: 1px solid #f56c6c;
          }
          .lifespan-unknown {
            color: #808080;
            background-color: rgba(190, 169, 233, 0.3);
            border: 1px solid #bea9e9;
          }
          .warranty-expiring {
            color: #808080;
            background-color: rgba(250, 190, 70, 0.3);
            border: 1px solid #fabe46;
          }
          .warranty-overdue {
            color: #808080;
            background-color: rgba(245, 108, 108, 0.3);
            border: 1px solid #f56c6c;
          }
          .warranty-unknown {
            color: #808080;
            background-color: rgba(190, 169, 233, 0.3);
            border: 1px solid #bea9e9;
          }
        }
      }

      .list-content {
        margin-top: 5px;
        overflow-y: auto;
        overflow-x: hidden;
        .list-subheader {
          font-size: 14px;
          color: #606266;
          margin: 8px 0;
          padding-left: 10px;

          &.offline {
            border-left: 3px solid #909399;
          }
          &.renew {
            border-left: 3px solid #fabe46;
          }
          &.overdue {
            border-left: 3px solid #f56c6c;
          }
          &.lifespan-unknown {
            border-left: 3px solid #bea9e9;
          }
          &.warranty-expiring {
            border-left: 3px solid #fabe46;
          }
          &.warranty-overdue {
            border-left: 3px solid #f56c6c;
          }
          &.warranty-unknown {
            border-left: 3px solid #bea9e9;
          }
        }

        .device-item {
          display: flex;
          align-items: center;
          padding: 8px;
          border-radius: 4px;
          background: #f5f7fa;
          transition: all 0.3s;
          margin: 5px 0;

          &:hover {
            background: #f0f2f5;
          }

          i {
            font-size: 16px;
            margin-right: 10px;
          }

          .device-info {
            flex: 1;
            min-width: 0;
            .device-name {
              font-size: 14px;
              color: #303133;
              margin-bottom: 4px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              max-width: calc(100% - 10px);
            }

            .device-meta {
              font-size: 12px;
              color: #909399;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              max-width: calc(100% - 10px);
            }
          }

          &.offline {
            border-left: 3px solid #909399;
            i {
              color: #909399;
            }
          }
          &.renew {
            border-left: 3px solid #fabe46;
            i {
              color: #fabe46;
            }
          }
          &.overdue {
            border-left: 3px solid #f56c6c;
            i {
              color: #f56c6c;
            }
          }
          &.lifespan-unknown {
            border-left: 3px solid #bea9e9;
            i {
              color: #bea9e9;
            }
          }
          &.warranty-expiring {
            border-left: 3px solid #fabe46;
            i {
              color: #fabe46;
            }
          }
          &.warranty-overdue {
            border-left: 3px solid #f56c6c;
            i {
              color: #f56c6c;
            }
          }
          &.warranty-unknown {
            border-left: 3px solid #bea9e9;
            i {
              color: #bea9e9;
            }
          }
        }
      }
    }
    .not-as-expected-props-section .prop-card {
      height: 240px;
    }

    .not-as-expected-props-section,
    .report-section {
      margin-top: 10px;
      padding: 12px 16px;
      border-radius: 8px;
      box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;

      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;

        .title {
          font-size: 16px;
          font-weight: 600;
          display: flex;
          align-items: center;

          i {
            margin-right: 6px;
            color: #f56c6c;
          }
        }
      }

      .prop-card {
        border-radius: 8px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
        margin-bottom: 10px;
        border: 1px solid #ebeef5;
        display: flex;
        flex-direction: column;

        .card-header {
          padding: 10px 16px;
          border-bottom: 1px solid #ebeef5;
          display: flex;
          justify-content: space-between;
          align-items: center;
          background: #f8f9fa;

          .device-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            width: 100%;
            .device-name {
              font-size: 14px;
              margin-bottom: 4px;
            }

            .device-status {
              .Online {
                color: #808080;
                background-color: rgba(145, 205, 119, 0.3);
                border: 1px solid #91cd77;
              }
              .Offline {
                color: #808080;
                background-color: rgba(144, 147, 153, 0.3);
                border: 1px solid #909399;
              }
            }
          }
        }

        .card-content {
          flex: 1;
          min-height: 0;
        }
      }
    }
  }
  .el-col-5 {
    width: 20% !important;
  }
  .success-subheader {
    font-size: 14px;
    color: #606266;
    margin: 8px 0;
  }
  ::-webkit-scrollbar,
  ::v-deep .el-table__body-wrapper::-webkit-scrollbar {
    width: 8px;
    transition: all 0.3s ease;
  }

  ::-webkit-scrollbar-thumb,
  ::v-deep .el-table__body-wrapper::-webkit-scrollbar-thumb {
    background: #dce7f2;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  ::-webkit-scrollbar-thumb:hover,
  ::v-deep .el-table__body-wrapper::-webkit-scrollbar-thumb:hover {
    background: #dce7f2;
    outline: 0;
    border: 0;
  }

  // 报表
  .report-wrap {
    flex: 1;
    min-height: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow-y: auto;
    overflow-x: hidden;
    .report {
      background-image: url('/iot-web/img/inspect-report-bg.jpg');
      background-repeat: repeat-y;
      //宽高比21:29.7,A4纸宽高比
      background-size: 700px 992px;
      //为保证导出效果一致，固定宽度
      width: 700px;
      display: flex;
      flex-direction: column;

      .report-header {
        margin: 40px 20px 10px 20px;
        background: rgba(225, 255, 255, 0.85);
        padding: 10px;
        border-radius: 12px;

        .report-title {
          font-size: 20px;
          font-weight: bold;
          margin-bottom: 5px;
          display: flex;
          align-items: center;
          width: 100%;
          .el-tag {
            margin-right: 10px;
          }
        }

        .report-info {
          display: grid;
          grid-template-columns: repeat(2, 1fr);

          .info-item {
            text-align: left;
            padding: 5px;
            border-radius: 8px;
            transition: all 0.3s ease;

            &:hover {
              background: rgba(24, 144, 255, 0.05);
            }

            .label {
              color: #696969;
              margin-right: 8px;
              font-size: 14px;
            }

            .value {
              color: #262626;
              font-weight: 500;
              font-size: 14px;
            }
          }
        }
      }

      .report-section {
        margin: 0 20px 10px 20px;
        background: rgba(225, 255, 255, 0.85);
        padding: 10px;
        border-radius: 12px;

        .section-title {
          font-size: 16px;
          font-weight: bold;
          color: #262626;
          margin-bottom: 10px;
          padding-left: 12px;
          border-left: 3px solid #1890ff;
          display: flex;
          align-items: center;

          &::after {
            content: '';
            flex: 1;
            height: 1px;
            background: linear-gradient(to right, rgba(24, 144, 255, 0.2), transparent);
            margin-left: 12px;
          }
        }
        .chart-container {
          width: 100%;
          height: 150px;
          display: flex;
          align-items: center;
          .status-chart {
            width: 50%;
            height: 100%;
          }
          .abnormal-count {
            width: 50%;
            padding-left: 30px;
          }
        }
      }

      .overview-cards {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 25px;
        margin-bottom: 10px;

        .overview-card {
          padding: 10px;
          border-radius: 12px;
          text-align: center;
          background: #fff;
          overflow: hidden;
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
          }

          &.normal {
            background: linear-gradient(135deg, #91d5ff 0%, #1890ff 100%);
          }

          &.warning {
            background: linear-gradient(135deg, #ffd666 0%, #faad14 100%);
          }

          &.success {
            background: linear-gradient(135deg, #95de64 0%, #52c41a 100%);
          }

          &.abnormal {
            background: linear-gradient(135deg, #f56c6c 0%, #ff404a 100%);
          }

          .card-value {
            font-size: 20px;
            font-weight: bold;
            color: #fff;
          }

          .card-label {
            font-size: 14px;
            color: #fff;
            margin-top: 10px;
          }
        }
      }
    }
  }
</style>
