<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
    <link rel="stylesheet" href="40e93b11cbd4174c60b22cba0d630300.css" />
    <style>
      ul {
        margin: 0;
        padding: 0;
      }
      ul li {
        list-style: none;
      }
      body,
      html {
        height: 100%;
        margin: 0;
        user-select: none;
      }
      .wrap {
        width: 100%;
        height: 100%;
        position: relative;
        box-sizing: border-box;
      }

      .content-wrap {
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        padding: 1vw;
      }

      .title {
        color: #999;
        text-align: justify;
        text-align-last: justify;
        /* max-width: 100px; */
        /* font-weight: bold; */
        font-size: 3vw;
      }
      .value {
        color: rgb(54, 54, 54);
        /* font-weight: bold; */
        text-align: right;
        font-size: 3vw;
      }

      .caption {
        color: rgb(0, 0, 0);
        margin-top: 3vh;
        margin-bottom: 4vh;
        font-weight: bold;
        font-size: 3.7vw;
      }
      .stock {
        /* display: flex; */
        /* justify-content: center; */
        /* align-items: center; */
        /* margin: 1vw 0 4vw; */
        color: rgb(0, 0, 0);
        font-size: 3.6vw;
        margin-right: 3vw;
      }
      .stock .name {
        /* margin-right: 2vw; */
        font-weight: bold;
      }
      .key {
        color: #999;
        font-size: 1.6vw;
      }
      .current {
        /* display: flex; */
        /* margin-bottom: 2vh; */
      }
      .current .left {
        display: flex;
        align-items: flex-end;
        /* justify-content: center; */
        /* margin-right: 2vw; */
      }
      .current .p {
        font-size: 5.5vw;
        /* font-size: 4.3vw; */
        /* margin-bottom: 1vh; */
        /* font-weight: bold; */
      }
      .current .rate {
        display: flex;
        /* justify-content: space-between; */
        /* font-size: 3.3vw; */
        /* margin-left: 2vw; */
      }
      .current .ud {
        margin: 0 3vw;
        font-size: 4vw;
        /* font-size: 6.5vw; */
      }
      .current .pc {
        font-size: 4vw;
        /* font-size: 6.5vw; */
      }
      .current .right {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }
      .current .row {
        display: grid;
        grid-template-columns: repeat(3, 9vw 1fr);
        grid-gap: 2vw;
      }
      .overview .row {
        display: grid;
        grid-template-columns: repeat(3, 12vw 1fr);
        grid-gap: 3vw;
        margin-bottom: 4vh;
      }
      .overview .row:nth-child(even) {
        /* margin-bottom: 6vh; */
      }
      .empty-wrap {
        width: 100vw;
        height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 8vw;
        color: #409eff;
      }

      .red {
        color: rgb(207, 52, 67);
      }
      .green {
        color: rgb(35, 145, 111);
      }

      [v-cloak] {
        display: none;
      }
    </style>
    <!-- vue -->
    <script src="174365807c06490c848d7b1d45fdc348.js"></script>
    <!-- jquery.js -->
    <script src="13c0a5055cca7b2463b2f73701960b9e.js"></script>
    <!-- mqtt -->
    <script src="d4bfcfa9de50875589eaab322a015441.js"></script>
    <script src="page.js"></script>
  </head>

  <body>
    <div id="app" class="wrap" v-cloak :style="style">
      <div class="wrap">
        <div class="content-wrap" v-if="Object.keys(listInfo).length">
          <div style="display: flex; align-items: flex-end; margin-bottom: 4vh">
            <div class="stock">
              <div class="name">{{listInfo.name}}</div>
              <div class="key">{{key}}.{{listInfo.market}}</div>
            </div>
            <div class="current">
              <div class="left">
                <!-- 当前价格（元） -->
                <div class="p" :class="color">{{listInfo.p}}</div>
                <div class="rate">
                  <!-- 当前涨跌额（元） -->
                  <div class="ud" :class="listInfo.ud>=0?'red':'green'">
                    {{listInfo.ud}}
                  </div>
                  <!-- 涨跌幅（%） -->
                  <div class="pc" :class="color">{{listInfo.pc+'%'}}</div>
                </div>
              </div>
              <div class="right"></div>
            </div>
          </div>

          <!-- <div class="caption">盘口信息</div> -->
          <div class="overview">
            <div class="row">
              <div class="title">最高</div>
              <!-- 最高价（元） -->
              <div class="value" :class="listInfo.h > listInfo.p?'red':'green'">
                {{listInfo.h}}
              </div>

              <div class="title">今开</div>
              <!-- 开盘价（元） -->
              <div class="value" :class="listInfo.o > listInfo.p?'red':'green'">
                {{listInfo.o}}
              </div>

              <div class="title">成交量</div>
              <!-- 成交量(手) -->
              <div class="value">
                {{IsValid(listInfo.vText)?listInfo.vText+'手':'-'}}
              </div>
            </div>
            <div class="row">
              <div class="title">最低</div>
              <!-- 最低价（元） -->
              <div class="value" :class="listInfo.l > listInfo.p?'red':'green'">
                {{listInfo.l}}
              </div>

              <div class="title">昨收</div>
              <!-- 昨收 -->
              <div class="value">{{listInfo.yc}}</div>

              <div class="title">成交额</div>
              <!-- 成交额（元） -->
              <div class="value">{{listInfo.cjeText}}</div>
            </div>
            <div class="row">
              <div class="title">换手率</div>
              <!-- 换手（%） -->
              <div class="value">
                {{IsValid(listInfo.hs)?listInfo.hs+'%':'-'}}
              </div>
              <div class="title">市盈率</div>
              <!-- 市盈率（动态，总市值除以预估全年净利润，例如当前公布一季度净利润1000万，则预估全年净利润4000万） -->
              <div class="value">{{IsValid(listInfo.pe)?listInfo.pe:'-'}}</div>
              <div class="title">涨速</div>
              <!-- 涨速（%） -->
              <div class="value">
                {{IsValid(listInfo.zs)?listInfo.zs+'%':'-'}}
              </div>
            </div>
            <div class="row">
              <div class="title">振幅</div>
              <!-- 振幅（%） -->
              <div class="value">
                {{IsValid(listInfo.zf)?listInfo.zf+'%':'-'}}
              </div>
              <div class="title">市净率</div>
              <!-- 市净率 -->
              <div class="value">{{listInfo.sjl}}</div>
              <div class="title">量比</div>
              <!-- 量比（%） -->
              <div class="value">
                {{IsValid(listInfo.lb)?listInfo.lb+'%':'-'}}
              </div>
            </div>
            <div class="row">
              <!-- <div class="title">60日涨跌幅</div> -->
              <!-- 60日涨跌幅（%） -->
              <!-- <div class="value">{{listInfo.zdf60?listInfo.zdf60+'%':'-'}}</div> -->

              <div class="title">流通市值</div>
              <!-- 流通市值（元） -->
              <div class="value">{{listInfo.ltText}}</div>
              <div class="title">总市值</div>
              <!-- 总市值（元） -->
              <div class="value">{{listInfo.szText}}</div>
            </div>
          </div>
        </div>
        <div v-else class="empty-wrap">当前无股票信息</div>
      </div>
    </div>
  </body>

  <script>
    window.$page = page;
    const STYLE = ["backgroundColor"];
    const PROP = ["key"];
    const TYPE = "REAL_TIME_SPECIFIED_STOCK";

    const app = new Vue({
      el: "#app",
      data: {
        listInfo: {
          fm: "-0.06",
          h: "16.53",
          hs: "0.58",
          lb: "1.18",
          l: "16.11",
          lt: "10855932471.00",
          ltText: "108.56亿",
          o: "16.20",
          pe: "45.35",
          pc: "0.98",
          p: "16.43",
          sz: "20403724311.00",
          szText: "204.04亿",
          cje: "62366319.00",
          cjeText: "6236.63万",
          ud: "0.16",
          v: "38143",
          vText: "3.81万",
          yc: "16.27",
          zf: "2.58",
          zs: "0.00",
          sjl: "3.91",
          zdf60: "-2.78",
          zdfnc: "-3.07",
          t: "2023-05-19 11:22:06",
          name: "三六一",
          market: "SH",
        },
        key: "601352",
        props: page.props,
        mqttClient: null,
        mqttTopic: "",
        mqtt: {},
      },
      computed: {
        color() {
          return this.listInfo.pc >= 0 ? "red" : "green";
        },
        style() {
          let style = {};
          for (let item of this.props) {
            if (STYLE.includes(item.field)) {
              style[item.field] = item.value;
            }
          }
          return style;
        },
      },
      created() {
        this.log("created");
        if (this.isWindows()) {
          return;
        }
        this.listInfo = {};
        if (window.DSS20AndroidJS && window.DSS20AndroidJS.getWebMqttWs) {
          var data = window.DSS20AndroidJS.getWebMqttWs();
          this.log("mqtt地址：" + data);
          try {
            var info = JSON.parse(JSON.parse(data));
            this.getMqttServerAndConnect(info);
          } catch (err) {
            this.log("mqtt地址解析失败");
          }
        } else {
          this.log("获取mqtt地址失败");
        }
      },
      mounted() {
        window["update"] = (val, mqtt = null) => {
          let styles = [],
            props = [];
          for (let i = 0; i < val.length; i++) {
            let item = val[i];
            if (STYLE.includes(item.field)) {
              styles.push(item);
            } else if (PROP.includes(item.field)) {
              props.push(item);
              let index = this.getPropIndex(item.field);
              this.props[index].value = item.value;
            }
          }

          styles.length && this.updateStyles(styles);
          if (mqtt) {
            this.getMqttServerAndConnect(mqtt);
          } else {
            this.updateProps();
          }
        };

        window["setMqttParam"] = (param) => {
          this.getMqttServerAndConnect(param);
        };

        window["updateMqtt"] = (param) => {
          this.updateMqtt(param);
        };
      },

      beforeDestory() {
        this.mqttClose();
      },
      methods: {
        /*
         * 值是否有效
         */
        IsValid(data) {
          if (
            data === "" ||
            data === null ||
            data === undefined ||
            data === "null"
          ) {
            return false;
          }
          return true;
        },
        /*
         * 终端打印
         */
        log(msg) {
          console.log(msg);
          window.DSS20AndroidJS &&
            window.DSS20AndroidJS.htmlLogcat("股票节目：" + msg);
        },
        /*
         * 判断当前环境是否是pc
         */
        isWindows() {
          return window.DSS20AndroidJS === undefined;
        },
        getMac() {
          if (this.isWindows()) {
            return ''
          }
          var data = window.DSS20AndroidJS.terminalInfo();
          var info = JSON.parse(data);
          return info.mac;
        },
        updateMqtt(mqtt) {
          if (!mqtt) {
            return;
          }
          mqtt = JSON.parse(mqtt);

          if (
            (this.mqttClient && this.mqtt.ws !== mqtt.ws) ||
            (!this.mqttClient && !Object.keys(this.mqtt).length)
          ) {
            this.log("mqtt重连");
            this.mqttClose();
            this.mqtt = mqtt;
            this.getMqttServerAndConnect(mqtt);
          }
        },
        updateProps() {
          for (let prop of this.props) {
            if (prop.field === "key" && prop.value && prop.value.length === 6) {
              if (this.mqttClient) {
                this.mqttPublish(prop.value);
              }
            }
          }
        },
        updateStyles(styles) {
          for (let style of styles) {
            let index = this.getPropIndex(style.field);
            if (index !== -1) {
              let data = this.props[index];
              data.value = style.value;
              this.$set(this.props, index, data);
            }
          }
        },
        getPropIndex(name) {
          for (let i = 0; i < this.props.length; i++) {
            if (this.props[i].field === name) {
              return i;
            }
          }
          return -1;
        },
        /**
         * 格式化价格
         */
        numberFormat(value) {
          if (isNaN(value)) {
            return "-";
          }
          let param = {};
          let k = 10000;
          let sizes = ["", "万", "亿", "万亿"];
          let i;
          if (value < k) {
            param.value = value;
            param.unit = "";
          } else {
            i = Math.floor(Math.log(value) / Math.log(k));
            param.value = (value / Math.pow(k, i)).toFixed(2);
            param.unit = sizes[i];
          }

          return param.value + param.unit;
        },
        /**
         * 随机ID
         * @param {*} len
         * @param {*} radix
         * @returns
         */
        randomId(len, radix) {
          var chars =
            '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('')
          var uuid = [],
            i
          radix = radix || chars.length
          if (len) {
            // Compact form
            for (i = 0; i < len; i++) uuid[i] = chars[0 | (Math.random() * radix)]
          } else {
            // rfc4122, version 4 form
            var r
            // rfc4122 requires these characters
            uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-'
            uuid[14] = '4'
            // Fill in random data.  At i==19 set the high bits of clock sequence as
            // per rfc4122, sec. 4.1.5
            for (i = 0; i < 36; i++) {
              if (!uuid[i]) {
                r = 0 | (Math.random() * 16)
                uuid[i] = chars[i == 19 ? (r & 0x3) | 0x8 : r]
              }
            }
          }
          return uuid.join('')
        },
        /**
         * 连接到MQTT服务器并订阅
         */
        getMqttServerAndConnect(param) {
          this.log("mqtt参数：" + JSON.stringify(param));

          const that = this;
          let { ws, username, password } = param;
          let mac = this.getMac() || ''
          // 客户端ID
          let clientId = `js_stock1_${mac}_${this.randomId(16, 16)}`;
          that.mqttClient = mqtt.connect(ws, {
            clientId,
            username,
            password,
            clean: true,
            connectTimeout: 5 * 1000,
            keepalive: 30
          });
          that.mqttClient.on("connect", () => {
            this.log("mqtt连接成功");

            this.updateProps();
          });
          that.mqttClient.on("error", (error) => {
            this.log("mqtt连接失败:" + error);
            this.listInfo = {};
          });
          //监听接收消息事件
          that.mqttClient.on("message", (topic, message) => {
            try {
              this.log("获取mqtt消息:" + message);
              message = JSON.parse(message.toString());
              this.key = message.key;
              // 格式化成交量、成交额、流通市值、总市值
              message.value.cjeText = this.numberFormat(message.value.cje);
              message.value.vText = this.numberFormat(message.value.v);
              message.value.ltText = this.numberFormat(message.value.lt);
              message.value.szText = this.numberFormat(message.value.sz);
              message.value.market = message.value.market.toUpperCase();
              this.listInfo = message.value;
            } catch (err) {
              this.log("mqtt消息解析失败");
            }
          });
        },

        /**
         * 发布MQTT主题
         */
        mqttPublish(key) {
          let topic = `dss2/server/datum/watch`;
          let message = `{"type":"${TYPE}","key":"${key}"}`;
          this.log("mqtt发布主题:" + message);

          this.mqttClient.publish(
            topic,
            message,
            { qos: 1, retain: true },
            (err, res) => {
              if (err) {
                this.log("mqtt发布主题失败", err);
                return;
              }
              //mqtt订阅主题
              this.mqttRefreshSubscribe(key);
            }
          );
        },
        /**
         * 订阅MQTT主题
         */
        mqttRefreshSubscribe(key) {
          // 取消之前的订阅
          if (this.mqttTopic) {
            this.mqttClient.unsubscribe(this.mqttTopic);
          }
          this.mqttTopic = `dss2/server/datum/notify/${TYPE}/${key}`;
          this.log("mqtt订阅主题:" + this.mqttTopic);

          this.mqttClient.subscribe(
            this.mqttTopic,
            { qos: 1 },
            function (err, res) {
              if (err) {
                this.log("mqtt订阅主题失败:", err);
                return;
              }
            }
          );
        },
        /**
         * 释放MQTT客户端
         */
        mqttClose() {
          if (this.mqttClient) {
            this.mqttClient.unsubscribe(this.mqttTopic);
            this.mqttClient.end();
          }
        },
      },
    });
  </script>
</html>
