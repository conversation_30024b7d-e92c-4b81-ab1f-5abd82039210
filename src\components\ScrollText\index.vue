<template>
  <div class="marquee-wrap" ref="marquee-wrap">
    <div class="scroll" ref="scroll">
      <div class="marquee" ref="marquee">{{ text }}</div>
      <div class="copy" ref="copy"></div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    text: { type: String, default: "" },
    bScroll: { type: Boolean, default: true }
  },
  data() {
    return {
      timer: null
    };
  },
  mounted() {
    if (this.bScroll) {
      let timer = setTimeout(() => {
        this.move();
        clearTimeout(timer);
      }, 1000);
    }
  },
  watch: {
    bScroll(newValue) {
      if (newValue) {
        clearInterval(this.timer);
        let timer = setTimeout(() => {
          this.move();
          clearTimeout(timer);
        }, 1000);
      } else {
        clearInterval(this.timer);
        this.$refs["scroll"].style.transform = "translateX(" + 0 + "px)";
      }
    }
  },
  methods: {
    move() {
      let maxWidth = this.$refs["marquee-wrap"].clientWidth;
      let scroll = this.$refs["scroll"];
      let width = this.$refs["marquee"].scrollWidth;

      if (width <= maxWidth) {
          this.$refs["marquee"].style.paddingRight = 0
          return;
      }
      let copy = this.$refs["copy"];
      copy.innerText = this.text;
      let distance = 0;
      this.timer = setInterval(() => {
        distance -= 1;
        if (-distance >= width) {
          distance = 0;
        }
        scroll.style.transform = "translateX(" + distance + "px)";
      }, 20);
    }
  },
  beforeDestroy() {
    clearInterval(this.timer);
  }
};
</script>
<style lang="scss" scoped>
.scroll {
  >div {
    display: inline-block;
    word-break: keep-all; // 不换行
    white-space: nowrap;
    margin: 0;
    /* 设置前后间隔 */
    &.marquee {
      padding-right: 140px;
    }
  }
}
</style>
