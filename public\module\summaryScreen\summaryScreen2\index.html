<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Document</title>
  <link rel="stylesheet" href="40e93b11cbd4174c60b22cba0d630300.css" />
  <style>
    ul {
      margin: 0;
      padding: 0;
    }

    ul li {
      list-style: none;
    }

    body,
    html {
      height: 100%;
      margin: 0;
    }

    .wrap {
      width: 100%;
      height: 100%;
      position: relative;
      display: flex;
      flex-direction: column;
    }

    .wrap>img {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .header-wrap {
      width: 100%;
      height: 12vh;
    }

    .content-wrap {
      flex: 1;
      overflow: hidden;
      position: relative;
      box-sizing: border-box;
      width: 100%;
      padding: 5vh 1vw;
      display: flex;
      flex-direction: column;
    }

    .content {
      flex: 1;
      overflow: hidden;
    }

    .content .room-list {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      column-gap: 1vw;
    }

    .content .room-wrap {
      background-color: rgba(255, 255, 255, 0.1);
      width: 18.8vw;
      padding: 3vh 2vw;
      margin-bottom: 1vw;
      box-sizing: border-box;
      box-shadow: 0 2px 4px rgba(255, 255, 255, .15), 0 0 6px rgba(255, 255, 255, .04);
    }

    .content .room-name {
      font-size: 1.6vw;
      padding-bottom: 2vh;
      border-bottom: 2px solid white;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .conf-list {
      font-size: 1.2vw;
    }

    .content .time-wrap {
      margin: 3vh 0 2vh;
      overflow: hidden;
      display: flex;
      flex-direction: row;
    }

    .conf-wrap+.conf-wrap {
      margin-top: 6vh;
    }

    .conf-wrap .subject,
    .conf-wrap .dept {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .conf-wrap .dept {
      margin-left: 5px;
      flex: 1;
    }

    .conf-wrap .dept,
    .conf-wrap .time {
      flex-shrink: 0;
    }

    .scroll-wrap {
      height: 26vh;
      overflow: hidden;
    }

    @media screen and (max-width: 1600px) {
      .content .room-wrap {
        width: 23.75vw;
      }

      .conf-list {
        font-size: 1.4vw;
      }

      .content .room-name {
        font-size: 1.8vw;
      }
    }

    @media screen and (max-width: 1200px) {
      .content .room-wrap {
        width: 32vw;
      }

      .conf-list {
        font-size: 1.6vw;
      }

      .content .room-name {
        font-size: 2.2vw;
      }
    }

    @media screen and (max-width: 800px) {
      .content .room-wrap {
        width: 48.5vw;
      }

      .conf-list {
        font-size: 1.8vw;
      }

      .content .room-name {
        font-size: 2.4vw;
      }
    }

    [v-cloak] {
      display: none;
    }
  </style>
  <!-- vue -->
  <script src="174365807c06490c848d7b1d45fdc348.js"></script>
  <!-- jquery.js -->
  <script src="13c0a5055cca7b2463b2f73701960b9e.js"></script>
  <!-- mqtt -->
  <script src="d4bfcfa9de50875589eaab322a015441.js"></script>
  <!-- 文字横向滚动组件 -->
  <script src="59e4fb71c5c2fa1dba4969d59dcad854.js"></script>
  <!-- 列表向上滚动组件 -->
  <script src="e7d8d5311299370dfa42b75593af0b47.js"></script>
  <script src="page.js"></script>
</head>

<body>
  <div id="app" class="wrap" v-cloak>
    <div :style="style" class="wrap">
      <img src="./401bbad740e8542986b7df0cf6c4c2cf.jpg" alt="" v-if="style.bShowBg" />
      <div class="header-wrap" v-if="style.bShowHeader"></div>
      <div class="content-wrap">
        <div class="content" id="scrollWrap">
          <ul class="room-list">
            <li class="room-wrap" v-for="(room,index) in conferenceList"
              :style="{height:style.bShowHeader?'38vh':'44vh'}">
              <div class="room-name" :style="{borderColor:style.color}">{{room.roomName}}</div>
              <div class="conf-list">
                <scroll-list :b-scroll="parseInt(index / 10) === currentPageIndex ">
                  <template v-slot:content="{curPage}">
                    <div class="conf-wrap" v-for="item in room.data" :key="item.confId">
                      <div class="time-wrap">
                        <span class="time">{{item.period}}</span>
                        <span class="dept">{{item.dept}}</span>
                      </div>
                      <div class="subject">{{item.subject}}</div>
                    </div>
                  </template>
                </scroll-list>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</body>

<script>
  window.$page = page;
  const cssProperty = ["height", "width", "fontSize"];
  const STYLE = ["color", "backgroundColor"];
  const PROP = ["conferenceRoom"];
  const topicPrefix = "dss2/web/conference/room/";

  const app = new Vue({
    el: "#app",
    data: {
      props: page.props,
      mqttClient: null,
      timer: null,
      roomIdList: new Set(),
      conferenceList: [],
      mqtt: {},
      // 当前页码
      currentPageIndex: 0
    },
    computed: {
      style() {
        let style = {};
        for (let prop of this.props) {
          if (cssProperty.includes(prop.field)) {
            style[prop.field] = prop.value + "%"
          } else {
            style[prop.field] = prop.value
          }
        }
        return style;
      }
    },
    created() {
      for (let i = 1; i <= 13; i++) {
        this.roomIdList.add(-i);
        this.conferenceList.push({
          roomId: -i,
          roomName: "会议室" + i,
          data: [
            {
              id: -i,
              subject: "研究会议" + i,
              period: "10:00-12:00",
              dept: "机构" + i
            },
            {
              id: -i,
              subject: "研究会议" + i,
              period: "14:00-16:00",
              dept: "机构" + i
            },
            {
              id: -i,
              subject: "研究会议" + i,
              period: "14:00-16:00",
              dept: "机构" + i
            }
          ]
        });
      }
      // 调试
      if (this.isWindows()) {
        // this.roomIdList.clear();
        // this.conferenceList = [];
        // let ws = "ws://192.168.89.251:8080/mqtt";
        // let username = "admin";
        // let password = "nst@aliyun";
        // this.getMqttServerAndConnect({ ws, username, password });
        return;
      }

      this.roomIdList.clear();
      this.conferenceList = [];
      var data = window.DSS20AndroidJS.getWebMqttWs();
      var info = JSON.parse(JSON.parse(data));
      this.getMqttServerAndConnect(info);
    },
    mounted() {
      this.scroll();
      window["update"] = (val, mqtt = null) => {
        this.updateProps(val);
      };

      window["setMqttParam"] = param => {
        this.getMqttServerAndConnect(param);
      };

      window["updateMqtt"] = param => {
        this.updateMqtt(param);
      };
    },

    beforeDestory() {
      this.timer && clearInterval(this.timer);
      this.mqttClose();
    },
    methods: {
      /*
       * 终端打印
       */
      log(msg) {
        if (this.isWindows()) {
          console.log(msg)
          return;
        }
        window.DSS20AndroidJS &&
          window.DSS20AndroidJS.htmlLogcat("汇总屏：" + msg);
      },
      updateProps(props) {
        for (let prop of props) {
          let index = this.getPropIndex(prop.field);
          if (index !== -1) {
            let data = this.props[index];
            data.value = prop.value;
            this.$set(this.props, index, data);
          }
        }
      },
      getPropIndex(name) {
        for (let i = 0; i < this.props.length; i++) {
          if (this.props[i].field === name) {
            return i;
          }
        }
        return -1;
      },
      updateMqtt(mqtt) {
        if (!mqtt) {
          return;
        }
        mqtt = JSON.parse(mqtt);

        if (
          (this.mqttClient && this.mqtt.ws !== mqtt.ws) ||
          (!this.mqttClient && !Object.keys(this.mqtt).length)
        ) {
          this.log("mqtt重连");
          this.mqttClose();
          this.roomIdList.clear();
          this.conferenceList = [];
          this.mqtt = mqtt;
          this.getMqttServerAndConnect(mqtt);
        }
      },
      isWindows() {
        // var userAgent = navigator.userAgent;
        // return userAgent.indexOf("Windows") > -1;
        return window.DSS20AndroidJS === undefined;
      },
      getMac() {
        if(this.isWindows()){
          return ''
        }
        var data = window.DSS20AndroidJS.terminalInfo();
        var info = JSON.parse(data);
        return info.mac;
      },
      // 根据页面宽度，获取每行列数
      getColumns() {
        let viewportWidth = window.innerWidth;
        if (viewportWidth > 1600) {
          return 5
        } else if (viewportWidth > 1200) {
          return 4
        } else if (viewportWidth > 800) {
          return 3
        } else {
          return 2
        }
      },
      // 获取总页数
      getTotalPage() {
        const total = document.querySelectorAll('.room-wrap').length
        const columns = this.getColumns()
        // 计算总行数，使用 Math.ceil 确保即使最后一行未满也计入
        let totalRows = Math.ceil(total / columns);
        // 每页展示行数
        let pageRows = 2;
        // 计算总页数，基于每页展示的行数
        // let totalPages = Math.max(1, totalRows - pageRows + 1);
        let totalPages = Math.ceil(totalRows / pageRows);

        console.log('总行数', totalRows)
        console.log('总页数', totalPages)
        return totalPages;
      },
      scroll() {
        const scrollBox = $("#scrollWrap .room-list");
        let totalPage = this.getTotalPage();

        // 每页展示行数
        let pageRows = 2;

        function scrollToPage(index) {
          if (index >= 0 && index < totalPage) {
            this.currentPageIndex = index;
            console.log(this.currentPageIndex)
            // outerHeight(true)包括margin
            let scrollHeight = scrollBox.find("li").outerHeight(true)
            scrollBox.stop().animate(
              {
                marginTop: -scrollHeight * this.currentPageIndex * pageRows
              },
              1600)
          }
        }

        // 自动滚动逻辑（例如每5秒切换一次）
        setInterval(() => {
          this.currentPageIndex = (this.currentPageIndex + 1) % totalPage
          scrollToPage(this.currentPageIndex); // 循环滚动
        }, 20 * 1000); // 每隔20秒滚动
      },
      conferenceRoomSubscribe() {
        for (let prop of this.props) {
          if (prop.field === "conferenceRoom") {
            if (!prop.value.length && this.isWindows()) {
              return;
            }

            //删除之前的订阅列表及会议信息
            this.roomIdList.forEach(id => {
              let topic = this.getTopic(id);
              this.mqttClient.unsubscribe(topic);
            });
            this.conferenceList = [];
            this.roomIdList.clear();

            //订阅当前的会议室
            this.roomIdList = new Set(
              prop.value.map(item => {
                this.mqttRefreshSubscribe(item.roomId);
                return item.roomId;
              })
            );
          }
        }
      },
      formateData(dstList, roomId, roomName) {
        let index = this.conferenceList.findIndex(
          item => item.roomId === roomId
        );
        if (index !== -1) {
          this.conferenceList[index].data = dstList;
        } else {
          this.conferenceList.push({ data: dstList, roomId, roomName });
        }
      },
      deleteRoomById(roomId) {
        let srcList = JSON.parse(JSON.stringify(this.conferenceList));
        return srcList.filter(item => item.roomId !== roomId);
      },
      getPeriod(startTimeStamp, endTimeStamp, periodArr) {
        if (periodArr[1].includes('永久')) {
          return '永久会议'
        }
        let startTimeArr = periodArr[0].split(' ')
        let endTimeArr = periodArr[1].split(' ')
        let curEndTime = new Date(new Date(startTimeStamp).setHours(23, 59, 59, 999))
          .getTime()
        // 跨天会议,超过一天显示+1
        if (endTimeStamp > curEndTime) {
          endTimeArr[1] += '+1'
        }
        return startTimeArr[1] + '-' + endTimeArr[1]
      },
      /**
       * 随机ID
       * @param {*} len
       * @param {*} radix
       * @returns
       */
      randomId(len, radix) {
        var chars =
          '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('')
        var uuid = [],
          i
        radix = radix || chars.length
        if (len) {
          // Compact form
          for (i = 0; i < len; i++) uuid[i] = chars[0 | (Math.random() * radix)]
        } else {
          // rfc4122, version 4 form
          var r
          // rfc4122 requires these characters
          uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-'
          uuid[14] = '4'
          // Fill in random data.  At i==19 set the high bits of clock sequence as
          // per rfc4122, sec. 4.1.5
          for (i = 0; i < 36; i++) {
            if (!uuid[i]) {
              r = 0 | (Math.random() * 16)
              uuid[i] = chars[i == 19 ? (r & 0x3) | 0x8 : r]
            }
          }
        }
        return uuid.join('')
      },
      /**
       * 连接到MQTT服务器并订阅
       */
      getMqttServerAndConnect(param) {
        this.log("mqtt参数：" + JSON.stringify(param));

        const that = this;
        let { ws, username, password } = param;
        let mac = this.getMac() || ''
        // 客户端ID
        let clientId = `js_summaryScreen2_${mac}_${this.randomId(16, 16)}`;
        that.mqttClient = mqtt.connect(ws, {
          clientId,
          username,
          password,
          clean: true,
          connectTimeout: 5 * 1000,
          keepalive: 30
        });
        that.mqttClient.on("connect", () => {
          this.log("mqtt连接成功");
          this.conferenceRoomSubscribe();
        });
        that.mqttClient.on("error", error => {
          this.log("mqtt连接失败:" + error);
        });
        //监听接收消息事件
        that.mqttClient.on("message", (topic, message) => {
          message = JSON.parse(message.toString());
          this.log("获取mqtt消息：" + JSON.stringify(message));
          const { meetingroom, confDetailDTOList } = message;

          // 会议室被删除后，返回的roomName为空，删除该会议室的所有会议
          if (!meetingroom.roomName) {
            this.log("会议室被删除");
            this.conferenceList = this.deleteRoomById(meetingroom.id);
            return;
          }

          const { id, roomName } = meetingroom;
          let res = [];
          if (confDetailDTOList.length) {
            res = confDetailDTOList.filter(item => {
              if (item.approved !== 4) {
                return false;
              }

              let periodArr = item.period.split("-");
              item.period = this.getPeriod(item.start, item.end, periodArr)

              // 已经结束的会议不显示
              if (item.end < +new Date()) {
                return false
              }

              return true;
            });
          }

          if (!res.length) {
            res = [
              {
                subject: "",
                period: "",
                confId: Math.random(),
                roomId: id
              }
            ];
          }
          this.log("格式化后数据：" + JSON.stringify(res));
          this.formateData(res, id, roomName);
        });
      },

      /**
       * 发布MQTT主题
       */
      mqttPublish(id) {
        let topic = `dss2/web/conference/room/${id}`;
        let message = `{"command":"WEATHER","parameters":{${id}:"${id}"}}`;
        this.log("mqtt发布主题:" + message);
        this.mqttClient.publish(
          topic,
          message,
          { qos: 1, retain: true },
          (err, res) => {
            if (err) {
              this.log("mqtt发布主题失败", err);
              return;
            }
            this.mqttRefreshSubscribe(id);
          }
        );
      },
      /**
       * 订阅MQTT主题
       */
      mqttRefreshSubscribe(id) {
        const that = this;
        let topic = topicPrefix + id;
        this.log("mqtt订阅主题:" + topic);
        that.mqttClient.subscribe(topic, { qos: 1 }, (err, res) => {
          if (err) {
            console.log(err)
            this.log("mqtt订阅主题失败:", err);
            return;
          }
        });
      },
      /**
       * 释放MQTT客户端
       */
      mqttClose() {
        this.mqtt = {};
        if (this.mqttClient) {
          this.roomIdList.forEach(id => {
            let topic = this.getTopic(id);
            this.mqttClient.unsubscribe(topic);
          });
          this.mqttClient.end();
        }
      },
      getTopic(id) {
        return topicPrefix + id;
      }
    }
  });
</script>

</html>