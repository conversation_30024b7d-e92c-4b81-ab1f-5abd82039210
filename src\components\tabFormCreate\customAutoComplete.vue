<template>
  <el-autocomplete
    class="inline-input"
    v-model="active"
    :fetch-suggestions="querySearch"
    placeholder="请输入内容"
    :disabled="disabled"
    suffix-icon="el-icon-arrow-down"
  ></el-autocomplete>
</template>

<script>
export default {
  props: {
    //预定义
    value: String | Array,
    options: {
      type: Array,
      default: () => [],
    },
    disabled:Boolean
  },
  data() {
    return {};
  },
  computed: {
    active: {
      get() {
        return this.value;
      },
      set(newValue) {
        //当组件值发生变化后,通过 input 事件更新值
        this.$emit("input", newValue);
      },
    },
  },
  methods: {
    querySearch(queryString, cb) {
      // var options = this.options;
      // var results = queryString
      //   ? options.filter(this.createFilter(queryString))
      //   : options;
      // 调用 callback 返回建议列表的数据
      cb(this.options);
    },
    createFilter(queryString) {
      return (option) => {
        return (
          option.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0
        );
      };
    },
  },
};
</script>
<style lang="scss" scoped>
.el-autocomplete{
  width: 100%;
}
  </style>
