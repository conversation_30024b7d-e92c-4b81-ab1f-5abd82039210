<template>
  <el-form
    :rules="rules"
    :model="model"
    ref="form"
    label-width="100px"
    @submit.native.prevent
  >
    <el-form-item label="权限" prop="auth">
      <el-radio-group v-model="model.auth">
        <el-radio :label="1">{{'本'+$t('deptLabel')+'可见'}}</el-radio>
        <el-radio :label="2">{{'指定'+$t('deptLabel')+'可见'}}</el-radio>
        <el-radio :label="3">{{'所有'+$t('deptLabel')+'可见'}}</el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item
      v-if="model.auth === 2"
      :label="`选择${$t('deptLabel')}`"
      prop="companyIds"
    >
      <el-select
        v-model="model.companyIds"
        multiple
        placeholder="请选择"
        style="width: 85%;"
      >
        <el-option
          v-for="item in companyList"
          :key="item.id"
          :label="item.deptName"
          :value="item.id"
        >
        </el-option>
      </el-select>
    </el-form-item>
  </el-form>
</template>

<script>
import { getCompanyList } from "@/api/system";

export default {
  props: {
    auth: {
      type: Number,
      default: 1
    },
    companyIds: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
  data() {
    return {
      companyList: [],
      model: {
        auth: this.auth,
        companyIds: this.companyIds
      },
      rules: {
        auth: [
          {
            required: true,
            message: "请选择权限！",
            trigger: "blur"
          }
        ],
        companyIds: [
          {
            required: true,
            message: "请选择指定可见的公司！",
            trigger: "blur"
          }
        ]
      }
    };
  },
  watch: {
    'model.auth'() {
      this.$emit("update:auth", this.model.auth);
    },
    'model.companyIds'() {
      this.$emit("update:companyIds", this.model.companyIds);
    },
    auth(){
        this.model.auth = this.auth
    },
    companyIds(){
        this.model.companyIds = this.companyIds
    }
  },
  created() {
    this.getCompanyList();
  },
  methods: {
    getCompanyList() {
      getCompanyList().then(res => {
        this.companyList = res.data;
      });
    }
  }
};
</script>
<style lang="scss" scoped></style>
