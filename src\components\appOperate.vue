<template>
  <div id="appOperate">
    <el-dialog
      title="应用操作"
      :visible.sync="bDialogAppVisible"
      width="800px"
      :close-on-click-modal="false"
      @close="handleClose"
    >
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="我的" name="first"></el-tab-pane>
        <el-tab-pane label="其他" name="second"></el-tab-pane>
      </el-tabs>

      <div class="package-table" v-loading="loading">
        <el-table :data="aAppList" height="300" highlight-current-row>
          <el-table-column label="序号" width="50">
            <template slot-scope="scope">
              {{ scope.$index + (iCurrentPage - 1) * iPageSize + 1 }}
            </template>
          </el-table-column>
          <el-table-column
            sortable="custom"
            label="应用名称"
            width="100"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              {{ scope.row.prop.appName }}
            </template>
          </el-table-column>
          <el-table-column
            label="版本名称"
            width="100"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              {{ scope.row.prop.vname }}
            </template>
          </el-table-column>
          <el-table-column
            label="版本号"
            width="100"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              {{ scope.row.prop.vcode }}
            </template>
          </el-table-column>
          <el-table-column
            prop="companyName"
            :label="$t('deptLabel')"
            :show-overflow-tooltip="true"
            width="100"
          >
          </el-table-column>
          <el-table-column label="操作" min-width="275" fixed="right">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="success"
                @click.stop="operateApp(scope.row, 'upgrade')"
                v-action:applicationManage
                >安装</el-button
              >
              <el-button
                size="mini"
                type="primary"
                @click.stop="operateApp(scope.row, 'launch')"
                >启动</el-button
              >
              <el-button
                size="mini"
                type="warning"
                @click.stop="stopApp(scope.row)"
                >停止</el-button
              >
              <el-button
                size="mini"
                type="danger"
                @click.stop="operateApp(scope.row, 'unInstall')"
                >卸载</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
      <pagination
        :total="iTotal"
        :page.sync="iCurrentPage"
        :limit.sync="iPageSize"
        @pagination="getList"
        layout="prev,pager,next"
        :autoScroll="false"
      />
    </el-dialog>
  </div>
</template>

<script>
import {
  getApkList,
  getOtherApkList,
  uninstallApkList,
  upgradeCoreVersion,
  startApk,
  stopApk
} from "@/api/terminal";
export default {
  name: "appOperate",
  data() {
    return {
      bDialogAppVisible: false,
      aTerminalList: [],
      aAppList: [],
      iPageSize: 20,
      iCurrentPage: 1,
      iTotal: 0,
      loading: false,
      activeName: "first",
      type: ""
    };
  },
  methods: {
    handleClick() {
      this.iCurrentPage = 1;
      this.iPageSize = 20;
      this.getList();
    },
    show(terminalList, type = "") {
      this.bDialogAppVisible = true;
      this.type = type;
      this.aTerminalList = terminalList;
      this.getList();
    },
    handleClose() {
      this.bDialogAppVisible = false;
      this.activeName = "first";
      this.iCurrentPage = 1;
      this.iPageSize = 20;
      this.aTerminalList = [];
    },
    getList() {
      if (this.activeName === "first") {
        this.getAppList();
      } else {
        this.getOtherApkList();
      }
    },
    // 获取升级包
    getAppList() {
      let that = this;
      this.loading = true;
      getApkList({
        page: that.iCurrentPage,
        rows: that.iPageSize,
        search: "",
        type: that.type
      }).then(response => {
        that.aAppList = response.data.content;
        that.iTotal = response.data.total;
        this.loading = false;
      });
    },
    // 获取其他公司升级包
    getOtherApkList() {
      let that = this;
      this.loading = true;
      getOtherApkList({
        page: that.iCurrentPage,
        rows: that.iPageSize,
        search: "",
        type: that.type
      }).then(response => {
        that.aAppList = response.data.content;
        that.iTotal = response.data.total;
        this.loading = false;
      });
    },
    operateApp(row, type) {
      let that = this;
      let aMac = that.aTerminalList.map(item => item.mac).join(",");
      let aTerminalId = that.aTerminalList.map(item => item.terminalId);
      if ("unInstall" === type) {
        uninstallApkList({
          mac: aMac,
          appName: row.prop.appName,
          pkgName: row.prop.pkgName
        }).then(() => {
          this.$message.success("卸载成功！");
          that.handleClose();
        });
      } else if ("upgrade" === type) {
        upgradeCoreVersion({
          id: row.id,
          macs: aMac,
          terminalIds: aTerminalId.join(",")
        }).then(() => {
          this.$message.success("安装成功！");
          that.bDialogSelectTerminalVisible = false;
        });
      } else {
        startApk({
          mac: aMac,
          appName: row.prop.appName,
          pkgName: row.prop.pkgName
        }).then(() => {
          this.$message.success("启动成功！");
          that.handleClose();
        });
      }
    },
    /**
     * 停止应用
     */
    stopApp(row) {
      let macs = this.aTerminalList.map(item => item.mac).join(",");
      stopApk({
        mac: macs,
        appName: row.prop.appName,
        pkgName: row.prop.pkgName
      }).then(() => {
        this.$message.success("停止成功！");
        this.handleClose();
      });
    }
  }
};
</script>

<style scoped lang="scss">
::v-deep .el-tabs__header {
  margin: 0;
}
.package-table {
  height: 300px;
}
</style>
