/**
 * 首页相关的请求
 * @author: <PERSON><PERSON><PERSON>
 * @create: 2021-01-23
 */

import request from "@/utils/request";
import $qs from "qs";

/**
 * 查询各状态的终端数量
 * @return {AxiosPromise}
 */
export function getTerminalInfo() {
  return request({
    url: "/webapp/homepage/getTerminalInfo",
    method: "get"
  });
}

/**
 * 查询待审批会议
 * @return {AxiosPromise}
 */
export function getUnApprovedConference() {
  return request({
    url: "/webapp/conf/statistic/unApprovedConference",
    method: "get"
  });
}

/**
 * 查询待审批素材
 * 统计需要当前用户审核的素材数量
 * @return {AxiosPromise}
 */
export function getUnApprovedMaterial() {
  return request({
    url: "/webapp/material/statistics/unapproved",
    method: "get"
  });
}

/**
 * 查询待审批节目
 * 统计需要当前用户审核的节目数量
 * @return {AxiosPromise}
 */
export function getUnApprovedProgram() {
  return request({
    url: "/webapp/program/statistics/unapproved",
    method: "get"
  });
}

/**
 * 查询过期素材
 * @return {AxiosPromise}
 */
export function getExpiredMaterial() {
  return request({
    url: "/webapp/material/statistics/expired",
    method: "get"
  });
}

/**
 * 查询待参与会议
 * @return {AxiosPromise}
 */
export function getPendingConference() {
  return request({
    url: "/weapp/conference/statistic",
    method: "get"
  });
}

/**
 * 获取各类型素材的统计数据
 * @return {AxiosPromise}
 */
export function getMaterialClassifiedNum() {
  return request({
    url: "/webapp/material/statistics/type",
    method: "get"
  });
}

/**
 * 获取节目播放次数排行
 * @return {AxiosPromise}
 */
export function getPlaydataStatistic(params) {
  return request({
    url: "/webapp/program/getPlaydataStatistic",
    method: "get",
    params
  });
}

/**
 * 获取呼叫排行
 * @return {AxiosPromise}
 */
export function getCallStatistic(params) {
  return request({
    url: "/webapp/vc/getCallStatistic",
    method: "get",
    params
  });
}

/**
 * 查询用户首页卡片设置
 * @return {AxiosPromise}
 */
export function getDashboardLayout() {
  return request({
    url: "/oauth/card",
    method: "get"
  });
}

/**
 * 保存用户首页卡片设置
 * @return {AxiosPromise}
 */
export function saveDashboardLayout(data) {
  return request({
    url: "/oauth/card",
    method: "post",
    headers: { "Content-Type": "application/json" },
    data
  });
}

/**
 * 服务器状态监控信息
 * @returns 
 */
export function getServerMonitor(id) {
  return request({
    url: `/dashboard/server/monitor?serverId=${id}`,
    method: "get",
  });
}

/**
 * 会议室使用率
 * @param {*} params 
 * @returns 
 */
export function getVCRoomUsage(params) {
  return request({
    url: `/dashboard/vc/room/usage`,
    method: "get",
    params
  });
}

/**
 * 查询我的会议场次
 * @param {*} params 
 * @returns 
 */
export function getVCConfMine(params) {
  return request({
    url: `/dashboard/vc/conference/mine`,
    method: "get",
    params
  });
}


/**
 * 查询总的会议场次
 * @param {*} params 
 * @returns 
 */
 export function getVCConfAll(params) {
  return request({
    url: `/dashboard/vc/conference/all`,
    method: "get",
    params
  });
}

/**
 * 查询服务状态
 * @returns 
 */
export function getServiceStatus() {
  return request({
    url: `/dashboard/service`,
    method: "get",
  });
}



