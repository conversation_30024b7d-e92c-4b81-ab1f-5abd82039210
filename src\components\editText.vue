<template>
  <div :class="{ 'edit-text': true, [type]:true }" @click.stop="$emit('doEdit')">
    <div class="text" v-if="type === 'edit'">
      <slot name="text">{{ text }}</slot>
      <i class="el-icon-edit"></i>
    </div>
    <el-tooltip effect="dark" placement="top" v-else-if="type === 'error'">
      <div slot="content">
        {{ text }} <br />
        <div v-if="bShowWarning">{{ warningText }}</div>
      </div>
      <div class="text">
        <slot name="text">
          {{ text }}
        </slot>
        <i
          class="el-icon-warning"
          style="color: #f56c6c"
          v-if="bShowWarning"
        ></i>
      </div>
    </el-tooltip>
  </div>
</template>

<script>
export default {
  props: {
    text: {
      type: String,
      default: ""
    },
    warningText: {
      type: String,
      default: ""
    },
    type: {
      type: String,
      default: "edit"
    },
    bShowWarning: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {};
  }
};
</script>
<style lang="scss" scoped>
.edit-text {
  width: 100%;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  .text {
    display: inline-block;
    max-width: 95%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  &.error {
    justify-content: center;
    .text {
      color: #1f7bc1;
      position: relative;
      &:hover:after {
        content: "";
        position: absolute;
        left: 0;
        right: 0;
        height: 0;
        bottom: 0;
        border-bottom: 1px solid #1f7bc1;
      }
    }
  }
  .el-icon-edit {
    display: none;
    margin-left: 3px;
  }
  &:hover {
    color: #68b2ff;
    .el-icon-edit {
      display: inline-block;
    }
  }
}
</style>
