<template>
  <!--上传升级包-->
  <el-dialog title="上传" :visible.sync="bShowUploadDlg" :close-on-click-modal="false" :before-close="handleClose" width="420px">
    <div class="mt-10">
      <el-upload ref="upload" drag action="" name="file" :file-list="fileList" :on-change="upload" accept=".zip" multiple :auto-upload="false">
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">点击上传压缩包文件</div>
      </el-upload>
      <el-progress :percentage="iProcess" v-if="bUpload"></el-progress>
    </div>
    <!-- <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose"> 取消 </el-button>
      <el-button class="okBtn" @click="submitUpload" :loading="loading || iProcess > 0"> 上传 </el-button>
    </div> -->
  </el-dialog>
</template>

<script>
  import { uploadConfModule } from '@/api/advance';

  export default {
    data() {
      return {
        bShowUploadDlg: false,
        bUpload: false,
        iProcess: 0,
        fileList: [],
        loading: false,
        moduleId:''
      };
    },
    created() {},
    methods: {
      show(id) {
        this.bShowUploadDlg = true;
        this.moduleId = id
      },
      handleClose() {
        this.bShowUploadDlg = false;
        this.fileList = [];
        this.iProcess = 0;
        this.bUpload = false;
        this.moduleId = ''
      },
      checkFileType(file) {
        const type = file.raw.type;
        if (type.includes('zip')) {
          return true;
        } else {
          return false;
        }
      },
      upload(file) {
        if (!this.checkFileType(file)) {
          this.$message.error(`仅支持压缩包文件`);
          this.$refs.upload.clearFiles();
          return false;
        }
        this.loading = true;
        this.bUpload = true;
        this.iProcess = 0;
        let progress = (event) => {
          this.iProcess = parseInt((event.loaded / event.total) * 100);
        };
        uploadConfModule({files:file.raw,id:this.moduleId}, progress)
          .then(({ data }) => {
            this.$message.success(data);
            this.handleClose();
            this.$emit('refresh');
            this.$refs.upload.clearFiles();
          })
          .catch(function (error) {
            // do nothing
            this.bUpload = false;
            this.fileList = [];
          })
          .finally(() => (this.loading = false));
      },
      submitUpload() {
        this.$refs.upload.submit();
      },
    },
  };
</script>

<style scoped lang="scss">
  ::v-deep .el-dialog__body {
    padding: 15px 30px 25px;
  }
  ::v-deep .el-dialog__footer {
    padding: 5px 30px 25px;
  }
  ::v-deep .el-upload {
    background-color: #fff;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    box-sizing: border-box;
    width: 100%;
    height: 150px;
    text-align: center;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    &:hover {
      border-color: #409eff;
    }
    .el-icon-upload {
      font-size: 67px;
      color: #c0c4cc;
      margin: 30px 0 16px;
      line-height: 50px;
    }
    .el-upload-dragger {
      border: none;
      .el-upload__text {
        color: #606266;
        font-size: 14px;
        text-align: center;
      }
    }
  }
</style>
