<template>
  <div class="input-wrap" @click="handleClick">
    <el-input :placeholder="placeholder" v-model="value" :disabled="disabled" autocomplete="new-password">
    </el-input>
    <i class="el-icon-circle-close clear-icon" @click.stop="$emit('clear')" v-if="clearable"></i>
    <el-button icon="el-icon-search" :disabled="disabled"></el-button>
  </div>
</template>

<script>
export default {
  props: {
    placeholder: {
      default: "请选择",
      type: String
    },
    value: "",
    disabled: {
      type: <PERSON>olean,
      default: false
    },
    clearable: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      // value: initValue
    };
  },
  watch: {
    // initValue(newValue, oldValue) {
    //   this.value = newValue;
    // }
  },
  methods: {
    //   $refs.terminalForm.show(form.terminals)
    handleClick() {
      if (!this.disabled) {
        this.$emit('click')
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.input-wrap {
  display: flex;
  flex-direction: row;
  position: relative;

  ::v-deep .el-input__inner {
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;
    border-right: 0px;
    padding: 0 30px 0 15px;
  }

  .el-button {
    border-top-left-radius: 0px;
    border-bottom-left-radius: 0px;
  }

  .clear-icon {
    position: absolute;
    right: 65px;
    top: 50%;
    transform: translate(0, -50%);
    cursor: pointer;
    display: none;

    &:hover {
      color: rgb(92, 182, 255);
    }
  }

  &:hover {
    .clear-icon {
      display: inline-block;
    }
  }
}
</style>
