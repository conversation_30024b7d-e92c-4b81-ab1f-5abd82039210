<template>
  <div
    class="pt-directory pt-explorer"
    :class="[{ inline: size !== 'mini', hover: hover || selected }, size]"
    @click="doClick"
    @mouseenter="hover = true"
    @mouseleave="hover = false"
  >
    <div
      style="text-align: left"
      @click.stop=""
      :style="{
        visibility: (hover && selectable) || selected ? 'visible' : 'hidden'
      }"
      class="select"
    >
      <el-checkbox
        v-model="selected"
        @change="doSelect"
        :fill="'#09AAFF'"
        size="mini"
      />
    </div>

    <el-image :src="icon" class="icon" fit="contain"/>

    <!-- 正常显示文件名 -->
    <div class="name" v-if="status === 'existed'">
      <el-tooltip effect="light" :content="name" placement="bottom">
        <div class="name-inner">{{ name }}</div>
      </el-tooltip>
    </div>

    <!-- 显示输入框，供创建文件夹使用 -->
    <div class="name, creating" v-else>
      <input
        type="text"
        ref="input"
        v-model="createName"
        @keyup.enter.stop="create"
        @keyup.esc.stop="cancel"
        @click.stop="stop"
      />
      <el-button icon="el-icon-check" size="mini" @click.stop="create" />
      <el-button icon="el-icon-close" size="mini" @click.stop="cancel" />
    </div>
  </div>
</template>

<script>
export default {
  name: "PtDirectory",
  props: {
    icon: {
      type: String,
      default: require("@/assets/mkos/folder.svg"),
      required: true
    },
    name: {
      type: String,
      default: "",
      required: false
    },
    size: {
      type: String,
      default: "medium",
      required: false
    },
    particular: {
      type: Object,
      required: false
    },
    // 文件夹状态，可设置为existed，creating, renaming
    status: {
      type: String,
      default: "existed",
      required: false
    },
    selectable: {
      type: Boolean,
      default: true,
      required: false
    },
    isSelected: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  data() {
    return {
      createName:
        this.status === "creating" ? "新建文件夹" : this.name,
      hover: false,
      selected: this.isSelected
    };
  },
  methods: {
    /**
     * 点击文件夹
     * @returns {this}
     */
    doClick() {
      return this.$emit("click", this.particular);
    },
    /**
     * 创建/更新文件夹
     */
    create() {
      if (this.status === "creating") {
        return this.$emit("create", this.createName);
      } else {
        return this.$emit("update", this.createName);
      }
    },
    /**
     * 取消创建
     */
    cancel() {
      return this.$emit("cancel");
    },
    doSelect() {
      const that = this;
      that.$emit("select", that.selected);
    },
    stop() {
      return true;
    }
  },
  watch: {
    isSelected(next) {
      this.selected = next;
      this.doSelect();
    }
  },
  mounted() {
    const that = this;
    if (that.status === "creating") {
      that.$refs.input.select();
    }
  }
};
</script>

<style scoped lang="scss">
@import "@/styles/explorer.scss";
</style>
