/**
 * 自定义属性
 * 组件候选值由另一个组件选定值决定
 * 数据格式
 * [
    {
      "key": "scRegisterAddress",
      "strategies": [
        {
          "active": "97fe524c-658e-459e-b8fa-a893940b06f7",
          "passive": [
            {
              "label": "************",
              "value": "************"
            },
            {
              "label": "**************",
              "value": "**************"
            }
          ]
        },
        {
          "active": "8ab9e3c4-69ce-04e5-0169-ce058e5d1213",
          "passive": [
            {
              "label": "************",
              "value": "************"
            },
            {
              "label": "**************",
              "value": "**************"
            }
          ]
        }
      ]   
    }
  ]
 */
export default {
  name: "linkages",
  value({ value }, rule, fapi) {
    for (let link of value) {
      const { key, strategies } = link;

      const res = strategies.find((item) => item.active === rule.value);

      if (res && res.passive && res.passive.length) {
        fapi.setValue({ [key]: res.passive[0].value });
        if (fapi.getRule(key)) {
          fapi.getRule(key).options = res.passive;
          fapi.getRule(key).props.options = res.passive;
        }
      }
    }
  },
};
