<template>
  <div
    class="pt-file pt-explorer"
    :class="[{ inline: size !== 'mini', hover: hover || selected }, size]"
    @click="doClick"
    @mouseenter="hover = true"
    @mouseleave="hover = false"
  >
    <div
      style="text-align: left"
      @click.stop=""
      :style="{ visibility: hover || selected ? 'visible' : 'hidden' }"
      class="select"
    >
      <el-checkbox v-model="selected" @change="doSelect" />
    </div>

    <div
      style="text-align: right"
      @click.stop=""
      :style="{ visibility: hover ? 'visible' : 'hidden' }"
      class="enter-dir"
      v-if="showTraceToDir"
    >
      <el-tooltip
        class="item"
        effect="light"
        content="所在目录"
        placement="bottom-end"
      >
        <img
          @mouseenter="hoverTrace(true)"
          @mouseleave="hoverTrace(false)"
          :src="traceIcon"
          @click="traceToDir"
        />
      </el-tooltip>
    </div>

    <div class="img-wrap">
      <el-image
        :src="icon"
        lazy
        class="icon"
        fit="cover"
        :class="{
          'icon-img': isIconImg,
          expired: particular.isExpired
        }"
      >
        <div slot="error" class="image-slot">
          <i class="el-icon-picture-outline"></i>
        </div>
        <div slot="placeholder" class="image-slot">
          <i class="el-icon-loading"></i>
        </div>
      </el-image>

      <div class="expired-box" v-if="particular.isExpired">
        <p>过期</p>
      </div>
      <div :class="['audit-box', auditClass]" v-if="particular.approved !== 4">
        <p v-if="particular.approved === 1">未审核</p>
        <p v-else-if="particular.approved === 2">审核中</p>
        <p v-else-if="particular.approved === 3">审核不通过</p>
      </div>
    </div>

    <div class="name" v-if="status === 'existed'">
      <el-tooltip effect="light" :content="particular.name" placement="bottom">
        <div class="name-inner">{{ particular.name }}</div>
      </el-tooltip>
    </div>
    <!-- 显示输入框，供创建文件夹使用 -->
    <div class="name, creating" v-else>
      <input
        type="text"
        ref="input"
        v-model="createName"
        @keyup.enter.stop="update"
        @keyup.esc.stop="cancel"
        @click.stop="stop"
      />
      <el-button icon="el-icon-check" size="mini" @click.stop="update" />
      <el-button icon="el-icon-close" size="mini" @click.stop="cancel" />
    </div>
  </div>
</template>

<script>
export default {
  name: "PtFile",
  props: {
    showTraceToDir: {
      type: Boolean,
      required: false,
      default: false
    },
    size: {
      type: String,
      required: false,
      default: "medium"
    },
    // 文件状态，可设置为existed, renaming
    status: {
      type: String,
      default: "existed",
      required: false
    },
    particular: {
      type: Object,
      required: true,
      default: {
        id: 1,
        name: "1.png",
        desc: "xxx",
        type: "staticPicture",
        url:
          "https://ss3.bdstatic.com/70cFv8Sh_Q1YnxGkpoWK1HF6hhy/it/u=2534506313,1688529724&fm=26&gp=0.jpg",
        cover:
          "https://ss3.bdstatic.com/70cFv8Sh_Q1YnxGkpoWK1HF6hhy/it/u=2534506313,1688529724&fm=26&gp=0.jpg",
        creator: "admin",
        createTime: "2020-07-16 14:48:13",
        updateTime: "2020-07-28 16:35:04",
        duration: 0.0,
        size: 550117,
        gmtExpired: -1,
        isExpired: false,
        preview: null,
        prop: null,
        isInUse: false,
        approved: 4,
        dir: "/-101/37"
      }
    },
    isSelected: {
      type: Boolean,
      default: false,
      required: false
    }
  },
  data() {
    return {
      url: this.particular.url,
      hover: false,
      selected: this.isSelected,
      // 图标是图片
      isIconImg: false,
      createName: this.particular.name.slice(
        0,
        this.particular.name.lastIndexOf(".")
      ),
      // 进入目录图标
      traceIcon: require("@/assets/enter.png")
    };
  },
  watch: {
    isSelected(next) {
      this.selected = next;
      this.doSelect();
    }
  },
  computed: {
    icon: function() {
      const that = this;
      switch (that.particular.type) {
        case "staticPicture":
        case "dynamicPicture":
          that.isIconImg = true;
          return that.particular.url;
        case "video":
          that.isIconImg = true;
          if (that.hover && that.particular.preview) {
            return that.particular.preview;
          }
          return that.particular.cover;
        case "txt":
          return require("@/assets/mkos/text-x-preview.png");
        case "pdf":
          return require("@/assets/mkos/gnome-mime-application-pdf.svg");
        case "audio":
          return require("@/assets/mkos/audio-x-generic.svg");
        case "office":
          const split = that.particular.name.split(".");
          switch (split.pop().toLowerCase()) {
            case "doc":
            case "docx":
              return require("@/assets/mkos/application-vnd.openxmlformats-officedocument.wordprocessingml.document.svg");
            case "xls":
            case "xlsx":
              return require("@/assets/mkos/application-vnd.openxmlformats-officedocument.spreadsheetml.sheet.svg");
            case "ppt":
            case "pptx":
              return require("@/assets/mkos/application-vnd.openxmlformats-officedocument.presentationml.presentation.svg");
          }
          return "";
        case "website":
          return require("@/assets/mkos/text-html.svg");
      }
    },
    auditClass: function() {
      switch (this.particular.approved) {
        case 1:
          return "waiting";
        case 2:
          return "progress";
        case 3:
          return "no";
        default:
          return "";
      }
    }
  },
  methods: {
    /**
     * 点击文件
     * @returns {this}
     */
    doClick() {
      if (this.particular.approved === 4) {
        return this.$emit("click");
      } else {
        this.$router.push({
          name: "MyAudit",
          query: { id: this.particular.id }
        });
      }
    },
    /**
     * 选中文件事件
     */
    doSelect() {
      const that = this;
      that.$emit("select", that.selected);
    },
    /**
     * 处理鼠标进入事件
     */
    doMouseEnter() {},
    /**
     * 处理鼠标离开事件
     */
    doMouseLeave() {},
    /**
     * 更新文件夹
     */
    update() {
      let name =
        this.createName +
        this.particular.name.slice(this.particular.name.lastIndexOf("."));
      return this.$emit("update", name);
    },
    /**
     * 取消创建
     */
    cancel() {
      return this.$emit("cancel");
    },
    stop() {
      return true;
    },
    /**
     * 处理进入文件所在目录事件
     */
    traceToDir() {
      this.$emit("traceToDir");
    },
    hoverTrace(isHover) {
      this.traceIcon = isHover
        ? require("@/assets/enter-hover.png")
        : require("@/assets/enter.png");
    }
  }
};
</script>

<style scoped lang="scss">
@import "@/styles/explorer.scss";
.enter-dir {
  position: absolute;
  right: 5px;
  top: 10px;
  img {
    width: 20px;
  }
}
.icon-img {
  border-radius: 3px !important;
  box-shadow: 2px 2px 4px 2px rgba(0, 0, 0, 0.2);
}
.image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: #f5f7fa;
  color: #909399;
  font-size: 20px;
}
.img-wrap {
  position: relative;
  width: 60%;
  height: 60%;
  margin: 20px auto 0;
  border-radius: 3px;
  overflow: hidden;
  .icon {
    width: 100%;
    height: 100%;
    margin: 0;
  }
  .expired-box {
    width: 82%;
    height: 75%;
    background-color: rgb(163, 163, 163);
    transform: rotate(44deg);
    position: absolute;
    right: -35%;
    top: -35%;
    p {
      position: absolute;
      left: 50%;
      bottom: -8px;
      color: #fff;
      font-weight: bold;
      transform: translateX(-40%);
      letter-spacing: 2px;
    }
  }
  .audit-box {
    width: 100%;
    text-align: center;
    position: absolute;
    bottom: 0;
    right: 0;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    p {
      font-size: 12px;
      color: white;
      margin: 3px;
    }
    &.waiting {
      background-color: rgb(163, 163, 163);
    }
    &.progress {
      background-color: rgb(87, 156, 219);
    }
    &.no {
      background-color: rgb(221, 110, 82);
    }
  }
}
</style>
