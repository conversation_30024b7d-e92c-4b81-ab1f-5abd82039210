<template>
  <div class="wrap">
    <div class="title">资产统计</div>
    <div id="asset-statistic-charts" class="asset-statistic" v-if="data.length > 0"></div>
    <div class="null-data" v-else>暂无资产</div>
  </div>
</template>

<script>
  import { getAssetStatistic, getSpaceLabel } from '@/api/iotControl';
  export default {
    data() {
      return {
        data: [],
        spaceLabelList: [],
      };
    },
    created() {
      this.getSpaceLabel();
      this.timer = setInterval(this.getData, 120000);
    },
    methods: {
      getSpaceLabel() {
        getSpaceLabel().then((res) => {
          this.spaceLabelList = res.data;
          this.getData();
        });
      },
      getData() {
        getAssetStatistic().then((res) => {
          this.data = res.data;
          this.$nextTick(() => {
            this.draw();
          });
        });
      },
      draw() {
        let maxCount = Math.max(...this.data.map((item) => item.count));
        let option = {
          grid: {
            left: '3%',
            right: '4%',
            bottom: '0%',
            containLabel: true,
          },
          dataZoom: {
            type: 'inside',
            start: 0,
            end: 50,
          },
          xAxis: {
            type: 'category',
            data: this.data.sort((a, b) => b.count - a.count).map((item) => item.devSubTypeName),
            axisLabel: {
              color: 'rgba(255,255,255,0.8)',
              interval: 0,
              rotate: 45,
              formatter: function (value) {
                if (value.length > 8) {
                  return value.substring(0, 8) + '...';
                }
                return value;
              },
            },
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              color: 'rgba(255,255,255,0.8)',
            },
            minInterval: 1,
            splitLine: {
              show: false,
            },
            max: maxCount,
          },
          series: [
            {
              name: '设备',
              type: 'bar',
              data: this.data.map((item) => item.count),
              itemStyle: {
                color: new this.$echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: 'rgba(0,255,255,0.6)' },
                  { offset: 1, color: 'rgba(75,121,228,0.6)' },
                ]),
              },
              barMaxWidth: '20',
            },
          ],
          tooltip: {
            trigger: 'axis',
            confine: true,
            formatter: '{b}: {c}件',
          },
        };
        this.updateChart(option);
      },
      updateChart(option) {
        let dom = this.$el.querySelector('#asset-statistic-charts');
        let chart = this.$echarts.init(dom);
        chart.setOption(option, true);
      },
    },
    beforeDestroy() {
      this.timer && clearTimeout(this.timer);
      this.timer = null;
    },
  };
</script>

<style lang="scss" scoped>
  .wrap {
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    background-color: rgba(6, 30, 93, 0.4);

    .title {
      height: 45px;
      font-size: 18px;
      display: flex;
      align-items: center;
      padding-left: 60px;
      width: 100%;
      background-image: url('./img/card-title.png');
      background-size: contain;
      background-repeat: no-repeat;
      font-family: 'YouSheBiaoTiHei';
    }
    .asset-statistic {
      height: calc(100% - 50px);
    }
    .null-data {
      height: calc(100% - 50px);
      font-size: 18px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
</style>
