<template>
  <div class="sider-tree">
    <div class="sider-header">
      <div v-if="typeOptions.length === 1">{{ typeObj.label }}</div>
      <el-dropdown v-else @command="handleTypeChange">
        <span class="el-dropdown-link"> {{ typeObj.label }}<i class="el-icon-arrow-down el-icon--right"></i> </span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item v-for="(item, index) in typeOptions" :key="index" :command="item.value">{{ item.label }} </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
    <el-input
      v-model="nameToSearch"
      :placeholder="`请输入${$t('deptLabel')}名称`"
      size="mini"
      style="margin: 10px 10px 0; width: calc(100% - 20px)"
      v-if="type === 'dept'"
      clearable
    ></el-input>
    <el-scrollbar>
      <!-- :style="{height:type === 'dept'?'calc(100% - 20px)' : 'calc(100% - 60px)'}" -->
      <div class="sider-content" v-loading="loading" element-loading-spinner="el-icon-loading">
        <div class="tree-wrap">
          <el-tree
            :data="treeData"
            node-key="id"
            :default-expanded-keys="expandNodes"
            @node-click="handleNodeClick"
            :highlight-current="true"
            :check-on-click-node="true"
            ref="tree"
            :default-checked-keys="defaultCheckedKeys"
            :load="getChildDept"
            :lazy="type === 'dept'"
            :props="{
              isLeaf: 'leaf',
            }"
          >
            <span
              class="custom-tree-node"
              slot-scope="{ node, data }"
              :data-item="JSON.stringify(data)"
              :title="data.abbreviation || data.name || data.deptName"
            >
              {{ data.abbreviation || data.name || data.deptName }}
            </span>
          </el-tree>
        </div>
      </div>
    </el-scrollbar>
  </div>
</template>

<script>
  import { getServiceAreaList } from '@/api/conferenceGuide';
  import { getDepartementList, getCompanyList, getChildrenDept, getMyCompany, getMyDept, getDeptByName } from '@/api/system';
  import { reverseTree } from '@/utils/utils';
  import { getSpaceLabel } from '@/api/iotControl';

  export default {
    props: {
      options: {
        type: Array,
        default: () => ['server'],
      },
      init: {
        type: String,
        default: 'server',
      },
      bOwn: {
        type: Boolean,
        default: true,
      },
      bCompanyDept: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        loading: false,
        // 搜索机构名称
        nameToSearch: '',
        // 当前树的根机构id
        rootDeptId: null,
        treeData: [],
        type: this.init,
        defaultCheckedKeys: [],
        OPTIONS_ENUM: [
          {
            value: 'server',
            label: this.$t('serviceArea'),
            fn: getServiceAreaList,
          },
          {
            value: 'dept',
            label: this.$t('deptLabel'),
            fn: null,
          },
          {
            value: 'company',
            label: this.$t('deptLabel'),
            fn: this.bOwn ? getCompanyList : this.filterCompany,
          },
          {
            value: 'spaceLabel',
            fn: getSpaceLabel,
            label: '空间标签',
          },
        ],
      };
    },
    computed: {
      /**
       * 默认展开树的所有节点
       */
      expandNodes() {
        let tempNodes = [];
        reverseTree(this.treeData, (item) => {
          tempNodes.push(item.id);
        });
        return tempNodes;
      },
      treeList() {
        return treeToObj(this.treeData, 'id');
      },
      typeOptions() {
        let self = this;
        let temp = this.OPTIONS_ENUM.reduce((prev, item) => {
          if (self.options.includes(item.value)) {
            if (!self.bOwn && item.value === 'dept') {
              item.fn = getDepartementList;
            }
            if (!self.bOwn && item.value === 'company') {
              item.fn = self.filterCompany;
            }
            prev.push(item);
          }
          return prev;
        }, []);
        return temp;
      },
      typeObj() {
        return this.OPTIONS_ENUM.find((item) => item.value === this.type);
      },
      bRoot() {
        return this.$store.getters.bRoot;
      },
      companyId() {
        return this.$store.getters.companyId;
      },
    },
    created() {},
    methods: {
      filterCompany() {
        return new Promise((resolve) => {
          getCompanyList().then((res) => {
            //根机构下用户返回所有公司列表
            if (this.bRoot) {
              resolve(res);
            } else {
              //非根机构下用户仅返回当前所属公司列表
              let companyInfo = res.data.find((item) => item.id == this.companyId);
              resolve({ data: companyInfo ? [companyInfo] : [] });
            }
          });
        });
      },
      /**
       * 初始化机构树
       */
      initDept(cb) {
        this.loading = true;
        let fn = this.bCompanyDept ? getMyCompany : getMyDept;
        fn()
          .then(({ data }) => {
            // 保存当前树的根机构id
            this.rootDeptId = data.id;
            let res = { deptName: data.name, id: data.id };
            // bOwn为true时，根机构仅显示根机构，不显示子级
            if (this.bRoot && this.bOwn) {
              cb({ ...res, leaf: true });
            } else {
              cb(res);
            }
          })
          .finally(() => {
            this.loading = false;
          });
      },
      /**
       * 懒加载机构树
       */
      getChildDept(node, resolve) {
        if (this.type !== 'dept') {
          return;
        }
        if (node.level === 0) {
          this.initDept((data) => {
            resolve([data]);
            this.$nextTick(() => {
              this.setCurrentKey(data);
            });
          });
        }
        if (node.level > 0) {
          if (!node.data || !node.data.id) {
            resolve([]);
          }
          getChildrenDept(node.data.id).then(({ data }) => {
            resolve(data);
          });
        }
      },
      // 获取平台列表
      getData() {
        if (this.type === 'dept') {
          return;
        }
        this.loading = true;
        let fn = this.typeObj.fn;
        fn()
          .then(({ data }) => {
            this.treeData = data;

            // 设置当前默认选中的节点
            this.$nextTick(() => {
              if (data.length === 0) {
                return;
              }
              this.setCurrentKey(data[0]);
            });
          })
          .finally(() => {
            this.loading = false;
          });
      },
      /**
       * 设置tree默认选中的节点
       */
      setCurrentKey(node) {
        if (!Object.keys(node).length || !node.id) {
          return;
        }
        this.$refs.tree.setCurrentKey(node.id);
        this.$emit('handleServiceChange', {
          node,
          type: this.type,
        });
      },
      /**
       * 点击节点
       */
      handleNodeClick(node) {
        this.$emit('handleServiceChange', { node, type: this.type });
      },
      /**
       * 查找项改变
       */
      handleTypeChange(command) {
        this.type = command;
        this.initTreeAndGetData();
      },
      /**
       * 重置树状态并更新数据
       */
      initTreeAndGetData() {
        if (this.type === 'dept') {
          this.initDept((val) => {
            this.treeData = [val];
            this.$nextTick(() => {
              this.setCurrentKey(val);
              //将当前节点设置为懒加载
              let node = this.$refs.tree.getNode(val.id);
              if (node) {
                node.store.lazy = true;
                node.updateLeafState();
                node.collapse();
              }
            });
          });
        } else {
          //关闭el-tree的懒加载
          this.$refs.tree.store.lazy = false;
          this.getData();
        }
      },
    },
    watch: {
      options: {
        immediate: true,
        handler(val) {
          this.getData();
        },
      },
      nameToSearch(newVal) {
        if (newVal === '') {
          this.initTreeAndGetData();
          return;
        }
        if (!this.rootDeptId) {
          return;
        }
        getDeptByName(this.rootDeptId, newVal).then(({ data }) => {
          //关闭el-tree的懒加载
          this.$refs.tree.store.lazy = false;
          this.treeData = data;
        });
      },
      bCompanyDept() {
        //  更新树数据
        this.initTreeAndGetData();
      },
    },
  };
</script>
<style lang="scss" scoped>
  .sider-tree {
    width: 210px;
    height: 100%;
    display: flex;
    flex-direction: column;

    .sider-header {
      padding: 15px 0 15px 8px;
      color: rgba(0, 0, 0, 0.85);
      border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    }

    .el-scrollbar {
      // vh - %
      // height: calc(100% - 60px);
      flex: 1;

      ::v-deep .el-scrollbar__wrap {
        overflow-x: hidden;
      }
    }

    .sider-content {
      padding-left: 8px;
      padding: 8px;

      .btn-wrap {
        .el-button span {
          display: flex;
          align-items: center;

          i {
            font-size: 18px;
            margin-right: 4px;
          }
        }
      }

      .custom-tree-node {
        font-size: 14px;
      }
    }
  }
  ::v-deep .el-tree .el-icon-caret-right {
    color: #333;
  }
  ::v-deep .el-tree .is-leaf.el-icon-caret-right {
    visibility: hidden;
  }
</style>
