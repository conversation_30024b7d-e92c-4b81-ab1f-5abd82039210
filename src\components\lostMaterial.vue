<template>
  <el-dialog
    title="缺失素材"
    :visible.sync="dlgVisible"
    width="450px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div style="border: 1px solid #eeeeee;height: 300px;">
      <el-table :data="dataList" height="100%">
        <el-table-column label="序号" type="index" width="60">
        </el-table-column>
        <el-table-column
          prop="name"
          :show-overflow-tooltip="true"
          label="已删除的素材名称"
          width="auto"
        >
        </el-table-column>
      </el-table>
    </div>
    <pagination
      layout="prev, pager, next"
      :page.sync="pagination.page"
      :limit.sync="pagination.size"
      @pagination="getLostMaterials"
      :autoScroll="false"
    />
  </el-dialog>
</template>

<script>
export default {
  props: {
    getLostMaterialsInterface: {
      type: Function,
      require: true
    }
  },
  data() {
    return {
      dlgVisible: false,
      dataList: [],
      pagination: {
        total: 0,
        page: 1,
        size: 20
      },
      id: null
    };
  },
  methods: {
    show(id) {
      this.id = id;
      this.getLostMaterials();
      this.dlgVisible = true;
    },
    handleClose() {
      this.dlgVisible = false;
      this.pagination.page = 1;
      this.pagination.size = 20;
    },
    getLostMaterials() {
      this.getLostMaterialsInterface(
        this.id,
        this.pagination.page,
        this.pagination.size
      ).then(res => {
        this.dataList = res.data.rows;
        this.pagination.total = res.data.total;
      });
    }
  }
};
</script>
<style lang="scss" scoped></style>
