import request from '@/utils/request';
import $qs from 'qs';

// 获得节目列表
export function getProgramList(data) {
  return request({
    url: '/webapp/program/getAll',
    method: 'post',
    data: $qs.stringify(data),
  });
}

// 获得节目详情
export function getProgram(data) {
  return request({
    url: '/webapp/program/getContentById',
    method: 'post',
    data: $qs.stringify(data),
  });
}

// 批量删除节目
export function deleteProgram(data) {
  return request({
    url: '/webapp/program/delete',
    method: 'post',
    data: $qs.stringify(data),
  });
}

// 查询节目是否正在被使用
export function getProgramIsInUse(programIds) {
  return request({
    url: `/webapp/program/isLostAndInuse?programIds=${programIds}`,
    method: 'get',
  });
}

//查询节目单编号
export function getNumberedPlaylist() {
  return request({
    url: '/webapp/playlist/numberedPlayList',
    method: 'get',
  });
}

//保存节目单编号
export function saveNumberedPlaylist(data) {
  let file = new FormData();
  Object.keys(data).forEach((key) => {
    file.append(key, data[key]);
  });
  return request({
    url: '/webapp/playlist/numberedPlayList',
    method: 'post',
    data: file,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

//删除节目单编号
export function delNumberedPlaylist(data) {
  return request({
    url: '/webapp/playlist/numberedPlayList',
    method: 'delete',
    params: data,
  });
}

//批量设置终端的默认播放频道
export function changeDefaultPlaylist(data) {
  return request({
    url: '/webapp/terminal/defaultPlaylist',
    method: 'post',
    data: $qs.stringify(data),
  });
}

/**
 * 发布频道角标
 * @param {*} data-macs 终端mac地址 
 * @returns 
 */
export function publishChannelLogo(data) {
  return request({
    url: '/webapp/playlist/pushChannelLogo',
    method: 'post',
    data: $qs.stringify(data),
  });
}

/**
 * 停止频道角标的播放
 * @param {*} macs 终端mac字符串数组，如1,2 
 * @returns 
 */
export function stopPlayChannelLogo(macs) {
  return request({
    url: `/webapp/playlist/delChannelLogoInTerminal?macs=${macs}`,
    method: 'delete',
  });
}

//查找节目中缺失素材信息
export function getMaterialLost(programId, page, size) {
  return request({
    url: '/webapp/program/findLost/' + programId + '/' + page + '/' + size,
    method: 'get',
  });
}

//获得节目使用详情
export function getProgramOccupy(data) {
  return request({
    url: '/webapp/program/occupy',
    method: 'post',
    data: $qs.stringify(data),
  });
}

// 获取节目单列表
export function getProgrammeList(data) {
  return request({
    url: '/webapp/playlist/getAll',
    method: 'post',
    data: $qs.stringify(data),
  });
}

// 删除节目单列表
export function deleteProgrammeList(data) {
  return request({
    url: '/webapp/playlist/delete',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//查询节目单中的节目封面（最多5个）
export function getProgramCovers(data) {
  return request({
    url: '/webapp/playlist/getProgramCovers',
    method: 'get',
    params: data,
  });
}

//上传节目单封面
export function uploadPlaylistCover(data) {
  let file = new FormData();
  Object.keys(data).forEach((key) => {
    file.append(key, data[key]);
  });
  return request({
    url: '/webapp/playlist/uploadPlaylistCover',
    method: 'post',
    data: file,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    // onUploadProgress,
  });
}

//停止立即播放节目的播放
export function stopImmePlay(data) {
  return request({
    url: '/webapp/playlist/stopImmePlay',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//查看导出历史
export function getAllExportLog() {
  return request({
    url: '/media/playlist/getAllExportTasks',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
    },
  });
}

// 添加导出节目单任务
export function addExportTask(data) {
  return request({
    url: '/media/playlist/addExportTask',
    method: 'post',
    data: $qs.stringify(data),
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
    },
  });
}

/**
 * 查询正在播放指定节目单的终端列表
 * @param data-playlistId 节目单id
 * @param data-page 
 * @param data-rows 
 * @param data-deptId 
 * @param data-includeChild 
 */
export function getPlayProgrammeTerminalList(data) {
  return request({
    url: '/webapp/playlist/terminalInPlaying',
    method: 'get',
    params:data,
  });
}

/**
 * 查询节目单的发布历史
 * @param data-playlistId 节目单id
 * @param data-page 
 * @param data-rows 
 */
export function getProgrammePlayList(data) {
  return request({
    url: '/webapp/playlist/getTerminalPlaylist.json',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//发布节目单
export function publishPlaylist(data) {
  return request({
    url: '/webapp/terminal/publishPlaylist',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//取消发布节目单
export function canclePublishPlaylist(data) {
  return request({
    url: '/webapp/playlist/cancelDownload',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//查找节目单中缺失节目信息
export function getLostProgram(data) {
  return request({
    url: '/webapp/playlist/findLost',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//查找节目单中缺失素材信息
export function getLostMaterial(data) {
  return request({
    url: '/webapp/playlist/findLostMaterial',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//保存节目单
export function saveProgramme(data) {
  return request({
    url: '/webapp/playlist/save',
    method: 'post',
    data,
    headers:{
      'Content-Type': 'application/json;',
    }
  });
}

//更新节目单
export function updateProgramme(data) {
  return request({
    url: '/webapp/playlist/update',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;',
    }
  });
}

//获得节目单的详情（不包括节目）
export function getProgrammeDetail(playlistId) {
  return request({
    url: `/webapp/playlist/detail?playlistId=${playlistId}`,
    method: 'get',
  });
}

//获得节目单的节目
export function getProgrammeContent(data) {
  return request({
    url: '/webapp/playlist/getContentById',
    method: 'post',
    data: $qs.stringify(data)
  });
}

//打包节目单
export function packProgramme(id, packageType) {
  return request({
    url: '/webapp/playlist/package/' + id + '/' + packageType,
    method: 'get',
  });
}

//查看打包节目单进度
export function getPackProgrammeProgress() {
  return request({
    url: '/webapp/playlist/progress/package',
    method: 'get',
  });
}

//删除打包节目单进度
export function deletePackProgrammeProgress(uuid) {
  return request({
    url: '/webapp/playlist/progress/package/' + uuid,
    method: 'delete',
  });
}

//能否下载节目单（若素材缺失则禁止节目单下载）
export function canDownloadProgramme(id) {
  return request({
    url: '/webapp/template/canDownload/' + id,
    method: 'get',
  });
}

//查看默认素材
export function getDefaultMaterial(data) {
  return request({
    url: '/webapp/defaultMaterial',
    method: 'get',
    params: data,
  });
}

//取消下发默认素材
export function cancleDefaultMaterial(data) {
  return request({
    url: '/webapp/defaultMaterial/cancel',
    method: 'delete',
    data: $qs.stringify(data),
  });
}

//下发默认素材
export function postDefaultMaterial(data) {
  return request({
    url: '/webapp/defaultMaterial',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//查看下发默认素材
export function getDefaultMaterialProgress(data) {
  return request({
    url: '/webapp/defaultMaterial/downloadProgress',
    method: 'get',
    params: data,
  });
}

//查询切割视频进度
export function getCutProgress() {
  return request({
    url: '/webapp/program/getCutProgress',
    method: 'get',
  });
}

//删除切割进度
export function deleteCutProgress(uuid) {
  return request({
    url: '/webapp/program/deleteCutProgress/' + uuid,
    method: 'delete',
  });
}

//查询素材上传进度
export function getUploadProgress() {
  return request({
    url: '/webapp/material/getUploadProgress',
    method: 'get',
  });
}

//删除素材上传进度
export function deleteUploadProgress(uuid) {
  return request({
    url: '/webapp/material/deleteUploadProgress/' + uuid,
    method: 'delete',
  });
}

//上传素材
export function uploadMaterial(url, data) {
  let file = new FormData();
  file.append('file', data);
  return request({
    url,
    method: 'post',
    data: file,
    headers: {
      'Content-Type': 'multipart/form-data; charset=UTF-8',
    },
  });
}

//获取素材详情
export function getMaterialById(data, bHideAlert = false) {
  return request({
    url: '/webapp/material/getMaterialById',
    method: 'post',
    data: $qs.stringify(data),
    headers: {
      noAlert: bHideAlert,
    },
  });
}

export function getTxt(url) {
  return request({
    url,
    method: 'get',
  });
}

//获取我的文件的folderId
export function getMineRootFolderId() {
  return request({
    url: '/webapp/material/getMineRootFolderId',
    method: 'get',
  });
}

//获取文件夹路径
export function getDirByFolder(data) {
  return request({
    url: '/webapp/material/getDirByFolder',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

//新建文件夹
export function addFolder(data) {
  return request({
    url: '/webapp/material/addFolder',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//删除文件夹
export function deleteFolder(data) {
  return request({
    url: '/webapp/material/deleteFolder',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//删除素材
export function deleteMaterial(data) {
  return request({
    url: '/webapp/material/delete',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//彻底删除素材
export function emptyMaterial(data) {
  return request({
    url: '/webapp/material/deleteAll',
    method: 'delete',
    data: $qs.stringify(data),
  });
}

//获取父文件夹下一级素材
export function getMaterialsByFolder(data, params) {
  return request({
    url: '/webapp/material/getMaterialsByFolder',
    method: 'post',
    data,
    params,
    headers: {
      'Content-Type': 'application/json; charset=UTF-8',
    },
  });
}

//根据父文件夹获取下一级文件夹
export function getFoldersByParent(data, params = null) {
  return request({
    url: '/webapp/material/getFoldersByParent',
    method: 'post',
    data,
    params,
    headers: {
      'Content-Type': 'application/json; charset=UTF-8',
    },
  });
}

//根据素材类型查询素材
export function getMaterialsByType(data) {
  return request({
    url: '/webapp/material/getMaterialsByType',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//移动文件夹
export function moveFolders(data) {
  return request({
    url: '/webapp/material/moveFolders',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//移动素材
export function moveMaterials(data) {
  return request({
    url: '/webapp/material/moveMaterials',
    method: 'post',
    data: $qs.stringify(data),
  });
}

/**
 * 复制文件夹
 * @param materialIds 文件夹id集合
 * @param parentId 另存为到的文件夹id
 */
export function copyFolders(data) {
  return request({
    url: 'webapp/material/copyFolders',
    method: 'post',
    data: $qs.stringify(data),
  });
}

/**
 * 复制素材
 * @param materialIds 素材id集合
 * @param parentId 另存为到的文件夹id
 */
export function copyMaterials(data) {
  return request({
    url: 'webapp/material/copyMaterials',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//获取mqtt服务的ip、用户名、密码
export function getMqttServer() {
  return request({
    url: '/sysManagement/mqtt',
    method: 'get',
  });
}

//节目提交审核
export function commitProgram(data) {
  return request({
    url: '/webapp/program/commit',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//节目撤销审核
export function rollbackProgram(data) {
  return request({
    url: '/webapp/program/rollback',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//添加节目
export function addProgram(data) {
  return request({
    url: '/webapp/program/add',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//更新节目
export function updateProgram(data) {
  return request({
    url: '/webapp/program/update',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//判断该素材是否可以上传
export function canUploadMaterial(data) {
  return request({
    url: '/webapp/material/canUpload',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

//判断该素材是否已经上传过
export function needUploadMaterial(data) {
  return request({
    url: '/webapp/material/needUpload',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

//获得该素材的使用情况
export function getMaterialOccupy(data) {
  return request({
    url: '/webapp/material/occupy',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//已经上传的素材新增一条记录
export function addMaterialRecord(data) {
  return request({
    url: '/webapp/material/addMaterialRecord',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//更新素材
export function updateMaterial(data) {
  return request({
    url: '/webapp/material/updateMaterial',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//搜索素材
export function getSearchMaterial(data) {
  return request({
    url: '/webapp/material/nameToSearch',
    method: 'get',
    params: data,
  });
}

//更新文件夹
export function updateFolder(data) {
  return request({
    url: '/webapp/material/updateFolder',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//获得当前素材所在的目录结构
export function getDirByMaterialId(data) {
  return request({
    url: '/webapp/material/getDirByMaterialId',
    method: 'get',
    params: data,
  });
}

//获取实时信息列表
export function getRealTimeInfoList(data) {
  return request({
    url: '/webapp/realInfo/getAll',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//获取实时信息内容
export function getRealTimeInfoContent(data) {
  return request({
    url: '/webapp/realInfo/getContentById',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//保存/更新实时信息
export function saveRealTimeInfo(data) {
  return request({
    url: '/webapp/realInfo/save',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//删除实时信息
export function deleteRealTimeInfo(data) {
  return request({
    url: '/webapp/realInfo/delete',
    method: 'post',
    data: $qs.stringify(data),
  });
}

/**
 * 获取实时信息播放的终端
 * @param data-page
 * @param data-rows
 * @param data-direction
 * @param data-properties
 * @param data-realtimeId   实时信息id
 * @param data-deptId       机构id
 * @param data-includeChild 是否包含下级
 */
export function getTerminalShowInfo(data) {
  return request({
    url: '/webapp/realInfo/getTerminalShowInfo',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//停止实时信息的播放
export function stopPlayRealTimeInfo(data) {
  return request({
    url: '/webapp/realInfo/stop',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//发布实时信息
export function publishRealTimeInfo(data) {
  return request({
    url: '/webapp/realInfo/publish',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//获得信发管理的所有终端
export function getTerminalList(data) {
  // 默认包含下级
  if (data.includeChild === undefined || data.includeChild === null) {
    data.includeChild = true;
  }

  return request({
    url: '/webapp/playlist/getDssTerminalLst',
    method: 'post',
    data: $qs.stringify(data),
  });
}

/**
 * 查询华为终端
 * @param {*} data
 * @returns
 */
export function getHWTerminalList(data) {
  return request({
    url: '/webapp/hwterminal',
    method: 'get',
    params: data,
  });
}

/**
 * 查询指定终端详情
 * @param {*} mac
 * @returns
 */
export function getHWTerminalDetail(mac) {
  return request({
    url: `/webapp/hwterminal/detail?mac=${mac}`,
    method: 'get',
  });
}

/**
 * 查询运行状态历史数据，用于画曲线
 * @param {*} data
 * @returns
 */
export function getHWTerminalSysInfo(data) {
  return request({
    url: '/webapp/hwterminal/sysinfo',
    method: 'get',
    params: data,
  });
}

/**
 * 清空运行记录
 * @param {*} data
 * @returns
 */
export function delHWTerminalSysInfo(data) {
  return request({
    url: '/webapp/hwterminal/sysinfo',
    method: 'delete',
    params: data,
  });
}

//获得终端的节目列表
export function getTerminalProgramList(data) {
  return request({
    url: '/webapp/terminal/getProgramList',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//批量删除终端的节目
export function deleteTerminalProgram(data) {
  return request({
    url: '/webapp/terminal/deleteProgram',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//获得播放列表
export function getPlayList(data) {
  return request({
    url: '/webapp/terminal/getPlaylistLst',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//获得终端的播放列表历史
export function getTerminalHistoryPlayList(data) {
  return request({
    // 接口地址更改为 webapp/playlist/getOpHistoryList => webapp/terminal/getOpHistoryList
    url: '/webapp/terminal/getOpHistoryList',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//获得待审核列表
export function getApproveChainList(data) {
  return request({
    url: '/webapp/approveChain/getTurnResource',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//获得审核详情
export function getApproveChain(data) {
  return request({
    url: '/webapp/approveChain/getContentById',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//审核
export function updateApproveResult(data) {
  return request({
    url: '/webapp/approveChain/updateApproveResult',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//节目另存为模板
export function addTemplate(data) {
  return request({
    url: '/webapp/template/add',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//编辑模板
export function updateTemplate(data) {
  return request({
    url: '/webapp/template/update',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//获得我所属公司节目模板列表
export function getTemplateList(data) {
  return request({
    url: '/webapp/template/getAll',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//获得其他公司节目模板列表
export function getOtherTemplateList(data) {
  return request({
    url: '/webapp/template/getAll/other',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//获得节目模板详情
export function getTemplate(data) {
  return request({
    url: '/webapp/template/getContentById',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//批量删除节目模板
export function deleteTemplate(data) {
  return request({
    url: '/webapp/template/delete',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//打包模板
export function packTemplate(templateId) {
  return request({
    url: '/webapp/template/package/' + templateId,
    method: 'get',
  });
}

//查看打包模板进度
export function getPackTempProgress() {
  return request({
    url: '/webapp/template/progress/package',
    method: 'get',
  });
}

//删除打包模板进度
export function deletePackTempProgress(uuid) {
  return request({
    url: '/webapp/template/progress/package/' + uuid,
    method: 'delete',
  });
}

//能否下载模板（若素材缺失则禁止模板下载）
export function canDownloadTemp(templateId) {
  return request({
    url: '/webapp/template/canDownload/' + templateId,
    method: 'get',
  });
}

// 创建模板导入任务
export function startUploadTemplate(uuid) {
  return request({
    url: '/webapp/template/import/' + uuid,
    method: 'post',
  });
}

//上传模板文件
export function uploadTemplate(data) {
  let file = new FormData();
  Object.keys(data).forEach((key) => {
    data[key] && file.append(key, data[key]);
  });

  return request({
    url: '/webapp/template/import',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    data: file,
  });
}

//导入模板文件进度
export function uploadTempProgress() {
  return request({
    url: '/webapp/template/progress/import',
    method: 'get',
  });
}

//删除导入模板文件进度
export function deleteUploadTempProgress(uuid) {
  return request({
    url: '/webapp/template/progress/import/' + uuid,
    method: 'delete',
  });
}

//查看模板中缺失素材
export function getLostMaterialInTemp(templateId, page, size) {
  return request({
    url: '/webapp/template/lost/' + templateId + '/' + page + '/' + size,
    method: 'get',
  });
}

//获取Top10模板标签列表
export function getTopTenTempTag() {
  return request({
    url: '/webapp/template/classify',
    method: 'get',
  });
}

//获取模板标签列表
export function getTempTag(nameToSearch) {
  return request({
    url: '/webapp/template/classify/' + nameToSearch,
    method: 'get',
  });
}

//获取我上传的素材
export function getMyAuditMaterial(data) {
  return request({
    url: '/webapp/material/mine',
    method: 'get',
    params: data,
  });
}

//获取我所处的机构
export function getMyDepartment() {
  return request({
    url: '/oauth/department/user',
    method: 'get',
  });
}

//获取我的过期素材
export function getMyExpired(data) {
  return request({
    url: 'webapp/material/expired',
    method: 'get',
    params: data,
  });
}

//上传节目封面
export function uploadProgramCover(data, onUploadProgress) {
  let file = new FormData();
  Object.keys(data).forEach((key) => {
    file.append(key, data[key]);
  });
  return request({
    url: '/webapp/program/uploadProgramCover',
    method: 'post',
    data: file,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    onUploadProgress,
  });
}

//节目截图
export function screenShot(data) {
  return request({
    url: '/puppeteer/snapshot',
    method: 'post',
    data,
    retry: 2,
    retryDelay: 2000,
    timeout: 25000,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}

//保存节目截图
export function saveCover(data) {
  return request({
    url: '/webapp/program/cover',
    method: 'post',
    data: $qs.stringify(data),
  });
}

//终端设置红外传感器功能
export function setInfrared(data) {
  return request({
    url: 'webapp/terminal/infrared',
    method: 'get',
    params: data,
  });
}

//查询播放记录
export function getPlayHistory(mac, page, rows, properties = 'endtime', direction = 'DESC') {
  return request({
    url: `webapp/terminal/getPlayHistory?mac=${mac}&page=${page}&rows=${rows}&properties=${properties}&direction=${direction}`,
    method: 'get',
  });
}

//清空播放记录
export function clearPlayHistory(mac) {
  return request({
    url: `/webapp/terminal/clearPlayHistory?mac=${mac}`,
    method: 'get',
  });
}

/**
 * 发布素材
 * materalId为空时， 删除终端上的背景素材
 * @param {*} data =
 * {
 *    materialId,
 *    terminals: [
 *      {mac, id}
 *    ]
 * }
 * @returns
 */
export function postBgMaterial(data) {
  return request({
    url: '/webapp/bgMaterial',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

/**
 * 查看素材
 * @param {*} mac
 * @returns
 */
export function getBgMaterial(data) {
  return request({
    url: `/webapp/bgMaterial`,
    method: 'get',
    params: data,
  });
}

/**
 * 取消下载
 * @param {macs} data
 * @returns
 */
export function cancelBgMaterial(data) {
  return request({
    url: `/webapp/bgMaterial/cancel`,
    method: 'delete',
    data: $qs.stringify(data),
  });
}

/**
 * 查看素材下载进度
 * @param {terminalIds}  终端id列表
 * @returns
 */
export function getBgMaterialProgress(data) {
  return request({
    url: `/webapp/bgMaterial/downloadProgress`,
    method: 'get',
    params: data,
  });
}

/**
 * 查询所有股票列表
 * @param {terminalIds}  终端id列表
 * @returns
 */
export function getAllStocks() {
  return request({
    url: `/webapp/datum/ALL_STOCKS_PROFILE/profiles`,
    method: 'get',
  });
}

/**
 * 保存终端管理员
 * @param data-macs  终端列表
 * @param data-usernames  管理员列表
 * @returns
 */
export function setDssTerminalManager(data) {
  return request({
    url: `/webapp/terminal/manager`,
    method: 'post',
    params: data,
  });
}

/**
 * 添加终端管理员
 * @param data-macs  终端列表
 * @param data-usernames  管理员列表
 * @returns
 */
export function addDssTerminalManager(data) {
  return request({
    url: `/webapp/terminal/manager`,
    method: 'put',
    params: data,
  });
}

/**
 * 查询终端管理员
 * @param mac  终端mac地址
 * @returns
 */
export function getDssTerminalManager(mac) {
  return request({
    url: `/webapp/terminal/manager?mac=${mac}`,
    method: 'get',
  });
}

/**
 * 删除终端管理员
 * @param data
 * [{manager,mac}]
 * @returns
 */
export function deleteDssTerminalManager(data) {
  return request({
    url: `/webapp/terminal/manager`,
    method: 'delete',
    data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

/**
 * 设置终端下载速度
 * @param data-macs  终端列表
 * @param data-speed  下载速度，单位kbps
 * @returns
 */
export function setDssTerminalSpeed(data) {
  return request({
    url: `/webapp/terminal/speed/control`,
    method: 'post',
    params: data,
  });
}

/**
 * 查询终端下载速度
 * @param mac  终端mac地址
 * @returns
 */
export function getDssTerminalSpeed(mac) {
  return request({
    url: `/webapp/terminal/speed/control?mac=${mac}`,
    method: 'get',
  });
}

/**
 * 清空终端磁盘
 * @param macs  终端mac地址列表 1，2
 * @returns
 */
export function cleanDssTerminalDisk(macs) {
  return request({
    url: `/webapp/terminal/cleanDisk?macs=${macs}`,
    method: 'get',
  });
}

/**
 * 获得终端上的正在播放的节目单
 * @param {*} mac 终端mac
 * @returns
 */
export function getTerminalPlayList(mac) {
  return request({
    url: `webapp/terminal/playlistInTerminal?mac=${mac}`,
    method: 'get',
  });
}

/**
 * 获得终端上的正在播放的实时信息
 * @param {*} mac 终端mac
 * @returns
 */
export function getTerminalRealInfo(mac) {
  return request({
    url: `/webapp/realInfo/inTerminal?mac=${mac}`,
    method: 'get',
  });
}

/**
 * 设置终端下发点击事件开关与行为命令接口
 * @param data-macs               
 * @param data-config.enableDoubleTap    启用双击功能
 * @param data-config.doubleTapFunction  1选择节目 2退出信发应用 
 * @param data-config.enableSingleTap    启用单击功能
 * @param data-config.singleTapPlayerMenuBackKeyFunction  播放器控制键 0不显示返回按键 1显示返回按键，点击返回到封面
 * @param data-config.playerMenuKeysCtl  播放器菜单键 0 全打开 1 隐藏控制按键 2 隐藏进度条
 * @param data-config.delMaterialIfReplacePlaylist  替换节目单后删除之前节目单素材
 * @param data-config.fadeInOutIfSwitchPrograms     切换节目显示淡入淡出效果
 * @param data-config.fadeInTimems                  淡入时长
 * @param data-config.fadeOutTimems                 淡出时长
 * @returns
 */
export function setDssTerminalClickControl(data) {
  return request({
    url: `/webapp/terminal/dss20_control`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;',
    }
  });
}

/**
 * 查询终端下发点击事件配置
 * @param mac               
 * @returns
 */
export function getDssTerminalClickControl(mac) {
  return request({
    url: `/webapp/terminal/getDss20Config?mac=${mac}`,
    method: 'get',
  });
}

/**
 * 获得节目单的节目列表
 * @param {*} playlistId 节目单id
 * @returns
 */
export function getPlayListPrograms(playlistId) {
  return request({
    url: `/webapp/playlist/program?playlistId=${playlistId}`,
    method: 'get',
  });
}

/**
 * 获得节目的素材列表
 * @param {*} programId 节目id
 * @returns
 */
export function getProgramMaterials(programId) {
  return request({
    url: `/webapp/program/material?programId=${programId}`,
    method: 'get',
  });
}

/**
 * 查询使用指定节目的节目单详情
 * @param {*} data-programId 节目id
 * @param {*} data-page 
 * @param {*} data-rows 
 * @param {*} data-direction 
 * @param {*} data-properties 
 * @returns
 */
export function getProgramPlaylist(data) {
  return request({
    url: `/webapp/program/getPlaylist`,
    method: 'get',
    params:data
  });
}

/**
 * 设置休眠时长
 */
export function setSleepTime(data) {
  let file = new FormData();
  Object.keys(data).forEach((key) => {
    data[key] && file.append(key, data[key]);
  });
  return request({
    url: `/webapp/hwterminal/setSleepTime`,
    method: 'post',
    data: file,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

/**
 * 应用使用统计信息
 */
export function getAppUsage(data) {
  return request({
    url: `/webapp/hwterminal/getAppUsage`,
    method: 'get',
    params: data,
  });
}

//查询信发终端正在播放的节目单
export function getTerminalPlaying(mac) {
  return request({
    url: `/webapp/terminal/playing?mac=${mac}`,
    method: 'get',
  });
}

/**
 * 统计用户上传的素材
 * @param {*} type 统计区间类型 0：天，1： 周，2：月， 3：年
 * @param {*} value 统计区间内时间戳
 * @param {*} username  指定用户, 为空时统计所有用户
 * @returns 
 */
export function getMaterialStatistics(type,value,username='') {
  return request({
    url: `/webapp/material/statistics/user&time?type=${type}&value=${value}&username=${username}`,
    method: 'get',
  });
}