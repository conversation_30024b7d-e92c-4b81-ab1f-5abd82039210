<template>
  <!--停止播放-->
  <el-dialog title="停止播放" :visible.sync="visible" width="900px" :close-on-click-modal="false" @close="close">
    <tree-layout @handleTreeChange="handleTreeChange" :treeOptions="['dept']" :treebOwn="false">
      <div class="margin-bottom10 mt-10">
        <el-button size="small" type="danger" icon="el-icon-close" @click="stop('batch')">停止播放</el-button>
        <el-button size="small" type="primary" icon="el-icon-refresh-right" @click="getImTerminal">刷新 </el-button>
        <el-checkbox v-model="oStopPlay.excludeChild" class="ml-10">仅当前层级</el-checkbox>
      </div>
      <div class="margin-top10" style="width: 600px; margin-top: 10px; border: 1px solid #eee">
        <el-table :data="oStopPlay.aTerminalList" height="300" ref="multipleTable1" @selection-change="handleSelectionChange" @row-click="clickRow">
          <el-table-column type="selection" width="45"></el-table-column>
          <el-table-column prop="terminalName" label="终端名称" :show-overflow-tooltip="true" width="150"> </el-table-column>
          <el-table-column prop="mac" label="终端ID" width="140" :show-overflow-tooltip="true"> </el-table-column>
          <!-- <el-table-column prop="publisher" label="发布者" width="110" :show-overflow-tooltip="true"> </el-table-column> -->
          <el-table-column label="操作" min-width="90">
            <template slot-scope="scope">
              <el-button size="mini" icon="el-icon-close" class="delete-btn" @click.stop="stop(scope.row)">停止 </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <pagination
        :total="oStopPlay.iTotal"
        :page.sync="oStopPlay.iCurrentPage"
        :limit.sync="oStopPlay.iPageSize"
        @pagination="getImTerminal"
        layout="total,prev,pager,next"
        :autoScroll="false"
      />
    </tree-layout>
  </el-dialog>
</template>

<script>
  import dlg from '@/mixins/dlg';
  import treeLayout from '@/components/treeLayout';
  import { getPlayProgrammeTerminalList } from '@/api/info';
  import { deletePlayList } from '@/api/terminal';
  import { excludeChildMixin } from '@/mixins/prefer.js';

  export default {
    mixins: [dlg, excludeChildMixin],
    components: { treeLayout },
    props: {
      // 节目单id
      playlistId: {
        type: String | Number,
        default: null,
      },
      // 节目单播放类型
      playlistType: {
        type: String | Number,
        default: null,
      },
    },
    data() {
      return {
        oStopPlay: {
          aMultipleSelection: [],
          aTerminalList: [],
          iTotal: 0,
          iCurrentPage: 1,
          iPageSize: 20,
          // 机构id筛选
          searchDeptId: null,
          // 仅当前层级筛选
          excludeChild: false,
        },
      };
    },
    mounted() {
      // 偏好设置
      this.oStopPlay.excludeChild = this.excludeChildPrefer;
    },
    watch: {
      'oStopPlay.excludeChild'(newVal) {
        // 保存偏好
        this.excludeChildPrefer = newVal;

        if (!this.oStopPlay.searchDeptId) {
          return;
        }
        this.oStopPlay.iCurrentPage = 1;
        this.oStopPlay.iPageSize = 20;
        this.getImTerminal();
      },
    },
    methods: {
      handleSelectionChange(val) {
        this.oStopPlay.aMultipleSelection = val;
      },
      clickRow(row) {
        this.$refs.multipleTable1.toggleRowSelection(row);
      },
      clearSelection() {
        this.$nextTick(() => {
          if (this.$refs.multipleTable1) {
            this.$refs.multipleTable1.clearSelection();
          }
        });
      },
      // 树控件选项改变
      handleTreeChange({ node, type }) {
        this.oStopPlay.searchDeptId = node.id;
        this.getImTerminal();
      },
      // 获取节目单下发的终端列表
      getImTerminal() {
        let that = this;
        getPlayProgrammeTerminalList({
          page: that.oStopPlay.iCurrentPage,
          rows: that.oStopPlay.iPageSize,
          playlistId: that.playlistId,
          deptId: this.oStopPlay.searchDeptId,
          includeChild: !this.oStopPlay.excludeChild,
        }).then((response) => {
          that.clearSelection();
          that.oStopPlay.aTerminalList = response.data.rows;
          that.oStopPlay.iTotal = response.data.total;
        });
      },
      stop(row) {
        let that = this;
        let aTerminalIds = [];
        let aMacs = [];
        if ('batch' === row) {
          aTerminalIds = that.oStopPlay.aMultipleSelection.map((item) => item.terminalId);
          aMacs = that.oStopPlay.aMultipleSelection.map((item) => item.mac);
        } else {
          aTerminalIds = [row.terminalId];
          aMacs = [row.mac];
        }
        if (!aTerminalIds || !aTerminalIds.length) {
          this.$message.warning('请选择要操作的终端！');
          return;
        }
        deletePlayList({
          type: this.playlistType,
          includeChild: true,
          realInfo: false,
          material: true,
          macs: aMacs.join(','),
          terminalIds: aTerminalIds.join(','),
        }).then(() => {
          this.$message.success('操作成功');
          this.getImTerminal();
        });
      },
    },
  };
</script>

<style></style>
