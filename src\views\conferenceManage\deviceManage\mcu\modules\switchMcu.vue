<template>
  <el-dialog :title="title" :visible.sync="visible" width="400px" :close-on-click-modal="false" @close="close">
    <el-tree :data="mcuTree" node-key="id" default-expand-all :expand-on-click-node="false">
      <span
        class="custom-tree-node"
        slot-scope="{ node, data }"
        @click="() => switchMcu(data)"
        :class="{ mcu: data.type !== 'coverage', error: !data.available }"
      >
        <span>
          <svg-icon icon-class="parentLevel" class="coverage-icon" v-if="data.type === 'coverage'"></svg-icon>
          <svg-icon icon-class="circle" class="mcu-icon" v-else></svg-icon>
          {{ data.name }}</span
        >
        <span v-if="data.type !== 'coverage'">
          <el-button type="text" size="mini"> 选择 </el-button>
        </span>
      </span>
    </el-tree>
  </el-dialog>
</template>

<script>
  import dlg from '@/mixins/dlg';
  import { getServiceAreaList, switchMcu, getMcuAvailable } from '@/api/conferenceGuide';
  import { transformTree, reverseTree } from '@/utils/utils';

  export default {
    mixins: [dlg],
    components: {},
    props: {
      // 源平台id
      srcId: {
        type: Number | String,
        require: true,
      },
      // 源平台名称
      srcName: {
        type: String,
        require: true,
      },
    },
    data() {
      return {
        title: '故障转移',
        mcuTree: [],
      };
    },
    computed: {
      mcuList() {
        let list = [];
        reverseTree(this.mcuTree, (item) => {
          if (item.type !== 'coverage') list.push(item);
        });
        return list;
      },
    },
    created() {
      this.getServiceAreaList();
    },
    methods: {
      getServiceAreaList() {
        getServiceAreaList().then(({ data }) => {
          if (!data || !data.length) {
            return;
          }

          let tranformer = (node) => {
            node.mcus.forEach((mcu) => (mcu.available = true));
            return {
              name: node.name,
              id: node.id,
              type: 'coverage',
              children: [...node.mcus],
            };
          };

          this.mcuTree = [transformTree(data[0], tranformer)];
          this.getMcuListAvailable();
        });
      },
      getMcuListAvailable() {
        for (let mcu of this.mcuList) {
          getMcuAvailable(mcu.id).then(({ data }) => {
            mcu.available = data.isAvailable;
          });
        }
      },
      switchMcu(selectItem) {
        if (selectItem.type === 'coverage') {
          return;
        }
        if (!selectItem.available) {
          this.$message.error(`【${selectItem.name}】平台故障不可用！`);
          return;
        }
        this.$confirm(`确定将【${this.srcName}】平台的所有会议切换到【${selectItem.name}】平台?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          beforeClose: (action, instance, done) => {
            if (action === 'confirm') {
              instance.confirmButtonLoading = true;
              instance.confirmButtonText = '切换中...';
              switchMcu(this.srcId, selectItem.id)
                .finally(() => {
                  done();
                  setTimeout(() => {
                    instance.confirmButtonLoading = false;
                  }, 300);
                })
                .then(() => {
                  setTimeout(() => {
                    this.$message.success('操作成功！');
                    this.close()
                  }, 600);
                });
            } else {
              done();
            }
          },
        });
      },
    },
  };
</script>
<style lang="scss" scoped>
  .el-tree {
    padding-bottom: 20px;
  }
  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;

    .coverage-icon {
      font-size: 16px;
      color: #606266;
    }

    &.mcu {
      // color: #67c23a;
      .mcu-icon {
        color: #67c23a;
      }
      .el-button--text {
        // color: #67c23a;
      }
      &.error {
        // color: #f56c6c;
        .mcu-icon {
          color: #f56c6c;
        }
        .el-button--text {
          // color: #f56c6c;
        }
      }
    }
  }
</style>
