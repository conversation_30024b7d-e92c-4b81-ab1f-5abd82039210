<template>
  <div class="full-wh">
    <div class="content-top">
      <div class="btn-area">
        <el-button type="primary" :size="size" icon="el-icon-plus" @click.native="viewEditInspect('add')" v-action:inspectTask|addInspectTask>
          添加
        </el-button>
        <el-button :size="size" icon="el-icon-delete" @click.native="deleteInspect()" type="danger" v-action:inspectTask|deleteInspectTask>
          删除
        </el-button>
        <el-button :size="size" class="mr-10" icon="el-icon-document" @click.native="inspectTemplate()" type="primary">巡检模板</el-button>
        <el-select
          :size="size"
          :filterable="true"
          style="width: 150px"
          v-model="filter.enabled"
          placeholder="是否启用"
          clearable
          @change="
            {
              page.current = 1;
              getInspectList();
            }
          "
        >
          <el-option :label="item.label" :value="item.value" v-for="item in ENABLED_ENUM" :key="item.value"></el-option>
        </el-select>
        <el-input
          class="ml-10"
          placeholder="巡检计划名称"
          suffix-icon="el-icon-search"
          :size="size"
          style="width: 150px"
          v-model="filter.name"
          v-debounce="[
            (e) => {
              page.current = 1;
              getInspectList(e);
            },
          ]"
        />
        <el-input
          class="ml-10"
          placeholder="巡检计划描述"
          suffix-icon="el-icon-search"
          :size="size"
          style="width: 150px"
          v-model="filter.remark"
          v-debounce="[
            (e) => {
              page.current = 1;
              getInspectList(e);
            },
          ]"
        />
      </div>
    </div>
    <div class="content-body">
      <el-table
        :data="inspectList"
        row-key="id"
        highlight-current-row
        @sort-change="doSortChange"
        @row-click="handleClickRow"
        @selection-change="handleSelectionChange"
        ref="table"
        :default-sort="{ prop: 'id', order: 'descending' }"
        height="100px"
        v-loading="loading"
        v-adaptive
        class="border-table"
      >
        <el-table-column type="selection" width="45" reserve-selection fixed="left"></el-table-column>
        <el-table-column label="序号" width="55" align="center" fixed="left">
          <template slot-scope="scope">
            {{ scope.$index + (page.current - 1) * pageSize + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="name" label="巡检计划名称" min-width="200" show-overflow-tooltip fixed="left"> </el-table-column>
        <el-table-column prop="remark" label="巡检计划描述" min-width="200" show-overflow-tooltip> </el-table-column>
        <el-table-column label="执行类型" min-width="100" align="center">
          <template slot-scope="{ row }">
            {{ row.inspectType === 'auto' ? '自动' : '手动' }}
          </template>
        </el-table-column>
        <el-table-column label="是否启用" min-width="100" align="center">
          <template slot-scope="{ row }">
            <el-tag :type="row.enableStatus ? 'success' : 'danger'">
              {{ row.enableStatus ? '已启用' : '未启用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="inspector" label="巡检人" min-width="100" show-overflow-tooltip> </el-table-column>
        <el-table-column prop="creator" label="编辑人" min-width="100" show-overflow-tooltip> </el-table-column>
        <el-table-column prop="gmtModified" label="编辑时间" align="center" min-width="160" show-overflow-tooltip> </el-table-column>
        <el-table-column prop="latestTime" label="最近一次执行" align="center" min-width="160" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <el-tooltip content="查看最近一次执行记录详情" placement="top" v-if="row.latestTaskId">
              <el-link type="primary" @click.stop="openInspectRecordDetail(row.latestTaskId)"> {{ formatDate(row.latestTime) }} </el-link>
            </el-tooltip>
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column label="操作项" align="center" :show-overflow-tooltip="true" width="350" fixed="right">
          <template slot-scope="{ row }">
            <el-button
              class="add-btn"
              icon="el-icon-video-play"
              :size="size"
              @click.stop="executeInspect(row)"
              v-action:inspectTask|executeInspectTask
            >
              执行
            </el-button>
            <el-button class="edit-btn" icon="el-icon-edit" :size="size" @click.stop="viewEditInspect('view', row)">详情</el-button>
            <el-button class="warning-btn" icon="el-icon-tickets" :size="size" @click.stop="viewInspectRecord(row.id)">记录</el-button>
            <el-dropdown>
              <el-button class="purple-btn ml-10" :size="size" v-action:inspectTask|editInspectTask>
                更多
                <i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item @click.native.stop="setEnable(row)" v-if="!row.enableStatus" v-action:inspectTask|editInspectTask>
                  启用
                </el-dropdown-item>
                <el-dropdown-item @click.native.stop="setEnable(row)" v-else v-action:inspectTask|editInspectTask> 禁用 </el-dropdown-item>
                <el-dropdown-item @click.native.stop="deleteInspect(row)" v-action:inspectTask|deleteInspectTask> 删除 </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <pagination :total="page.total" :page.sync="page.current" :limit.sync="pageSize" @pagination="getInspectList" />
    </div>
  </div>
</template>

<script>
  import { tablePageSize as pageSize } from '@/mixins/prefer.js';
  import { getInspectList, setInspectEnable, executeInspect, deleteInspect } from '@/api/iotControl';
  import { ENABLED_ENUM } from '@/views/iotControl/enum';
  import inspectRecord from './modules/inspectRecord.vue';
  import inspectTemplate from './modules/inspectTemplate.vue';
  import transform from '@/utils/transform';
  export default {
    components: { inspectRecord },
    mixins: [pageSize],
    data() {
      return {
        ENABLED_ENUM,
        size: 'small',
        multipleSelection: [],
        filter: {
          name: '',
          enabled: true,
          remark: '',
        },
        page: {
          total: 0,
          current: 1,
        },
        inspectList: [],
        sort: {
          direction: 'DESC',
          properties: 'id',
        },
        loading: false,
      };
    },
    created() {
      this.getInspectList();
    },
    methods: {
      /**
       * 获取巡检计划列表
       */
      getInspectList() {
        this.loading = true;
        let params = {
          page: this.page.current,
          size: this.pageSize,
          properties: this.sort.properties,
          direction: this.sort.direction,
          name: this.filter.name,
          enabled: this.filter.enabled,
          remark: this.filter.remark,
          unpaged: false,
        };
        getInspectList(params)
          .then(({ data }) => {
            this.inspectList = data.rows;
            this.page.total = data.total;
          })
          .finally(() => {
            this.loading = false;
          });
      },
      handleClickRow(row) {
        this.$refs.table.toggleRowSelection(row);
      },
      handleSelectionChange(val) {
        this.multipleSelection = val;
      },
      doSortChange(col) {
        this.sort.direction = 'ascending' === col.order ? 'ASC' : 'DESC';
        this.sort.properties = col.prop;
        this.getInspectList();
      },
      viewEditInspect(type, row) {
        this.$router.push({ name: 'viewEditInspect', params: { id: row?.id, type: type } });
      },
      setEnable(row) {
        setInspectEnable(row.id, !row.enableStatus).then(() => {
          this.getInspectList();
        });
      },
      executeInspect(row) {
        executeInspect(row.id).then(({ data }) => {
          this.$message.success('执行成功');
          this.$router.push({ name: 'viewInspectRecordDetail', params: { id: data.id } });
        });
      },
      deleteInspect(row) {
        if (row && row.id) {
          deleteInspect([row.id]).then(() => {
            this.$message.success('删除成功');
            this.getInspectList();
          });
        } else {
          if (this.multipleSelection.length === 0) {
            this.$message.warning('请选择要删除的巡检计划');
            return;
          }
          deleteInspect(this.multipleSelection.map((item) => item.id)).then(() => {
            this.$message.success('删除成功');
            this.multipleSelection = [];
            this.$refs.table.clearSelection();
            this.getInspectList();
          });
        }
      },
      inspectRecordDlg: transform(inspectRecord),
      viewInspectRecord(id) {
        this.inspectRecordDlg({
          propsData: {
            inspectId: id,
          },
          methods: {
            handleViewDetail: (id) => {
              this.$router.push({
                name: 'viewInspectRecordDetail',
                params: { id: id },
              });
            },
          },
        });
      },
      inspectTemplateDlg: transform(inspectTemplate),
      inspectTemplate() {
        this.inspectTemplateDlg({
          propsData: {},
          methods: {
            handleViewTemplate: (type, id) => {
              this.$router.push({
                name: 'viewEditInspectTemplate',
                params: { id: id, type: type },
              });
            },
          },
        });
      },
      formatDate(inputDate) {
        const date = new Date(inputDate);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        return `${year}-${month}-${day} ${hours}:${minutes}`;
      },
      openInspectRecordDetail(id) {
        this.$router.push({
          name: 'viewInspectRecordDetail',
          params: { id: id },
        });
      },
    },
  };
</script>

<style lang="scss" scoped></style>
