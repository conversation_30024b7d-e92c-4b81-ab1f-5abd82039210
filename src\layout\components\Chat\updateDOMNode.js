// import { insertCursor, removeCursor } from '.'

/** 核心函数, 对比节点的内容 实现动态更新 markdown 的 div 而不是用 innerHTML 的属性全部刷新 */
export function updateDOMNode(oldNode, newNode) {
  // 递归比较更新新、旧节点的子节点
  function _diffAndUpdate(before, after) {
    // 情况 1：更新文本内容
    if (
      before
      && before.nodeType === Node.TEXT_NODE
      && after.nodeType === Node.TEXT_NODE
    ) {
      if (before.nodeValue !== after.nodeValue) {
        before.nodeValue = after.nodeValue
      }
      return
    }

    // 情况 2：新旧节点标签名不同，替换整个节点
    if (!before || before.tagName !== after.tagName) {
      // 克隆新节点
      const newNode = after.cloneNode(true)
      if (before) {
        // 替换旧节点
        before?.parentNode?.replaceChild(newNode, before)
      }
      else {
        // 若不存在旧节点，直接新增
        after?.parentNode?.appendChild(newNode)
      }
      return
    }

    // 情况 3：递归对比和更新子节点
    const beforeChildren = Array.from(before.childNodes)
    const afterChildren = Array.from(after.childNodes)

    // 遍历新节点的子节点，逐个与旧节点的对应子节点比较
    afterChildren.forEach((afterChild, index) => {
      const beforeChild = beforeChildren[index]
      if (!beforeChild) {
        // 若旧节点的子节点不存在，直接克隆新节点的子节点并添加到 before
        const newChild = afterChild.cloneNode(true)
        before.appendChild(newChild)
      }
      else {
        // 若旧节点的子节点存在，递归比较和更新
        _diffAndUpdate(beforeChild , afterChild)
      }
    })

    // 删除旧节点中多余的子节点
    if (beforeChildren.length > afterChildren.length) {
      for (let i = afterChildren.length; i < beforeChildren.length; i++) {
        before.removeChild(beforeChildren[i])
      }
    }
  }

  // 从根开始
  // removeCursor(oldNode)
  // insertCursor(oldNode)
  // insertCursor(newNode)
  _diffAndUpdate(oldNode, newNode)
}
