// 树形拖拽组件
<template>
  <el-tree :data="node" node-key="id" default-expand-all :props="defaultProps">
    <div class="custom-tree-node" slot-scope="{ node, data }">
      <div class="list-item" v-if="data.type === 'platform'">
        <el-checkbox v-model="data.checked" v-if="bShowCheckbox" @change="handleCheckChild(data)">
          <svg-icon icon-class="confDag@mcu"></svg-icon>{{ data.name }}
        </el-checkbox>
        <template v-else> <svg-icon icon-class="confDag@mcu"></svg-icon>{{ data.name }} </template>
      </div>

      <draggable
        :list="[data]"
        :group="{
          name: 'article',
          pull: true,
          put: 'false',
        }"
        :sort="false"
        class="dragArea"
        filter=".undraggable"
        @end="dragEnd"
        v-else
      >
        <div :class="{ undraggable: data.bUnDrag ? true : false }">
          <el-checkbox v-model="data.checked" v-if="bShowCheckbox">
            <svg-icon icon-class="confDag@terminal@disabled" class="terminal" v-if="data.bUnDrag"></svg-icon>
            <svg-icon icon-class="confDag@terminal" class="terminal" v-else></svg-icon>
            {{ data.name }}
          </el-checkbox>

          <template v-else>
            <svg-icon icon-class="confDag@terminal@disabled" class="terminal" v-if="data.bUnDrag"></svg-icon>
            <svg-icon icon-class="confDag@terminal" class="terminal" v-else></svg-icon>
            {{ data.name }}
          </template>
        </div>
      </draggable>
    </div>
  </el-tree>
</template>

<script>
  import draggable from 'vuedraggable';
  import { reverseTree } from '@/utils/utils';

  export default {
    name: 'TreeDraggable',
    components: { draggable },
    props: {
      node: {
        type: Array,
        required: true,
      },
      // 是否显示选中框
      bShowCheckbox: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        defaultProps: {
          children: 'children',
          label: 'name',
        },
      };
    },
    methods: {
      /**
       * 拖拽结束
       */
      dragEnd() {
        this.$emit('dragEnd');
      },
      /**
       * 选中子级
       */
      handleCheckChild(data) {
        reverseTree([data], (node) => {
          node.checked = data.checked;
        });
      },
    },
  };
</script>

<style scoped lang="scss">
  .handle {
    cursor: move;
  }

  .undraggable {
    cursor: not-allowed;
    color: #ccc;
    width: 100%;
  }

  .custom-tree-node {
    flex: 1;
  }

  .svg-icon {
    font-size: 19px;
    margin-right: 4px;
    &.terminal {
      margin-right: 0px;
    }
  }

  .list-item {
    color: rgb(110, 110, 155);
  }
</style>
