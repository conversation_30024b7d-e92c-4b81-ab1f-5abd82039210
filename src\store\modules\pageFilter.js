//页面条件筛选
const state = {
  productManagement: {
    filter: { name: '', code: '', published: '', deviceType: [], brand: '', model: '', description: '' },
    page: {
      current: 1,
    },
    sort: {
      direction: 'DESC',
      properties: 'id',
    },
  },
};

const mutations = {
  SET_PRODUCT_MANAGEMENT_FILTER: (state, filter) => {
    state.productManagement.filter = filter;
  },
  SET_PRODUCT_MANAGEMENT_PAGE: (state, page) => {
    state.productManagement.page.current = page;
  },
  SET_PRODUCT_MANAGEMENT_SORT: (state, sort) => {
    state.productManagement.sort = sort;
  },
};

export default {
  namespaced: true,
  state,
  mutations,
};
