'use strict';
const path = require('path');
const defaultSettings = require('./src/settings.js');
const webpack = require('webpack');
const TerserPlugin = require('terser-webpack-plugin');
// "webpack-bundle-analyzer": "^4.9.1"
const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin;
const FiledMD5RenamePlugin = require('./FiledMD5RenamePlugin');
const CompressionPlugin = require('compression-webpack-plugin');

function resolve(dir) {
  return path.join(__dirname, dir);
}

const name = defaultSettings.title; // page title

// If your port is set to 80,
// use administrator privileges to execute the command line.
// For example, Mac: sudo npm run
// You can change the port by the following method:
// port = 9527 npm run dev OR npm run dev --port = 9527
const port = process.env.port || process.env.npm_config_port || 9527; // dev port

let outputDir = 'dist';
if (process.env.VUE_APP_ENV === 'staging') {
  outputDir = 'dist@standalone';
} else if (process.env.VUE_APP_ENV === 'linux') {
  outputDir = 'dist@linux';
}

// All configuration item explanations can be find in https://cli.vuejs.org/config/
module.exports = {
  /**
   * You will need to set publicPath if you plan to deploy your site under a sub path,
   * for example GitHub Pages. If you plan to deploy your site to https://foo.github.io/bar/,
   * then publicPath should be set to "/bar/".
   * In most cases please use '/' !!!
   * Detail: https://cli.vuejs.org/config/#publicpath
   */
  publicPath: './',
  outputDir: outputDir,
  assetsDir: 'static',
  lintOnSave: process.env.NODE_ENV === 'development',
  productionSourceMap: process.env.NODE_ENV === 'development',
  devServer: {
    host: '0.0.0.0',
    port: port,
    open: true,
    overlay: {
      warnings: false,
      errors: true,
    },
    https: false,
    // compress: true, // 启用 gzip 压缩
    //跨域
    proxy: {
      '/(base|storage)': {
        target: 'http://localhost:8848',
        changeOrigin: true,
        // pathRewrite: {
        //   "^/base": ""
        // }
      },
      '/mqtt': {
        target: 'http://localhost:8848',
        changeOrigin: true,
        ws: true,
      },
    },
    // before: require('./mock/mock-server.js')
  },
  //调整内部的 webpack 配置
  configureWebpack: (config) => {
    // provide the app's title in webpack's name field, so that
    // it can be accessed in index.html to inject the correct title.
    config.name = name;
    config.devtool = 'cheap-module-source-map';
    config.resolve.alias['@'] = resolve('src');
    config.externals = {
      echarts: 'echarts',
    };
    config.optimization.minimizer = [
      new TerserPlugin({
        terserOptions: {
          ecma: undefined,
          warnings: false,
          parse: {},
          compress: {
            // 传true就是干掉所有的console.*这些函数的调用
            // drop_console: true,
            // 干掉debugger
            // drop_debugger: false,
            // 如果你要干掉特定的函数比如console.info ，又想删掉后保留其参数中的副作用，那用pure_funcs来处理
            pure_funcs: ['console.log'],
          },
        },
      }),
    ];
    // 解决element ui图标乱码的问题
    config.module.rules
      .filter((rule) => {
        return rule.test.toString().indexOf('scss') !== -1;
      })
      .forEach((rule) => {
        rule.oneOf.forEach((oneOfRule) => {
          oneOfRule.use.splice(oneOfRule.use.indexOf(require.resolve('sass-loader')), 0, {
            loader: require.resolve('css-unicode-loader'),
          });
        });
      });
  },
  chainWebpack(config) {
    // config.plugin('BundleAnalyzerPlugin').use(new BundleAnalyzerPlugin(), []);
    // npm run build:prod 打包编译时会自动将打包后的dist目录下/module/文件进行更名，并将换行符统一为 \n (linux LF)
    // config.plugin('FiledMD5RenamePlugin').use(new FiledMD5RenamePlugin(), []);
    // gzip 压缩
    // config.plugin('CompressionPlugin').use(
    //   new CompressionPlugin({
    //     algorithm: 'gzip', // 使用gzip压缩
    //     test: /\.(js|css|html|svg)$/, // 需要压缩的文件类型
    //     filename: '[path][base].gz', // 压缩后的文件名(保持原文件名，后缀加.gz)
    //     minRatio: 0.8, // 压缩率小于1才会压缩
    //     threshold: 10240, // 对超过10k的数据压缩
    //     deleteOriginalAssets: false, // 是否删除未压缩的源文件，谨慎设置，如果希望提供非gzip的资源，可不设置或者设置为false（比如删除打包后的gz后还可以加载到原始资源文件）
    //   })
    // );

    // config.plugin('IgnorePlugin').use(
    //   new webpack.IgnorePlugin({
    //     resourceRegExp: /^\.\/locale$/,
    //     contextRegExp: /moment$/,
    //   }),
    //   []
    // );

    // it can improve the speed of the first screen, it is recommended to turn on preload
    // it can improve the speed of the first screen, it is recommended to turn on preload
    config.plugin('preload').tap(() => [
      {
        rel: 'preload',
        // to ignore runtime.js
        // https://github.com/vuejs/vue-cli/blob/dev/packages/@vue/cli-service/lib/config/app.js#L171
        fileBlacklist: [/\.map$/, /hot-update\.js$/, /runtime\..*\.js$/],
        include: 'initial',
      },
    ]);

    // when there are many pages, it will cause too many meaningless requests
    config.plugins.delete('prefetch');

    // set svg-sprite-loader
    config.module.rule('svg').exclude.add(resolve('src/icons')).end();
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]',
      })
      .end();
    config.module.rule('images').exclude.add(resolve('src/views/ndt/img')).end();
    config.module
      .rule('urlImg')
      .test(/\.(png|jpg)$/)
      .include.add(resolve('src/views/ndt/img'))
      .end()
      .use('url-loader')
      .loader('url-loader')
      .options({ name: 'static/[name].[hash:8].[ext]', limit: 1 })
      .end();
    config.module
      .rule('excel')
      .test(/\.(xls|xlsx)$/)
      .use('file-loader')
      .loader('file-loader')
      .options({
        name: '[name].[ext]',
      })
      .end();

    config.when(process.env.NODE_ENV !== 'development', (config) => {
      config
        .plugin('ScriptExtHtmlWebpackPlugin')
        .after('html')
        .use('script-ext-html-webpack-plugin', [
          {
            // `runtime` must same as runtimeChunk name. default is `runtime`
            inline: /runtime\..*\.js$/,
          },
        ])
        .end();
      config.optimization.splitChunks({
        chunks: 'all',
        maxSize: 800000, // chunks about 500kb
        cacheGroups: {
          libs: {
            name: 'chunk-libs',
            test: /[\\/]node_modules[\\/]/,
            priority: 10,
            chunks: 'initial', // only package third parties that are initially dependent
          },
          elementUI: {
            name: 'chunk-elementUI', // split elementUI into a single package
            priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
            test: /[\\/]node_modules[\\/]_?element-ui(.*)/, // in order to adapt to cnpm
          },
          commons: {
            name: 'chunk-commons',
            test: resolve('src/components'), // can customize your rules
            minChunks: 3, //  minimum common number
            priority: 5,
            reuseExistingChunk: true,
          },
        },
      });
      // https:// webpack.js.org/configuration/optimization/#optimizationruntimechunk
      config.optimization.runtimeChunk('single');
    });

    config.plugin('provide').use(webpack.ProvidePlugin, [
      {
        $: 'jquery',
        jquery: 'jquery',
        jQuery: 'jquery',
        'window.jQuery': 'jquery',
      },
    ]);
  },
};
