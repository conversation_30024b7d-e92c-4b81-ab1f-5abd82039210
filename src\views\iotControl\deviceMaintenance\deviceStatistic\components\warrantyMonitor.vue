<template>
  <div class="wrap">
    <div class="header-wrap">
      <div class="title">保修分布</div>
      <el-link @click="showDetail">查看详情 ></el-link>
    </div>
    <div class="chart-wrap">
      <div ref="chart" class="pie-chart"></div>
    </div>
  </div>
</template>

<script>
  import { getWarrantyRatio } from '@/api/iotControl';
  export default {
    data() {
      return {
        data: [
          { name: '保修中', count: 0, color: '#87CEFA', value: 'under' },
          { name: '即将到期', count: 0, color: '#fabe46', value: 'expiring' },
          { name: '过保', count: 0, color: '#f56c6c', value: 'overdue' },
        ],
        chart: null,
        timer: null,
      };
    },
    mounted() {
      this.getData();
      this.timer = setInterval(() => this.getData(), 10 * 1000);
      window.addEventListener('resize', this.resize);
    },
    methods: {
      getData() {
        getWarrantyRatio().then(({ data }) => {
          this.data.forEach((item) => {
            item.count = data[item.value];
          });
          if (!this.chart) {
            this.chart = this.$echarts.init(this.$refs.chart);
          }
          this.draw();
        });
      },
      resize() {
        this.$nextTick(() => {
          this.chart && this.chart.resize();
        });
      },

      draw() {
        let option = {
          legend: {
            orient: 'vertical',
            right: '1%',
            top: 'center',
            formatter: (name) => {
              let item = this.data.find((d) => d.name === name);
              let total = this.data.reduce((sum, d) => sum + d.count, 0);
              let percent = ((item.count / total) * 100).toFixed(2);
              if (percent === 'NaN') {
                percent = 0;
              }
              return `{name|${name}} ${item.count}台 {percent|(${percent}%)}`;
            },
            textStyle: {
              rich: {
                name: {
                  color: '#000',
                  fontSize: 12,
                  lineHeight: 16,
                },
                count: {
                  color: '#000',
                  fontSize: 12,
                  lineHeight: 16,
                },
                percent: {
                  color: '#999',
                  fontSize: 12,
                  lineHeight: 16,
                },
              },
            },
            data: this.data.map((item) => item.name),
          },
          series: [
            {
              type: 'pie',
              radius: ['45%', '65%'],
              center: ['27%', '50%'],
              label: {
                show: false,
              },
              data: this.data.map((item) => ({
                name: item.name,
                value: item.count,
                itemStyle: {
                  color: item.color,
                },
              })),
            },
          ],
          tooltip: {
            trigger: 'item',
            formatter: '{b} : {c}台',
          },
        };
        this.chart.setOption(option);
      },
      showDetail() {
        this.$router.push({
          name: 'IntelligentMonitor',
          params: {
            activeType: 'warrantyMonitor',
          },
        });
      },
      refresh() {
        this.getData();
      },
    },
    beforeDestroy() {
      this.chart && this.chart.dispose();
      this.chart = null;
      clearInterval(this.timer);
      window.removeEventListener('resize', this.resize);
    },
  };
</script>

<style lang="scss" scoped>
  .wrap {
    width: 100%;
    height: 100%;
  }
  .chart-wrap {
    height: calc(100% - 40px);
    width: 100%;
    display: flex;
    .pie-chart {
      width: 100%;
      height: 100%;
    }
  }
</style>
