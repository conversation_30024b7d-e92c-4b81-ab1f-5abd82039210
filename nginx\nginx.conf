
user  root;
worker_processes  5;

#error_log  logs/error.log;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;

#pid        logs/nginx.pid;

events {
    worker_connections  1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;
    sendfile        on;
    keepalive_timeout  65;
    proxy_read_timeout  20m;

    server {
        listen       443 ssl http2;
        ssl_certificate      conf.d/cert/cacert.pem;
        ssl_certificate_key  conf.d/cert/private.pem;
        include conf.d/server.conf;
    }

    server {
        listen       8080;
        include conf.d/server.conf;
    }

    server {
        listen       80;
        return 301 https://$host$request_uri;
    }
}

stream {
	upstream emqx_tls {
		server emqx:1883;
	}

	server {
		listen 54321 ssl;
		ssl_certificate      conf.d/cert/cacert.pem;
		ssl_certificate_key  conf.d/cert/private.pem;
		ssl_protocols TLSv1.1 TLSv1.2 TLSv1.3;
		proxy_connect_timeout 8s;
		proxy_timeout 24h;
		proxy_pass emqx_tls;
	}
}