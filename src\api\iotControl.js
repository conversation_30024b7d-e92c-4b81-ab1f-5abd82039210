import request from '@/utils/request';
import $qs from 'qs';

/**
 * 网关列表
 * @param {*} data
 * @returns
 */
export function getGatewayList(data) {
  return request({
    url: '/iot/gateway/getGatewayList',
    method: 'get',
    params: data,
  });
}

/**
 * 不分页查询网关列表
 * @param {*} data
 * @returns
 */
export function getAllGatewayList(data) {
  return request({
    url: '/iot/gateway/nopagination',
    method: 'get',
    params: data,
  });
}

/**
 * 查询网关内的设备
 */
export function getDeviceList(data) {
  return request({
    url: '/iot/gateway/getDeviceList',
    method: 'get',
    params: data,
  });
}

/**
 * 查询空间内的设备
 * @param {*} data
 * @returns
 */
export function getDeviceInSpace(data) {
  return request({
    url: '/iot/gateway/getDeviceInSpace',
    method: 'get',
    params: data,
  });
}

/**
 * 查询网关内的空间
 * @param {*} data
 * @returns
 */
export function getSpaceList(data) {
  return request({
    url: '/iot/gateway/getSpaceList',
    method: 'get',
    params: data,
  });
}

/**
 * 查询设备分类列表
 * @returns
 */
export function getDeviceClassifications() {
  return request({
    url: '/iot/device/getDeviceClassifications',
    method: 'get',
  });
}

/**
 * 查询设备子类
 * @returns
 */
export function getDeviceSubTypes(data) {
  return request({
    url: '/iot/device/getDeviceTypes',
    method: 'get',
    params: data,
  });
}

/**
 * 根据设备分类查看设备详情
 * @param {*} classification
 * @returns
 */
export function getSpecification(classification) {
  return request({
    url: `/iot/device/getSpecification?classification=${classification}`,
    method: 'get',
  });
}

/**
 * 查询设备列表
 * @returns
 */
export function deviceGetDeviceList(data) {
  return request({
    url: '/iot/device/getDeviceList',
    method: 'get',
    params: data,
  });
}

/**
 * 不分页查询设备列表
 * @param {*} data
 * @returns
 */
export function deviceGetAllList(data) {
  return request({
    url: '/iot/device/nopagination',
    method: 'get',
    params: data,
  });
}

/**
 * 编辑设备
 * @returns
 */
export function editDevice(data) {
  return request({
    url: '/iot/device/edt',
    method: 'post',
    params: data,
  });
}

/**
 * 删除设备
 * @param {*} deviceId
 * @returns
 */
export function delDevice(deviceId) {
  return request({
    url: `/iot/device?deviceId=${deviceId}`,
    method: 'delete',
  });
}

/**
 * 查询设备操作历史
 * @param {*} data
 * @returns
 */
export function getDeviceOperationHistory(data) {
  return request({
    url: '/iot/device/operationHistory',
    method: 'get',
    params: data,
  });
}

/**
 * 清空设备操作历史
 * @param deviceId 设备id
 * @returns
 */
export function clearDeviceOperationHistory(deviceId) {
  return request({
    url: '/iot/device/operationHistory',
    method: 'delete',
    params: { deviceId },
  });
}

/**
 * 删除设备操作历史
 * @param ids 操作历史id集合
 * @returns
 */
export function deleteDeviceOperationHistory(ids) {
  return request({
    url: '/iot/device/operationHistory',
    method: 'delete',
    params: { deviceId },
  });
}

/**
 * 查询设备上报的事件记录
 * @param {*} data
 * @returns
 */
export function getDeviceEventList(data) {
  return request({
    url: '/iot/event/list/pageable',
    method: 'get',
    params: data,
  });
}

export function saveDeviceLogo(data, onUploadProgress) {
  let file = new FormData();
  Object.keys(data).forEach((key) => {
    file.append(key, data[key]);
  });
  return request({
    url: '/iot/device/logo',
    method: 'post',
    data: file,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    onUploadProgress,
  });
}

/**
 * 查看设备详情
 * @param {*} deviceId
 * @returns
 */
export function getDeviceDetail(deviceId) {
  return request({
    url: `/iot/device/detail?deviceId=${deviceId}`,
    method: 'get',
  });
}

export function setDeviceMethod(data) {
  return request({
    url: `/iot/device/setMethod`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

export function setDeviceProperty(data) {
  return request({
    url: `/iot/device/setProperty`,
    method: 'post',
    params: data,
  });
}

/**
 * 保存自定义空间/编辑自定义空间
 * @param {*} data
 * @returns
 */
export function saveUserSpace(data) {
  return request({
    url: `/iot/space/userSpace`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

/**
 * 查询自定义空间列表
 * @returns
 */
export function getUserSpace(data) {
  return request({
    url: `/iot/space/userSpace`,
    method: 'get',
    params: data,
  });
}

/**
 * 查询自定义空间中的网关空间
 * userSpaceId
 * @returns
 */
export function getGatewaySpaceByUserSpace(data) {
  return request({
    url: `/iot/space/getSpaceList`,
    method: 'get',
    params: data,
  });
}

/**
 * 查询自定义空间中的设备
 * @param {*} data
 * @returns
 */
export function getUserSpaceDevice(data) {
  return request({
    url: `/iot/space/getDeviceList`,
    method: 'get',
    params: data,
  });
}

/**
 * 查询网关空间列表
 * @param {*} data
 * @returns
 */
export function getGatewaySpaceList(data) {
  return request({
    url: `/iot/space`,
    method: 'get',
    params: data,
  });
}

/**
 * 查询网关空间关联的自定义空间
 * @param {*} data
 * @returns
 */
export function getSpaceLink(data) {
  return request({
    url: `/iot/space/spacelink`,
    method: 'get',
    params: data,
  });
}

/**
 * 不分页查询所有网关空间
 * @param {*} data
 * @returns
 */
export function getAllGatewaySpace(data) {
  return request({
    url: `/iot/space/nonsort`,
    method: 'get',
    params: data,
  });
}

/**
 * 编辑网关空间
 * @param {*} data
 * @returns
 */
export function editGateSpace(data) {
  return request({
    url: `/iot/space/edt`,
    method: 'post',
    params: data,
  });
}

/**
 * 删除自定义网关
 * @param {*} data
 * @returns
 */
export function deleteUserSpace(data) {
  return request({
    url: `/iot/space/userSpace`,
    method: 'delete',
    params: data,
  });
}

/**
 * 查询设备属性历史值
 * @param {*} data
 * @returns
 */
export function getDevicePropHis(data) {
  return request({
    url: `/iot/device/history`,
    method: 'get',
    params: data,
  });
}

/**
 * 会议室绑定空间
 * @param {*} data
 * @returns
 */
export function roomBindSpace(data) {
  return request({
    url: `/weapp/room/space`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

/**
 * 获取会议室绑定空间
 * @param {*} data
 * @returns
 */
export function getRoomBindSpace(data) {
  return request({
    url: `/weapp/room/space`,
    method: 'get',
    params: data,
  });
}

/**
 * 查询报警记录
 * @param {*} data
 * @returns
 */
export function getAlarmRecord(data) {
  return request({
    url: `/iot/alarm/record/pageable`,
    method: 'get',
    params: data,
  });
}

/**
 * 查询所有报警记录（不分页）
 * @param {*} data
 * @returns
 */
export function getAllAlarmRecord(params) {
  return request({
    url: `/iot/alarm/record/nopagination`,
    method: 'get',
    params,
  });
}

/**
 * 查看单个报警规则详情
 * @param {*} data
 * @returns
 */
export function getAlarmRuleById(params) {
  return request({
    url: `/iot/alarm/info`,
    method: 'get',
    params,
  });
}

/**
 * 查询报警规则
 * @param {*} data
 * @returns
 */
export function getAlarmRule(data) {
  return request({
    url: `/iot/alarm`,
    method: 'get',
    params: data,
  });
}

/**
 * 获取所有报警规则（不分页）
 */
export function getAllAlarmRule() {
  return request({
    url: `/iot/alarm/list/all`,
    method: 'get',
  });
}

/**
 * 保存报警规则
 * @param {*} data
 * @returns
 */
export function saveAlarmRule(data) {
  return request({
    url: `/iot/alarm/sync`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

/**
 * 删除报警规则
 * @param {*} data
 * @returns
 */
export function deleteAlarmRule(data) {
  return request({
    url: `/iot/alarm/delete`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

/**
 * 删除报警记录
 * @param {*} data
 * @returns
 */
export function deleteAlarmRecord(data) {
  return request({
    url: `/iot/alarm/record/delete`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

/**
 * 确认报警记录
 * @param {*} data
 * @returns
 */
export function confirmAlarm(data) {
  return request({
    url: `/iot/alarm/confirm`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

/**
 * 确认某个设备的所有告警记录
 * @param {*} param
 * @returns
 */
export function confirmDeviceAllAlarm(params) {
  return request({
    url: `/iot/alarm/record/confirm/device`,
    method: 'post',
    params,
  });
}

/**
 * 批量确认告警记录（选中的）
 * @param {*} param
 * @returns
 */
export function confirmAllAlarm(data) {
  return request({
    url: `/iot/alarm/record/confirm/all`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

/**
 * 删除所有告警记录
 * @param {*} param
 * @returns
 */
export function batchDeleteAlarmRecord(data) {
  return request({
    url: `/iot/alarm/record/deleteAll`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

/**
 * 获取报警记录详情
 */
export function getAlarmRecordDetail(id) {
  return request({
    url: `/iot/alarm/record/${id}`,
    method: 'post',
  });
}

/**
 * 删除报警信息
 * @param ids 报警信息ID
 * @returns {AxiosPromise}
 */
export function deleteAlarm(ids) {
  return request({
    url: `/iot/alarm/delete`,
    headers: {
      'Content-Type': 'application/json;',
    },
    method: 'post',
    data: JSON.stringify(ids),
  });
}

/**
 * 查询场景模式
 * @param {*} data
 * @returns
 */
export function getSceneMode(data) {
  data.direction = data.direction || 'ASC';
  data.properties = data.properties || 'gmtCreate';
  return request({
    url: `/iot/scenemode`,
    method: 'get',
    params: data,
  });
}

/**
 * 删除场景模式
 * @param {*} id
 * @returns
 */
export function deleteSceneMode(id) {
  return request({
    url: `/iot/scenemode?id=${id}`,
    method: 'delete',
  });
}

/**
 * 保存场景模式
 * @param {*} data
 * @returns
 */
export function saveSceneMode(data) {
  return request({
    url: `/iot/scenemode`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

/**
 * 设置执行场景模式
 * @param {*} data
 * @returns
 */
export function setSceneMode(data) {
  return request({
    url: `/iot/scenemode/setSceneRule`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

/**
 * 不分页查询场景模式列表
 * @returns
 */
export function getAllSceneMode(data) {
  return request({
    url: `/iot/scenemode/nonsort`,
    method: 'get',
    params: data,
  });
}

/**
 * 保存要在会议室上执行的模式
 * @param {*} data
 * @returns
 */
export function setRoomSceneMode(data) {
  return request({
    url: `/weapp/room/scenemode`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

/**
 * 查询指定会议室上要执行的模式
 * @param {*} roomId
 * @returns
 */
export function getRoomSceneMode(roomId) {
  return request({
    url: `/weapp/room/scenemode?roomId=${roomId}`,
    method: 'get',
  });
}

/**
 * 批量删除会议室上的模式
 * @param {*} ids
 * @returns
 */
export function deleteRoomSceneMode(ids) {
  return request({
    url: `/weapp/room/scenemode?ids=${ids}`,
    method: 'delete',
  });
}

/**
 * 物联网关审核/编辑
 * @param {*} params
 * @returns
 */
export function editGateway(params) {
  return request({
    url: `/iot/gateway/edit`,
    method: 'post',
    params,
  });
}

/**
 * 删除网关
 * @param {*} macs
 * @returns
 */
export function delGateway(macs) {
  return request({
    url: `/iot/gateway?macs=${macs}`,
    method: 'delete',
  });
}

/**
 * 同步网关内设备
 * @param {*} mac
 * @returns
 */
export function getSyncDevice(mac) {
  return request({
    url: `/iot/gateway/syncDevice?mac=${mac}`,
    method: 'get',
  });
}

/**
 * 场景关联空间或空间关联场景
 * @param data {type,modeSpace}
 * @param type      绑定类型. mode: 一个场景绑定多个空间, space: 一个空间绑定多个场景
 * @param modeSpace 绑定列表
 * @returns
 */
export function sceneBindSpace(data) {
  return request({
    url: `/iot/scenemode/bindSpace`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

/**
 * 取消场景关联空间或取消空间关联场景
 * @param ids 绑定id列表
 * @returns
 */
// export function sceneUnbindSpace(ids) {
//   return request({
//     url: `/iot/scenemode/delBind`,
//     method: "delete",
//     data: ids,
//     headers: {
//       "Content-Type": "application/json;",
//     },
//   });
// }

/**
 * 获取场景关联的空间
 * @param modeId 场景id
 * @returns
 */
export function getSceneBindSpace(modeId) {
  return request({
    url: `/iot/scenemode/space?modeId=${modeId}`,
    method: 'get',
  });
}

/**
 * 空间关联场景
 * @param data
 * @returns
 */
//  export function spaceBindScene(data) {
//   return request({
//     url: `/iot/space/bind`,
//     method: "post",
//     data,
//     headers: {
//       "Content-Type": "application/json;",
//     },
//   });
// }

/**
 * 获取空间关联的场景
 * @param spaceId   空间id
 * @returns
 */
export function getSpaceBindScene(spaceId, isNoAlert = false) {
  return request({
    url: `/iot/space/scenemode?spaceId=${spaceId}`,
    method: 'get',
    headers: {
      noAlert: isNoAlert,
    },
  });
}

/**
 * 保存联动规则
 * @param {*} data
 * @returns
 */
export function saveLinkageRule(data) {
  return request({
    url: `/iot/linkage`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

/**
 * 获取设备联动规则列表
 * @param {*} params
 * @returns
 */
export function getLinkageRule(params) {
  return request({
    url: `/iot/linkage`,
    method: 'get',
    params,
  });
}

/**
 * 删除联动规则
 * @param {*} ids
 * @returns
 */
export function delLinkageRule(ids) {
  return request({
    url: `/iot/linkage?ids=${ids}`,
    method: 'delete',
  });
}

/**
 * 规则绑定空间
 * @param {*} data
 * @returns
 */
export function bindLinkageRuleToSpace(data) {
  return request({
    url: `/iot/linkage/space`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

/**
 * 查询规则绑定的空间
 * @param {*} linkageId linkageId
 * @returns
 */
export function getLinkageRuleBindSpace(linkageId) {
  return request({
    url: `/iot/linkage/space?linkageId=${linkageId}`,
    method: 'get',
  });
}

/**
 * 把空间绑定到规则上
 * @param {*} data
 * @returns
 */
export function bindSpaceToLinkageRule(data) {
  return request({
    url: `/iot/space/linkage`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

/**
 * 查询空间绑定的规则 根据空间查询绑定的规则
 * @param {*} params
 * @returns
 */
export function getLinkageRuleBySpace(params) {
  return request({
    url: `/iot/space/linkage`,
    method: 'get',
    params,
  });
}

/**
 * 批量设置设备管理员接口
 * @param {*} data
 * {
 * "deviceIds": ["002","003","004"],
 * "usernames": ["lm","xujia","wj"]
 * }
 * @returns
 */
export function bindDeviceManager(data) {
  return request({
    url: `/iot/device/manager`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

/**
 * 获取设备管理员
 * @param {*} deviceId
 * @returns
 */
export function getDeviceManager(deviceId) {
  return request({
    url: `/iot/device/manager?deviceId=${deviceId}`,
    method: 'get',
  });
}

/**
 * 根据规则id批量确认报警信息

 * @param {*} params
 * @returns
 */
export function confirmByRule(params) {
  return request({
    url: `/iot/alarm/confirmByAlarm`,
    method: 'post',
    params,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

/**
 * 查询默认场景
 * @returns
 */
export function getDefaultMode() {
  return request({
    url: `/iot/defaultSceneMode`,
    method: 'get',
    headers: {
      noAlert: true,
    },
  });
}

/**
 * 执行默认场景
 * @param {*} data
 * @returns
 */
export function setDefaultMode(data) {
  return request({
    url: `/iot/defaultSceneMode`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

/**
 * 查询设备组的组内设备
 * @param {*} params
 * @returns
 */
export function getGroupDevices(params) {
  return request({
    url: `/iot/device/getGroupDevices`,
    method: 'get',
    params,
  });
}

/**
 * 检查cron表达式是否合法
 * @param {*} cron
 * @returns
 */
export function validateCron(cron) {
  return request({
    url: `/iot/scenemode//cron/validate?cron=${cron}`,
    method: 'get',
  });
}

/**
 * 查询cron最近执行时间
 * @param {*} params
 * @returns
 */
export function getCronTriggerTime(params) {
  return request({
    url: `/iot/scenemode/cron/getTriggerTime`,
    method: 'get',
    params,
  });
}

/**
 * 查询硬件信息
 * @param {*} params
 * @returns
 */
export function getHardware(params) {
  return request({
    url: `/iot/third_gateway`,
    method: 'get',
    params,
  });
}

//查询指定型号硬件的通信参数
export function getHardwareParam(type) {
  return request({
    url: `/iot/third_gateway/${type}/param`,
    method: 'get',
  });
}

/**
 * 添加网关
 * @param {*} data
 * @returns
 */
export function addGateway(data) {
  return request({
    url: `/iot/gateway`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

/**
 * 获取该类型的可供下载的设备类型
 * @param {*} type
 * @returns
 */
export function getAvailableUpgradeDevice(type) {
  return request({
    url: `/iot/upgrade/device/${type}`,
    method: 'get',
  });
}

/**
 * 推送下载命令
 * @param {*} data
 * @returns
 */
export function publishUpgrade(data) {
  return request({
    url: `/iot/upgrade/publish`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

/**
 * 查看该升级文件的安装记录
 * @param {*} fileId
 * @returns
 */
export function getUpgradeRecordByFile(fileId) {
  return request({
    url: `/iot/upgrade/record/file/${fileId}`,
    method: 'get',
  });
}

/**
 * 清除该文件的安装记录
 * @param {*} fileId
 * @returns
 */
export function deleteUpgradeRecordByFile(fileId) {
  return request({
    url: `/iot/upgrade/record/fileClear/${fileId}`,
    method: 'get',
  });
}

/**
 * 根据升级记录查看升级的设备具体信息
 * @param {*} recordId
 * @returns
 */
export function getDeviceRecord(recordId) {
  return request({
    url: `/iot/upgrade/record/device/${recordId}`,
    method: 'get',
  });
}
/**
 * 保存空间
 * @param {*} data
 * @returns
 */
export function saveSpace(data) {
  let file = new FormData();
  Object.keys(data).forEach((key) => {
    data[key] && file.append(key, data[key]);
  });
  return request({
    url: `/iot/space`,
    method: 'post',
    data: file,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

/**
 * 获取当前用户所在公司的树状空间标签
 * @returns
 */
export function getSpaceLabel() {
  return request({
    url: `/iot/label`,
    method: 'get',
  });
}

/**
 * 保存空间标签
 * @param {*} data
 * @returns
 */
export function saveSpaceLabel(data) {
  return request({
    url: `/iot/label`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

/**
 * 删除空间标签
 * @param {*} labelId
 * @returns
 */
export function delSpaceLabel(labelId) {
  return request({
    url: `/iot/label?labelId=${labelId}`,
    method: 'delete',
  });
}

/**
 * 批量向空间中关联设备
 * @param {*} data
 * @returns
 */
export function bindSpaceDevice(data) {
  return request({
    url: `/iot/space/device`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

/**
 * 删除空间
 * @param {*} spaceId
 * @returns
 */
export function delSpace(spaceId) {
  return request({
    url: `/iot/space?spaceId=${spaceId}`,
    method: 'delete',
  });
}

/**
 * 查询网关，设备及告警统计数据
 */
export function getMaintenanceStatistic(params) {
  return request({
    url: '/iot/maintenance/statistic',
    method: 'get',
    params,
  });
}

/**
 * 根据id查询空间信息
 * @param spaceId   空间id
 * @returns
 */
export function getSpaceInfo(spaceId) {
  return request({
    url: `/iot/space/${spaceId}`,
    method: 'get',
  });
}

/**
 * 获取当前空间的会议信息(开始或结束时间在当天的会议)
 * @param spaceId   空间id
 * @returns
 */
export function getSpaceConf(spaceId) {
  return request({
    url: `/iot/space/conf/${spaceId}`,
    method: 'get',
  });
}

/**
 * 获取当前空间的摄像头
 * @param spaceId   空间id
 * @returns
 */
export function getSpaceCamera(spaceId) {
  return request({
    url: `/iot/space/camera/${spaceId}`,
    method: 'get',
  });
}

/**
 * 能耗统计，可按照周月年与空间进行统计
 * @param {*} params
 * @returns
 */
export function getEnergyStatistics(params) {
  return request({
    url: `/iot/energy/getEnergyStatistics`,
    method: 'get',
    params,
  });
}

/**
 * 能耗统计，空间的一天
 * @param {*} params
 * @returns
 */
export function getDayEnergy(params) {
  return request({
    url: `/iot/energy/getEnergyAndPowerStatistics`,
    method: 'get',
    params,
  });
}

/**
 * 能耗基本信息
 * @returns
 */
export function getEnergy(params) {
  return request({
    url: `/iot/energy/global`,
    method: 'get',
    params,
  });
}

/**
 * 能耗设备列表
 * @param {*} params
 * @returns
 */
export function getMeterEnergyList(params) {
  return request({
    url: `/iot/energy/getMeterEnergyList`,
    method: 'get',
    params,
  });
}

/**
 * 设备状态监测
 * @returns
 */
export function deviceStatusMonitor(params) {
  return request({
    url: `/iot/device/maintenance/deviceStatusMonitoring`,
    method: 'get',
    params,
  });
}

/**
 * 资产统计
 * @returns
 */
export function getAssetStatistic(params) {
  return request({
    url: `/iot/device/maintenance/asset`,
    method: 'get',
    params,
  });
}

/**
 * 寿命监测
 * @returns
 */
export function getAssetLifeMonitor(params) {
  return request({
    url: `/iot/device/maintenance/assetList`,
    method: 'get',
    params,
  });
}

/**
 * 保修监测
 * @returns
 */
export function getAssetWarrantyMonitor(params) {
  return request({
    url: `/iot/device/maintenance/warrantyList`,
    method: 'get',
    params,
  });
}

/**
 * 查询工单
 * @param {*} params
 * @returns
 */
export function getWorkOrder(params) {
  return request({
    url: `/iot/workorder`,
    method: 'get',
    params,
  });
}

/**
 * 查询工单类型
 * @param {*} params
 * @returns
 */
export function getWorkOrderType() {
  return request({
    url: `/iot/workorder/workordertype`,
    method: 'get',
  });
}

/**
 * 保存工单
 * @param {*} data
 * @returns
 */
export function saveWorkOrder(data) {
  return request({
    url: `/iot/workorder`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

/**
 * 上传工单附件
 * @param {*} data
 * @returns
 */
export function saveWorkOrderAttachment(data) {
  return request({
    url: `/iot/workorder/attachment`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

/**
 * 获取工单附件
 * @param {*} params
 * @returns
 */
export function getWorkOrderAttachment(params) {
  return request({
    url: `/iot/workorder/attachment`,
    method: 'get',
    params,
  });
}

/**
 * 删除工单附件
 * @param {*} params
 */
export function deleteWorkOrderAttachment(id, data) {
  return request({
    url: `/iot/workorder/attachment/delete?id=${id}`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

/**
 * 查询我创建的工单
 * @param {*} params
 * @returns
 */
export function getMyCreateWorkOrder(params) {
  return request({
    url: `/iot/workorder/my/create`,
    method: 'get',
    params,
  });
}

/**
 * 查询我需要处理的工单
 * @param {*} params
 * @returns
 */
export function getMyConfirmWorkOrder(params) {
  return request({
    url: `/iot/workorder/my/confirm`,
    method: 'get',
    params,
  });
}

/**
 * 查询工单详情
 * @param {*} params
 * @returns
 */
export function getWorkOrderDetail(params) {
  return request({
    url: `/iot/workorder/detail`,
    method: 'get',
    params,
  });
}

/**
 * 处理工单
 * @param {*} data
 * @returns
 */
export function disposeWorkOrder(params) {
  return request({
    url: `/iot/workorder/dispose`,
    method: 'post',
    params,
  });
}

/**
 * 获取空间自动生成的拓扑图信息
 * @param spaceId   空间id
 * @returns
 */
export function getSpaceTopo(spaceId) {
  return request({
    url: `/iot/space/topology/${spaceId}`,
    method: 'get',
  });
}

/**
 * 获取设备分类（级联，新设备分类）
 * @returns
 */
export function getNewDeviceTypes() {
  return request({
    url: `/iot/device/getDeviceTypes`,
    method: 'get',
  });
}

/**
 * 查询空间中的快捷操作设备
 * @returns
 */
export function getShortcutDevice(params) {
  return request({
    url: `/iot/space/shortcutDevice`,
    method: 'get',
    params,
  });
}

/**
 * 向空间中添加快捷设备
 * @returns
 */
export function updateShortcutDevice(data) {
  return request({
    url: `/iot/space/shortcutDevice`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

/**
 * 分页查询物模型
 */
export function getObjectModelPageable(data) {
  return request({
    url: `/iot/thingModel/list/pageable`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

/**
 * 添加/更新物模型
 */
export function updateObjectModel(data) {
  return request({
    url: `/iot/thingModel/sync`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

/**
 * 批量删除物模型
 */
export function deleteObjectModel(ids) {
  return request({
    url: `/iot/thingModel/batchDelete`,
    headers: {
      'Content-Type': 'application/json;',
    },
    method: 'post',
    data: ids,
  });
}

/**
 * 查询产品类型列表
 */
export function getProductType(data) {
  return request({
    url: `/iot/product/deviceType/list`,
    method: 'post',
    params: data,
  });
}

/**
 * 添加/更新产品分类
 */
export function updateProductType(data) {
  return request({
    url: `/iot/product/deviceType/sync`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

/**
 * 查询产品子类型列表
 */
export function getProductSubType(data) {
  return request({
    url: `/iot/product/deviceSubType/list`,
    method: 'post',
    params: data,
  });
}

/**
 * 添加/更新产品子类型
 */
export function updateProductSubType(data) {
  return request({
    url: `/iot/product/deviceSubType/sync`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

/**
 * 删除产品类型
 */
export function deleteProductType(ids) {
  return request({
    url: `/iot/product/deviceType/delete`,
    headers: {
      'Content-Type': 'application/json;',
    },
    method: 'post',
    data: ids,
  });
}
/**
 * 删除产品子类型
 */
export function deleteProductSubType(data) {
  return request({
    url: `/iot/product/deviceSubType/delete`,
    headers: {
      'Content-Type': 'application/json;',
    },
    method: 'post',
    data: data,
  });
}

/**
 * 查询品牌列表
 */
export function getBrandList(data) {
  return request({
    url: `/iot/brand/list`,
    method: 'post',
    params: data,
  });
}

/**
 * 添加/更新品牌
 */
export function updateBrand(data) {
  return request({
    url: `/iot/brand/save`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

/**
 * 删除品牌
 */
export function deleteBrand(ids) {
  return request({
    url: `/iot/brand/delete`,
    headers: {
      'Content-Type': 'application/json;',
    },
    method: 'post',
    data: ids,
  });
}

/**
 * 分页查询产品
 */
export function getProductPageable(data) {
  return request({
    url: `/iot/product/list/pageable`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

/**
 * 根据ID获取产品基础信息
 */
export function getProductInfo(data) {
  return request({
    url: `/iot/product/info`,
    method: 'post',
    params: data,
  });
}

/**
 * 根据ID获取产品详情
 * 根据传入参数type分类别获取数据列表
 * 【CONFIG（配置项）、ALL（所有物模型）、PROPERTY、METHOD、EVENT】
 */
export function getProductDetail(data) {
  return request({
    url: `/iot/product/detail`,
    method: 'post',
    params: data,
  });
}

/**
 * 批量删除产品
 */
export function deleteProduct(ids) {
  return request({
    url: `/iot/product/batchDelete`,
    headers: {
      'Content-Type': 'application/json;',
    },
    method: 'post',
    data: ids,
  });
}

/**
 * 添加/更新产品基础信息
 */
export function updateProductInfo(data) {
  let file = new FormData();
  Object.keys(data).forEach((key) => {
    if (data[key] !== undefined && data[key] !== null && data[key] !== '') {
      file.append(key, data[key]);
    }
  });
  return request({
    url: `/iot/product/sync`,
    method: 'post',
    data: file,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

/**
 * 添加物模型并绑定到产品
 */
export function addProductObjectModel(productId, saveToCommon, data) {
  return request({
    url: `/iot/product/syncThingModel?productId=${productId}&saveToCommon=${saveToCommon}`,
    headers: {
      'Content-Type': 'application/json;',
    },
    method: 'post',
    data: data,
  });
}

/**
 * 绑定通用物模型到产品
 */
export function bindModelToProduct(productId, data) {
  return request({
    url: `/iot/product/bindThingModel?productId=${productId}`,
    headers: {
      'Content-Type': 'application/json;',
    },
    method: 'post',
    data: data,
  });
}

/**
 * 发布产品
 */
export function publishProduct(params) {
  return request({
    url: `/iot/product/publish`,
    headers: {
      'Content-Type': 'application/json;',
    },
    method: 'post',
    params: params,
  });
}

/**
 * 更新.保存产品的配置项列表
 * 1.productId存在时
 *   productId合法时需要传【全量配置项列表】 >> 将配置项和产品关联
 * 2.productId不存在
 *   保存或者修改通用配置项【通用配置项，供其他产品选择使用】
 */
export function updateProductConfig(productId, data) {
  return request({
    url: `/iot/product/config/sync?productId=${productId}`,
    headers: {
      'Content-Type': 'application/json;',
    },
    method: 'post',
    data: data,
  });
}

/**
 * 分页查询配置项
 */
export function getConfigPageable(data) {
  return request({
    url: `/iot/product/config/list/pageable`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

/**
 * 删除配置项
 */
export function deleteConfig(ids) {
  return request({
    url: `/iot/product/config/delete`,
    headers: {
      'Content-Type': 'application/json;',
    },
    method: 'post',
    data: ids,
  });
}

/**
 * 产品类型树
 */
export function getAllProductTypes() {
  return request({
    url: `/iot/product/deviceType/recursion`,
    headers: {
      'Content-Type': 'application/json;',
    },
    method: 'post',
  });
}

/**
 * 保存驱动脚本
 */
export function saveDriverScript(productId, data) {
  return request({
    url: `/iot/product/driver/save?productId=${productId}`,
    headers: {
      'Content-Type': 'text/plain',
    },
    data: data,
    method: 'post',
  });
}

/**
 * 获取驱动脚本
 */
export function getDriverScript(productId) {
  return request({
    url: `/iot/product/driver/get?productId=${productId}`,
    headers: {
      'Content-Type': 'application/json;',
    },
    method: 'post',
  });
}

/**
 * 导出产品
 */
export function exportProduct(productId) {
  return request({
    url: `/iot/product/export?productId=${productId}`,
    headers: {
      'Content-Type': 'application/json;',
    },
    method: 'post',
  });
}

/**
 * 根据code获取产品详情
 */
export function getProductDetailByCode(code) {
  return request({
    url: `/iot/product/code?code=${code}`,
    headers: {
      'Content-Type': 'application/json;',
    },
    method: 'post',
  });
}

/**
 * 保存规则
 * @param {*} data
 * @returns
 */
export function saveRule(data) {
  return request({
    url: `/iot/automation/info/update`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

/**
 * 分页查询规则
 */
export function getRulePageable(data) {
  return request({
    url: `/iot/automation/list/pageable`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

/**
 * 根据id获取规则
 */
export function getRuleById(id) {
  return request({
    url: `/iot/automation/info/${id}`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

/**
 * 执行规则
 */
export function executeRule(id) {
  return request({
    url: `/iot/automation/trigger?id=${id}`,
    headers: {
      'Content-Type': 'application/json;',
    },
    method: 'post',
  });
}

/**
 * 设置规则是否可用
 */
export function setEnableRule(id, enabled) {
  return request({
    url: `/iot/automation/setEnable?id=${id}&enabled=${enabled}`,
    headers: {
      'Content-Type': 'application/json;',
    },
    method: 'post',
  });
}

/**
 * 获取所有规则
 */
export function getAllRule(data) {
  return request({
    url: `/iot/automation/list`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

/**
 * 删除规则
 */
export function deleteRule(id) {
  return request({
    url: `/iot/automation/delete/${id}`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

/**
 * 查询规则所绑定的空间
 */
export function getSpaceInRule(id) {
  return request({
    url: `/iot/automation/list/rule_space/${id}`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

/**
 * 绑定空间到规则
 */
export function bindSpaceToRule(id, spaceList) {
  return request({
    url: `/iot/automation/bind/rule_space/${id}`,
    method: 'post',
    data: spaceList,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

/**
 * 查询空间所绑定的规则
 */
export function getRuleInSpace(id, type) {
  return request({
    url: `/iot/automation/list/space_rule/${id}`,
    method: 'post',
    params: type,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

/**
 * 绑定规则到空间
 */
export function bindRuleToSpace(id, ruleList) {
  return request({
    url: `/iot/automation/bind/space_rule/${id}`,
    method: 'post',
    data: ruleList,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

//产品ZIP包导入
export function uploadProductZip(data) {
  let file = new FormData();
  if (data instanceof Array) {
    data.forEach((item) => {
      file.append('files', item);
    });
  } else {
    file.append('files', data);
  }
  return request({
    url: '/iot/product/upload/zip',
    method: 'post',
    data: file,
    headers: {
      'Content-Type': 'multipart/form-data;',
    },
  });
}

/**
 * 同步规则到网关
 */
export function syncRuleToGateway(data) {
  return request({
    url: `/iot/automation/syncToGateway`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

/**
 * 同步产品驱动文件到网关
 * @param {*} data
 * @returns
 */
export function syncProductToGateway(data) {
  return request({
    url: `/iot/product/syncToGateway`,
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

/**
 * 获取所有巡检任务
 */
export function getInspectList(params) {
  return request({
    url: `/iot/inspect/findAll`,
    method: 'get',
    params,
  });
}

/**
 * 获取巡检记录
 */
export function getInspectRecord(params) {
  return request({
    url: `/iot/inspect/findAllRecord`,
    method: 'get',
    params,
  });
}

/**
 * 获取巡检任务详情
 */
export function getInspectDetail(id) {
  return request({
    url: `/iot/inspect/info/${id}`,
    method: 'get',
  });
}

/**
 * 保存巡检任务
 */
export function saveInspect(data) {
  return request({
    url: '/iot/inspect/save',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

/**
 * 设置巡检任务是否启用
 */
export function setInspectEnable(id, enabled) {
  return request({
    url: `/iot/inspect/setEnable?id=${id}&enabled=${enabled}`,
    method: 'get',
  });
}

/**
 * 立即执行巡检任务
 */
export function executeInspect(id) {
  return request({
    url: `/iot/inspect/execute/${id}`,
    method: 'get',
  });
}

/**
 * 删除巡检任务
 */
export function deleteInspect(data) {
  return request({
    url: `/iot/inspect/delete`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

/**
 * 获取巡检模版
 */
export function getInspectTemplate(params) {
  return request({
    url: `/iot/inspect/template/findAll`,
    method: 'get',
    params,
  });
}

/**
 * 删除巡检模板
 */
export function deleteInspectTemplate(data) {
  return request({
    url: `/iot/inspect/template/delete`,
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

/**
 * 保存巡检模板
 */
export function saveInspectTemplate(data) {
  return request({
    url: '/iot/inspect/template/save',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;',
    },
  });
}

/**
 * 获取巡检模板详情
 */
export function getInspectTemplateDetail(id) {
  return request({
    url: `/iot/inspect/template/info/${id}`,
    method: 'get',
  });
}

/**
 * 获取巡检记录分析
 */
export function getInspectRecordAnalysis(id) {
  return request({
    url: `/iot/inspect/task/statistical/record/analysis`,
    method: 'get',
    params: {
      id,
    },
  });
}

/**
 * 获取巡检任务准备信息
 */
export function getInspectPrepare(id) {
  return request({
    url: `/iot/inspect/prepare/${id}`,
    method: 'get',
  });
}

/**
 * 查询机构下的空间列表
 */
export function getSpaceListByDept(params, deptId) {
  return request({
    url: `/iot/space/dept/${deptId}`,
    method: 'get',
    params,
  });
}

/**
 * 查询设备组内设备寿命状态
 */
export function getLifeStatusInGroup(params) {
  return request({
    url: `/iot/device/maintenance/lifeStatusInGroup`,
    method: 'get',
    params,
  });
}

/**
 * 查询设备组内设备保修状态
 */
export function getWarrantyStatusInGroup(params) {
  return request({
    url: `/iot/device/maintenance/warrantyInGroup`,
    method: 'get',
    params,
  });
}

/**
 * 设备运维中心Top统计
 * timeType 统计区间  1：按月， 2：按周
 * itemType 统计类型  1：告警设备top10，2：离线次数Top10，3：使用时长Top10
 */
export function getDeviceStatisticTop(params) {
  return request({
    url: `/iot/device/maintenance/top10`,
    method: 'get',
    params,
  });
}

/**
 * 设备寿命分布
 */
export function getLifeRatio() {
  return request({
    url: `/iot/device/maintenance/lifeRatio`,
  });
}

/**
 * 设备运行状态分布
 */
export function getStatusRatio() {
  return request({
    url: `/iot/device/maintenance/statusRatio`,
  });
}

/**
 * 设备保修分布
 */
export function getWarrantyRatio() {
  return request({
    url: `/iot/device/maintenance/warrantyRatio`,
  });
}

/**
 * 设备运维中心告警设备分类统计
 */
export function getDeviceTypeAlarm(params) {
  return request({
    url: `/iot/device/maintenance/assetAlarm`,
    method: 'get',
    params,
  });
}
