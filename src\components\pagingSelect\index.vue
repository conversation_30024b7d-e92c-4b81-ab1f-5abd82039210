<template>
  <el-select filterable remote v-model="value" :multiple="multiple" :placeholder="placeholder" :remote-method="getList"
    :loading="loading" :clearable="clearable" @visible-change="handleSelectVisibleChange">
    <el-option v-for="item in options" :key="item[_key]" :label="item[label]" :value="item[_key]">
    </el-option>
    <pagination :total="total" :page.sync="pagination.curPage" :limit.sync="pagination.size" @pagination="getList()"
      :background="false" layout="prev,pager,next,total" :autoScroll="false" />
  </el-select>
</template>

<script>
export default {
  name: "PagingSelect",
  props: {
    placeholder: {
      type: String,
      default: ""
    },
    loading: {
      type: Boolean,
      default: false
    },
    options: {
      type: Array,
      default: () => []
    },
    total: {
      type: Number,
      default: 0
    },
    multiple: {
      type: Boolean,
      default: false
    },
    _key: {
      type: String,
      default: "id"
    },
    label: {
      type: String,
      default: "name"
    },
    selectValue: {
      type: String | Number,
      default: ""
    },
    clearable: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      // value: "",
      pagination: {
        size: 10,
        curPage: 1
      }
    };
  },
  computed: {
    value: {
      get() {
        return this.selectValue;
      },
      set(newValue) {
        this.$emit("selectChange", newValue);
      }
    }
  },
  methods: {
    /**
     * 会议模板下拉框出现/隐藏
     */
    handleSelectVisibleChange(visible) {
      visible && this.getList();
      let rulesDom = this.$el.querySelector(
        ".el-input .el-input__suffix .el-input__suffix-inner .el-input__icon"
      );
      if (visible) {
        rulesDom.classList.add('is-reverse')
      } else {
        rulesDom.classList.remove('is-reverse')
      }
    },
    getList(nameToSearch = "") {
      this.$emit("getList", {
        page: this.pagination.curPage,
        size: this.pagination.size,
        name: nameToSearch
      });
    }
  },
  /**
   * 添加远程搜索输入框右侧的下拉箭头
   */
  mounted() {
    let rulesDom = this.$el.querySelector(
      ".el-input .el-input__suffix .el-input__suffix-inner .el-input__icon"
    );
    // 找到dom 添加el-icon-arrow-up类
    rulesDom.classList.add("el-icon-arrow-up");// 对dom新增class
  }
};
</script>
<style lang="scss" scoped>
</style>
