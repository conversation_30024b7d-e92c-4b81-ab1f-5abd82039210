import request from "@/utils/request";

/**
 * 分页查询直播频道
 * @param page 当前页号
 * @param size 页内行数
 * @param deptId 机构id
 * @param includeChild Boolean，是否包含子部门
 * @param ended Integer,0：正在进行，1：已结束
 */
export function getChannelList(data) {
  return request({
    url: `/stream/srs/channel`,
    method: 'get',
    params: data,
  });
}

/**
 * 删除直播频道
 * @param ids Array,频道id数组
 */
export function deleteChannel(ids) {
  return request({
    url: `/stream/srs/channel`,
    method: 'delete',
    data: ids,
    headers: {
      "Content-Type": "application/json;"
    }
  });
}

/**
 * 保存直播频道
 * @param name String 频道名称
 * @param srsId Long 所属服务器id
 * @param sourceDevice String 源设备
 * @param endTime Long 结束时间
 * @param concurrent Long 并发人数
 * @param orderNum Long 排序
 * @param backgroundImage 背景图片
 */
export function saveChannel(data) {
  let file = new FormData();
  Object.keys(data).forEach((key) => {
    file.append(key, data[key]);
  });
  return request({
    url: `/stream/srs/channel`,
    method: 'post',
    data: file,
    headers: {
      "Content-Type": "multipart/form-data"
    }
  });
}

/**
 * 查询直播频道详情
 * @param id 频道id
 * @returns
 */
export function getChannelDetail(id) {
  return request({
    url: `/stream/srs/channel/${id}`,
    method: 'get',
  });
}

/**
 * 直播频道发布/取消发布
 * @param id 频道id
 * @param published 0-未发布,1-已发布
 * @returns
 */
export function publishChannel(id, published) {
  return request({
    url: `/stream/srs/channel/${id}`,
    method: 'post',
    params: { published }
  });
}

/**
 * 分页查询直播流
 * @param page 当前页号
 * @param size 页内行数
 * @param deptId 机构id
 * @param includeChild Boolean，是否包含子部门
 */
export function getChannelStreamList(data) {
  return request({
    url: `/stream/srs/channel/streams`,
    method: 'get',
    params: data,
  });
}

/**
 * 分页查询直播流客户端
 * @param page 当前页号
 * @param size 页内行数
 * @param srsId：服务器id
 * @param url: 流url（16位字符串）
 */
export function getChannelStreamClients(data) {
  return request({
    url: `/stream/srs/channel/stream/clients`,
    method: 'get',
    params: data,
  });
}



/**
 * 分页查询SRS服务器
 * @params page Array 当前页号
 * @params size Array 页内行数
 * @params direction ASC/DESC
 * @params property 排序字段
 */
export function getSRSList(data) {
  return request({
    url: `/stream/srs/summary`,
    method: 'get',
    params: data,
  });
}

/**
 * 删除SRS服务器
 * @params ids Array 服务器id数组
 */
export function deleteSRS(ids) {
  return request({
    url: `/stream/srs`,
    method: 'delete',
    data: ids,
    headers: {
      "Content-Type": "application/json;"
    }
  });
}

/**
 * 保存SRS服务器
 * @param name String 服务器名称
 * @param ip String 服务器ip
 * @param type Integer 服务器类型 0:推流, 1:拉流
 * @param id Long 服务器id
 */
export function saveSRS(data) {
  return request({
    url: `/stream/srs`,
    method: 'post',
    data,
    headers: {
      "Content-Type": "application/json;"
    }
  });
}

/**
 * 查询SRS服务器详情
 * @param id SRS服务id
 * @returns
 */
export function getSRSDetail(id) {
  return request({
    url: `/stream/srs/${id}`,
    method: 'get',
  });
}

/**
 * 分页查询区域
 * @params page Array 当前页号
 * @params size Array 页内行数
 * @params direction ASC/DESC
 * @params property 排序字段
 */
export function getAreaList(data) {
  return request({
    url: `/stream/srs/area`,
    method: 'get',
    params: data,
  });
}

/**
 * 删除区域
 * @params ids Array 区域id数组
 */
export function deleteArea(ids) {
  return request({
    url: `/stream/srs/area`,
    method: 'delete',
    data: ids,
    headers: {
      "Content-Type": "application/json;"
    }
  });
}

/**
 * 保存区域
 * @param name String 区域名称
 * @param srsIds JSON数组字符串 服务器id
 */
export function saveArea(data) {
  return request({
    url: `/stream/srs/area`,
    method: 'post',
    data,
    headers: {
      "Content-Type": "application/json;"
    }
  });
}

/**
 * 查询区域详情
 * @param id 区域id
 * @returns
 */
export function getAreaDetail(id) {
  return request({
    url: `/stream/srs/area/${id}`,
    method: 'get',
  });
}

/**
 * 分页查询路由表
 * @params page Array 当前页号
 * @params size Array 页内行数
 * @params direction ASC/DESC
 * @params property 排序字段
 * @params ipSearch
 */
export function getRouterList(data) {
  return request({
    url: `/stream/srs/router`,
    method: 'get',
    params: data,
  });
}

/**
 * 删除路由表
 * @params ids Array 路由表id数组
 */
export function deleteRouter(ids) {
  return request({
    url: `/stream/srs/router`,
    method: 'delete',
    data: ids,
    headers: {
      "Content-Type": "application/json;"
    }
  });
}

/**
 * 保存路由表
 * @param name String 路由表名称
 * @param srsIds JSON数组字符串 服务器id
 */
export function saveRouter(data) {
  return request({
    url: `/stream/srs/router`,
    method: 'post',
    data,
    headers: {
      "Content-Type": "application/json;"
    }
  });
}

/**
 * 查询路由表详情
 * @param id 路由表id
 * @returns
 */
export function getRouterDetail(id) {
  return request({
    url: `/stream/srs/router/${id}`,
    method: 'get',
  });
}

/**
 * 导入路由表
 * @param data excel文件
 */
export function uploadRouter(data) {
  let file = new FormData();
  Object.keys(data).forEach((key) => {
    file.append(key, data[key]);
  });
  return request({
    url: `/stream/srs/router/import`,
    method: 'post',
    data: file,
    headers: {
      "Content-Type": "multipart/form-data"
    }
  });
}

/**
 * 分页获取频道统计
 * @param page 当前页号
 * @param size 页内行数
 * @param deptId 机构id
 * @param includeChild Boolean，是否包含子部门
 * @param ended Integer,0：正在进行，1：已结束
 * @returns
 */
export function getChannelStatisticsList(data) {
  return request({
    url: `/stream/srs/channel/statistics`,
    method: 'get',
    params: data
  });
}

/**
 * 获取单个频道统计详情
 * @param startTime 开始时间
 * @param endTime 结束时间
 * @param count 数量
 * @returns
 */
export function getChannelStatisticsDetail(url, data) {
  return request({
    url: `/stream/srs/channel/statistics/${url}`,
    method: 'get',
    params: data
  });
}

/**
 * 查询域名
 */
export function getDomainList(data) {
  return request({
    url: `/stream/srs/domain`,
    method: 'get',
    params: data,
  });
}

/**
 * 删除域名
 * @params ids Array 域名id数组
 */
export function deleteDomain(ids) {
  return request({
    url: `/stream/srs/domain`,
    method: 'delete',
    data: ids,
    headers: {
      "Content-Type": "application/json;"
    }
  });
}

/**
 * 保存域名
 * @param name 域名
 * @param deptId 部门id
 * @param vod 点播地址
 * @param bannerFile 横幅图片
 * @param bannerImage 部门id
 * @param channelThumbnailFile 频道缩略图
 * @param channelThumbnail 频道缩略图url
 */
export function saveDomain(data) {
  let file = new FormData();
  Object.keys(data).forEach((key) => {
    file.append(key, data[key]);
  });
  return request({
    url: `/stream/srs/domain`,
    method: 'post',
    data: file,
    headers: {
      "Content-Type": "multipart/form-data"
    }
  });
}

/**
 * 查询域名详情
 * @param id 域名id
 * @returns
 */
export function getDomainDetail(id) {
  return request({
    url: `/stream/srs/domain/${id}`,
    method: 'get',
  });
}

/**
 * 查询源设备列表
 */
export function getSourceDeviceList(params) {
  return request({
    url: `/stream/srs/source`,
    method: 'get',
    params
  });
}

/**
 * 查询机构源设备列表
 */
export function getDeptSourceDeviceList(deptId) {
  return request({
    url: `/stream/srs/source/dept/${deptId}`,
    method: 'get',
  });
}

/**
 * 删除源设备
 * @params ids Array 源设备id数组
 */
export function deleteSourceDevice(ids) {
  return request({
    url: `/stream/srs/source`,
    method: 'delete',
    data: ids,
    headers: {
      "Content-Type": "application/json;"
    }
  });
}

/**
 * 保存源设备
 * @param name 源设备
 * @param type 类型
 * @param ip ip
 */
export function saveSourceDevice(data) {
  return request({
    url: `/stream/srs/source`,
    method: 'post',
    data,
    headers: {
      "Content-Type": "application/json"
    }
  });
}

/**
 * 查询源设备详情
 * @param id 源设备id
 * @returns
 */
export function getSourceDeviceDetail(id) {
  return request({
    url: `/stream/srs/source/${id}`,
    method: 'get',
  });
}

/**
 * 查询源设备可用模板
 * @param sourceId 源设备id
 */
export function getSourceDeviceTemplate(sourceId) {
  return request({
    url: `/stream/srs/source/${sourceId}/template`,
    method: 'get',
  });
}

/**
 * 查询授权列表
 */
export function getAuthList() {
  return request({
    url: `/stream/srs/auth`,
    method: 'get',
  });
}

/**
 * 删除授权
 * @params ids Array 授权id数组
 */
export function deleteAuth(ids) {
  return request({
    url: `/stream/srs/auth`,
    method: 'delete',
    data: ids,
    headers: {
      "Content-Type": "application/json;"
    }
  });
}

/**
 * 保存授权
 * @param deptId 部门id
 * @param channelNum 频道并发数
 * @param audienceNum 观看并发数
 */
export function saveAuth(data) {
  return request({
    url: `/stream/srs/auth`,
    method: 'post',
    data,
    headers: {
      "Content-Type": "application/json"
    }
  });
}

/**
 * 查询授权详情
 * @param id 授权id
 * @returns
 */
export function getAuthDetail(id) {
  return request({
    url: `/stream/srs/auth/${id}`,
    method: 'get',
  });
}


