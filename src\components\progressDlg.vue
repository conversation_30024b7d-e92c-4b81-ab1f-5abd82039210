<template>
  <transition name="slide">
    <div class="upload-dlg" v-show="dlgVisible" ref="progressDlg">
      <div class="header-wrap">
        <div class="title">{{ title }}</div>
        <div class="btn-wrap">
          <el-button
            type="primary"
            :icon="minMaxIcon"
            @click="handleMinMax()"
          ></el-button>
          <el-button
            v-show="uploadProgress"
            type="primary"
            icon="el-icon-close"
            @click="handleClose()"
          ></el-button>
        </div>
      </div>
      <el-table :data="progressList" style="width: 100%" height="400px">
        <el-table-column
          prop="name"
          :label="strValue"
          width="180"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column label="上传目录" v-if="showUploadDirColumn">
          <template slot-scope="scope">
            <slot name="uploadDir" :row="scope.row"></slot>
          </template>
        </el-table-column>
        <el-table-column label="状态">
          <template slot-scope="scope">
            <i :class="scope.row.icon"></i>{{ scope.row.desc }}
          </template>
        </el-table-column>
        <el-table-column label="进度">
          <template slot-scope="scope">
            <el-progress
              v-if="scope.row.progress < 100"
              :percentage="scope.row.progress"
            ></el-progress>
            <div
              v-else-if="
                scope.row.progress == 100 &&
                  !['FAILED', 'TIMEOUT'].includes(scope.row.state)
              "
            >
              已完成
            </div>
            <div v-else>失败</div>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <el-tooltip effect="light" content="清除记录" placement="bottom">
              <el-button
                class="btn-delete"
                type="primary"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
              ></el-button>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </transition>
</template>

<script>
const PROGRESS_STATE = [
  {
    type: "PREPARING",
    desc: "准备中",
    icon: "el-icon-loading"
  },
  {
    type: "ONGOING",
    desc: "",
    icon: "el-icon-upload"
  },
  {
    type: "COMPLETED",
    desc: "成功",
    icon: "el-icon-success"
  },
  {
    type: "FAILED",
    desc: "失败",
    icon: "el-icon-error"
  },
  {
    type: "TIMEOUT",
    desc: "超时",
    icon: "el-icon-error"
  }
];

export default {
  props: {
    getProgressInterface: {
      type: Function,
      required: true
    },
    deleteProgressInterface: {
      type: Function,
      required: true
    },
    strAction: {
      type: String,
      default: "上传"
    },
    strValue: {
      type: String,
      default: "素材"
    },
    showUploadDirColumn: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dlgVisible: false,
      progressList: [],
      minState: false,
      timer: null,
      uploadProgress: false
    };
  },
  computed: {
    minMaxIcon() {
      return this.minState ? "el-icon-full-screen" : "el-icon-minus";
    },
    title() {
      return this.uploadProgress
        ? this.strAction + "完成"
        : this.strAction + "中";
    },
    progressState() {
      return PROGRESS_STATE.map(state => {
        let desc = state.desc ? state.desc : this.strAction + "中";
        return { ...state, desc };
      });
    }
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
  },
  methods: {
    show() {
      this.getProgress();
      this.dlgVisible = true;
      this.resetMinMaxState();
      let that = this;
      //只有当前定时器为null才开启定时器
      if (!this.timer) {
        this.timer = setInterval(function() {
          that.getProgress();
        }, 2000);
      }
    },
    handleClose() {
      this.dlgVisible = false;
      this.progressList = [];
      clearInterval(this.timer);
      this.timer = null;
    },
    resetMinMaxState() {
      this.minState = false;
      this.$refs.progressDlg.style.bottom = "0px";
    },
    handleMinMax() {
      this.minState = !this.minState;
      this.$refs.progressDlg.style.bottom = this.minState ? "-400px" : "0px";
    },
    getProgress() {
      this.getProgressInterface().then(res => {
        let preList = JSON.parse(JSON.stringify(this.progressList));
        let newRecordFinished = false;
        let recordFinishedNum = 0;
        if (preList.length === 0 && res.data.length > 0) {
          // 首次有数据，更新当前目录
          this.$emit("refresh");
        }

        for (let next of res.data) {
          let isNewRecord = true;
          for (let pre of preList) {
            if (pre.uuid === next.uuid) {
              isNewRecord = false;
              if (next.progress === 100 && pre.progress !== 100) {
                this.$emit("refresh");
              }
            }
          }
          if (isNewRecord && next.progress === 100) {
            newRecordFinished = true;
          }
          if (next.progress === 100) {
            recordFinishedNum++;
          }

          let data = this.progressState.find(item => item.type === next.state);
          next.icon = data && data.icon;
          next.desc = data && data.desc;
        }

        if (newRecordFinished) {
          this.$emit("refresh");
        }

        this.progressList = res.data;
        this.uploadProgress = recordFinishedNum === res.data.length;
      });
    },
    handleDelete(row) {
      this.deleteProgressInterface(row.uuid).then(res => {
        this.getProgress();
        this.$message.success(`【${row.name}】${this.strAction}记录删除成功`);
      });
    }
  }
};
</script>

<style scoped lang="scss">
.upload-dlg {
  position: fixed;
  bottom: 0;
  right: 0;
  z-index: 999;
  width: 50%;
  border-top-left-radius: 7px;
  border-top-right-radius: 7px;
  border: 1px solid #e2e2e2;
  box-shadow: 0 0 10px #ccc;
  overflow: hidden;
  margin: 0;
  background-color: #fff;
  transition: all .5s ease;
  .header-wrap {
    height: 40px;
    line-height: 40px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px 0 20px;
    .title {
      font-size: 14px;
      color: #666;
    }
    .btn-wrap {
      .el-button {
        background-color: transparent;
        border-color: transparent;
        color: #607194;
        font-size: 20px;
        padding: 10px 10px;
        & + .el-button {
          margin-left: 0;
        }
      }
    }
  }
  ::v-deep .el-table th {
    font-size: 12px;
    color: #424e67;
    height: 35px;
    background-color: #fff;
    font-weight: normal;
    box-sizing: border-box;
    &.is-leaf {
      border-top: 1px solid #ebeef5;
    }
  }
  ::v-deep .el-table tr {
    height: 40px;
    .cell {
      text-align: center;
    }
  }
}
.el-icon-success:before,
.el-icon-upload:before {
  color: rgb(92, 218, 147);
  margin-right: 4px;
}
.el-icon-upload:before {
  color: rgb(245, 220, 108);
}

.el-icon-error:before {
  color: rgb(239, 99, 99);
  margin-right: 4px;
}
::v-deep .el-progress-bar__inner {
  background-color: #a5c5f3;
}
.btn-delete {
  background: transparent;
  border-color: transparent;
  color: #1c4d81;
  font-size: 18px;
}
.slide-enter,
.slide-leave-to {
  transform: translateY(442px);
}
</style>
