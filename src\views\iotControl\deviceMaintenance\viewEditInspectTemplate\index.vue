<template>
  <div class="wrap">
    <div class="btn-wrap">
      <el-button type="primary" @click="doEdit" v-if="type === 'view'" v-action:inspectTask|editInspectTaskTemplate>编辑模板</el-button>
      <el-button type="success" @click="doSave" v-if="canEdit">保存模板</el-button>
      <el-button @click="doCancle">返回</el-button>
    </div>
    <div class="content">
      <div class="left">
        <el-card class="form-card">
          <div slot="header" class="card-header">
            <div>
              <svg-icon class="title-icon" icon-class="deviceInspect@info" />
              基本信息
            </div>
          </div>
          <el-form ref="form" :model="form" :rules="rules" label-width="153px" label-position="left">
            <el-form-item label="模板名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入模板名称" v-if="canEdit"></el-input>
              <span v-else>{{ form.name }}</span>
            </el-form-item>
            <el-form-item label="模板描述" prop="remark">
              <el-input type="textarea" :rows="2" placeholder="请输入模板描述" v-model="form.remark" v-if="canEdit"> </el-input>
              <span v-else>{{ form.remark }}</span>
            </el-form-item>
          </el-form>
        </el-card>
        <el-card class="table-card">
          <div slot="header" class="card-header">
            <div> <svg-icon class="title-icon" icon-class="deviceInspect@list" /> 巡检产品列表 </div>
            <div class="card-header-btn">
              <el-select v-model="searchProductCode" placeholder="选择产品" size="mini" style="width: 160px" class="mr-10" filterable clearable>
                <el-option v-for="(item, index) in form.params" :key="index" :label="item.productName" :value="item.productCode"> </el-option>
              </el-select>
              <el-button class="edit-btn" icon="el-icon-plus" size="mini" @click="addProduct" v-if="canEdit">添加</el-button>
              <el-button class="delete-btn" icon="el-icon-delete" size="mini" @click="deleteProduct" v-if="canEdit">删除</el-button>
            </div>
          </div>

          <el-table
            :data="searchProductCode ? form.params.filter((item) => item.productCode === searchProductCode) : form.params"
            height="100%"
            header-cell-class-name="device-table-header"
            @selection-change="handleProductSelectionChange"
          >
            <template slot="empty">
              <el-empty description="暂无产品" :image-size="60"> </el-empty>
            </template>
            <el-table-column type="selection" width="35" align="center"> </el-table-column>
            <el-table-column prop="productName" label="产品名称" min-width="180" show-overflow-tooltip> </el-table-column>
            <el-table-column label="巡检项个数" min-width="120" show-overflow-tooltip>
              <template slot-scope="{ row }">
                {{ row.inspectionParams.length }}
              </template>
            </el-table-column>
            <el-table-column label="操作" min-width="120">
              <template slot-scope="{ row }">
                <el-button @click="changeSelectedProduct(row.productCode, row.productName)" size="mini" type="primary" plain>查看巡检项</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </div>
      <div class="right">
        <el-card class="table-card">
          <div slot="header" class="card-header">
            <div class="tag-wrap">
              <svg-icon class="title-icon" icon-class="deviceInspect@detail" />
              巡检详情 <el-tag class="tag" v-if="selectedProduct.productName">{{ selectedProduct.productName }}</el-tag>
            </div>
          </div>
          <div class="basic-inspect">
            <div class="title">
              <span class="title-text"> 基本状态巡检 </span>
            </div>
            <div class="table-wrap">
              <el-table
                :data="selectedProduct.productCode ? getBasicStatusInspectList : []"
                height="100%"
                style="width: 100%"
                header-cell-class-name="device-table-header"
              >
                <template slot="empty"> 未选中巡检产品 </template>
                <el-table-column prop="name" label="巡检类型" min-width="180"> </el-table-column>
                <el-table-column prop="description" label="描述" min-width="170"> </el-table-column>
              </el-table>
            </div>
          </div>
          <div class="property-inspect">
            <div class="title">
              <span class="title-text">产品属性巡检</span>
              <div>
                <el-button class="edit-btn" icon="el-icon-plus" size="mini" @click="addPropertyInspect" v-if="canEdit">添加</el-button>
                <el-button class="delete-btn" icon="el-icon-delete" size="mini" @click="deletePropertyInspect" v-if="canEdit">删除</el-button>
              </div>
            </div>
            <div class="table-wrap">
              <el-table
                :data="getPropertyInspectParams()"
                height="100%"
                style="width: 100%"
                header-cell-class-name="device-table-header"
                @selection-change="handlePropertySelectionChange"
              >
                <template slot="empty">
                  <el-empty description="暂无巡检项" :image-size="60"> </el-empty>
                </template>
                <el-table-column type="selection" width="35" align="center"> </el-table-column>
                <el-table-column prop="metric" label="巡检属性" min-width="200" show-overflow-tooltip>
                  <template slot-scope="{ row }">
                    <template v-if="row.isCompleted">
                      {{ getPropertyName(row) }}
                    </template>
                    <template v-else>
                      <el-select v-model="row.metric" placeholder="请选择巡检属性" style="width: 180px" @change="selectedPropertyChange(row)">
                        <el-option :label="item.label" :value="item.value" v-for="item in inspectPropertyList" :key="item.value"></el-option>
                      </el-select>
                    </template>
                  </template>
                </el-table-column>
                <el-table-column prop="operator" label="比较符" min-width="130" show-overflow-tooltip>
                  <template slot-scope="{ row }">
                    <template v-if="row.isCompleted">
                      {{ PROPERTY_OPT_ENUM.find((item) => item.value === row.operator).label }}
                    </template>
                    <template v-else>
                      <el-select
                        v-model="row.operator"
                        placeholder="比较符"
                        style="width: 110px"
                        v-if="['INTEGER', 'DECIMAL'].includes(getPropertyDataType(row))"
                      >
                        <el-option :label="item.label" :value="item.value" v-for="item in PROPERTY_OPT_ENUM" :key="item.value"></el-option>
                      </el-select>
                      <!-- 非数字比较符 -->
                      <el-select v-model="row.operator" placeholder="比较符" style="width: 110px" v-else>
                        <el-option :label="item.label" :value="item.value" v-for="item in PROPERTY_OPT_ENUM_NOT_NUMBER" :key="item.value"></el-option>
                      </el-select>
                    </template>
                  </template>
                </el-table-column>
                <el-table-column prop="threshold" label="预期值" min-width="200">
                  <template slot-scope="{ row }">
                    <template v-if="row.isCompleted">
                      {{ getPropertyValueLabel(row) }}
                    </template>
                    <template v-else>
                      <!-- 数值类型 -->
                      <template v-if="['INTEGER', 'DECIMAL'].includes(getPropertyDataType(row))">
                        <el-input-number
                          v-model="row.threshold"
                          style="width: 160px"
                          controls-position="right"
                          :min="getPropertyMin(row)"
                          :max="getPropertyMax(row)"
                          :step="getPropertyStep(row)"
                        >
                        </el-input-number>
                      </template>

                      <!-- 布尔类型 -->
                      <template v-else-if="getPropertyDataType(row) === 'BOOLEAN'">
                        <el-select v-model="row.threshold" style="width: 160px">
                          <el-option v-for="option in getPropertyBolOptions(row)" :key="option.value" :label="option.label" :value="option.value">
                          </el-option>
                        </el-select>
                      </template>

                      <!-- 字符串类型 -->
                      <template v-else-if="getPropertyDataType(row) === 'STRING'">
                        <el-input v-model="row.threshold" style="width: 160px" :maxlength="getPropertyMaxLength(row)" clearable />
                      </template>

                      <!-- 枚举类型 -->
                      <template v-else-if="['ENUM', 'NUMBER_ENUM'].includes(getPropertyDataType(row))">
                        <el-select v-model="row.threshold" style="width: 160px">
                          <el-option v-for="option in getPropertyOptions(row)" :key="option.value" :label="option.label" :value="option.value">
                          </el-option>
                        </el-select>
                      </template>
                    </template>
                  </template>
                </el-table-column>
                <el-table-column prop="description" label="描述" min-width="150">
                  <template slot-scope="{ row }">
                    <template v-if="row.isCompleted">
                      {{ row.description }}
                    </template>
                    <template v-else>
                      <el-input v-model="row.description" placeholder="请输入描述" style="width: 130px"></el-input>
                    </template>
                  </template>
                </el-table-column>
                <el-table-column label="操作" min-width="100" align="center" v-if="canEdit">
                  <template slot-scope="{ row }">
                    <el-button size="mini" type="text" @click="editPropertyInspect(row)" v-if="row.isCompleted">编辑</el-button>
                    <el-button size="mini" type="success" plain @click="confirmPropertyInspect(row)" v-else>完成</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script>
  import { PROPERTY_OPT_ENUM, PROPERTY_OPT_ENUM_NOT_NUMBER } from '@/views/iotControl/enum';
  import { getProductDetailByCode, getInspectTemplateDetail, saveInspectTemplate } from '@/api/iotControl';
  import transform from '@/utils/transform';
  import addProduct from '@/views/iotControl/deviceMaintenance/viewEditInspectTemplate/modules/addProduct.vue';
  export default {
    components: { addProduct },
    props: {},
    data() {
      return {
        PROPERTY_OPT_ENUM,
        PROPERTY_OPT_ENUM_NOT_NUMBER,
        //整个任务是否可编辑
        canEdit: true,
        type: '',
        id: '',
        form: {
          id: '',
          name: '',
          //备注
          remark: '',
          //{productCode:'',productName:'',inspectionParams:[]}
          params: [],
        },
        rules: {
          name: [{ required: true, message: '请输入模板名称', trigger: 'blur' }],
        },
        basicStatusInspectList: [
          { metricType: 'onlineStatus', name: '设备状态', description: '设备状态' },
          { metricType: 'lifespan', name: '设备生命周期', description: '设备寿命状态' },
          { metricType: 'warranty', name: '设备保修状态', description: '设备保修状态' },
        ],
        //当前查看巡检项的产品
        selectedProduct: {},
        //当前产品巡检属性列表
        inspectPropertyList: [],
        //当前选中巡检属性
        propertySelection: [],
        //当前选中巡检产品
        productSelection: [],
        //产品列表筛选
        searchProductCode: '',
      };
    },
    created() {
      this.type = this.$route.params.type;
      this.id = this.$route.params.id;
      if (this.type === 'view') {
        this.getInspectTemplateDetail();
        //查看时，整个任务不可编辑
        this.canEdit = false;
      }
    },
    computed: {
      getBasicStatusInspectList() {
        const params = this.form.params.find((item) => item.productCode === this.selectedProduct.productCode)?.inspectionParams || [];
        //仅显示当前选中设备包含的
        const existingMetricTypes = params.map((param) => param.metricType) || [];
        return this.basicStatusInspectList.filter((item) => existingMetricTypes.includes(item.metricType));
      },
    },
    methods: {
      getInspectTemplateDetail() {
        getInspectTemplateDetail(this.id).then(({ data }) => {
          this.form = data;
          this.formatInspectionProducts();
        });
      },
      //编辑时，预处理产品列表
      formatInspectionProducts() {
        //每项添加isCompleted:true
        this.form.params.forEach((item) => {
          item.inspectionParams.forEach((param) => {
            this.$set(param, 'isCompleted', true);
          });
        });
      },
      addProductDlg: transform(addProduct),
      addProduct() {
        this.addProductDlg({
          propsData: {
            selectedProducts: this.form.params,
          },
          methods: {
            handleSelect: (products) => {
              //params中已有的产品不添加
              const newProducts = products
                .filter((item) => !this.form.params.some((product) => product.productCode === item.code))
                .map((item) => ({
                  productName: item.name,
                  productCode: item.code,
                  //产品默认包含在线状态和生命周期巡检
                  inspectionParams: [
                    {
                      metricType: 'onlineStatus',
                      metric: 'onlineStatus',
                      operator: 'EQ',
                      threshold: '',
                      description: '在线状态',
                      inspectionType: 'staticThreshold',
                      isCompleted: true,
                    },
                    {
                      metricType: 'lifespan',
                      metric: 'lifespan',
                      operator: 'EQ',
                      threshold: '',
                      description: '寿命状态',
                      inspectionType: 'staticThreshold',
                      isCompleted: true,
                    },
                    {
                      metricType: 'warranty',
                      metric: 'warranty',
                      operator: 'EQ',
                      threshold: '',
                      description: '保修状态',
                      inspectionType: 'staticThreshold',
                      isCompleted: true,
                    },
                  ],
                }));
              this.form.params = [...this.form.params, ...newProducts];
            },
          },
        });
      },
      deleteProduct() {
        if (this.productSelection.length === 0) {
          this.$message.warning('请先选择产品');
          return;
        }
        this.form.params = this.form.params.filter((item) => !this.productSelection.includes(item));
      },
      handleProductSelectionChange(selection) {
        this.productSelection = selection;
      },
      //查看产品巡检项
      changeSelectedProduct(productCode, productName) {
        // 切换前需校验，是否填写完成
        const currentProduct = this.form.params.find((item) => item.productCode === this.selectedProduct.productCode);
        if (currentProduct) {
          const hasIncomplete = currentProduct.inspectionParams.some((param) => !param.isCompleted);
          if (hasIncomplete) {
            this.$message.warning(`请填写产品【${currentProduct.productName}】的完整巡检项`);
            return;
          }
        }
        this.selectedProduct = { productCode, productName };
        if (this.selectedProduct?.productCode) {
          this.getInspectPropertyList();
        }
      },
      getInspectPropertyList() {
        getProductDetailByCode(this.selectedProduct.productCode).then(({ data }) => {
          //仅显示基本数据类型的属性
          this.inspectPropertyList = data.properties
            .filter((item) => ['INTEGER', 'DECIMAL', 'BOOLEAN', 'STRING', 'ENUM', 'NUMBER_ENUM'].includes(item.constraint.dataType))
            .map((item) => ({
              ...item,
              label: item.name,
              value: item.address,
            }));
        });
      },
      getPropertyInspectParams() {
        //仅显示metricType为propertyKey的巡检项
        return (
          this.form.params
            .find((item) => item.productCode === this.selectedProduct.productCode)
            ?.inspectionParams.filter((param) => param.metricType === 'propertyKey') || []
        );
      },
      handlePropertySelectionChange(selection) {
        this.propertySelection = selection;
      },
      addPropertyInspect() {
        if (!this.selectedProduct.productCode) {
          this.$message.warning('请先选择产品');
          return;
        }
        this.form.params
          .find((item) => item.productCode === this.selectedProduct.productCode)
          ?.inspectionParams.push({
            metricType: 'propertyKey',
            metric: '',
            operator: 'EQ',
            threshold: '',
            description: '',
            inspectionType: 'staticThreshold',
            //是否填写完成
            isCompleted: false,
          });
      },
      deletePropertyInspect() {
        if (this.propertySelection.length === 0) {
          this.$message.warning('请先选择巡检项');
          return;
        }
        const currentProduct = this.form.params.find((item) => item.productCode === this.selectedProduct.productCode);
        if (currentProduct) {
          currentProduct.inspectionParams = currentProduct.inspectionParams.filter((item) => !this.propertySelection.includes(item));
        }
      },
      // 获取属性的数据类型
      getPropertyDataType(row) {
        const property = this.inspectPropertyList.find((p) => p.value === row.metric);
        return property?.constraint?.dataType;
      },

      // 获取数值类型的最小值
      getPropertyMin(row) {
        const property = this.inspectPropertyList.find((p) => p.value === row.metric);
        return this.isValidValue(property?.constraint?.min) ? property.constraint.min : -Infinity;
      },

      // 获取数值类型的最大值
      getPropertyMax(row) {
        const property = this.inspectPropertyList.find((p) => p.value === row.metric);
        return this.isValidValue(property?.constraint?.max) ? property.constraint.max : Infinity;
      },

      // 获取数值类型的步长
      getPropertyStep(row) {
        const property = this.inspectPropertyList.find((p) => p.value === row.metric);
        return this.isValidValue(property?.constraint?.step) ? property.constraint.step : 1;
      },

      // 获取字符串类型的最大长度
      getPropertyMaxLength(row) {
        const property = this.inspectPropertyList.find((p) => p.value === row.metric);
        return property?.constraint?.maxLength;
      },

      // 获取枚举类型的选项
      getPropertyOptions(row) {
        const property = this.inspectPropertyList.find((p) => p.value === row.metric);
        return Object.entries(property?.constraint?.range).map(([label, value]) => ({
          value,
          label,
        }));
      },

      // 获取布尔类型的选项
      getPropertyBolOptions(row) {
        const property = this.inspectPropertyList.find((p) => p.value === row.metric);
        return [
          {
            label: property?.constraint?.trueText,
            value: true,
          },
          {
            label: property?.constraint?.falseText,
            value: false,
          },
        ];
      },

      // 判断值是否有效
      isValidValue(value) {
        return value !== undefined && value !== null && value !== '';
      },
      editPropertyInspect(row) {
        this.$set(row, 'isCompleted', false);
      },
      confirmPropertyInspect(row) {
        if (this.isValidValue(row.metric) && this.isValidValue(row.operator) && this.isValidValue(row.threshold)) {
          row.isCompleted = true;
        } else {
          this.$message.warning('请填写完整巡检项');
        }
      },
      // 取消
      doCancle() {
        this.$router.go(-1);
      },
      doEdit() {
        this.canEdit = true;
      },
      async doSave() {
        let bValid = await this.$refs.form.validate();
        if (!bValid) {
          return;
        }
        const currentProduct = this.form.params.find((item) => item.productCode === this.selectedProduct.productCode);
        if (currentProduct) {
          const hasIncomplete = currentProduct.inspectionParams.some((param) => !param.isCompleted);
          if (hasIncomplete) {
            this.$message.warning(`请填写产品【${currentProduct.productName}】的完整巡检项`);
            return;
          }
        }
        saveInspectTemplate(this.form).then(() => {
          this.$message.success('保存成功');
          this.$router.go(-1);
        });
      },
      // 获取属性名称
      getPropertyName(row) {
        const property = this.inspectPropertyList.find((p) => p.value === row.metric);
        return property?.name || row.metric;
      },
      // 获取属性显示值
      getPropertyValueLabel(row) {
        const property = this.inspectPropertyList.find((p) => p.value === row.metric);
        if (!property) return row.threshold;
        const dataType = property.constraint.dataType;
        switch (dataType) {
          case 'BOOLEAN':
            return row.threshold ? property.constraint.trueText : property.constraint.falseText;
          case 'ENUM':
          case 'NUMBER_ENUM':
            return Object.entries(property.constraint.range).find(([label, value]) => value === row.threshold)[0] || row.threshold;
          case 'INTEGER':
          case 'DECIMAL':
            return property.constraint.unit ? `${row.threshold}${property.constraint.unit}` : row.threshold;
          default:
            return row.threshold;
        }
      },
      //选中的属性发生变化,需重置巡检项设置
      selectedPropertyChange(row) {
        row.operator = 'EQ';
        row.threshold = '';
        row.description = '';
      },
    },
  };
</script>
<style lang="scss" scoped>
  .wrap {
    width: 100%;
    height: 100%;
    padding: 10px;
    display: flex;
    min-width: 1520px;
    min-height: 950px;
    flex-direction: column;
    .btn-wrap {
      margin-bottom: 10px;
    }
    .content {
      display: flex;
      width: 100%;
      flex: 1;
      min-height: 0;
      .left {
        width: 40%;
        height: 100%;
        display: flex;
        flex-direction: column;
        .table-card {
          flex: 1;
          min-height: 0;
          margin-top: 10px;
          display: flex;
          flex-direction: column;
          ::v-deep .el-card__body {
            flex: 1;
            min-height: 0;
          }
        }
      }
      .right {
        width: 60%;
        height: 100%;
        padding-left: 20px;
        .table-card {
          height: 100%;
          width: 100%;
          display: flex;
          flex-direction: column;

          ::v-deep .el-card__body {
            flex: 1;
            min-height: 0;
            display: flex;
            flex-direction: column;
          }
          .basic-inspect {
            height: 230px;
            display: flex;
            flex-direction: column;
            .title {
              margin-bottom: 10px;
              font-weight: bold;
              background-color: #f2f6ff;
              padding: 10px 5px;
              border-radius: 4px;
              .title-text {
                font-size: 14px;
              }
            }
            .table-wrap {
              flex: 1;
              min-height: 0;
            }
          }
          .property-inspect {
            margin-top: 10px;
            flex: 1;
            min-height: 0;
            display: flex;
            flex-direction: column;
            .title {
              margin-bottom: 10px;
              display: flex;
              justify-content: space-between;
              align-items: center;
              font-weight: bold;
              background-color: #f2f6ff;
              padding: 10px 5px;
              border-radius: 4px;
              .title-text {
                font-size: 14px;
              }
            }
            .table-wrap {
              flex: 1;
              min-height: 0;
            }
          }
        }
      }
    }
  }
  ::v-deep .device-table-header {
    background-color: #fafafc !important;
    color: #303133 !important;
  }
  ::v-deep .el-table {
    border: 1px solid #dfe6ec;
  }
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: bold;
    .title-icon {
      margin-right: 5px;
      color: #0054ff;
    }
  }
  .tag-wrap {
    display: flex;
    align-items: center;
    justify-content: center;
    .tag {
      margin-left: 10px;
    }
  }
</style>
