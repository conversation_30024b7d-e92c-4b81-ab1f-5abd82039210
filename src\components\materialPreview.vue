<template>
  <!--预览弹框-->
  <el-dialog
    title="预览"
    :visible.sync="bDialogPreviewVisible"
    width="550px"
    :close-on-click-modal="false"
    @close="closePreviewDlg"
  >
    <div>
      <img
        :src="material.url"
        class="src-width"
        v-if="['staticPicture', 'dynamicPicture'].includes(material.type)"
      />
      <video
        id="video"
        :src="material.url"
        preload="metadata"
        style="width: 500px;"
        controls
        v-else-if="material.type === 'video'"
        alt=""
      ></video>
      <audio
        id="audio"
        :src="material.url"
        preload="metadata"
        style="width: 500px;"
        controls
        v-else-if="material.type === 'audio'"
        alt=""
      ></audio>
      <iframe
        v-else-if="['pdf', 'office'].includes(material.type)"
        :src="material.url"
        width="100%"
        height="400"
      >
        This browser does not support PDFs. Please download the PDF to view it
      </iframe>
      <div
        v-else-if="material.type === 'txt'"
        class="src-width"
        style="height: 400px;overflow: auto;"
      >
        {{ material.szText }}
      </div>
    </div>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      material: {},
      bDialogPreviewVisible: false
    };
  },
  methods: {
    show(row) {
      this.material = row;
      this.bDialogPreviewVisible = true;
      if ("txt" === row.type) {
        $.ajax({
          async: false,
          url: row.url,
          success: function(result) {
            row.szText = result;
          }
        });
      }
    },
    /**
     * 在浏览器中打开链接
     * @param url
     */
    openInBrowser(url) {
      window.open(window.location.origin + "/" + url);
    },
    closePreviewDlg() {
      this.bDialogPreviewVisible = false;
      let audioEle = document.getElementById("audio");
      let videoEle = document.getElementById("video");
      audioEle && audioEle.pause();
      videoEle && video.pause();
      console.log(audioEle);
      console.log(videoEle);
    }
  }
};
</script>
<style lang="scss" scoped></style>
